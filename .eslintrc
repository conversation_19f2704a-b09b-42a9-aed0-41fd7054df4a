{
  "env": {
    "browser": true,
    "es2021": true,
    "node": true,
    "meteor": true,
    "mocha": true
  },
  "plugins": ["meteor", "mocha"],
  "extends": [
    "airbnb-base",
    "plugin:meteor/recommended",
    "plugin:mocha/recommended"
  ],
  "settings": {
    "import/resolver": "meteor"
  },
  "overrides": [
    {
      "env": {
        "node": true
      },
      "files": [".eslintrc.{js,cjs}"],
      "parserOptions": {
        "sourceType": "script"
      },
    },
  ],
  "parserOptions": {
    "ecmaVersion": "latest",
    "sourceType": "module"
  },
  "rules": {
    "no-underscore-dangle": [
      1,
      {
        "allow": ["_id", "_collectionName", "_str"]
      }
    ],
    "import/no-extraneous-dependencies": "off",
    "import/no-absolute-path": "off",
    "import/first": "off",
    "import/prefer-default-export": "off",
    "no-plusplus": [
      "error",
      {
        "allowForLoopAfterthoughts": true
      }
    ],
    "linebreak-style": ["error", "windows"],
    "prefer-arrow-callback": 0,
    "mocha/prefer-arrow-callback": 2
  }
}
