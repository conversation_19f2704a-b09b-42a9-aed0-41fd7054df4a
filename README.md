# Chemicals

Application for managing Chemicals Disposal. Chemicals is a combination of Pack and Receipt. These applications are included in the repo as packages.

## Package Structure

The directory structure of the packages is as follows:

````text
package-name/
  .npm/
  client/
  deployment/
  imports/
  private/
  public/
  server/
  package.js
  [package-name].js
````

### Notes

**.npm**
Contains the npm modules specific to the package.

**client**
Normally used for importing client startup. In packages, it's used to contain CSS styling for the application.

**package.js**
Contains configuration for the package.

- Atmosphere/NPM Dependencies
- Static Assets
- Styling
- Exports

**[package-name.js]**
Main export of package. Use this for exporting modules to be used by the parent application or common startup routines. In the case of Pack and Receipt, this is split into the server-side and client-side components of the package.

## Overall Package Architecture

The package architecture required some common Meteor functionality to be pulled out into packages to share functionality across the application.

A **Router** and **Collection Manager** package have been added. These are simple wrappers around the **FlowRouter** package and **Meteor.Collection**. They allow extra options to be added during initialisation like prefixes to the routes and collection names of each package.

The **Mediator** package facilitates communication between Pack and Receipt while keeping both packages independent of each other.

## Package Implementation

Meteor applications export two main entry points, one for the server and one for the client. These **index** files are now imported by the parent application instead of being used internally to initialise each app independently.

Importing these entry points means during startup all client and server code is initialised within the parent application including routes, collections, API methods, views, etc...

**/imports/startup/package-settings.js** contains a config file that is used during startup. This contains configuration such as collection and route prefixes.
