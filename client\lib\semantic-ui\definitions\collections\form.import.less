/*
  DO NOT MODIFY - This file has been generated and will be regenerated
  Semantic UI v2.2.6
*/
/*!
 * # Semantic UI - Form
 * http://github.com/semantic-org/semantic-ui/
 *
 *
 * Released under the MIT license
 * http://opensource.org/licenses/MIT
 *
 */

/*******************************
            Theme
*******************************/

@type    : 'collection';
@element : 'form';

@import (multiple) '../../theme.config.import.less';

/*******************************
            Elements
*******************************/

/*--------------------
        Form
---------------------*/

.ui.form {
  position: relative;
  max-width: 100%;
}

/*--------------------
        Content
---------------------*/

.ui.form > p {
  margin: @paragraphMargin;
}

/*--------------------
        Field
---------------------*/

.ui.form .field {
  clear: both;
  margin: @fieldMargin;
}

.ui.form .field:last-child,
.ui.form .fields:last-child .field {
  margin-bottom: 0em;
}

.ui.form .fields .field {
  clear: both;
  margin: 0em;
}


/*--------------------
        Labels
---------------------*/

.ui.form .field > label {
  display: block;
  margin: @labelMargin;
  color: @labelColor;
  font-size: @labelFontSize;
  font-weight: @labelFontWeight;
  text-transform: @labelTextTransform;
}

/*--------------------
    Standard Inputs
---------------------*/


.ui.form textarea,
.ui.form input:not([type]),
.ui.form input[type="date"],
.ui.form input[type="datetime-local"],
.ui.form input[type="email"],
.ui.form input[type="number"],
.ui.form input[type="password"],
.ui.form input[type="search"],
.ui.form input[type="tel"],
.ui.form input[type="time"],
.ui.form input[type="text"],
.ui.form input[type="file"],
.ui.form input[type="url"] {
  width: @inputWidth;
  vertical-align: top;
}

/* Set max height on unusual input */
.ui.form ::-webkit-datetime-edit,
.ui.form ::-webkit-inner-spin-button {
  height: @inputLineHeight;
}

.ui.form input:not([type]),
.ui.form input[type="date"],
.ui.form input[type="datetime-local"],
.ui.form input[type="email"],
.ui.form input[type="number"],
.ui.form input[type="password"],
.ui.form input[type="search"],
.ui.form input[type="tel"],
.ui.form input[type="time"],
.ui.form input[type="text"],
.ui.form input[type="file"],
.ui.form input[type="url"] {
  font-family: @inputFont;
  margin: 0em;
  outline: none;
  -webkit-appearance: none;
  tap-highlight-color:  rgba(255, 255, 255, 0);

  line-height: @inputLineHeight;
  padding: @inputPadding;
  font-size: @inputFontSize;

  background: @inputBackground;
  border: @inputBorder;
  color: @inputColor;
  border-radius: @inputBorderRadius;
  box-shadow: @inputBoxShadow;
  transition: @inputTransition;
}

/* Text Area */
.ui.form textarea {
  margin: 0em;
  -webkit-appearance: none;
  tap-highlight-color:  rgba(255, 255, 255, 0);

  padding: @textAreaPadding;
  font-size: @textAreaFontSize;
  background: @textAreaBackground;
  border: @textAreaBorder;
  outline: none;
  color: @inputColor;
  border-radius: @inputBorderRadius;
  box-shadow: @inputBoxShadow;
  transition: @textAreaTransition;
  font-size: @textAreaFontSize;
  line-height: @textAreaLineHeight;
  resize: @textAreaResize;
}
.ui.form textarea:not([rows]) {
  height: @textAreaHeight;
  min-height: @textAreaMinHeight;
  max-height: @textAreaMaxHeight;
}

.ui.form textarea,
.ui.form input[type="checkbox"] {
  vertical-align: @checkboxVerticalAlign;
}

/*--------------------------
  Input w/ attached Button
---------------------------*/

.ui.form input.attached {
  width: auto;
}


/*--------------------
     Basic Select
---------------------*/

.ui.form select {
  display: block;
  height: auto;
  width: 100%;
  background: @selectBackground;
  border: @selectBorder;
  border-radius: @selectBorderRadius;
  box-shadow: @selectBoxShadow;
  padding: @selectPadding;
  color: @selectColor;
  transition: @selectTransition;
}

/*--------------------
       Dropdown
---------------------*/

/* Block */
.ui.form .field > .selection.dropdown {
  width: 100%;
}
.ui.form .field > .selection.dropdown > .dropdown.icon {
  float: right;
}

/* Inline */
.ui.form .inline.fields .field > .selection.dropdown,
.ui.form .inline.field > .selection.dropdown {
  width: auto;
}
.ui.form .inline.fields .field > .selection.dropdown > .dropdown.icon,
.ui.form .inline.field > .selection.dropdown > .dropdown.icon {
  float: none;
}

/*--------------------
       UI Input
---------------------*/

/* Block */
.ui.form .field .ui.input,
.ui.form .fields .field .ui.input,
.ui.form .wide.field .ui.input {
  width: 100%;
}

/* Inline  */
.ui.form .inline.fields .field:not(.wide) .ui.input,
.ui.form .inline.field:not(.wide) .ui.input {
  width: auto;
  vertical-align: middle;
}

/* Auto Input */
.ui.form .fields .field .ui.input input,
.ui.form .field .ui.input input {
  width: auto;
}

/* Full Width Input */
.ui.form .ten.fields .ui.input input,
.ui.form .nine.fields .ui.input input,
.ui.form .eight.fields .ui.input input,
.ui.form .seven.fields .ui.input input,
.ui.form .six.fields .ui.input input,
.ui.form .five.fields .ui.input input,
.ui.form .four.fields .ui.input input,
.ui.form .three.fields .ui.input input,
.ui.form .two.fields .ui.input input,
.ui.form .wide.field .ui.input input {
  flex: 1 0 auto;
  width: 0px;
}


/*--------------------
   Types of Messages
---------------------*/

.ui.form .success.message,
.ui.form .warning.message,
.ui.form .error.message {
  display: none;
}

/* Assumptions */
.ui.form .message:first-child {
  margin-top: 0px;
}

/*--------------------
   Validation Prompt
---------------------*/

.ui.form .field .prompt.label {
  white-space: normal;
  background: @promptBackground !important;
  border: @promptBorder !important;
  color: @promptTextColor !important;
}
.ui.form .inline.fields .field .prompt,
.ui.form .inline.field .prompt {
  vertical-align: top;
  margin: @inlinePromptMargin;
}
.ui.form .inline.fields .field .prompt:before,
.ui.form .inline.field .prompt:before {
  border-width: 0px 0px @inlinePromptBorderWidth @inlinePromptBorderWidth;
  bottom: auto;
  right: auto;
  top: 50%;
  left: 0em;
}


/*******************************
            States
*******************************/

/*--------------------
      Autofilled
---------------------*/

.ui.form .field.field input:-webkit-autofill {
  box-shadow: 0px 0px 0px 100px @inputAutoFillBackground inset !important;
  border-color: @inputAutoFillBorder !important;
}

/* Focus */
.ui.form .field.field input:-webkit-autofill:focus {
  box-shadow: 0px 0px 0px 100px @inputAutoFillFocusBackground inset !important;
  border-color: @inputAutoFillFocusBorder !important;
}

/* Error */
.ui.form .error.error input:-webkit-autofill {
  box-shadow: 0px 0px 0px 100px @inputAutoFillErrorBackground inset !important;
  border-color: @inputAutoFillErrorBorder !important;
}



/*--------------------
      Placeholder
---------------------*/

/* browsers require these rules separate */
.ui.form ::-webkit-input-placeholder {
  color: @inputPlaceholderColor;
}
.ui.form :-ms-input-placeholder {
  color: @inputPlaceholderColor;
}
.ui.form ::-moz-placeholder {
  color: @inputPlaceholderColor;
}

.ui.form :focus::-webkit-input-placeholder {
  color: @inputPlaceholderFocusColor;
}
.ui.form :focus:-ms-input-placeholder {
  color: @inputPlaceholderFocusColor;
}
.ui.form :focus::-moz-placeholder {
  color: @inputPlaceholderFocusColor;
}

/* Error Placeholder */
.ui.form .error ::-webkit-input-placeholder {
  color: @inputErrorPlaceholderColor;
}
.ui.form .error :-ms-input-placeholder {
  color: @inputErrorPlaceholderColor !important;
}
.ui.form .error ::-moz-placeholder {
  color: @inputErrorPlaceholderColor;
}

.ui.form .error :focus::-webkit-input-placeholder {
  color: @inputErrorPlaceholderFocusColor;
}
.ui.form .error :focus:-ms-input-placeholder {
  color: @inputErrorPlaceholderFocusColor !important;
}
.ui.form .error :focus::-moz-placeholder {
  color: @inputErrorPlaceholderFocusColor;
}


/*--------------------
        Focus
---------------------*/

.ui.form input:not([type]):focus,
.ui.form input[type="date"]:focus,
.ui.form input[type="datetime-local"]:focus,
.ui.form input[type="email"]:focus,
.ui.form input[type="number"]:focus,
.ui.form input[type="password"]:focus,
.ui.form input[type="search"]:focus,
.ui.form input[type="tel"]:focus,
.ui.form input[type="time"]:focus,
.ui.form input[type="text"]:focus,
.ui.form input[type="file"]:focus,
.ui.form input[type="url"]:focus {
  color: @inputFocusColor;
  border-color: @inputFocusBorderColor;
  border-radius: @inputFocusBorderRadius;
  background: @inputFocusBackground;
  box-shadow: @inputFocusBoxShadow;
}
.ui.form textarea:focus {
  color: @textAreaFocusColor;
  border-color: @textAreaFocusBorderColor;
  border-radius: @textAreaFocusBorderRadius;
  background: @textAreaFocusBackground;
  box-shadow: @textAreaFocusBoxShadow;
  -webkit-appearance: none;
}


/*--------------------
        Success
---------------------*/

/* On Form */
.ui.form.success .success.message:not(:empty) {
  display: block;
}
.ui.form.success .compact.success.message:not(:empty) {
  display: inline-block;
}
.ui.form.success .icon.success.message:not(:empty) {
  display: flex;
}

/*--------------------
        Warning
---------------------*/

/* On Form */
.ui.form.warning .warning.message:not(:empty) {
  display: block;
}
.ui.form.warning .compact.warning.message:not(:empty) {
  display: inline-block;
}
.ui.form.warning .icon.warning.message:not(:empty) {
  display: flex;
}

/*--------------------
        Error
---------------------*/

/* On Form */
.ui.form.error .error.message:not(:empty) {
  display: block;
}
.ui.form.error .compact.error.message:not(:empty) {
  display: inline-block;
}
.ui.form.error .icon.error.message:not(:empty) {
  display: flex;
}

/* On Field(s) */
.ui.form .fields.error .field label,
.ui.form .field.error label,
.ui.form .fields.error .field .input,
.ui.form .field.error .input {
  color: @formErrorColor;
}

.ui.form .fields.error .field .corner.label,
.ui.form .field.error .corner.label {
  border-color: @formErrorColor;
  color: @white;
}

.ui.form .fields.error .field textarea,
.ui.form .fields.error .field select,
.ui.form .fields.error .field input:not([type]),
.ui.form .fields.error .field input[type="date"],
.ui.form .fields.error .field input[type="datetime-local"],
.ui.form .fields.error .field input[type="email"],
.ui.form .fields.error .field input[type="number"],
.ui.form .fields.error .field input[type="password"],
.ui.form .fields.error .field input[type="search"],
.ui.form .fields.error .field input[type="tel"],
.ui.form .fields.error .field input[type="time"],
.ui.form .fields.error .field input[type="text"],
.ui.form .fields.error .field input[type="file"],
.ui.form .fields.error .field input[type="url"],
.ui.form .field.error textarea,
.ui.form .field.error select,
.ui.form .field.error input:not([type]),
.ui.form .field.error input[type="date"],
.ui.form .field.error input[type="datetime-local"],
.ui.form .field.error input[type="email"],
.ui.form .field.error input[type="number"],
.ui.form .field.error input[type="password"],
.ui.form .field.error input[type="search"],
.ui.form .field.error input[type="tel"],
.ui.form .field.error input[type="time"],
.ui.form .field.error input[type="text"],
.ui.form .field.error input[type="file"],
.ui.form .field.error input[type="url"] {
  background: @formErrorBackground;
  border-color: @formErrorBorder;
  color: @formErrorColor;
  border-radius: @inputErrorBorderRadius;
  box-shadow: @inputErrorBoxShadow;
}
.ui.form .field.error textarea:focus,
.ui.form .field.error select:focus,
.ui.form .field.error input:not([type]):focus,
.ui.form .field.error input[type="date"]:focus,
.ui.form .field.error input[type="datetime-local"]:focus,
.ui.form .field.error input[type="email"]:focus,
.ui.form .field.error input[type="number"]:focus,
.ui.form .field.error input[type="password"]:focus,
.ui.form .field.error input[type="search"]:focus,
.ui.form .field.error input[type="tel"]:focus,
.ui.form .field.error input[type="time"]:focus,
.ui.form .field.error input[type="text"]:focus,
.ui.form .field.error input[type="file"]:focus,
.ui.form .field.error input[type="url"]:focus {
  background: @inputErrorFocusBackground;
  border-color: @inputErrorFocusBorder;
  color: @inputErrorFocusColor;

  -webkit-appearance: none;
  box-shadow: @inputErrorFocusBoxShadow;
}

/* Preserve Native Select Stylings */
.ui.form .field.error select {
  -webkit-appearance: menulist-button;
}

/*------------------
    Dropdown Error
--------------------*/

.ui.form .fields.error .field .ui.dropdown,
.ui.form .fields.error .field .ui.dropdown .item,
.ui.form .field.error .ui.dropdown,
.ui.form .field.error .ui.dropdown .text,
.ui.form .field.error .ui.dropdown .item {
  background: @formErrorBackground;
  color: @formErrorColor;
}
.ui.form .fields.error .field .ui.dropdown,
.ui.form .field.error .ui.dropdown {
  border-color: @formErrorBorder !important;
}
.ui.form .fields.error .field .ui.dropdown:hover,
.ui.form .field.error .ui.dropdown:hover {
  border-color: @formErrorBorder !important;
}
.ui.form .fields.error .field .ui.dropdown:hover .menu,
.ui.form .field.error .ui.dropdown:hover .menu {
  border-color: @formErrorBorder;
}
.ui.form .fields.error .field .ui.multiple.selection.dropdown > .label,
.ui.form .field.error .ui.multiple.selection.dropdown > .label {
  background-color: @dropdownErrorLabelBackground;
  color: @dropdownErrorLabelColor;
}

/* Hover */
.ui.form .fields.error .field .ui.dropdown .menu .item:hover,
.ui.form .field.error .ui.dropdown .menu .item:hover {
  background-color: @dropdownErrorHoverBackground;
}

/* Selected */
.ui.form .fields.error .field .ui.dropdown .menu .selected.item,
.ui.form .field.error .ui.dropdown .menu .selected.item {
  background-color: @dropdownErrorSelectedBackground;
}


/* Active */
.ui.form .fields.error .field .ui.dropdown .menu .active.item,
.ui.form .field.error .ui.dropdown .menu .active.item {
  background-color: @dropdownErrorActiveBackground !important;
}

/*--------------------
    Checkbox Error
---------------------*/

.ui.form .fields.error .field .checkbox:not(.toggle):not(.slider) label,
.ui.form .field.error .checkbox:not(.toggle):not(.slider) label,
.ui.form .fields.error .field .checkbox:not(.toggle):not(.slider) .box,
.ui.form .field.error .checkbox:not(.toggle):not(.slider) .box {
  color: @formErrorColor;
}
.ui.form .fields.error .field .checkbox:not(.toggle):not(.slider) label:before,
.ui.form .field.error .checkbox:not(.toggle):not(.slider) label:before,
.ui.form .fields.error .field .checkbox:not(.toggle):not(.slider) .box:before,
.ui.form .field.error .checkbox:not(.toggle):not(.slider) .box:before {
  background: @formErrorBackground;
  border-color: @formErrorBorder;
}
.ui.form .fields.error .field .checkbox label:after,
.ui.form .field.error .checkbox label:after,
.ui.form .fields.error .field .checkbox .box:after,
.ui.form .field.error .checkbox .box:after {
  color: @formErrorColor;
}

/*--------------------
       Disabled
---------------------*/

.ui.form .disabled.fields .field,
.ui.form .disabled.field,
.ui.form .field :disabled {
  pointer-events: none;
  opacity: @disabledOpacity;
}
.ui.form .field.disabled > label,
.ui.form .fields.disabled > label {
  opacity: @disabledLabelOpacity;
}
.ui.form .field.disabled :disabled {
  opacity: 1;
}


/*--------------
    Loading
---------------*/

.ui.loading.form {
  position: relative;
  cursor: default;
  pointer-events: none;
}
.ui.loading.form:before {
  position: absolute;
  content: '';
  top: 0%;
  left: 0%;
  background: @loaderDimmerColor;
  width: 100%;
  height: 100%;
  z-index: @loaderDimmerZIndex;
}
.ui.loading.form:after {
  position: absolute;
  content: '';
  top: 50%;
  left: 50%;

  margin: @loaderMargin;
  width: @loaderSize;
  height: @loaderSize;

  animation: form-spin @loaderSpeed linear;
  animation-iteration-count: infinite;

  border-radius: @circularRadius;

  border-color: @loaderLineColor @loaderFillColor @loaderFillColor @loaderFillColor;
  border-style: solid;
  border-width: @loaderLineWidth;

  box-shadow: 0px 0px 0px 1px transparent;
  visibility: visible;
  z-index: @loaderLineZIndex;
}

@keyframes form-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}


/*******************************
         Element Types
*******************************/

/*--------------------
     Required Field
---------------------*/

.ui.form .required.fields:not(.grouped) > .field > label:after,
.ui.form .required.fields.grouped > label:after,
.ui.form .required.field > label:after,
.ui.form .required.fields:not(.grouped) > .field > .checkbox:after,
.ui.form .required.field > .checkbox:after {
  margin: @requiredMargin;
  content: @requiredContent;
  color: @requiredColor;
}

.ui.form .required.fields:not(.grouped) > .field > label:after,
.ui.form .required.fields.grouped > label:after,
.ui.form .required.field > label:after {
  display: inline-block;
  vertical-align: top;
}

.ui.form .required.fields:not(.grouped) > .field > .checkbox:after,
.ui.form .required.field > .checkbox:after {
  position: absolute;
  top: 0%;
  left: 100%;
}


/*******************************
           Variations
*******************************/


/*--------------------
    Inverted Colors
---------------------*/

.ui.inverted.form label,
.ui.form .inverted.segment label,
.ui.form .inverted.segment .ui.checkbox label,
.ui.form .inverted.segment .ui.checkbox .box,
.ui.inverted.form .ui.checkbox label,
.ui.inverted.form .ui.checkbox .box,
.ui.inverted.form .inline.fields > label,
.ui.inverted.form .inline.fields .field > label,
.ui.inverted.form .inline.fields .field > p,
.ui.inverted.form .inline.field > label,
.ui.inverted.form .inline.field > p {
  color: @invertedLabelColor;
}

/* Inverted Field */
.ui.inverted.form input:not([type]),
.ui.inverted.form input[type="date"],
.ui.inverted.form input[type="datetime-local"],
.ui.inverted.form input[type="email"],
.ui.inverted.form input[type="number"],
.ui.inverted.form input[type="password"],
.ui.inverted.form input[type="search"],
.ui.inverted.form input[type="tel"],
.ui.inverted.form input[type="time"],
.ui.inverted.form input[type="text"],
.ui.inverted.form input[type="file"],
.ui.inverted.form input[type="url"] {
  background: @invertedInputBackground;
  border-color: @invertedInputBorderColor;
  color: @invertedInputColor;
  box-shadow: @invertedInputBoxShadow;
}


/*--------------------
     Field Groups
---------------------*/

/* Grouped Vertically */
.ui.form .grouped.fields {
  display: block;
  margin: @groupedMargin;
}
.ui.form .grouped.fields:last-child {
  margin-bottom: 0em;
}

.ui.form .grouped.fields > label {
  margin: @groupedLabelMargin;
  color: @groupedLabelColor;
  font-size: @groupedLabelFontSize;
  font-weight: @groupedLabelFontWeight;
  text-transform: @groupedLabelTextTransform;
}

.ui.form .grouped.fields .field,
.ui.form .grouped.inline.fields .field {
  display: block;
  margin: @groupedFieldMargin;
  padding: 0em;
}

/*--------------------
        Fields
---------------------*/

/* Split fields */
.ui.form .fields {
  display: flex;
  flex-direction: row;
  margin: @fieldsMargin;
}
.ui.form .fields > .field {
  flex: 0 1 auto;
  padding-left: (@gutterWidth / 2);
  padding-right: (@gutterWidth / 2);
}
.ui.form .fields > .field:first-child {
  border-left: none;
  box-shadow: none;
}

/* Other Combinations */
.ui.form .two.fields > .fields,
.ui.form .two.fields > .field {
  width: @twoColumn;
}
.ui.form .three.fields > .fields,
.ui.form .three.fields > .field {
  width: @threeColumn;
}
.ui.form .four.fields > .fields,
.ui.form .four.fields > .field {
  width: @fourColumn;
}
.ui.form .five.fields > .fields,
.ui.form .five.fields > .field {
  width: @fiveColumn;
}
.ui.form .six.fields > .fields,
.ui.form .six.fields > .field {
  width: @sixColumn;
}
.ui.form .seven.fields > .fields,
.ui.form .seven.fields > .field {
  width: @sevenColumn;
}
.ui.form .eight.fields > .fields,
.ui.form .eight.fields > .field {
  width: @eightColumn;
}
.ui.form .nine.fields > .fields,
.ui.form .nine.fields > .field {
  width: @nineColumn;
}
.ui.form .ten.fields > .fields,
.ui.form .ten.fields > .field {
  width: @tenColumn;
}

/* Swap to full width on mobile */
@media only screen and (max-width : @largestMobileScreen) {
  .ui.form .fields {
    flex-wrap: wrap;
  }

  .ui[class*="equal width"].form .fields > .field,
  .ui.form [class*="equal width"].fields > .field,
  .ui.form .two.fields > .fields,
  .ui.form .two.fields > .field,
  .ui.form .three.fields > .fields,
  .ui.form .three.fields > .field,
  .ui.form .four.fields > .fields,
  .ui.form .four.fields > .field,
  .ui.form .five.fields > .fields,
  .ui.form .five.fields > .field,
  .ui.form .six.fields > .fields,
  .ui.form .six.fields > .field,
  .ui.form .seven.fields > .fields,
  .ui.form .seven.fields > .field,
  .ui.form .eight.fields > .fields,
  .ui.form .eight.fields > .field,
  .ui.form .nine.fields > .fields,
  .ui.form .nine.fields > .field,
  .ui.form .ten.fields > .fields,
  .ui.form .ten.fields > .field {
    width: @oneColumn !important;
    margin: 0em 0em @rowDistance;
  }
}


/* Sizing Combinations */
.ui.form .fields .wide.field {
  width: @oneWide;
  padding-left: (@gutterWidth / 2);
  padding-right: (@gutterWidth / 2);
}

.ui.form .one.wide.field {
  width: @oneWide !important;
}
.ui.form .two.wide.field {
  width: @twoWide !important;
}
.ui.form .three.wide.field {
  width: @threeWide !important;
}
.ui.form .four.wide.field {
  width: @fourWide !important;
}
.ui.form .five.wide.field {
  width: @fiveWide !important;
}
.ui.form .six.wide.field {
  width: @sixWide !important;
}
.ui.form .seven.wide.field {
  width: @sevenWide !important;
}
.ui.form .eight.wide.field {
  width: @eightWide !important;
}
.ui.form .nine.wide.field {
  width: @nineWide !important;
}
.ui.form .ten.wide.field {
  width: @tenWide !important;
}
.ui.form .eleven.wide.field {
  width: @elevenWide !important;
}
.ui.form .twelve.wide.field {
  width: @twelveWide !important;
}
.ui.form .thirteen.wide.field {
  width: @thirteenWide !important;
}
.ui.form .fourteen.wide.field {
  width: @fourteenWide !important;
}
.ui.form .fifteen.wide.field {
  width: @fifteenWide !important;
}
.ui.form .sixteen.wide.field {
  width: @sixteenWide !important;
}

/* Swap to full width on mobile */
@media only screen and (max-width : @largestMobileScreen) {
  .ui.form .two.fields > .fields,
  .ui.form .two.fields > .field,
  .ui.form .three.fields > .fields,
  .ui.form .three.fields > .field,
  .ui.form .four.fields > .fields,
  .ui.form .four.fields > .field,
  .ui.form .five.fields > .fields,
  .ui.form .five.fields > .field,
  .ui.form .fields > .two.wide.field,
  .ui.form .fields > .three.wide.field,
  .ui.form .fields > .four.wide.field,
  .ui.form .fields > .five.wide.field,
  .ui.form .fields > .six.wide.field,
  .ui.form .fields > .seven.wide.field,
  .ui.form .fields > .eight.wide.field,
  .ui.form .fields > .nine.wide.field,
  .ui.form .fields > .ten.wide.field,
  .ui.form .fields > .eleven.wide.field,
  .ui.form .fields > .twelve.wide.field,
  .ui.form .fields > .thirteen.wide.field,
  .ui.form .fields > .fourteen.wide.field,
  .ui.form .fields > .fifteen.wide.field,
  .ui.form .fields > .sixteen.wide.field {
    width: @oneColumn !important;
  }
  .ui.form .fields {
    margin-bottom: 0em;
  }
}

/*--------------------
     Equal Width
---------------------*/

.ui[class*="equal width"].form .fields > .field,
.ui.form [class*="equal width"].fields > .field {
  width: 100%;
  flex: 1 1 auto;
}

/*--------------------
    Inline Fields
---------------------*/

.ui.form .inline.fields {
  margin: @fieldMargin;
  align-items: center;
}
.ui.form .inline.fields .field {
  margin: 0em;
  padding: @inlineFieldsMargin;
}

/* Inline Label */
.ui.form .inline.fields > label,
.ui.form .inline.fields .field > label,
.ui.form .inline.fields .field > p,
.ui.form .inline.field > label,
.ui.form .inline.field > p {
  display: inline-block;
  width: auto;
  margin-top: 0em;
  margin-bottom: 0em;
  vertical-align: baseline;
  font-size: @inlineLabelFontSize;
  font-weight: @inlineLabelFontWeight;
  color: @inlineLabelColor;
  text-transform: @inlineLabelTextTransform;
}

/* Grouped Inline Label */
.ui.form .inline.fields > label {
  margin: @groupedInlineLabelMargin;
}

/* Inline Input */
.ui.form .inline.fields .field > input,
.ui.form .inline.fields .field > select,
.ui.form .inline.field > input,
.ui.form .inline.field > select {
  display: inline-block;
  width: auto;

  margin-top: 0em;
  margin-bottom: 0em;

  vertical-align: middle;
  font-size: @inlineInputSize;
}

/* Label */
.ui.form .inline.fields .field > :first-child,
.ui.form .inline.field > :first-child {
  margin: 0em @inlineLabelDistance 0em 0em;
}
.ui.form .inline.fields .field > :only-child,
.ui.form .inline.field > :only-child {
  margin: 0em;
}

/* Wide */
.ui.form .inline.fields .wide.field {
  display: flex;
  align-items: center;
}
.ui.form .inline.fields .wide.field > input,
.ui.form .inline.fields .wide.field > select {
  width: 100%;
}


/*--------------------
        Sizes
---------------------*/

.ui.mini.form {
  font-size: @mini;
}
.ui.tiny.form {
  font-size: @tiny;
}
.ui.small.form {
  font-size: @small;
}
.ui.form {
  font-size: @medium;
}
.ui.large.form {
  font-size: @large;
}
.ui.big.form {
  font-size: @big;
}
.ui.huge.form {
  font-size: @huge;
}
.ui.massive.form {
  font-size: @massive;
}

.loadUIOverrides();
