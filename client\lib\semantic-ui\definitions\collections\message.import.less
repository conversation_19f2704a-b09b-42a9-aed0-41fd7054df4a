/*
  DO NOT MODIFY - This file has been generated and will be regenerated
  Semantic UI v2.2.6
*/
/*!
 * # Semantic UI - Message
 * http://github.com/semantic-org/semantic-ui/
 *
 *
 * Released under the MIT license
 * http://opensource.org/licenses/MIT
 *
 */

/*******************************
            Theme
*******************************/

@type    : 'collection';
@element : 'message';

@import (multiple) '../../theme.config.import.less';

/*******************************
            Message
*******************************/

.ui.message {
  position: relative;
  min-height: 1em;
  margin: @verticalMargin 0em;
  background: @background;
  padding: @padding;
  line-height: @lineHeight;
  color: @textColor;
  transition: @transition;
  border-radius: @borderRadius;
  box-shadow: @boxShadow;
}

.ui.message:first-child {
  margin-top: 0em;
}
.ui.message:last-child {
  margin-bottom: 0em;
}


/*--------------
     Content
---------------*/

/* Header */
.ui.message .header {
  display: @headerDisplay;
  font-family: @headerFont;
  font-weight: @headerFontWeight;
  margin: @headerMargin;
}

/* Default font size */
.ui.message .header:not(.ui) {
  font-size: @headerFontSize;
}

/* Paragraph */
.ui.message p {
  opacity: @messageTextOpacity;
  margin: @messageParagraphMargin 0em;
}
.ui.message p:first-child {
  margin-top: 0em;
}
.ui.message p:last-child {
  margin-bottom: 0em;
}
.ui.message .header + p {
  margin-top: @headerParagraphDistance;
}

/* List */
.ui.message .list:not(.ui) {
  text-align: left;
  padding: 0em;
  opacity: @listOpacity;
  list-style-position: @listStylePosition;
  margin: @listMargin 0em 0em;
}
.ui.message .list:not(.ui):first-child {
  margin-top: 0em;
}
.ui.message .list:not(.ui):last-child {
  margin-bottom: 0em;
}
.ui.message .list:not(.ui) li {
  position: relative;
  list-style-type: none;
  margin: 0em 0em @listItemMargin @listItemIndent;
  padding: 0em;
}
.ui.message .list:not(.ui) li:before {
  position: absolute;
  content: '•';
  left: -1em;
  height: 100%;
  vertical-align: baseline;
}
.ui.message .list:not(.ui) li:last-child {
  margin-bottom: 0em;
}


/* Icon */
.ui.message > .icon {
  margin-right: @iconDistance;
}

/* Close Icon */
.ui.message > .close.icon {
  cursor: pointer;
  position: absolute;
  margin: 0em;
  top: @closeTopDistance;
  right: @closeRightDistance;
  opacity: @closeOpacity;
  transition: @closeTransition;
}
.ui.message > .close.icon:hover {
  opacity: 1;
}

/* First / Last Element */
.ui.message > :first-child {
  margin-top: 0em;
}
.ui.message > :last-child {
  margin-bottom: 0em;
}

/*******************************
            Coupling
*******************************/

.ui.dropdown .menu > .message {
  margin: 0px -@borderWidth;
}

/*******************************
            States
*******************************/

/*--------------
    Visible
---------------*/

.ui.visible.visible.visible.visible.message {
  display: block;
}

.ui.icon.visible.visible.visible.visible.message {
  display: flex;
}

/*--------------
     Hidden
---------------*/

.ui.hidden.hidden.hidden.hidden.message {
  display: none;
}


/*******************************
            Variations
*******************************/

/*--------------
    Compact
---------------*/

.ui.compact.message {
  display: inline-block;
}


/*--------------
    Attached
---------------*/

.ui.attached.message {
  margin-bottom: @attachedYOffset;
  border-radius: @borderRadius @borderRadius 0em 0em;
  box-shadow: @attachedBoxShadow;
  margin-left: @attachedXOffset;
  margin-right: @attachedXOffset;
}
.ui.attached + .ui.attached.message:not(.top):not(.bottom) {
  margin-top: @attachedYOffset;
  border-radius: 0em;
}
.ui.bottom.attached.message {
  margin-top: @attachedYOffset;
  border-radius: 0em 0em @borderRadius @borderRadius;
  box-shadow: @attachedBottomBoxShadow;
}
.ui.bottom.attached.message:not(:last-child) {
  margin-bottom: @verticalMargin;
}
.ui.attached.icon.message {
  width: auto;
}


/*--------------
      Icon
---------------*/

.ui.icon.message {
  display: flex;
  width: 100%;
  align-items: center;
}
.ui.icon.message > .icon:not(.close) {
  display: block;
  flex: 0 0 auto;
  width: auto;
  line-height: 1;
  vertical-align: @iconVerticalAlign;
  font-size: @iconSize;
  opacity: @iconOpacity;
}
.ui.icon.message > .content {
  display: block;
  flex: 1 1 auto;
  vertical-align: @iconVerticalAlign;
}


.ui.icon.message .icon:not(.close) + .content {
  padding-left: @iconContentDistance;
}
.ui.icon.message .circular.icon {
  width: 1em;
}

/*--------------
    Floating
---------------*/

.ui.floating.message {
  box-shadow: @floatingBoxShadow;
}


/*--------------
     Colors
---------------*/

.ui.black.message {
  background-color: @black;
  color: @invertedTextColor;
}

/*--------------
     Types
---------------*/

/* Positive */
.ui.positive.message {
  background-color: @positiveBackgroundColor;
  color: @positiveTextColor;
}
.ui.positive.message,
.ui.attached.positive.message {
  box-shadow: @positiveBoxShadow;
}
.ui.positive.message .header {
  color: @positiveHeaderColor;
}

/* Negative */
.ui.negative.message {
  background-color: @negativeBackgroundColor;
  color: @negativeTextColor;
}
.ui.negative.message,
.ui.attached.negative.message {
  box-shadow: @negativeBoxShadow;
}
.ui.negative.message .header {
  color: @negativeHeaderColor;
}

/* Info */
.ui.info.message {
  background-color: @infoBackgroundColor;
  color: @infoTextColor;
}
.ui.info.message,
.ui.attached.info.message {
  box-shadow: @infoBoxShadow;
}
.ui.info.message .header {
  color: @infoHeaderColor;
}

/* Warning */
.ui.warning.message {
  background-color: @warningBackgroundColor;
  color: @warningTextColor;
}
.ui.warning.message,
.ui.attached.warning.message {
  box-shadow: @warningBoxShadow;
}
.ui.warning.message .header {
  color: @warningHeaderColor;
}

/* Error */
.ui.error.message {
  background-color: @errorBackgroundColor;
  color: @errorTextColor;
}
.ui.error.message,
.ui.attached.error.message {
  box-shadow: @errorBoxShadow;
}
.ui.error.message .header {
  color: @errorHeaderColor;
}

/* Success */
.ui.success.message {
  background-color: @successBackgroundColor;
  color: @successTextColor;
}
.ui.success.message,
.ui.attached.success.message {
  box-shadow: @successBoxShadow;
}
.ui.success.message .header {
  color: @successHeaderColor;
}


/* Colors */
.ui.inverted.message,
.ui.black.message {
  background-color: @black;
  color: @invertedTextColor;
}

.ui.red.message {
  background-color: @redBackground;
  color: @redTextColor;
  box-shadow: @redBoxShadow;
}
.ui.red.message .header {
  color: @redHeaderColor;
}

.ui.orange.message {
  background-color: @orangeBackground;
  color: @orangeTextColor;
  box-shadow: @orangeBoxShadow;
}
.ui.orange.message .header {
  color: @orangeHeaderColor;
}

.ui.yellow.message {
  background-color: @yellowBackground;
  color: @yellowTextColor;
  box-shadow: @yellowBoxShadow;
}
.ui.yellow.message .header {
  color: @yellowHeaderColor;
}

.ui.olive.message {
  background-color: @oliveBackground;
  color: @oliveTextColor;
  box-shadow: @oliveBoxShadow;
}
.ui.olive.message .header {
  color: @oliveHeaderColor;
}

.ui.green.message {
  background-color: @greenBackground;
  color: @greenTextColor;
  box-shadow: @greenBoxShadow;
}
.ui.green.message .header {
  color: @greenHeaderColor;
}

.ui.teal.message {
  background-color: @tealBackground;
  color: @tealTextColor;
  box-shadow: @tealBoxShadow;
}
.ui.teal.message .header {
  color: @tealHeaderColor;
}

.ui.blue.message {
  background-color: @blueBackground;
  color: @blueTextColor;
  box-shadow: @blueBoxShadow;
}
.ui.blue.message .header {
  color: @blueHeaderColor;
}

.ui.violet.message {
  background-color: @violetBackground;
  color: @violetTextColor;
  box-shadow: @violetBoxShadow;
}
.ui.violet.message .header {
  color: @violetHeaderColor;
}

.ui.purple.message {
  background-color: @purpleBackground;
  color: @purpleTextColor;
  box-shadow: @purpleBoxShadow;
}
.ui.purple.message .header {
  color: @purpleHeaderColor;
}

.ui.pink.message {
  background-color: @pinkBackground;
  color: @pinkTextColor;
  box-shadow: @pinkBoxShadow;
}
.ui.pink.message .header {
  color: @pinkHeaderColor;
}

.ui.brown.message {
  background-color: @brownBackground;
  color: @brownTextColor;
  box-shadow: @brownBoxShadow;
}
.ui.brown.message .header {
  color: @brownHeaderColor;
}

/*--------------
     Sizes
---------------*/

.ui.mini.message {
  font-size: @relativeMini;
}
.ui.tiny.message {
  font-size: @relativeTiny;
}
.ui.small.message {
  font-size: @relativeSmall;
}
.ui.message {
  font-size: @relativeMedium;
}
.ui.large.message {
  font-size: @relativeLarge;
}
.ui.big.message {
  font-size: @relativeBig;
}
.ui.huge.message {
  font-size: @relativeHuge;
}
.ui.massive.message {
  font-size: @relativeMassive;
}

.loadUIOverrides();
