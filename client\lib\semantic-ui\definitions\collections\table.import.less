/*
  DO NOT MODIFY - This file has been generated and will be regenerated
  Semantic UI v2.2.6
*/
/*!
 * # Semantic UI - Table
 * http://github.com/semantic-org/semantic-ui/
 *
 *
 * Released under the MIT license
 * http://opensource.org/licenses/MIT
 *
 */


/*******************************
            Theme
*******************************/

@type    : 'collection';
@element : 'table';

@import (multiple) '../../theme.config.import.less';

/*******************************
             Table
*******************************/

/* Prototype */
.ui.table {
  width: 100%;
  background: @background;
  margin: @margin;
  border: @border;
  box-shadow: @boxShadow;
  border-radius: @borderRadius;
  text-align: @textAlign;
  color: @color;
  border-collapse: @borderCollapse;
  border-spacing: @borderSpacing;
}

.ui.table:first-child {
  margin-top: 0em;
}
.ui.table:last-child {
  margin-bottom: 0em;
}

/*******************************
             Parts
*******************************/

/* Table Content */
.ui.table th,
.ui.table td {
  transition: @transition;
}

/* Headers */
.ui.table thead {
  box-shadow: @headerBoxShadow;
}
.ui.table thead th {
  cursor: auto;
  background: @headerBackground;
  text-align: @headerAlign;
  color: @headerColor;
  padding: @headerVerticalPadding @headerHorizontalPadding;
  vertical-align: @headerVerticalAlign;
  font-style: @headerFontStyle;
  font-weight: @headerFontWeight;
  text-transform: @headerTextTransform;
  border-bottom: @headerBorder;
  border-left: @headerDivider;
}

.ui.table thead tr > th:first-child {
  border-left: none;
}

.ui.table thead tr:first-child > th:first-child {
  border-radius: @borderRadius 0em 0em 0em;
}
.ui.table thead tr:first-child > th:last-child {
  border-radius: 0em @borderRadius 0em 0em;
}
.ui.table thead tr:first-child > th:only-child {
  border-radius: @borderRadius @borderRadius 0em 0em;
}

/* Footer */
.ui.table tfoot {
  box-shadow: @footerBoxShadow;
}
.ui.table tfoot th {
  cursor: auto;
  border-top: @footerBorder;
  background: @footerBackground;
  text-align: @footerAlign;
  color: @footerColor;
  padding: @footerVerticalPadding @footerHorizontalPadding;
  vertical-align: @footerVerticalAlign;
  font-style: @footerFontStyle;
  font-weight: @footerFontWeight;
  text-transform: @footerTextTransform;
}
.ui.table tfoot tr > th:first-child {
  border-left: none;
}
.ui.table tfoot tr:first-child > th:first-child {
  border-radius: 0em 0em 0em @borderRadius;
}
.ui.table tfoot tr:first-child > th:last-child {
  border-radius: 0em 0em @borderRadius 0em;
}
.ui.table tfoot tr:first-child > th:only-child {
  border-radius: 0em 0em @borderRadius @borderRadius;
}

/* Table Row */
.ui.table tr td {
  border-top: @rowBorder;
}
.ui.table tr:first-child td {
  border-top: none;
}

/* Table Cells */
.ui.table td {
  padding: @cellVerticalPadding @cellHorizontalPadding;
  text-align: @cellTextAlign;
}

/* Icons */
.ui.table > .icon {
  vertical-align: @iconVerticalAlign;
}
.ui.table > .icon:only-child {
  margin: 0em;
}

/* Table Segment */
.ui.table.segment {
  padding: 0em;
}
.ui.table.segment:after {
  display: none;
}
.ui.table.segment.stacked:after {
  display: block;
}


/* Responsive */
@media only screen and (max-width : @largestMobileScreen) {
  .ui.table:not(.unstackable) {
    width: 100%;
  }
  .ui.table:not(.unstackable) tbody,
  .ui.table:not(.unstackable) tr,
  .ui.table:not(.unstackable) tr > th,
  .ui.table:not(.unstackable) tr > td  {
    display: block !important;
    width: auto !important;
    display: block !important;
  }

  .ui.table:not(.unstackable) {
    padding: 0em;
  }
  .ui.table:not(.unstackable) thead {
    display: @responsiveHeaderDisplay;
  }
  .ui.table:not(.unstackable) tfoot {
    display: @responsiveFooterDisplay;
  }
  .ui.table:not(.unstackable) tr {
    padding-top: @responsiveRowVerticalPadding;
    padding-bottom: @responsiveRowVerticalPadding;
    box-shadow: @responsiveRowBoxShadow;
  }

  .ui.table:not(.unstackable) tr > th,
  .ui.table:not(.unstackable) tr > td {
    background: none;
    border: none !important;
    padding: @responsiveCellVerticalPadding @responsiveCellHorizontalPadding !important;
    box-shadow: @responsiveCellBoxShadow;
  }
  .ui.table:not(.unstackable) th:first-child,
  .ui.table:not(.unstackable) td:first-child {
    font-weight: bold;
  }

  /* Definition Table */
  .ui.definition.table:not(.unstackable) thead th:first-child {
    box-shadow: none !important;
  }
}


/*******************************
            Coupling
*******************************/

/* UI Image */
.ui.table th .image,
.ui.table th .image img,
.ui.table td .image,
.ui.table td .image img {
  max-width: none;
}


/*******************************
             Types
*******************************/

/*--------------
    Complex
---------------*/

.ui.structured.table {
  border-collapse: collapse;
}
.ui.structured.table thead th {
  border-left: @headerDivider;
  border-right: @headerDivider;
}
.ui.structured.sortable.table thead th {
  border-left: @sortableBorder;
  border-right: @sortableBorder;
}
.ui.structured.basic.table th {
  border-left: @basicTableHeaderDivider;
  border-right: @basicTableHeaderDivider;
}
.ui.structured.celled.table tr th,
.ui.structured.celled.table tr td {
  border-left: @cellBorder;
  border-right: @cellBorder;
}

/*--------------
   Definition
---------------*/

.ui.definition.table thead:not(.full-width) th:first-child {
  pointer-events: none;
  background: @definitionHeaderBackground;
  font-weight: @definitionHeaderFontWeight;
  color: @definitionHeaderColor;
  box-shadow: -@borderWidth -@borderWidth 0px @borderWidth @definitionPageBackground;
}

.ui.definition.table tfoot:not(.full-width) th:first-child {
  pointer-events: none;
  background: @definitionFooterBackground;
  font-weight: @definitionFooterColor;
  color: @definitionFooterFontWeight;
  box-shadow: @borderWidth @borderWidth 0px @borderWidth @definitionPageBackground;
}

/* Remove Border */
.ui.celled.definition.table thead:not(.full-width) th:first-child {
  box-shadow: 0px -@borderWidth 0px @borderWidth @definitionPageBackground;
}
.ui.celled.definition.table tfoot:not(.full-width) th:first-child {
  box-shadow: 0px @borderWidth 0px @borderWidth @definitionPageBackground;
}

/* Highlight Defining Column */
.ui.definition.table tr td:first-child:not(.ignored),
.ui.definition.table tr td.definition {
  background: @definitionColumnBackground;
  font-weight: @definitionColumnFontWeight;
  color: @definitionColumnColor;
  text-transform: @definitionColumnTextTransform;
  box-shadow: @definitionColumnBoxShadow;
  text-align: @definitionColumnTextAlign;
  font-size: @definitionColumnFontSize;
  padding-left: @definitionColumnHorizontalPadding;
  padding-right: @definitionColumnHorizontalPadding;
}


/* Fix 2nd Column */
.ui.definition.table thead:not(.full-width) th:nth-child(2) {
  border-left: @borderWidth solid @borderColor;
}
.ui.definition.table tfoot:not(.full-width) th:nth-child(2) {
  border-left: @borderWidth solid @borderColor;
}
.ui.definition.table td:nth-child(2) {
  border-left: @borderWidth solid @borderColor;
}


/*******************************
             States
*******************************/

/*--------------
    Positive
---------------*/

.ui.table tr.positive,
.ui.table td.positive {
  box-shadow: @positiveBoxShadow;
}
.ui.table tr.positive,
.ui.table td.positive {
  background: @positiveBackgroundColor !important;
  color: @positiveColor !important;
}

/*--------------
     Negative
---------------*/

.ui.table tr.negative,
.ui.table td.negative {
  box-shadow: @negativeBoxShadow;
}
.ui.table tr.negative,
.ui.table td.negative {
  background: @negativeBackgroundColor !important;
  color: @negativeColor !important;
}

/*--------------
      Error
---------------*/

.ui.table tr.error,
.ui.table td.error {
  box-shadow: @errorBoxShadow;
}
.ui.table tr.error,
.ui.table td.error {
  background: @errorBackgroundColor !important;
  color: @errorColor !important;
}
/*--------------
     Warning
---------------*/

.ui.table tr.warning,
.ui.table td.warning {
  box-shadow: @warningBoxShadow;
}
.ui.table tr.warning,
.ui.table td.warning {
  background: @warningBackgroundColor !important;
  color: @warningColor !important;
}

/*--------------
     Active
---------------*/

.ui.table tr.active,
.ui.table td.active {
  box-shadow: @activeBoxShadow;
}
.ui.table tr.active,
.ui.table td.active {
  background: @activeBackgroundColor !important;
  color: @activeColor !important;
}



/*--------------
     Disabled
---------------*/

.ui.table tr.disabled td,
.ui.table tr td.disabled,
.ui.table tr.disabled:hover,
.ui.table tr:hover td.disabled {
  pointer-events: none;
  color: @disabledTextColor;
}

/*******************************
          Variations
*******************************/

/*--------------
    Stackable
---------------*/

@media only screen and (max-width : @largestTabletScreen) {

  .ui[class*="tablet stackable"].table,
  .ui[class*="tablet stackable"].table tbody,
  .ui[class*="tablet stackable"].table tr,
  .ui[class*="tablet stackable"].table tr > th,
  .ui[class*="tablet stackable"].table tr > td  {
    display: block !important;
    width: 100% !important;
    display: block !important;
  }

  .ui[class*="tablet stackable"].table {
    padding: 0em;
  }
  .ui[class*="tablet stackable"].table thead {
    display: @responsiveHeaderDisplay;
  }
  .ui[class*="tablet stackable"].table tfoot {
    display: @responsiveFooterDisplay;
  }
  .ui[class*="tablet stackable"].table tr {
    padding-top: @responsiveRowVerticalPadding;
    padding-bottom: @responsiveRowVerticalPadding;
    box-shadow: @responsiveRowBoxShadow;
  }
  .ui[class*="tablet stackable"].table tr > th,
  .ui[class*="tablet stackable"].table tr > td {
    background: none;
    border: none !important;
    padding: @responsiveCellVerticalPadding @responsiveCellHorizontalPadding;
    box-shadow: @responsiveCellBoxShadow;
  }

  /* Definition Table */
  .ui.definition[class*="tablet stackable"].table thead th:first-child {
    box-shadow: none !important;
  }
}

/*--------------
 Text Alignment
---------------*/

.ui.table[class*="left aligned"],
.ui.table [class*="left aligned"] {
  text-align: left;
}
.ui.table[class*="center aligned"],
.ui.table [class*="center aligned"] {
  text-align: center;
}
.ui.table[class*="right aligned"],
.ui.table [class*="right aligned"] {
  text-align: right;
}

/*------------------
 Vertical Alignment
------------------*/

.ui.table[class*="top aligned"],
.ui.table [class*="top aligned"] {
  vertical-align: top;
}
.ui.table[class*="middle aligned"],
.ui.table [class*="middle aligned"] {
  vertical-align: middle;
}
.ui.table[class*="bottom aligned"],
.ui.table [class*="bottom aligned"] {
  vertical-align: bottom;
}

/*--------------
    Collapsing
---------------*/

.ui.table th.collapsing,
.ui.table td.collapsing {
  width: 1px;
  white-space: nowrap;
}

/*--------------
     Fixed
---------------*/

.ui.fixed.table {
  table-layout: fixed;
}

.ui.fixed.table th,
.ui.fixed.table td {
  overflow: hidden;
  text-overflow: ellipsis;
}


/*--------------
   Selectable
---------------*/

.ui.selectable.table tbody tr:hover,
.ui.table tbody tr td.selectable:hover {
  background: @selectableBackground !important;
  color: @selectableTextColor !important;
}
.ui.selectable.inverted.table tbody tr:hover,
.ui.inverted.table tbody tr td.selectable:hover {
  background: @selectableInvertedBackground !important;
  color: @selectableInvertedTextColor !important;
}

/* Selectable Cell Link */
.ui.table tbody tr td.selectable {
  padding: 0em;
}
.ui.table tbody tr td.selectable > a:not(.ui) {
  display: block;
  color: inherit;
  padding: @cellVerticalPadding @cellHorizontalPadding;
}

/* Other States */
.ui.selectable.table tr.error:hover,
.ui.table tr td.selectable.error:hover,
.ui.selectable.table tr:hover td.error {
  background: @errorBackgroundHover !important;
  color: @errorColorHover !important;
}
.ui.selectable.table tr.warning:hover,
.ui.table tr td.selectable.warning:hover,
.ui.selectable.table tr:hover td.warning {
  background: @warningBackgroundHover !important;
  color: @warningColorHover !important;
}
.ui.selectable.table tr.active:hover,
.ui.table tr td.selectable.active:hover,
.ui.selectable.table tr:hover td.active {
  background: @activeBackgroundColor !important;
  color: @activeColor !important;
}
.ui.selectable.table tr.positive:hover,
.ui.table tr td.selectable.positive:hover,
.ui.selectable.table tr:hover td.positive {
  background: @positiveBackgroundHover !important;
  color: @positiveColorHover !important;
}
.ui.selectable.table tr.negative:hover,
.ui.table tr td.selectable.negative:hover,
.ui.selectable.table tr:hover td.negative {
  background: @negativeBackgroundHover !important;
  color: @negativeColorHover !important;
}



/*-------------------
      Attached
--------------------*/

/* Middle */
.ui.attached.table {
  top: 0px;
  bottom: 0px;
  border-radius: 0px;
  margin: 0em @attachedHorizontalOffset;
  width: @attachedWidth;
  max-width: @attachedWidth;
  box-shadow: @attachedBoxShadow;
  border: @attachedBorder;
}
.ui.attached + .ui.attached.table:not(.top) {
  border-top: none;
}

/* Top */
.ui[class*="top attached"].table {
  bottom: 0px;
  margin-bottom: 0em;
  top: @attachedTopOffset;
  margin-top: @verticalMargin;
  border-radius: @borderRadius @borderRadius 0em 0em;
}
.ui.table[class*="top attached"]:first-child {
  margin-top: 0em;
}

/* Bottom */
.ui[class*="bottom attached"].table {
  bottom: 0px;
  margin-top: 0em;
  top: @attachedBottomOffset;
  margin-bottom: @verticalMargin;
  box-shadow: @attachedBottomBoxShadow;
  border-radius: 0em 0em @borderRadius @borderRadius;
}
.ui[class*="bottom attached"].table:last-child {
  margin-bottom: 0em;
}

/*--------------
     Striped
---------------*/

/* Table Striping */
.ui.striped.table > tr:nth-child(2n),
.ui.striped.table tbody tr:nth-child(2n) {
  background-color: @stripedBackground;
}

/* Stripes */
.ui.inverted.striped.table > tr:nth-child(2n),
.ui.inverted.striped.table tbody tr:nth-child(2n) {
  background-color: @invertedStripedBackground;
}

/* Allow striped active hover */
.ui.striped.selectable.selectable.selectable.table tbody tr.active:hover {
  background: @activeBackgroundHover !important;
  color: @activeColorHover !important;
}

/*--------------
   Single Line
---------------*/

.ui.table[class*="single line"],
.ui.table [class*="single line"] {
  white-space: nowrap;
}
.ui.table[class*="single line"],
.ui.table [class*="single line"] {
  white-space: nowrap;
}

/*-------------------
       Colors
--------------------*/

/* Red */
.ui.red.table {
  border-top: @coloredBorderSize solid @red;
}
.ui.inverted.red.table {
  background-color: @red !important;
  color: @white !important;
}

/* Orange */
.ui.orange.table {
  border-top: @coloredBorderSize solid @orange;
}
.ui.inverted.orange.table {
  background-color: @orange !important;
  color: @white !important;
}

/* Yellow */
.ui.yellow.table {
  border-top: @coloredBorderSize solid @yellow;
}
.ui.inverted.yellow.table {
  background-color: @yellow !important;
  color: @white !important;
}

/* Olive */
.ui.olive.table {
  border-top: @coloredBorderSize solid @olive;
}
.ui.inverted.olive.table {
  background-color: @olive !important;
  color: @white !important;
}

/* Green */
.ui.green.table {
  border-top: @coloredBorderSize solid @green;
}
.ui.inverted.green.table {
  background-color: @green !important;
  color: @white !important;
}

/* Teal */
.ui.teal.table {
  border-top: @coloredBorderSize solid @teal;
}
.ui.inverted.teal.table {
  background-color: @teal !important;
  color: @white !important;
}

/* Blue */
.ui.blue.table {
  border-top: @coloredBorderSize solid @blue;
}
.ui.inverted.blue.table {
  background-color: @blue !important;
  color: @white !important;
}

/* Violet */
.ui.violet.table {
  border-top: @coloredBorderSize solid @violet;
}
.ui.inverted.violet.table {
  background-color: @violet !important;
  color: @white !important;
}

/* Purple */
.ui.purple.table {
  border-top: @coloredBorderSize solid @purple;
}
.ui.inverted.purple.table {
  background-color: @purple !important;
  color: @white !important;
}

/* Pink */
.ui.pink.table {
  border-top: @coloredBorderSize solid @pink;
}
.ui.inverted.pink.table {
  background-color: @pink !important;
  color: @white !important;
}

/* Brown */
.ui.brown.table {
  border-top: @coloredBorderSize solid @brown;
}
.ui.inverted.brown.table {
  background-color: @brown !important;
  color: @white !important;
}

/* Grey */
.ui.grey.table {
  border-top: @coloredBorderSize solid @grey;
}
.ui.inverted.grey.table {
  background-color: @grey !important;
  color: @white !important;
}

/* Black */
.ui.black.table {
  border-top: @coloredBorderSize solid @black;
}
.ui.inverted.black.table {
  background-color: @black !important;
  color: @white !important;
}


/*--------------
  Column Count
---------------*/

/* Grid Based */
.ui.one.column.table td {
  width: @oneColumn;
}
.ui.two.column.table td {
  width: @twoColumn;
}
.ui.three.column.table td {
  width: @threeColumn;
}
.ui.four.column.table td {
  width: @fourColumn;
}
.ui.five.column.table td {
  width: @fiveColumn;
}
.ui.six.column.table td {
  width: @sixColumn;
}
.ui.seven.column.table td {
  width: @sevenColumn;
}
.ui.eight.column.table td {
  width: @eightColumn;
}
.ui.nine.column.table td {
  width: @nineColumn;
}
.ui.ten.column.table td {
  width: @tenColumn;
}
.ui.eleven.column.table td {
  width: @elevenColumn;
}
.ui.twelve.column.table td {
  width: @twelveColumn;
}
.ui.thirteen.column.table td {
  width: @thirteenColumn;
}
.ui.fourteen.column.table td {
  width: @fourteenColumn;
}
.ui.fifteen.column.table td {
  width: @fifteenColumn;
}
.ui.sixteen.column.table td {
  width: @sixteenColumn;
}

/* Column Width */
.ui.table th.one.wide,
.ui.table td.one.wide {
  width: @oneWide;
}
.ui.table th.two.wide,
.ui.table td.two.wide {
  width: @twoWide;
}
.ui.table th.three.wide,
.ui.table td.three.wide {
  width: @threeWide;
}
.ui.table th.four.wide,
.ui.table td.four.wide {
  width: @fourWide;
}
.ui.table th.five.wide,
.ui.table td.five.wide {
  width: @fiveWide;
}
.ui.table th.six.wide,
.ui.table td.six.wide {
  width: @sixWide;
}
.ui.table th.seven.wide,
.ui.table td.seven.wide {
  width: @sevenWide;
}
.ui.table th.eight.wide,
.ui.table td.eight.wide {
  width: @eightWide;
}
.ui.table th.nine.wide,
.ui.table td.nine.wide {
  width: @nineWide;
}
.ui.table th.ten.wide,
.ui.table td.ten.wide {
  width: @tenWide;
}
.ui.table th.eleven.wide,
.ui.table td.eleven.wide {
  width: @elevenWide;
}
.ui.table th.twelve.wide,
.ui.table td.twelve.wide {
  width: @twelveWide;
}
.ui.table th.thirteen.wide,
.ui.table td.thirteen.wide {
  width: @thirteenWide;
}
.ui.table th.fourteen.wide,
.ui.table td.fourteen.wide {
  width: @fourteenWide;
}
.ui.table th.fifteen.wide,
.ui.table td.fifteen.wide {
  width: @fifteenWide;
}
.ui.table th.sixteen.wide,
.ui.table td.sixteen.wide {
  width: @sixteenWide;
}

/*--------------
    Sortable
---------------*/

.ui.sortable.table thead th {
  cursor: pointer;
  white-space: nowrap;
  border-left: @sortableBorder;
  color: @sortableColor;
}
.ui.sortable.table thead th:first-child {
  border-left: none;
}
.ui.sortable.table thead th.sorted,
.ui.sortable.table thead th.sorted:hover {
  user-select: none;
}

.ui.sortable.table thead th:after {
  display: none;
  font-style: normal;
  font-weight: normal;
  text-decoration: inherit;
  content: '';
  height: 1em;
  width: @sortableIconWidth;
  opacity: @sortableIconOpacity;
  margin: 0em 0em 0em @sortableIconDistance;
  font-family: @sortableIconFont;
}
.ui.sortable.table thead th.ascending:after {
  content: @sortableIconAscending;
}
.ui.sortable.table thead th.descending:after {
  content: @sortableIconDescending;
}

/* Hover */
.ui.sortable.table th.disabled:hover {
  cursor: auto;
  color: @sortableDisabledColor;
}
.ui.sortable.table thead th:hover {
  background: @sortableHoverBackground;
  color: @sortableHoverColor;
}

/* Sorted */
.ui.sortable.table thead th.sorted {
  background: @sortableActiveBackground;
  color: @sortableActiveColor;
}
.ui.sortable.table thead th.sorted:after {
  display: inline-block;
}

/* Sorted Hover */
.ui.sortable.table thead th.sorted:hover {
  background: @sortableActiveHoverBackground;
  color: @sortableActiveHoverColor;
}

/* Inverted */
.ui.inverted.sortable.table thead th.sorted {
  background: @sortableInvertedActiveBackground;
  color: @sortableInvertedActiveColor;
}
.ui.inverted.sortable.table thead th:hover {
  background: @sortableInvertedHoverBackground;
  color: @sortableInvertedHoverColor;
}
.ui.inverted.sortable.table thead th {
  border-left-color: @sortableInvertedBorderColor;
  border-right-color: @sortableInvertedBorderColor;
}


/*--------------
    Inverted
---------------*/

/* Text Color */
.ui.inverted.table {
  background: @invertedBackground;
  color: @invertedCellColor;
  border: @invertedBorder;
}
.ui.inverted.table th {
  background-color: @invertedHeaderBackground;
  border-color: @invertedHeaderBorderColor !important;
  color: @invertedHeaderColor;
}
.ui.inverted.table tr td {
  border-color: @invertedCellBorderColor !important;
}

.ui.inverted.table tr.disabled td,
.ui.inverted.table tr td.disabled,
.ui.inverted.table tr.disabled:hover td,
.ui.inverted.table tr:hover td.disabled {
  pointer-events: none;
  color: @invertedDisabledTextColor;
}

/* Definition */
.ui.inverted.definition.table tfoot:not(.full-width) th:first-child,
.ui.inverted.definition.table thead:not(.full-width) th:first-child {
  background: @definitionPageBackground;
}
.ui.inverted.definition.table tr td:first-child {
  background: @invertedDefinitionColumnBackground;
  color: @invertedDefinitionColumnColor;
}

/*--------------
   Collapsing
---------------*/

.ui.collapsing.table {
  width: auto;
}

/*--------------
      Basic
---------------*/

.ui.basic.table {
  background: @basicTableBackground;
  border: @basicTableBorder;
  box-shadow: @basicBoxShadow;
}
.ui.basic.table thead,
.ui.basic.table tfoot {
  box-shadow: none;
}
.ui.basic.table th {
  background: @basicTableHeaderBackground;
  border-left: @basicTableHeaderDivider;
}
.ui.basic.table tbody tr {
  border-bottom: @basicTableCellBorder;
}
.ui.basic.table td {
  background: @basicTableCellBackground;
}
.ui.basic.striped.table tbody tr:nth-child(2n) {
  background-color: @basicTableStripedBackground !important;
}

/* Very Basic */
.ui[class*="very basic"].table {
  border: none;
}
.ui[class*="very basic"].table:not(.sortable):not(.striped) th,
.ui[class*="very basic"].table:not(.sortable):not(.striped) td {
  padding: @basicTableCellPadding;
}
.ui[class*="very basic"].table:not(.sortable):not(.striped) th:first-child,
.ui[class*="very basic"].table:not(.sortable):not(.striped) td:first-child {
  padding-left: 0em;
}
.ui[class*="very basic"].table:not(.sortable):not(.striped) th:last-child,
.ui[class*="very basic"].table:not(.sortable):not(.striped) td:last-child {
  padding-right: 0em;
}
.ui[class*="very basic"].table:not(.sortable):not(.striped) thead tr:first-child th {
  padding-top: 0em;
}

/*--------------
     Celled
---------------*/

.ui.celled.table tr th,
.ui.celled.table tr td {
  border-left: @cellBorder;
}
.ui.celled.table tr th:first-child,
.ui.celled.table tr td:first-child {
  border-left: none;
}

/*--------------
     Padded
---------------*/

.ui.padded.table th {
  padding-left: @paddedHorizontalPadding;
  padding-right: @paddedHorizontalPadding;
}
.ui.padded.table th,
.ui.padded.table td {
  padding: @paddedVerticalPadding @paddedHorizontalPadding;
}

/* Very */
.ui[class*="very padded"].table th {
  padding-left: @veryPaddedHorizontalPadding;
  padding-right: @veryPaddedHorizontalPadding;
}
.ui[class*="very padded"].table td {
  padding: @veryPaddedVerticalPadding @veryPaddedHorizontalPadding;
}

/*--------------
     Compact
---------------*/

.ui.compact.table th {
  padding-left: @compactHorizontalPadding;
  padding-right: @compactHorizontalPadding;
}
.ui.compact.table td {
  padding: @compactVerticalPadding @compactHorizontalPadding;
}

/* Very */
.ui[class*="very compact"].table th {
  padding-left: @veryCompactHorizontalPadding;
  padding-right: @veryCompactHorizontalPadding;
}
.ui[class*="very compact"].table td {
  padding: @veryCompactVerticalPadding @veryCompactHorizontalPadding;
}

/*--------------
      Sizes
---------------*/

/* Small */
.ui.small.table {
  font-size: @small;
}

/* Standard */
.ui.table {
  font-size: @medium;
}

/* Large */
.ui.large.table {
  font-size: @large;
}

.loadUIOverrides();
