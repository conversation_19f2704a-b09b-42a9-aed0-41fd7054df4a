/*
  DO NOT MODIFY - This file has been generated and will be regenerated
  Semantic UI v2.2.6
*/
/*!
 * # Semantic UI - Icon
 * http://github.com/semantic-org/semantic-ui/
 *
 *
 * Released under the MIT license
 * http://opensource.org/licenses/MIT
 *
 */


/*******************************
            Theme
*******************************/

@type    : 'element';
@element : 'icon';

@import (multiple) '../../theme.config.import.less';


/*******************************
             Icon
*******************************/

@font-face {
  font-family: 'Icons';
  src: @fallbackSRC;
  src: @src;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-decoration: inherit;
  text-transform: none;
}

i.icon {
  display: inline-block;
  opacity: @opacity;

  margin: 0em @distanceFromText 0em 0em;

  width: @width;
  height: @height;

  font-family: 'Icons';
  font-style: normal;
  font-weight: normal;
  text-decoration: inherit;
  text-align: center;

  speak: none;
  font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  backface-visibility: hidden;
}

i.icon:before {
  background: none !important;
}

/*******************************
             Types
*******************************/

/*--------------
    Loading
---------------*/

i.icon.loading {
  height: 1em;
  line-height: 1;
  animation: icon-loading @loadingDuration linear infinite;
}
@keyframes icon-loading {
 from {
    transform: rotate(0deg);
 }
 to {
    transform: rotate(360deg);
 }
}

/*******************************
             States
*******************************/

i.icon.hover {
  opacity: 1 !important;
}

i.icon.active {
  opacity: 1 !important;
}

i.emphasized.icon {
  opacity: 1 !important;
}

i.disabled.icon {
  opacity: @disabledOpacity !important;
}


/*******************************
           Variations
*******************************/


/*-------------------
        Fitted
--------------------*/

i.fitted.icon {
  width: auto;
  margin: 0em;
}

/*-------------------
         Link
--------------------*/

i.link.icon, i.link.icons {
  cursor: pointer;
  opacity: @linkOpacity;
  transition: opacity @defaultDuration @defaultEasing;
}
i.link.icon:hover, i.link.icons:hover {
  opacity: 1 !important;
}

/*-------------------
      Circular
--------------------*/

i.circular.icon {
  border-radius: 500em !important;
  line-height: 1 !important;

  padding: @circularPadding !important;
  box-shadow: @circularShadow;

  width: @circularSize !important;
  height: @circularSize !important;
}
i.circular.inverted.icon {
  border: none;
  box-shadow: none;
}

/*-------------------
      Flipped
--------------------*/

i.flipped.icon,
i.horizontally.flipped.icon {
  transform: scale(-1, 1);
}
i.vertically.flipped.icon {
  transform: scale(1, -1);
}

/*-------------------
      Rotated
--------------------*/

i.rotated.icon,
i.right.rotated.icon,
i.clockwise.rotated.icon {
  transform: rotate(90deg);
}

i.left.rotated.icon,
i.counterclockwise.rotated.icon {
  transform: rotate(-90deg);
}

/*-------------------
      Bordered
--------------------*/

i.bordered.icon {
  line-height: 1;
  vertical-align: baseline;

  width: @borderedSize;
  height: @borderedSize;
  padding: @borderedVerticalPadding @borderedHorizontalPadding !important;
  box-shadow: @borderedShadow;
}
i.bordered.inverted.icon {
  border: none;
  box-shadow: none;
}

/*-------------------
      Inverted
--------------------*/

/* Inverted Shapes */
i.inverted.bordered.icon,
i.inverted.circular.icon {
  background-color: @black !important;
  color: @white !important;
}

i.inverted.icon {
  color: @white;
}


/*-------------------
       Colors
--------------------*/

/* Red */
i.red.icon {
  color: @red !important;
}
i.inverted.red.icon {
  color: @lightRed !important;
}
i.inverted.bordered.red.icon,
i.inverted.circular.red.icon {
  background-color: @red !important;
  color: @white !important;
}

/* Orange */
i.orange.icon {
  color: @orange !important;
}
i.inverted.orange.icon {
  color: @lightOrange !important;
}
i.inverted.bordered.orange.icon,
i.inverted.circular.orange.icon {
  background-color: @orange !important;
  color: @white !important;
}

/* Yellow */
i.yellow.icon {
  color: @yellow !important;
}
i.inverted.yellow.icon {
  color: @lightYellow !important;
}
i.inverted.bordered.yellow.icon,
i.inverted.circular.yellow.icon {
  background-color: @yellow !important;
  color: @white !important;
}

/* Olive */
i.olive.icon {
  color: @olive !important;
}
i.inverted.olive.icon {
  color: @lightOlive !important;
}
i.inverted.bordered.olive.icon,
i.inverted.circular.olive.icon {
  background-color: @olive !important;
  color: @white !important;
}

/* Green */
i.green.icon {
  color: @green !important;
}
i.inverted.green.icon {
  color: @lightGreen !important;
}
i.inverted.bordered.green.icon,
i.inverted.circular.green.icon {
  background-color: @green !important;
  color: @white !important;
}

/* Teal */
i.teal.icon {
  color: @teal !important;
}
i.inverted.teal.icon {
  color: @lightTeal !important;
}
i.inverted.bordered.teal.icon,
i.inverted.circular.teal.icon {
  background-color: @teal !important;
  color: @white !important;
}

/* Blue */
i.blue.icon {
  color: @blue !important;
}
i.inverted.blue.icon {
  color: @lightBlue !important;
}
i.inverted.bordered.blue.icon,
i.inverted.circular.blue.icon {
  background-color: @blue !important;
  color: @white !important;
}

/* Violet */
i.violet.icon {
  color: @violet !important;
}
i.inverted.violet.icon {
  color: @lightViolet !important;
}
i.inverted.bordered.violet.icon,
i.inverted.circular.violet.icon {
  background-color: @violet !important;
  color: @white !important;
}

/* Purple */
i.purple.icon {
  color: @purple !important;
}
i.inverted.purple.icon {
  color: @lightPurple !important;
}
i.inverted.bordered.purple.icon,
i.inverted.circular.purple.icon {
  background-color: @purple !important;
  color: @white !important;
}

/* Pink */
i.pink.icon {
  color: @pink !important;
}
i.inverted.pink.icon {
  color: @lightPink !important;
}
i.inverted.bordered.pink.icon,
i.inverted.circular.pink.icon {
  background-color: @pink !important;
  color: @white !important;
}

/* Brown */
i.brown.icon {
  color: @brown !important;
}
i.inverted.brown.icon {
  color: @lightBrown !important;
}
i.inverted.bordered.brown.icon,
i.inverted.circular.brown.icon {
  background-color: @brown !important;
  color: @white !important;
}

/* Grey */
i.grey.icon {
  color: @grey !important;
}
i.inverted.grey.icon {
  color: @lightGrey !important;
}
i.inverted.bordered.grey.icon,
i.inverted.circular.grey.icon {
  background-color: @grey !important;
  color: @white !important;
}

/* Black */
i.black.icon {
  color: @black !important;
}
i.inverted.black.icon {
  color: @lightBlack !important;
}
i.inverted.bordered.black.icon,
i.inverted.circular.black.icon {
  background-color: @black !important;
  color: @white !important;
}

/*-------------------
        Sizes
--------------------*/

i.mini.icon,
i.mini.icons {
  line-height: 1;
  font-size: @mini;
}
i.tiny.icon,
i.tiny.icons {
  line-height: 1;
  font-size: @tiny;
}
i.small.icon,
i.small.icons {
  line-height: 1;
  font-size: @small;
}
i.icon,
i.icons {
  font-size: @medium;
}
i.large.icon,
i.large.icons {
  line-height: 1;
  vertical-align: middle;
  font-size: @large;
}
i.big.icon,
i.big.icons {
  line-height: 1;
  vertical-align: middle;
  font-size: @big;
}
i.huge.icon,
i.huge.icons {
  line-height: 1;
  vertical-align: middle;
  font-size: @huge;
}
i.massive.icon,
i.massive.icons {
  line-height: 1;
  vertical-align: middle;
  font-size: @massive;
}

/*******************************
            Groups
*******************************/

i.icons {
  display: inline-block;
  position: relative;
  line-height: 1;
}

i.icons .icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateX(-50%) translateY(-50%);
  margin: 0em;
  margin: 0;
}

i.icons .icon:first-child {
  position: static;
  width: auto;
  height: auto;
  vertical-align: top;
  transform: none;
  margin-right: @distanceFromText;
}

/* Corner Icon */
i.icons .corner.icon {
  top: auto;
  left: auto;
  right: 0;
  bottom: 0;
  transform: none;
  font-size: @cornerIconSize;
  text-shadow: @cornerIconShadow;
}

i.icons .inverted.corner.icon {
  text-shadow: @cornerIconInvertedShadow;
}

.loadUIOverrides();
