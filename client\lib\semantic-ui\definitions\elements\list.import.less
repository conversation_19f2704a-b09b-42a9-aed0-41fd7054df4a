/*
  DO NOT MODIFY - This file has been generated and will be regenerated
  Semantic UI v2.2.6
*/
/*!
 * # Semantic UI - List
 * http://github.com/semantic-org/semantic-ui/
 *
 *
 * Released under the MIT license
 * http://opensource.org/licenses/MIT
 *
 */

/*******************************
            Theme
*******************************/

@type    : 'element';
@element : 'list';

@import (multiple) '../../theme.config.import.less';

/*******************************
            List
*******************************/

ul.ui.list,
ol.ui.list,
.ui.list {
  list-style-type: @listStyleType;
  margin: @margin;
  padding: @verticalPadding @horizontalPadding;
}

ul.ui.list:first-child,
ol.ui.list:first-child,
.ui.list:first-child {
  margin-top: 0em;
  padding-top: 0em;
}

ul.ui.list:last-child,
ol.ui.list:last-child,
.ui.list:last-child {
  margin-bottom: 0em;
  padding-bottom: 0em;
}

/*******************************
            Content
*******************************/

/* List Item */
ul.ui.list li,
ol.ui.list li,
.ui.list > .item,
.ui.list .list > .item {
  display: list-item;
  table-layout: fixed;
  list-style-type: @listStyleType;
  list-style-position: @listStylePosition;

  padding: @itemPadding;
  line-height: @itemLineHeight;
}

ul.ui.list > li:first-child:after,
ol.ui.list > li:first-child:after,
.ui.list > .list > .item,
.ui.list > .item:after {
  content: '';
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

ul.ui.list li:first-child,
ol.ui.list li:first-child,
.ui.list .list > .item:first-child,
.ui.list > .item:first-child {
  padding-top: 0em;
}
ul.ui.list li:last-child,
ol.ui.list li:last-child,
.ui.list .list > .item:last-child,
.ui.list > .item:last-child {
  padding-bottom: 0em;
}

/* Child List */
ul.ui.list ul,
ol.ui.list ol,
.ui.list .list {
  clear: both;
  margin: 0em;
  padding: @childListPadding;
}

/* Child Item */
ul.ui.list ul li,
ol.ui.list ol li,
.ui.list .list > .item {
  padding: @childItemPadding;
  line-height: @childItemLineHeight;
}


/* Icon */
.ui.list .list > .item > i.icon,
.ui.list > .item > i.icon {
  display: table-cell;
  margin: 0em;
  padding-top: @iconOffset;
  padding-right: @iconDistance;
  vertical-align: @iconContentVerticalAlign;
  transition: @iconTransition;
}
.ui.list .list > .item > i.icon:only-child,
.ui.list > .item > i.icon:only-child {
  display: inline-block;
  vertical-align: @iconVerticalAlign;
}


/* Image */
.ui.list .list > .item > .image,
.ui.list > .item > .image {
  display: table-cell;
  background-color: transparent;
  margin: 0em;
  vertical-align: @imageAlign;
}
.ui.list .list > .item > .image:not(:only-child):not(img),
.ui.list > .item > .image:not(:only-child):not(img) {
  padding-right: @imageDistance;
}
.ui.list .list > .item > .image img,
.ui.list > .item > .image img {
  vertical-align: @imageAlign;
}

.ui.list .list > .item > img.image,
.ui.list .list > .item > .image:only-child,
.ui.list > .item > img.image,
.ui.list > .item > .image:only-child {
  display: inline-block;
}

/* Content */
.ui.list .list > .item > .content,
.ui.list > .item > .content {
  line-height: @contentLineHeight;
}
.ui.list .list > .item > .image + .content,
.ui.list .list > .item > .icon + .content,
.ui.list > .item > .image + .content,
.ui.list > .item > .icon + .content {
  display: table-cell;
  padding: 0em 0em 0em @contentDistance;
  vertical-align: @contentVerticalAlign;
}
.ui.list .list > .item > img.image + .content,
.ui.list > .item > img.image + .content {
  display: inline-block;
}
.ui.list .list > .item > .content > .list,
.ui.list > .item > .content > .list {
  margin-left: 0em;
  padding-left: 0em;
}

/* Header */
.ui.list .list > .item .header,
.ui.list > .item .header {
  display: block;
  margin: 0em;
  font-family: @itemHeaderFontFamily;
  font-weight: @itemHeaderFontWeight;
  color: @itemHeaderColor;
}

/* Description */
.ui.list .list > .item .description,
.ui.list > .item .description {
  display: block;
  color: @itemDescriptionColor;
}

/* Child Link */
.ui.list > .item a,
.ui.list .list > .item a {
  cursor: pointer;
}

/* Linking Item */
.ui.list .list > a.item,
.ui.list > a.item {
  cursor: pointer;
  color: @itemLinkColor;
}
.ui.list .list > a.item:hover,
.ui.list > a.item:hover {
  color: @itemLinkHoverColor;
}

/* Linked Item Icons */
.ui.list .list > a.item i.icon,
.ui.list > a.item i.icon {
  color: @itemLinkIconColor;
}

/* Header Link */
.ui.list .list > .item a.header,
.ui.list > .item a.header {
  cursor: pointer;
  color: @itemHeaderLinkColor !important;
}
.ui.list .list > .item a.header:hover,
.ui.list > .item a.header:hover {
  color: @itemHeaderLinkHoverColor !important;
}

/* Floated Content */
.ui[class*="left floated"].list {
  float: left;
}
.ui[class*="right floated"].list {
  float: right;
}

.ui.list .list > .item [class*="left floated"],
.ui.list > .item [class*="left floated"] {
  float: left;
  margin: @leftFloatMargin;
}
.ui.list .list > .item [class*="right floated"],
.ui.list > .item [class*="right floated"] {
  float: right;
  margin: @rightFloatMargin;
}

/*******************************
            Coupling
*******************************/

.ui.menu .ui.list > .item,
.ui.menu .ui.list .list > .item {
  display: list-item;
  table-layout: fixed;
  background-color: transparent;

  list-style-type: @listStyleType;
  list-style-position: @listStylePosition;

  padding: @itemVerticalPadding @itemHorizontalPadding;
  line-height: @itemLineHeight;
}
.ui.menu .ui.list .list > .item:before,
.ui.menu .ui.list > .item:before {
  border: none;
  background: none;
}
.ui.menu .ui.list .list > .item:first-child,
.ui.menu .ui.list > .item:first-child {
  padding-top: 0em;
}
.ui.menu .ui.list .list > .item:last-child,
.ui.menu .ui.list > .item:last-child {
  padding-bottom: 0em;
}


/*******************************
            Types
*******************************/

/*-------------------
      Horizontal
--------------------*/

.ui.horizontal.list {
  display: inline-block;
  font-size: 0em;
}
.ui.horizontal.list > .item {
  display: inline-block;
  margin-left: @horizontalSpacing;
  font-size: 1rem;
}
.ui.horizontal.list:not(.celled) > .item:first-child {
  margin-left: 0em !important;
  padding-left: 0em !important;
}
.ui.horizontal.list .list {
  padding-left: 0em;
  padding-bottom: 0em;
}

.ui.horizontal.list > .item > .image,
.ui.horizontal.list .list > .item > .image,
.ui.horizontal.list > .item > .icon,
.ui.horizontal.list .list > .item > .icon,
.ui.horizontal.list > .item > .content,
.ui.horizontal.list .list > .item > .content {
  vertical-align: @horizontalVerticalAlign;
}

/* Padding on all elements */
.ui.horizontal.list > .item:first-child,
.ui.horizontal.list > .item:last-child {
  padding-top: @itemVerticalPadding;
  padding-bottom: @itemVerticalPadding;
}

/* Horizontal List */
.ui.horizontal.list > .item > i.icon {
  margin: 0em;
  padding: 0em @horizontalIconDistance 0em 0em;
}
.ui.horizontal.list > .item > .icon,
.ui.horizontal.list > .item > .icon + .content {
  float: none;
  display: inline-block;
}


/*******************************
             States
*******************************/

/*-------------------
       Disabled
--------------------*/

.ui.list .list > .disabled.item,
.ui.list > .disabled.item {
  pointer-events: none;
  color: @disabledColor !important;
}
.ui.inverted.list .list > .disabled.item,
.ui.inverted.list > .disabled.item {
  color: @invertedDisabledColor !important;
}

/*-------------------
        Hover
--------------------*/

.ui.list .list > a.item:hover .icon,
.ui.list > a.item:hover .icon {
  color: @itemLinkIconHoverColor;
}


/*******************************
           Variations
*******************************/

/*-------------------
       Inverted
--------------------*/

.ui.inverted.list .list > a.item > .icon,
.ui.inverted.list > a.item > .icon {
  color: @invertedIconLinkColor;
}
.ui.inverted.list .list > .item .header,
.ui.inverted.list > .item .header {
  color: @invertedHeaderColor;
}
.ui.inverted.list .list > .item .description,
.ui.inverted.list > .item .description {
  color: @invertedDescriptionColor;
}

/* Item Link */
.ui.inverted.list .list > a.item,
.ui.inverted.list > a.item {
  cursor: pointer;
  color: @invertedItemLinkColor;
}
.ui.inverted.list .list > a.item:hover,
.ui.inverted.list > a.item:hover {
  color: @invertedItemLinkHoverColor;
}


/* Linking Content */
.ui.inverted.list .item a:not(.ui) {
  color: @invertedItemLinkColor !important;
}
.ui.inverted.list .item a:not(.ui):hover {
  color: @invertedItemLinkHoverColor !important;
}

/*-------------------
       Aligned
--------------------*/

.ui.list[class*="top aligned"] .image,
.ui.list[class*="top aligned"] .content,
.ui.list [class*="top aligned"] {
  vertical-align: top !important;
}
.ui.list[class*="middle aligned"] .image,
.ui.list[class*="middle aligned"] .content,
.ui.list [class*="middle aligned"] {
  vertical-align: middle !important;
}
.ui.list[class*="bottom aligned"] .image,
.ui.list[class*="bottom aligned"] .content,
.ui.list [class*="bottom aligned"] {
  vertical-align: bottom !important;
}

/*-------------------
       Link
--------------------*/

.ui.link.list .item,
.ui.link.list a.item,
.ui.link.list .item a:not(.ui) {
  color: @linkListItemColor;
  transition: @linkListTransition;
}
.ui.link.list a.item:hover,
.ui.link.list .item a:not(.ui):hover {
  color: @linkListItemHoverColor;
}
.ui.link.list a.item:active,
.ui.link.list .item a:not(.ui):active {
  color: @linkListItemDownColor;
}
.ui.link.list .active.item,
.ui.link.list .active.item a:not(.ui) {
  color: @linkListItemActiveColor;
}

/* Inverted */
.ui.inverted.link.list .item,
.ui.inverted.link.list a.item,
.ui.inverted.link.list .item a:not(.ui) {
  color: @invertedLinkListItemColor;
}
.ui.inverted.link.list a.item:hover,
.ui.inverted.link.list .item a:not(.ui):hover {
  color: @invertedLinkListItemHoverColor;
}
.ui.inverted.link.list a.item:active,
.ui.inverted.link.list .item a:not(.ui):active {
  color: @invertedLinkListItemDownColor;
}
.ui.inverted.link.list a.active.item,
.ui.inverted.link.list .active.item a:not(.ui) {
  color: @invertedLinkListItemActiveColor;
}

/*-------------------
      Selection
--------------------*/

.ui.selection.list .list > .item,
.ui.selection.list > .item {
  cursor: pointer;
  background: @selectionListBackground;
  padding: @selectionListItemVerticalPadding @selectionListItemHorizontalPadding;
  margin: @selectionListItemMargin;
  color: @selectionListColor;
  border-radius: @selectionListItemBorderRadius;
  transition: @selectionListTransition;
}
.ui.selection.list .list > .item:last-child,
.ui.selection.list > .item:last-child {
  margin-bottom: 0em;
}
.ui.selection.list.list >  .item:hover,
.ui.selection.list > .item:hover {
  background: @selectionListHoverBackground;
  color: @selectionListHoverColor;
}
.ui.selection.list .list > .item:active,
.ui.selection.list > .item:active {
  background: @selectionListDownBackground;
  color: @selectionListDownColor;
}
.ui.selection.list .list > .item.active,
.ui.selection.list > .item.active {
  background: @selectionListActiveBackground;
  color: @selectionListActiveColor;
}

/* Inverted */
.ui.inverted.selection.list > .item,
.ui.inverted.selection.list > .item {
  background: @invertedSelectionListBackground;
  color: @invertedSelectionListColor;
}
.ui.inverted.selection.list > .item:hover,
.ui.inverted.selection.list > .item:hover {
  background: @invertedSelectionListHoverBackground;
  color: @invertedSelectionListHoverColor;
}
.ui.inverted.selection.list > .item:active,
.ui.inverted.selection.list > .item:active {
  background: @invertedSelectionListDownBackground;
  color: @invertedSelectionListDownColor;
}
.ui.inverted.selection.list > .item.active,
.ui.inverted.selection.list > .item.active {
  background: @invertedSelectionListActiveBackground;
  color: @invertedSelectionListActiveColor;
}

/* Celled / Divided Selection List */
.ui.celled.selection.list .list > .item,
.ui.divided.selection.list .list > .item,
.ui.celled.selection.list > .item,
.ui.divided.selection.list > .item {
  border-radius: 0em;
}

/*-------------------
       Animated
--------------------*/

.ui.animated.list > .item {
  transition: @animatedListTransition;
}
.ui.animated.list:not(.horizontal) > .item:hover {
  padding-left: @animatedListIndent;
}

/*-------------------
       Fitted
--------------------*/
.ui.fitted.list:not(.selection) .list > .item,
.ui.fitted.list:not(.selection) > .item {
  padding-left: 0em;
  padding-right: 0em;
}
.ui.fitted.selection.list .list > .item,
.ui.fitted.selection.list > .item {
  margin-left: -@selectionListItemHorizontalPadding;
  margin-right: -@selectionListItemHorizontalPadding;
}

/*-------------------
      Bulleted
--------------------*/

ul.ui.list,
.ui.bulleted.list {
  margin-left: @bulletDistance;
}
ul.ui.list li,
.ui.bulleted.list .list > .item,
.ui.bulleted.list > .item {
  position: relative;
}
ul.ui.list li:before,
.ui.bulleted.list .list > .item:before,
.ui.bulleted.list > .item:before {
  user-select: none;
  pointer-events: none;
  position: absolute;
  top: auto;
  left: auto;
  font-weight: normal;
  margin-left: @bulletOffset;
  content: @bulletCharacter;
  opacity: @bulletOpacity;
  color: @bulletColor;
  vertical-align: @bulletVerticalAlign;
}

ul.ui.list li:before,
.ui.bulleted.list .list > a.item:before,
.ui.bulleted.list > a.item:before {
  color: @bulletLinkColor;
}

ul.ui.list ul,
.ui.bulleted.list .list {
  padding-left: @bulletChildDistance;
}

/* Horizontal Bulleted */
ul.ui.horizontal.bulleted.list,
.ui.horizontal.bulleted.list {
  margin-left: 0em;
}
ul.ui.horizontal.bulleted.list li,
.ui.horizontal.bulleted.list > .item {
  margin-left: @horizontalBulletSpacing;
}
ul.ui.horizontal.bulleted.list li:first-child,
.ui.horizontal.bulleted.list > .item:first-child {
  margin-left: 0em;
}
ul.ui.horizontal.bulleted.list li::before,
.ui.horizontal.bulleted.list > .item::before {
  color: @horizontalBulletColor;
}
ul.ui.horizontal.bulleted.list li:first-child::before,
.ui.horizontal.bulleted.list > .item:first-child::before {
  display: none;
}

/*-------------------
       Ordered
--------------------*/

ol.ui.list,
.ui.ordered.list,
.ui.ordered.list .list,
ol.ui.list ol {
  counter-reset: ordered;
  margin-left: @orderedCountDistance;
  list-style-type: none;
}
ol.ui.list li,
.ui.ordered.list .list > .item,
.ui.ordered.list > .item {
  list-style-type: none;
  position: relative;
}
ol.ui.list li:before,
.ui.ordered.list .list > .item:before,
.ui.ordered.list > .item:before {
  position: absolute;
  top: auto;
  left: auto;
  user-select: none;
  pointer-events: none;
  margin-left: -(@orderedCountDistance);
  counter-increment: @orderedCountName;
  content: @orderedCountContent;
  text-align: @orderedCountTextAlign;
  color: @orderedCountColor;
  vertical-align: @orderedCountVerticalAlign;
  opacity: @orderedCountOpacity;
}

ol.ui.inverted.list li:before,
.ui.ordered.inverted.list .list > .item:before,
.ui.ordered.inverted.list > .item:before {
  color: @orderedInvertedCountColor;
}

/* Value */
.ui.ordered.list > .list > .item[data-value],
.ui.ordered.list > .item[data-value] {
  content: attr(data-value);
}
ol.ui.list li[value]:before {
  content: attr(value);
}

/* Child Lists */
ol.ui.list ol,
.ui.ordered.list .list {
  margin-left: @orderedChildCountDistance;
}
ol.ui.list ol li:before,
.ui.ordered.list .list > .item:before {
  margin-left: @orderedChildCountOffset;
}

/* Horizontal Ordered */
ol.ui.horizontal.list,
.ui.ordered.horizontal.list {
  margin-left: 0em;
}
ol.ui.horizontal.list li:before,
.ui.ordered.horizontal.list .list > .item:before,
.ui.ordered.horizontal.list > .item:before {
  position: static;
  margin: 0em @horizontalOrderedCountDistance 0em 0em;
}

/*-------------------
       Divided
--------------------*/

.ui.divided.list > .item {
  border-top: @dividedBorder;
}
.ui.divided.list .list > .item {
  border-top: @dividedChildListBorder;
}
.ui.divided.list .item .list > .item {
  border-top: @dividedChildItemBorder;
}
.ui.divided.list .list > .item:first-child,
.ui.divided.list > .item:first-child {
  border-top: none;
}

/* Sub Menu */
.ui.divided.list:not(.horizontal) .list > .item:first-child {
  border-top-width: @dividedBorderWidth;
}

/* Divided bulleted */
.ui.divided.bulleted.list:not(.horizontal),
.ui.divided.bulleted.list .list {
  margin-left: 0em;
  padding-left: 0em;
}
.ui.divided.bulleted.list > .item:not(.horizontal) {
  padding-left: @bulletDistance;
}

/* Divided Ordered */
.ui.divided.ordered.list {
  margin-left: 0em;
}
.ui.divided.ordered.list .list > .item,
.ui.divided.ordered.list > .item {
  padding-left: @orderedCountDistance;
}
.ui.divided.ordered.list .item .list {
  margin-left: 0em;
  margin-right: 0em;
  padding-bottom: @itemVerticalPadding;
}
.ui.divided.ordered.list .item .list > .item {
  padding-left: @orderedChildCountDistance;
}

/* Divided Selection */
.ui.divided.selection.list .list > .item,
.ui.divided.selection.list > .item {
  margin: 0em;
  border-radius: 0em;
}

/* Divided horizontal */
.ui.divided.horizontal.list {
  margin-left: 0em;
}
.ui.divided.horizontal.list > .item:not(:first-child) {
  padding-left: @horizontalDividedSpacing;
}
.ui.divided.horizontal.list > .item:not(:last-child) {
  padding-right: @horizontalDividedSpacing;
}
.ui.divided.horizontal.list > .item {
  border-top: none;
  border-left: @dividedBorder;
  margin: 0em;
  line-height: @horizontalDividedLineHeight;
}
.ui.horizontal.divided.list > .item:first-child {
  border-left: none;
}
/* Inverted */
.ui.divided.inverted.list > .item,
.ui.divided.inverted.list > .list,
.ui.divided.inverted.horizontal.list > .item {
  border-color: @dividedInvertedBorderColor;
}


/*-------------------
        Celled
--------------------*/

.ui.celled.list > .item,
.ui.celled.list > .list {
  border-top: @celledBorder;
  padding-left: @celledHorizontalPadding;
  padding-right: @celledHorizontalPadding;
}
.ui.celled.list > .item:last-child {
  border-bottom: @celledBorder;
}

/* Padding on all elements */
.ui.celled.list > .item:first-child,
.ui.celled.list > .item:last-child {
  padding-top: @itemVerticalPadding;
  padding-bottom: @itemVerticalPadding;
}

/* Sub Menu */
.ui.celled.list .item .list > .item {
  border-width: 0px;
}
.ui.celled.list .list > .item:first-child {
  border-top-width: 0px;
}

/* Celled Bulleted */
.ui.celled.bulleted.list {
  margin-left: 0em;
}
.ui.celled.bulleted.list .list > .item,
.ui.celled.bulleted.list > .item {
  padding-left: (@bulletDistance);
}
.ui.celled.bulleted.list .item .list {
  margin-left: -(@bulletDistance);
  margin-right: -(@bulletDistance);
  padding-bottom: @itemVerticalPadding;
}

/* Celled Ordered */
.ui.celled.ordered.list {
  margin-left: 0em;
}
.ui.celled.ordered.list .list > .item,
.ui.celled.ordered.list > .item {
  padding-left: @orderedCountDistance;
}
.ui.celled.ordered.list .item .list {
  margin-left: 0em;
  margin-right: 0em;
  padding-bottom: @itemVerticalPadding;
}
.ui.celled.ordered.list .list > .item {
  padding-left: @orderedChildCountDistance;
}

/* Celled Horizontal */
.ui.horizontal.celled.list {
  margin-left: 0em;
}
.ui.horizontal.celled.list .list > .item,
.ui.horizontal.celled.list > .item {
  border-top: none;
  border-left: @celledBorder;
  margin: 0em;
  padding-left: @horizontalCelledSpacing;
  padding-right: @horizontalCelledSpacing;

  line-height: @horizontalCelledLineHeight;
}
.ui.horizontal.celled.list .list > .item:last-child,
.ui.horizontal.celled.list > .item:last-child {
  border-bottom: none;
  border-right: @celledBorder;
}

/* Inverted */
.ui.celled.inverted.list > .item,
.ui.celled.inverted.list > .list {
  border-color: @celledInvertedBorder;
}
.ui.celled.inverted.horizontal.list .list > .item,
.ui.celled.inverted.horizontal.list > .item {
  border-color: @celledInvertedBorder;
}

/*-------------------
       Relaxed
--------------------*/

.ui.relaxed.list:not(.horizontal) > .item:not(:first-child) {
  padding-top: @relaxedItemVerticalPadding;
}
.ui.relaxed.list:not(.horizontal) > .item:not(:last-child) {
  padding-bottom: @relaxedItemVerticalPadding;
}
.ui.horizontal.relaxed.list .list > .item:not(:first-child),
.ui.horizontal.relaxed.list > .item:not(:first-child) {
  padding-left: @relaxedHorizontalPadding;
}
.ui.horizontal.relaxed.list .list > .item:not(:last-child),
.ui.horizontal.relaxed.list > .item:not(:last-child) {
  padding-right: @relaxedHorizontalPadding;
}

/* Very Relaxed */
.ui[class*="very relaxed"].list:not(.horizontal) > .item:not(:first-child) {
  padding-top: @veryRelaxedItemVerticalPadding;
}
.ui[class*="very relaxed"].list:not(.horizontal) > .item:not(:last-child) {
  padding-bottom: @veryRelaxedItemVerticalPadding;
}
.ui.horizontal[class*="very relaxed"].list .list > .item:not(:first-child),
.ui.horizontal[class*="very relaxed"].list > .item:not(:first-child) {
  padding-left: @veryRelaxedHorizontalPadding;
}
.ui.horizontal[class*="very relaxed"].list .list > .item:not(:last-child),
.ui.horizontal[class*="very relaxed"].list > .item:not(:last-child) {
  padding-right: @veryRelaxedHorizontalPadding;
}

/*-------------------
      Sizes
--------------------*/

.ui.mini.list {
  font-size: @relativeMini;
}
.ui.tiny.list {
  font-size: @relativeTiny;
}
.ui.small.list {
  font-size: @relativeSmall;
}
.ui.list {
  font-size: @relativeMedium;
}
.ui.large.list {
  font-size: @relativeLarge;
}
.ui.big.list {
  font-size: @relativeBig;
}
.ui.huge.list {
  font-size: @relativeHuge;
}
.ui.massive.list {
  font-size: @relativeMassive;
}

.ui.mini.horizontal.list .list > .item,
.ui.mini.horizontal.list > .item {
  font-size: @mini;
}
.ui.tiny.horizontal.list .list > .item,
.ui.tiny.horizontal.list > .item {
  font-size: @tiny;
}
.ui.small.horizontal.list .list > .item,
.ui.small.horizontal.list > .item {
  font-size: @small;
}
.ui.horizontal.list .list > .item,
.ui.horizontal.list > .item {
  font-size: @medium;
}
.ui.large.horizontal.list .list > .item,
.ui.large.horizontal.list > .item {
  font-size: @large;
}
.ui.big.horizontal.list .list > .item,
.ui.big.horizontal.list > .item {
  font-size: @big;
}
.ui.huge.horizontal.list .list > .item,
.ui.huge.horizontal.list > .item {
  font-size: @huge;
}
.ui.massive.horizontal.list .list > .item,
.ui.massive.horizontal.list > .item {
  font-size: @massive;
}

.loadUIOverrides();

