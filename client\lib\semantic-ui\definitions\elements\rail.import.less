/*
  DO NOT MODIFY - This file has been generated and will be regenerated
  Semantic UI v2.2.6
*/
/*!
 * # Semantic UI - Rail
 * http://github.com/semantic-org/semantic-ui/
 *
 *
 * Released under the MIT license
 * http://opensource.org/licenses/MIT
 *
 */

/*******************************
            Theme
*******************************/

@type    : 'element';
@element : 'rail';

@import (multiple) '../../theme.config.import.less';

/*******************************
             Rails
*******************************/

.ui.rail {
  position: absolute;
  top: 0%;
  width: @width;
  height: @height;
}

.ui.left.rail {
  left: auto;
  right: 100%;
  padding: 0em @splitDistance 0em 0em;
  margin: 0em @splitDistance 0em 0em;
}

.ui.right.rail {
  left: 100%;
  right: auto;
  padding: 0em 0em 0em @splitDistance;
  margin: 0em 0em 0em @splitDistance;
}

/*******************************
           Variations
*******************************/

/*--------------
     Internal
---------------*/

.ui.left.internal.rail {
  left: 0%;
  right: auto;
  padding: 0em 0em 0em @splitDistance;
  margin: 0em 0em 0em @splitDistance;
}

.ui.right.internal.rail {
  left: auto;
  right: 0%;
  padding: 0em @splitDistance 0em 0em;
  margin: 0em @splitDistance 0em 0em;
}


/*--------------
    Dividing
---------------*/

.ui.dividing.rail {
  width: @dividingWidth;
}
.ui.left.dividing.rail {
  padding: 0em @splitDividingDistance 0em 0em;
  margin: 0em @splitDividingDistance 0em 0em;
  border-right: @dividingBorder;
}
.ui.right.dividing.rail {
  border-left: @dividingBorder;
  padding: 0em 0em 0em @splitDividingDistance;
  margin: 0em 0em 0em @splitDividingDistance;
}

/*--------------
    Distance
---------------*/

.ui.close.rail {
  width: @closeWidth;
}
.ui.close.left.rail {
  padding: 0em @splitCloseDistance 0em 0em;
  margin: 0em @splitCloseDistance 0em 0em;
}
.ui.close.right.rail {
  padding: 0em 0em 0em @splitCloseDistance;
  margin: 0em 0em 0em @splitCloseDistance;
}

.ui.very.close.rail {
  width: @veryCloseWidth;
}
.ui.very.close.left.rail {
  padding: 0em @splitVeryCloseDistance 0em 0em;
  margin: 0em @splitVeryCloseDistance 0em 0em;
}
.ui.very.close.right.rail {
  padding: 0em 0em 0em @splitVeryCloseDistance;
  margin: 0em 0em 0em @splitVeryCloseDistance;
}

/*--------------
    Attached
---------------*/

.ui.attached.left.rail,
.ui.attached.right.rail {
  padding: 0em;
  margin: 0em;
}

/*--------------
     Sizing
---------------*/

.ui.mini.rail {
  font-size: @mini;
}
.ui.tiny.rail {
  font-size: @tiny;
}
.ui.small.rail {
  font-size: @small;
}
.ui.rail {
  font-size: @medium;
}
.ui.large.rail {
  font-size: @large;
}
.ui.big.rail {
  font-size: @big;
}
.ui.huge.rail {
  font-size: @huge;
}
.ui.massive.rail {
  font-size: @massive;
}


.loadUIOverrides();
