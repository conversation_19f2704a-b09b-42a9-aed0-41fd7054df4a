/*
  DO NOT MODIFY - This file has been generated and will be regenerated
  Semantic UI v2.2.6
*/
/*!
 * # Semantic UI - Segment
 * http://github.com/semantic-org/semantic-ui/
 *
 *
 * Released under the MIT license
 * http://opensource.org/licenses/MIT
 *
 */

/*******************************
            Theme
*******************************/

@type    : 'element';
@element : 'segment';

@import (multiple) '../../theme.config.import.less';

/*******************************
            Segment
*******************************/

.ui.segment {
  position: relative;
  background: @background;
  box-shadow: @boxShadow;
  margin: @margin;
  padding: @padding;
  border-radius: @borderRadius;
  border: @border;
}

.ui.segment:first-child {
  margin-top: 0em;
}
.ui.segment:last-child {
  margin-bottom: 0em;
}


/* Vertical */
.ui.vertical.segment {
  margin: 0em;
  padding-left: 0em;
  padding-right: 0em;

  background: none transparent;
  border-radius: 0px;
  box-shadow: none;
  border: none;
  border-bottom: @borderWidth solid @borderColor;
}
.ui.vertical.segment:last-child {
  border-bottom: none;
}


/*-------------------
    Loose Coupling
--------------------*/

/* Header */
.ui.inverted.segment > .ui.header {
  color: @white;
}

/* Label */
.ui[class*="bottom attached"].segment > [class*="top attached"].label {
  border-top-left-radius: 0em;
  border-top-right-radius: 0em;
}
.ui[class*="top attached"].segment > [class*="bottom attached"].label {
  border-bottom-left-radius: 0em;
  border-bottom-right-radius: 0em;
}
.ui.attached.segment:not(.top):not(.bottom) > [class*="top attached"].label {
  border-top-left-radius: 0em;
  border-top-right-radius: 0em;
}
.ui.attached.segment:not(.top):not(.bottom) > [class*="bottom attached"].label {
  border-bottom-left-radius: 0em;
  border-bottom-right-radius: 0em;
}

/* Grid */
.ui.page.grid.segment,
.ui.grid > .row > .ui.segment.column,
.ui.grid > .ui.segment.column {
  padding-top: @pageGridMargin;
  padding-bottom: @pageGridMargin;
}
.ui.grid.segment {
  margin: @margin;
  border-radius: @borderRadius;
}

/* Table */
.ui.basic.table.segment {
  background: @background;
  border: @border;
  box-shadow: @boxShadow;
}
.ui[class*="very basic"].table.segment {
  padding: @padding;
}


/*******************************
             Types
*******************************/

/*-------------------
        Piled
--------------------*/

.ui.piled.segments,
.ui.piled.segment {
  margin: @piledMargin 0em;
  box-shadow: @piledBoxShadow;
  z-index: @piledZIndex;
}
.ui.piled.segment:first-child {
  margin-top: 0em;
}
.ui.piled.segment:last-child {
  margin-bottom: 0em;
}
.ui.piled.segments:after,
.ui.piled.segments:before,
.ui.piled.segment:after,
.ui.piled.segment:before {
  background-color: @white;
  visibility: visible;
  content: '';
  display: block;
  height: 100%;
  left: 0px;
  position: absolute;
  width: 100%;
  border: @piledBorder;
  box-shadow: @piledBoxShadow;
}
.ui.piled.segments:before,
.ui.piled.segment:before {
  transform: rotate(-@piledDegrees);
  top: 0;
  z-index: -2;
}
.ui.piled.segments:after,
.ui.piled.segment:after {
  transform: rotate(@piledDegrees);
  top: 0;
  z-index: -1;
}

/* Piled Attached */
.ui[class*="top attached"].piled.segment {
  margin-top: @piledMargin;
  margin-bottom: 0em;
}
.ui.piled.segment[class*="top attached"]:first-child {
  margin-top: 0em;
}
.ui.piled.segment[class*="bottom attached"] {
  margin-top: 0em;
  margin-bottom: @piledMargin;
}
.ui.piled.segment[class*="bottom attached"]:last-child {
  margin-bottom: 0em;
}

/*-------------------
       Stacked
--------------------*/

.ui.stacked.segment {
  padding-bottom: @stackedPadding;
}
.ui.stacked.segments:before,
.ui.stacked.segments:after,
.ui.stacked.segment:before,
.ui.stacked.segment:after {
  content: '';
  position: absolute;
  bottom: -(@stackedHeight / 2);
  left: 0%;

  border-top: 1px solid @borderColor;
  background: @stackedPageBackground;

  width: 100%;
  height: @stackedHeight;
  visibility: visible;
}
.ui.stacked.segments:before,
.ui.stacked.segment:before {
  display: none;
}

/* Add additional page */
.ui.tall.stacked.segments:before,
.ui.tall.stacked.segment:before {
  display: block;
  bottom: 0px;
}

/* Inverted */
.ui.stacked.inverted.segments:before,
.ui.stacked.inverted.segments:after,
.ui.stacked.inverted.segment:before,
.ui.stacked.inverted.segment:after {
  background-color: @subtleTransparentBlack;
  border-top: 1px solid @selectedBorderColor;
}

/*-------------------
       Padded
--------------------*/

.ui.padded.segment {
  padding: @paddedSegmentPadding;
}

.ui[class*="very padded"].segment {
  padding: @veryPaddedSegmentPadding;
}

/*-------------------
       Compact
--------------------*/

.ui.compact.segment {
  display: table;
}

/* Compact Group */
.ui.compact.segments {
  display: inline-flex;
}
.ui.compact.segments .segment,
.ui.segments .compact.segment {
  display: block;
  flex: 0 1 auto;
}

/*-------------------
       Circular
--------------------*/

.ui.circular.segment {
  display: table-cell;
  padding: @circularPadding;
  text-align: center;
  vertical-align: middle;
  border-radius: 500em;
}

/*-------------------
       Raised
--------------------*/

.ui.raised.segments,
.ui.raised.segment {
  box-shadow: @raisedBoxShadow;
}


/*******************************
            Groups
*******************************/

/* Group */
.ui.segments {
  flex-direction: column;
  position: relative;
  margin: @groupedMargin;
  border: @groupedBorder;
  box-shadow: @groupedBoxShadow;
  border-radius: @groupedBorderRadius;
}
.ui.segments:first-child {
  margin-top: 0em;
}
.ui.segments:last-child {
  margin-bottom: 0em;
}


/* Nested Segment */
.ui.segments > .segment {
  top: 0px;
  bottom: 0px;
  border-radius: 0px;
  margin: @groupedSegmentMargin;
  width: @groupedSegmentWidth;
  box-shadow: @groupedSegmentBoxShadow;
  border: @groupedSegmentBorder;
  border-top: @groupedSegmentDivider;
}

.ui.segments:not(.horizontal) > .segment:first-child {
  top: @attachedTopOffset;
  bottom: 0px;
  border-top: none;
  margin-top: 0em;
  bottom: 0px;
  margin-bottom: 0em;
  top: @attachedTopOffset;
  border-radius: @borderRadius @borderRadius 0em 0em;
}

/* Bottom */
.ui.segments:not(.horizontal) > .segment:last-child {
  top: @attachedBottomOffset;
  bottom: 0px;
  margin-top: 0em;
  margin-bottom: 0em;
  box-shadow: @attachedBottomBoxShadow;
  border-radius: 0em 0em @borderRadius @borderRadius;
}

/* Only */
.ui.segments:not(.horizontal) > .segment:only-child {
  border-radius: @borderRadius;
}


/* Nested Group */
.ui.segments > .ui.segments {
  border-top: @groupedSegmentDivider;
  margin: @nestedGroupMargin;
}
.ui.segments > .segments:first-child {
  border-top: none;
}
.ui.segments > .segment + .segments:not(.horizontal) {
  margin-top: 0em;
}

/* Horizontal Group */
.ui.horizontal.segments {
  display: flex;
  flex-direction: row;
  background-color: transparent;
  border-radius: 0px;
  padding: 0em;
  background-color: @background;
  box-shadow: @boxShadow;
  margin: @margin;
  border-radius: @borderRadius;
  border: @border;
}

/* Nested Horizontal Group */
.ui.segments > .horizontal.segments {
  margin: 0em;
  background-color: transparent;
  border-radius: 0px;
  border: none;
  box-shadow: none;
  border-top: @groupedSegmentDivider;
}

/* Horizontal Segment */
.ui.horizontal.segments > .segment {
  flex: 1 1 auto;
  -ms-flex: 1 1 0px; /* Solves #2550 MS Flex */
  margin: 0em;
  min-width: 0px;
  background-color: transparent;
  border-radius: 0px;
  border: none;
  box-shadow: none;
  border-left: @borderWidth solid @borderColor;
}

/* Border Fixes */
.ui.segments > .horizontal.segments:first-child {
  border-top: none;
}
.ui.horizontal.segments > .segment:first-child {
  border-left: none;
}


/*******************************
            States
*******************************/

/*--------------
    Disabled
---------------*/

.ui.disabled.segment {
  opacity: @disabledOpacity;
  color: @disabledTextColor;
}

/*--------------
    Loading
---------------*/

.ui.loading.segment {
  position: relative;
  cursor: default;
  pointer-events: none;
  text-shadow: none !important;
  color: transparent !important;
  transition: all 0s linear;
}
.ui.loading.segment:before {
  position: absolute;
  content: '';
  top: 0%;
  left: 0%;
  background: @loaderDimmerColor;
  width: 100%;
  height: 100%;
  border-radius: @borderRadius;
  z-index: @loaderDimmerZIndex;
}
.ui.loading.segment:after {
  position: absolute;
  content: '';
  top: 50%;
  left: 50%;

  margin: @loaderMargin;
  width: @loaderSize;
  height: @loaderSize;

  animation: segment-spin @loaderSpeed linear;
  animation-iteration-count: infinite;

  border-radius: @circularRadius;

  border-color: @loaderLineColor @loaderFillColor @loaderFillColor @loaderFillColor;
  border-style: solid;
  border-width: @loaderLineWidth;

  box-shadow: 0px 0px 0px 1px transparent;
  visibility: visible;
  z-index: @loaderLineZIndex;
}

@keyframes segment-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}


/*******************************
           Variations
*******************************/


/*-------------------
       Basic
--------------------*/

.ui.basic.segment {
  background: @basicBackground;
  box-shadow: @basicBoxShadow;
  border: @basicBorder;
  border-radius: @basicBorderRadius;
}

/*-------------------
       Clearing
--------------------*/

.ui.clearing.segment:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

/*-------------------
       Colors
--------------------*/

/* Red */
.ui.red.segment:not(.inverted) {
  border-top: @coloredBorderSize solid @red;
}
.ui.inverted.red.segment {
  background-color: @red !important;
  color: @white !important;
}

/* Orange */
.ui.orange.segment:not(.inverted) {
  border-top: @coloredBorderSize solid @orange;
}
.ui.inverted.orange.segment {
  background-color: @orange !important;
  color: @white !important;
}

/* Yellow */
.ui.yellow.segment:not(.inverted) {
  border-top: @coloredBorderSize solid @yellow;
}
.ui.inverted.yellow.segment {
  background-color: @yellow !important;
  color: @white !important;
}

/* Olive */
.ui.olive.segment:not(.inverted) {
  border-top: @coloredBorderSize solid @olive;
}
.ui.inverted.olive.segment {
  background-color: @olive !important;
  color: @white !important;
}

/* Green */
.ui.green.segment:not(.inverted) {
  border-top: @coloredBorderSize solid @green;
}
.ui.inverted.green.segment {
  background-color: @green !important;
  color: @white !important;
}

/* Teal */
.ui.teal.segment:not(.inverted) {
  border-top: @coloredBorderSize solid @teal;
}
.ui.inverted.teal.segment {
  background-color: @teal !important;
  color: @white !important;
}

/* Blue */
.ui.blue.segment:not(.inverted) {
  border-top: @coloredBorderSize solid @blue;
}
.ui.inverted.blue.segment {
  background-color: @blue !important;
  color: @white !important;
}

/* Violet */
.ui.violet.segment:not(.inverted) {
  border-top: @coloredBorderSize solid @violet;
}
.ui.inverted.violet.segment {
  background-color: @violet !important;
  color: @white !important;
}

/* Purple */
.ui.purple.segment:not(.inverted) {
  border-top: @coloredBorderSize solid @purple;
}
.ui.inverted.purple.segment {
  background-color: @purple !important;
  color: @white !important;
}

/* Pink */
.ui.pink.segment:not(.inverted) {
  border-top: @coloredBorderSize solid @pink;
}
.ui.inverted.pink.segment {
  background-color: @pink !important;
  color: @white !important;
}

/* Brown */
.ui.brown.segment:not(.inverted) {
  border-top: @coloredBorderSize solid @brown;
}
.ui.inverted.brown.segment {
  background-color: @brown !important;
  color: @white !important;
}

/* Grey */
.ui.grey.segment:not(.inverted) {
  border-top: @coloredBorderSize solid @grey;
}
.ui.inverted.grey.segment {
  background-color: @grey !important;
  color: @white !important;
}

/* Black */
.ui.black.segment:not(.inverted) {
  border-top: @coloredBorderSize solid @black;
}
.ui.inverted.black.segment {
  background-color: @black !important;
  color: @white !important;
}

/*-------------------
       Aligned
--------------------*/

.ui[class*="left aligned"].segment {
  text-align: left;
}
.ui[class*="right aligned"].segment {
  text-align: right;
}
.ui[class*="center aligned"].segment {
  text-align: center;
}

/*-------------------
       Floated
--------------------*/

.ui.floated.segment,
.ui[class*="left floated"].segment {
  float: left;
  margin-right: @floatedDistance;
}
.ui[class*="right floated"].segment {
  float: right;
  margin-left: @floatedDistance;
}


/*-------------------
      Inverted
--------------------*/

.ui.inverted.segment {
  border: none;
  box-shadow: none;
}
.ui.inverted.segment,
.ui.primary.inverted.segment {
  background: @invertedBackground;
  color: @invertedTextColor;
}

/* Nested */
.ui.inverted.segment .segment {
  color: @textColor;
}
.ui.inverted.segment .inverted.segment {
  color: @invertedTextColor;
}

/* Attached */
.ui.inverted.attached.segment {
  border-color: @solidWhiteBorderColor;
}

/*-------------------
     Emphasis
--------------------*/

/* Secondary */
.ui.secondary.segment {
  background: @secondaryBackground;
  color: @secondaryColor;
}
.ui.secondary.inverted.segment {
  background: @secondaryInvertedBackground;
  color: @secondaryInvertedColor;
}

/* Tertiary */
.ui.tertiary.segment {
  background: @tertiaryBackground;
  color: @tertiaryColor;
}
.ui.tertiary.inverted.segment {
  background: @tertiaryInvertedBackground;
  color: @tertiaryInvertedColor;
}


/*-------------------
      Attached
--------------------*/

/* Middle */
.ui.attached.segment {
  top: 0px;
  bottom: 0px;
  border-radius: 0px;
  margin: 0em @attachedHorizontalOffset;
  width: @attachedWidth;
  max-width: @attachedWidth;
  box-shadow: @attachedBoxShadow;
  border: @attachedBorder;
}
.ui.attached:not(.message) + .ui.attached.segment:not(.top) {
  border-top: none;
}

/* Top */
.ui[class*="top attached"].segment {
  bottom: 0px;
  margin-bottom: 0em;
  top: @attachedTopOffset;
  margin-top: @verticalMargin;
  border-radius: @borderRadius @borderRadius 0em 0em;
}
.ui.segment[class*="top attached"]:first-child {
  margin-top: 0em;
}

/* Bottom */
.ui.segment[class*="bottom attached"] {
  bottom: 0px;
  margin-top: 0em;
  top: @attachedBottomOffset;
  margin-bottom: @verticalMargin;
  box-shadow: @attachedBottomBoxShadow;
  border-radius: 0em 0em @borderRadius @borderRadius;
}
.ui.segment[class*="bottom attached"]:last-child {
  margin-bottom: 0em;
}

/*-------------------
        Size
--------------------*/

.ui.mini.segments .segment,
.ui.mini.segment {
  font-size: @mini;
}
.ui.tiny.segments .segment,
.ui.tiny.segment {
  font-size: @tiny;
}
.ui.small.segments .segment,
.ui.small.segment {
  font-size: @small;
}
.ui.segments .segment,
.ui.segment {
  font-size: @medium;
}
.ui.large.segments .segment,
.ui.large.segment {
  font-size: @large;
}
.ui.big.segments .segment,
.ui.big.segment {
  font-size: @big;
}
.ui.huge.segments .segment,
.ui.huge.segment {
  font-size: @huge;
}
.ui.massive.segments .segment,
.ui.massive.segment {
  font-size: @massive;
}

.loadUIOverrides();
