/*
  DO NOT MODIFY - This file has been generated and will be regenerated
  Semantic UI v2.2.6
*/
/*!
 * # Semantic UI - Reset
 * http://github.com/semantic-org/semantic-ui/
 *
 *
 * Released under the MIT license
 * http://opensource.org/licenses/MIT
 *
 */

/*******************************
            Theme
*******************************/

@type    : 'global';
@element : 'reset';

@import (multiple) '../../theme.config.import.less';

/*******************************
             Reset
*******************************/

/* Border-Box */
*,
*:before,
*:after {
  box-sizing: inherit;
}
html {
  box-sizing: border-box;
}

/* iPad Input Shadows */
input[type="text"], input[type="email"], input[type="search"], input[type="password"] {
  -webkit-appearance: none;
  -moz-appearance: none; /* mobile firefox too! */
}

.loadUIOverrides();
