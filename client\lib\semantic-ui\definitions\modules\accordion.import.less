/*
  DO NOT MODIFY - This file has been generated and will be regenerated
  Semantic UI v2.2.6
*/
/*!
 * # Semantic UI - Accordion
 * http://github.com/semantic-org/semantic-ui/
 *
 *
 * Released under the MIT license
 * http://opensource.org/licenses/MIT
 *
 */

/*******************************
            Theme
*******************************/

@type    : 'module';
@element : 'accordion';

@import (multiple) '../../theme.config.import.less';

/*******************************
            Accordion
*******************************/

.ui.accordion,
.ui.accordion .accordion {
  max-width: 100%;
}
.ui.accordion .accordion {
  margin: @childAccordionMargin;
  padding: @childAccordionPadding;
}

/* Title */
.ui.accordion .title,
.ui.accordion .accordion .title {
  cursor: pointer;
}

/* Default Styling */
.ui.accordion .title:not(.ui) {
  padding: @titlePadding;
  font-family: @titleFont;
  font-size: @titleFontSize;
  color: @titleColor;
}

/* Content */
.ui.accordion .title ~ .content,
.ui.accordion .accordion .title ~ .content {
  display: none;
}

/* Default Styling */
.ui.accordion:not(.styled) .title ~ .content:not(.ui),
.ui.accordion:not(.styled) .accordion .title ~ .content:not(.ui) {
  margin: @contentMargin;
  padding: @contentPadding;
}
.ui.accordion:not(.styled) .title ~ .content:not(.ui):last-child {
  padding-bottom: 0em;
}

/* Arrow */
.ui.accordion .title .dropdown.icon,
.ui.accordion .accordion .title .dropdown.icon {
  display: @iconDisplay;
  float: @iconFloat;
  opacity: @iconOpacity;
  width: @iconWidth;
  height: @iconHeight;
  margin: @iconMargin;
  padding: @iconPadding;
  font-size: @iconFontSize;
  transition: @iconTransition;
  vertical-align: @iconVerticalAlign;
  transform: @iconTransform;
}

/*--------------
    Coupling
---------------*/

/* Menu */
.ui.accordion.menu .item .title {
  display: block;
  padding: @menuTitlePadding;
}
.ui.accordion.menu .item .title > .dropdown.icon {
  float: @menuIconFloat;
  margin: @menuIconMargin;
  transform: @menuIconTransform;
}

/* Header */
.ui.accordion .ui.header .dropdown.icon {
  font-size: @iconFontSize;
  margin: @iconMargin;
}

/*******************************
            States
*******************************/

.ui.accordion .active.title .dropdown.icon,
.ui.accordion .accordion .active.title .dropdown.icon {
  transform: @activeIconTransform;
}

.ui.accordion.menu .item .active.title > .dropdown.icon {
  transform: @activeIconTransform;
}

/*******************************
            Types
*******************************/

/*--------------
     Styled
---------------*/

.ui.styled.accordion {
  width: @styledWidth;
}

.ui.styled.accordion,
.ui.styled.accordion .accordion {
  border-radius: @styledBorderRadius;
  background: @styledBackground;
  box-shadow: @styledBoxShadow;
}
.ui.styled.accordion .title,
.ui.styled.accordion .accordion .title {
  margin: @styledTitleMargin;
  padding: @styledTitlePadding;
  color: @styledTitleColor;
  font-weight: @styledTitleFontWeight;
  border-top: @styledTitleBorder;
  transition: @styledTitleTransition;
}
.ui.styled.accordion > .title:first-child,
.ui.styled.accordion .accordion .title:first-child {
  border-top: none;
}


/* Content */
.ui.styled.accordion .content,
.ui.styled.accordion .accordion .content {
  margin: @styledContentMargin;
  padding: @styledContentPadding;
}
.ui.styled.accordion .accordion .content {
  padding: @styledChildContentMargin;
  padding: @styledChildContentPadding;
}


/* Hover */
.ui.styled.accordion .title:hover,
.ui.styled.accordion .active.title,
.ui.styled.accordion .accordion .title:hover,
.ui.styled.accordion .accordion .active.title {
  background: @styledTitleHoverBackground;
  color: @styledTitleHoverColor;
}
.ui.styled.accordion .accordion .title:hover,
.ui.styled.accordion .accordion .active.title {
  background: @styledHoverChildTitleBackground;
  color: @styledHoverChildTitleColor;
}


/* Active */
.ui.styled.accordion .active.title {
  background: @styledActiveTitleBackground;
  color: @styledActiveTitleColor;
}
.ui.styled.accordion .accordion .active.title {
  background: @styledActiveChildTitleBackground;
  color: @styledActiveChildTitleColor;
}


/*******************************
            States
*******************************/

/*--------------
     Active
---------------*/

.ui.accordion .active.content,
.ui.accordion .accordion .active.content {
  display: block;
}

/*******************************
           Variations
*******************************/

/*--------------
     Fluid
---------------*/

.ui.fluid.accordion,
.ui.fluid.accordion .accordion {
  width: 100%;
}

/*--------------
     Inverted
---------------*/

.ui.inverted.accordion .title:not(.ui) {
  color: @invertedTitleColor;
}

.loadUIOverrides();

