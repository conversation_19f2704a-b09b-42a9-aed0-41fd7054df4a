/*
  DO NOT MODIFY - This file has been generated and will be regenerated
  Semantic UI v2.2.6
*/
/*!
 * # Semantic UI - Dropdown
 * http://github.com/semantic-org/semantic-ui/
 *
 *
 * Released under the MIT license
 * http://opensource.org/licenses/MIT
 *
 */


/*******************************
            Theme
*******************************/

@type    : 'module';
@element : 'dropdown';

@import (multiple) '../../theme.config.import.less';

/*******************************
            Dropdown
*******************************/

.ui.dropdown {
  cursor: pointer;
  position: relative;
  display: inline-block;
  outline: none;
  text-align: left;
  transition: @transition;

  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

/*******************************
            Content
*******************************/

/*--------------
      Menu
---------------*/

.ui.dropdown .menu {
  cursor: auto;
  position: absolute;
  display: none;
  outline: none;
  top: 100%;
  min-width: max-content;
  transition: @menuTransition;

  margin: @menuMargin;
  padding: @menuPadding;
  background: @menuBackground;

  font-size: @relativeMedium;
  text-shadow: none;
  text-align: @menuTextAlign;

  box-shadow: @menuBoxShadow;
  border: @menuBorder;
  border-radius: @menuBorderRadius;
  transition: @menuTransition;
  z-index: @menuZIndex;
  will-change: transform, opacity;
}

.ui.dropdown .menu > * {
  white-space: nowrap;
}


/*--------------
  Hidden Input
---------------*/

.ui.dropdown > input:not(.search):first-child,
.ui.dropdown > select {
  display: none !important;
}

/*--------------
 Dropdown Icon
---------------*/

.ui.dropdown > .dropdown.icon {
  position: relative;
  width: auto;
  font-size: @dropdownIconSize;
  margin: @dropdownIconMargin;
}
.ui.dropdown .menu > .item .dropdown.icon {
  width: auto;
  float: @itemDropdownIconFloat;
  margin: @itemDropdownIconMargin;
}
.ui.dropdown .menu > .item .dropdown.icon + .text {
  margin-right: @itemDropdownIconDistance;
}


/*--------------
      Text
---------------*/

.ui.dropdown > .text {
  display: inline-block;
  transition: @textTransition;
}

/*--------------
    Menu Item
---------------*/

.ui.dropdown .menu > .item {
  position: relative;
  cursor: pointer;
  display: block;
  border: @itemBorder;
  height: @itemHeight;
  text-align: @itemTextAlign;

  border-top: @itemDivider;
  line-height: @itemLineHeight;
  font-size: @itemFontSize;
  color: @itemColor;

  padding: @itemPadding !important;
  font-size: @itemFontSize;
  text-transform: @itemTextTransform;
  font-weight: @itemFontWeight;
  box-shadow: @itemBoxShadow;
  -webkit-touch-callout: none;
}
.ui.dropdown .menu > .item:first-child {
  border-top-width: 0px;
}

/*--------------
  Floated Content
---------------*/

.ui.dropdown > .text > [class*="right floated"],
.ui.dropdown .menu .item > [class*="right floated"] {
  float: right !important;
  margin-right: 0em !important;
  margin-left: @floatedDistance !important;
}
.ui.dropdown > .text > [class*="left floated"],
.ui.dropdown .menu .item > [class*="left floated"] {
  float: left !important;
  margin-left: 0em !important;
  margin-right: @floatedDistance !important;
}

.ui.dropdown .menu .item > .icon.floated,
.ui.dropdown .menu .item > .flag.floated,
.ui.dropdown .menu .item > .image.floated,
.ui.dropdown .menu .item > img.floated {
  margin-top: @itemLineHeightOffset;
}


/*--------------
  Menu Divider
---------------*/

.ui.dropdown .menu > .header {
  margin: @menuHeaderMargin;
  padding: @menuHeaderPadding;
  color: @menuHeaderColor;
  font-size: @menuHeaderFontSize;
  font-weight: @menuHeaderFontWeight;
  text-transform: @menuHeaderTextTransform;
}

.ui.dropdown .menu > .divider {
  border-top: @menuDividerBorder;
  height: 0em;
  margin: @menuDividerMargin;
}

.ui.dropdown .menu > .input {
  width: auto;
  display: flex;
  margin: @menuInputMargin;
  min-width: @menuInputMinWidth;
}
.ui.dropdown .menu > .header + .input {
  margin-top: 0em;
}
.ui.dropdown .menu > .input:not(.transparent) input {
  padding: @menuInputPadding;
}
.ui.dropdown .menu > .input:not(.transparent) .button,
.ui.dropdown .menu > .input:not(.transparent) .icon,
.ui.dropdown .menu > .input:not(.transparent) .label {
  padding-top: @menuInputVerticalPadding;
  padding-bottom: @menuInputVerticalPadding;
}

/*-----------------
  Item Description
-------------------*/

.ui.dropdown > .text > .description,
.ui.dropdown .menu > .item > .description {
  float: @itemDescriptionFloat;
  margin: @itemDescriptionMargin;
  color: @itemDescriptionColor;
}

/*-----------------
       Message
-------------------*/

.ui.dropdown .menu > .message {
  padding: @messagePadding;
  font-weight: @messageFontWeight;
}
.ui.dropdown .menu > .message:not(.ui) {
  color: @messageColor;
}

/*--------------
    Sub Menu
---------------*/

.ui.dropdown .menu .menu {
  top: @subMenuTop !important;
  left: @subMenuLeft !important;
  right: @subMenuRight !important;
  margin: @subMenuMargin !important;
  border-radius: @subMenuBorderRadius !important;
  z-index: @subMenuZIndex !important;
}

/* Hide Arrow */
.ui.dropdown .menu .menu:after {
  display: none;
}

/*--------------
   Sub Elements
---------------*/

/* Icons / Flags / Labels / Image */
.ui.dropdown > .text > .icon,
.ui.dropdown > .text > .label,
.ui.dropdown > .text > .flag,
.ui.dropdown > .text > img,
.ui.dropdown > .text > .image {
  margin-top: @textLineHeightOffset;
}
.ui.dropdown .menu > .item > .icon,
.ui.dropdown .menu > .item > .label,
.ui.dropdown .menu > .item > .flag,
.ui.dropdown .menu > .item > .image,
.ui.dropdown .menu > .item > img  {
  margin-top: @itemLineHeightOffset;
}

.ui.dropdown > .text > .icon,
.ui.dropdown > .text > .label,
.ui.dropdown > .text > .flag,
.ui.dropdown > .text > img,
.ui.dropdown > .text > .image,
.ui.dropdown .menu > .item > .icon,
.ui.dropdown .menu > .item > .label,
.ui.dropdown .menu > .item > .flag,
.ui.dropdown .menu > .item > .image,
.ui.dropdown .menu > .item > img  {
  margin-left: 0em;
  float: @itemElementFloat;
  margin-right: @itemElementDistance;
}

/*--------------
     Image
---------------*/

.ui.dropdown > .text > img,
.ui.dropdown > .text > .image,
.ui.dropdown .menu > .item > .image,
.ui.dropdown .menu > .item > img {
  display: inline-block;
  vertical-align: middle;
  width: auto;
  max-height: @menuImageMaxHeight;
}


/*******************************
            Coupling
*******************************/


/*--------------
      Menu
---------------*/

/* Remove Menu Item Divider */
.ui.dropdown .ui.menu > .item:before,
.ui.menu .ui.dropdown .menu > .item:before {
  display: none;
}

/* Prevent Menu Item Border */
.ui.menu .ui.dropdown .menu .active.item {
  border-left: none;
}

/* Automatically float dropdown menu right on last menu item */
.ui.menu .right.menu .dropdown:last-child .menu,
.ui.menu .right.dropdown.item .menu,
.ui.buttons > .ui.dropdown:last-child .menu {
  left: auto;
  right: 0em;
}

/*--------------
      Label
---------------*/

/* Dropdown Menu */
.ui.label.dropdown .menu {
  min-width: 100%;
}

/*--------------
     Button
---------------*/

/* No Margin On Icon Button */
.ui.dropdown.icon.button > .dropdown.icon {
  margin: 0em;
}
.ui.button.dropdown .menu {
  min-width: 100%;
}



/*******************************
              Types
*******************************/

/*--------------
    Selection
---------------*/

/* Displays like a select box */
.ui.selection.dropdown {
  cursor: pointer;
  word-wrap: break-word;
  line-height: 1em;
  white-space: normal;
  outline: 0;
  transform: rotateZ(0deg);

  min-width: @selectionMinWidth;
  min-height: @selectionMinHeight;

  background: @selectionBackground;
  display: @selectionDisplay;
  padding: @selectionPadding;
  color: @selectionTextColor;
  box-shadow: @selectionBoxShadow;
  border: @selectionBorder;
  border-radius: @selectionBorderRadius;
  transition: @selectionTransition;
}
.ui.selection.dropdown.visible,
.ui.selection.dropdown.active {
  z-index: @selectionZIndex;
}

select.ui.dropdown {
  height: @selectHeight;
  padding: @selectPadding;
  border: @selectBorder;
  visibility: @selectVisibility;
}
.ui.selection.dropdown > .search.icon,
.ui.selection.dropdown > .delete.icon,
.ui.selection.dropdown > .dropdown.icon {
  cursor: pointer;
  position: absolute;
  width: auto;
  height: auto;
  line-height: @searchSelectionLineHeight;
  top: @selectionVerticalPadding;
  right: @selectionHorizontalPadding;
  z-index: @selectionIconZIndex;
  margin: @selectionIconMargin;
  padding: @selectionIconPadding;
  opacity: @selectionIconOpacity;
  transition: @selectionIconTransition;
}

/* Compact */
.ui.compact.selection.dropdown {
  min-width:  0px;
}

/*  Selection Menu */
.ui.selection.dropdown .menu {
  overflow-x: hidden;
  overflow-y: auto;
  backface-visibility: hidden;
  -webkit-overflow-scrolling: touch;
  border-top-width: 0px !important;
  width: auto;
  outline: none;
  margin: 0px -@menuBorderWidth;
  min-width: @menuMinWidth;
  width: @menuMinWidth;

  border-radius: @selectionMenuBorderRadius;
  box-shadow: @selectionMenuBoxShadow;
  transition: @selectionMenuTransition;
}
.ui.selection.dropdown .menu:after,
.ui.selection.dropdown .menu:before {
  display: none;
}

/*--------------
    Message
---------------*/

.ui.selection.dropdown .menu > .message {
  padding: @selectionMessagePadding;
}

@media only screen and (max-width : @largestMobileScreen) {
  .ui.selection.dropdown .menu {
    max-height: @selectionMobileMaxMenuHeight;
  }
}
@media only screen and (min-width: @tabletBreakpoint) {
  .ui.selection.dropdown .menu {
    max-height: @selectionTabletMaxMenuHeight;
  }
}
@media only screen and (min-width: @computerBreakpoint) {
  .ui.selection.dropdown .menu {
    max-height: @selectionComputerMaxMenuHeight;
  }
}
@media only screen and (min-width: @widescreenMonitorBreakpoint) {
  .ui.selection.dropdown .menu {
    max-height: @selectionWidescreenMaxMenuHeight;
  }
}

/* Menu Item */
.ui.selection.dropdown .menu > .item {
  border-top: @selectionItemDivider;
  padding: @selectionItemPadding !important;
  white-space: normal;
  word-wrap: normal;
}

/* User Item */
.ui.selection.dropdown .menu > .hidden.addition.item {
  display: none;
}

/* Hover */
.ui.selection.dropdown:hover {
  border-color: @selectionHoverBorderColor;
  box-shadow: @selectionHoverBoxShadow;
}

/* Active */
.ui.selection.active.dropdown {
  border-color: @selectionVisibleBorderColor;
  box-shadow: @selectionVisibleBoxShadow;
}
.ui.selection.active.dropdown .menu {
  border-color: @selectionVisibleBorderColor;
  box-shadow: @selectionVisibleMenuBoxShadow;
}

/* Focus */
.ui.selection.dropdown:focus {
  border-color: @selectionFocusBorderColor;
  box-shadow: @selectionFocusBoxShadow;
}
.ui.selection.dropdown:focus .menu {
  border-color: @selectionFocusBorderColor;
  box-shadow: @selectionFocusMenuBoxShadow;
}

/* Visible */
.ui.selection.visible.dropdown > .text:not(.default) {
  font-weight: @selectionVisibleTextFontWeight;
  color: @selectionVisibleTextColor;
}

/* Visible Hover */
.ui.selection.active.dropdown:hover {
  border-color: @selectionActiveHoverBorderColor;
  box-shadow: @selectionActiveHoverBoxShadow;
}
.ui.selection.active.dropdown:hover .menu {
  border-color: @selectionActiveHoverBorderColor;
  box-shadow: @selectionActiveHoverMenuBoxShadow;
}

/* Dropdown Icon */
.ui.active.selection.dropdown > .dropdown.icon,
.ui.visible.selection.dropdown > .dropdown.icon {
  opacity: @selectionVisibleIconOpacity;
  z-index: 3;
}

/* Connecting Border */
.ui.active.selection.dropdown {
  border-bottom-left-radius: @selectionVisibleConnectingBorder !important;
  border-bottom-right-radius: @selectionVisibleConnectingBorder !important;
}

/* Empty Connecting Border */
.ui.active.empty.selection.dropdown {
  border-radius: @selectionBorderRadius !important;
  box-shadow: @selectionBoxShadow !important;
}
.ui.active.empty.selection.dropdown .menu {
  border: none !important;
  box-shadow: none !important;
}

/*--------------
   Searchable
---------------*/

/* Search Selection */
.ui.search.dropdown {
  min-width: @searchMinWidth;
}

/* Search Dropdown */
.ui.search.dropdown > input.search {
  background: none transparent !important;
  border: none !important;
  box-shadow: none !important;
  cursor: text;
  top: 0em;
  left: @textCursorSpacing;
  width: 100%;
  outline: none;
  -webkit-tap-highlight-color: rgba(255, 255, 255, 0);
  padding: inherit;
}

/* Text Layering */
.ui.search.dropdown > input.search {
  position: absolute;
  z-index: 2;
}
.ui.search.dropdown > .text {
  cursor: text;
  position: relative;
  left: @textCursorSpacing;
  z-index: 3;
}

/* Search Selection */
.ui.search.selection.dropdown > input.search {
  line-height: @searchSelectionLineHeight;
  padding: @searchSelectionInputPadding;
}

/* Used to size multi select input to character width */
.ui.search.selection.dropdown > span.sizer {
  line-height: @searchSelectionLineHeight;
  padding: @searchSelectionInputPadding;
  display: none;
  white-space: pre;
}

/* Active/Visible Search */
.ui.search.dropdown.active > input.search,
.ui.search.dropdown.visible > input.search {
  cursor: auto;
}
.ui.search.dropdown.active > .text,
.ui.search.dropdown.visible > .text {
  pointer-events: none;
}

/* Filtered Text */
.ui.active.search.dropdown input.search:focus + .text .icon,
.ui.active.search.dropdown input.search:focus + .text .flag {
  opacity: @selectionTextUnderlayIconOpacity;
}
.ui.active.search.dropdown input.search:focus + .text {
  color: @selectionTextUnderlayColor !important;
}

/* Search Menu */
.ui.search.dropdown .menu {
  overflow-x: hidden;
  overflow-y: auto;
  backface-visibility: hidden;
  -webkit-overflow-scrolling: touch;
}
@media only screen and (max-width : @largestMobileScreen) {
  .ui.search.dropdown .menu {
    max-height: @searchMobileMaxMenuHeight;
  }
}
@media only screen and (min-width: @tabletBreakpoint) {
  .ui.search.dropdown .menu {
    max-height: @searchTabletMaxMenuHeight;
  }
}
@media only screen and (min-width: @computerBreakpoint) {
  .ui.search.dropdown .menu {
    max-height: @searchComputerMaxMenuHeight;
  }
}
@media only screen and (min-width: @widescreenMonitorBreakpoint) {
  .ui.search.dropdown .menu {
    max-height: @searchWidescreenMaxMenuHeight;
  }
}

/*--------------
    Multiple
---------------*/

/* Multiple Selection */
.ui.multiple.dropdown {
  padding: @multipleSelectionPadding;
}
.ui.multiple.dropdown .menu {
  cursor: auto;
}

/* Multiple Search Selection */
.ui.multiple.search.dropdown,
.ui.multiple.search.dropdown > input.search {
  cursor: text;
}

/* Selection Label */
.ui.multiple.dropdown > .label {
  user-select: none;
  display: inline-block;
  vertical-align: top;
  white-space: normal;
  font-size: @labelSize;
  padding: @labelPadding;
  margin: @labelMargin;
  box-shadow: @labelBoxShadow;
}

/* Dropdown Icon */
.ui.multiple.dropdown .dropdown.icon {
  margin: @multipleSelectionDropdownIconMargin;
  padding: @multipleSelectionDropdownIconPadding;
}

/* Text */
.ui.multiple.dropdown > .text {
  position: static;
  padding: 0;
  max-width: 100%;
  margin: @multipleSelectionChildMargin;
  line-height: @multipleSelectionChildLineHeight;
}
.ui.multiple.dropdown > .label ~ input.search {
  margin-left: @multipleSelectionSearchAfterLabelDistance !important;
}
.ui.multiple.dropdown > .label ~ .text {
  display: none;
}

/*-----------------
  Multiple Search
-----------------*/

/* Prompt Text */
.ui.multiple.search.dropdown > .text {
  display: inline-block;
  position: absolute;
  top: 0;
  left: 0;
  padding: inherit;
  margin: @multipleSelectionChildMargin;
  line-height: @multipleSelectionChildLineHeight;
}

.ui.multiple.search.dropdown > .label ~ .text {
  display: none;
}

/* Search */
.ui.multiple.search.dropdown > input.search {
  position: static;
  padding: 0;
  max-width: 100%;
  margin: @multipleSelectionChildMargin;
  width: @multipleSelectionSearchStartWidth;
  line-height: @multipleSelectionChildLineHeight;
}


/*--------------
     Inline
---------------*/

.ui.inline.dropdown {
  cursor: pointer;
  display: inline-block;
  color: @inlineTextColor;
}
.ui.inline.dropdown .dropdown.icon {
  margin: @inlineIconMargin;
  vertical-align: baseline;
}
.ui.inline.dropdown > .text {
  font-weight: @inlineTextFontWeight;
}
.ui.inline.dropdown .menu {
  cursor: auto;
  margin-top: @inlineMenuDistance;
  border-radius: @inlineMenuBorderRadius;
}


/*******************************
            States
*******************************/


/*--------------------
        Active
----------------------*/

/* Menu Item Active */
.ui.dropdown .menu .active.item {
  background: @activeItemBackground;
  font-weight: @activeItemFontWeight;
  color: @activeItemColor;
  box-shadow: @activeItemBoxShadow;
  z-index: @activeItemZIndex;
}


/*--------------------
        Hover
----------------------*/

/* Menu Item Hover */
.ui.dropdown .menu > .item:hover {
  background: @hoveredItemBackground;
  color: @hoveredItemColor;
  z-index: @hoveredZIndex;
}

/*--------------------
       Loading
---------------------*/

.ui.loading.dropdown > i.icon {
  height: @relative14px !important;
  padding: @relative16px @relative15px !important;
}
.ui.loading.dropdown > i.icon:before {
  position: absolute;
  content: '';
  top: 50%;
  left: 50%;

  margin: @loaderMargin;
  width: @loaderSize;
  height: @loaderSize;

  border-radius: @circularRadius;
  border: @loaderLineWidth solid @loaderFillColor;
}
.ui.loading.dropdown > i.icon:after {
  position: absolute;
  content: '';
  top: 50%;
  left: 50%;
  box-shadow: 0px 0px 0px 1px transparent;

  margin: @loaderMargin;
  width: @loaderSize;
  height: @loaderSize;

  animation: dropdown-spin @loaderSpeed linear;
  animation-iteration-count: infinite;

  border-radius: @circularRadius;

  border-color: @loaderLineColor transparent transparent;
  border-style: solid;
  border-width: @loaderLineWidth;
}

/* Coupling */
.ui.loading.dropdown.button > i.icon:before,
.ui.loading.dropdown.button > i.icon:after {
  display: none;
}

@keyframes dropdown-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}


/*--------------------
     Default Text
----------------------*/

.ui.dropdown:not(.button) > .default.text,
.ui.default.dropdown:not(.button) > .text {
  color: @defaultTextColor;
}
.ui.dropdown:not(.button) > input:focus + .default.text,
.ui.default.dropdown:not(.button) > input:focus + .text {
  color: @defaultTextFocusColor;
}
/*--------------------
        Loading
----------------------*/

.ui.loading.dropdown > .text {
  transition: none;
}

/* Used To Check Position */
.ui.dropdown .loading.menu {
  display: block;
  visibility: hidden;
  z-index: @loadingZIndex;
}

/*--------------------
    Keyboard Select
----------------------*/

/* Selected Item */
.ui.dropdown.selected,
.ui.dropdown .menu .selected.item {
  background: @selectedBackground;
  color: @selectedColor;
}


/*--------------------
    Search Filtered
----------------------*/

/* Filtered Item */
.ui.dropdown > .filtered.text {
  visibility: hidden;
}
.ui.dropdown .filtered.item {
  display: none !important;
}


/*--------------------
        Error
----------------------*/

.ui.dropdown.error,
.ui.dropdown.error > .text,
.ui.dropdown.error > .default.text {
  color: @errorTextColor;
}

.ui.selection.dropdown.error {
  background: @errorBackgroundColor;
  border-color: @errorBorderColor;
}
.ui.selection.dropdown.error:hover {
  border-color: @errorBorderColor;
}

.ui.dropdown.error > .menu,
.ui.dropdown.error > .menu .menu {
  border-color: @errorBorderColor;
}
.ui.dropdown.error > .menu > .item {
  color: @errorItemTextColor;
}
.ui.multiple.selection.error.dropdown > .label {
  border-color: @errorBorderColor;
}

/* Item Hover */
.ui.dropdown.error > .menu > .item:hover {
  background-color: @errorItemHoverBackground;
}

/* Item Active */
.ui.dropdown.error > .menu .active.item {
  background-color: @errorItemActiveBackground;
}



/*--------------------
        Disabled
----------------------*/

/* Disabled */
.ui.disabled.dropdown,
.ui.dropdown .menu > .disabled.item {
  cursor: default;
  pointer-events: none;
  opacity: @disabledOpacity;
}


/*******************************
           Variations
*******************************/

/*--------------
    Direction
---------------*/

/* Flyout Direction */
.ui.dropdown .menu {
  left: 0px;
}


/* Default Side (Right) */
.ui.dropdown .right.menu > .menu,
.ui.dropdown .menu .right.menu {
  left: 100% !important;
  right: auto !important;
  border-radius: @subMenuBorderRadius !important;
}

/* Left Flyout Menu */
.ui.dropdown > .left.menu .menu,
.ui.dropdown .menu .left.menu {
  left: auto !important;
  right: 100% !important;
  border-radius: @leftSubMenuBorderRadius !important;
}

.ui.dropdown .item .left.dropdown.icon,
.ui.dropdown .left.menu .item .dropdown.icon {
  width: auto;
  float: @leftMenuDropdownIconFloat;
  margin: @leftMenuDropdownIconMargin;
}
.ui.dropdown .item .left.dropdown.icon,
.ui.dropdown .left.menu .item .dropdown.icon {
  width: auto;
  float: @leftMenuDropdownIconFloat;
  margin: @leftMenuDropdownIconMargin;
}
.ui.dropdown .item .left.dropdown.icon + .text,
.ui.dropdown .left.menu .item .dropdown.icon + .text {
  margin-left: @itemDropdownIconDistance;
}


/*--------------
     Upward
---------------*/

/* Upward Main Menu */
.ui.upward.dropdown > .menu {
  top: auto;
  bottom: 100%;
  box-shadow: @upwardMenuBoxShadow;
  border-radius: @upwardMenuBorderRadius;
}

/* Upward Sub Menu */
.ui.dropdown .upward.menu {
  top: auto !important;
  bottom: 0 !important;
}

/* Active Upward */
.ui.simple.upward.active.dropdown,
.ui.simple.upward.dropdown:hover {
  border-radius: @borderRadius @borderRadius 0em 0em !important;
}
.ui.upward.dropdown.button:not(.pointing):not(.floating).active {
  border-radius: @borderRadius @borderRadius 0em 0em;
}

/* Selection */
.ui.upward.selection.dropdown .menu {
  border-top-width: @menuBorderWidth !important;
  border-bottom-width: 0px !important;
  box-shadow: @upwardSelectionMenuBoxShadow;
}
.ui.upward.selection.dropdown:hover {
  box-shadow: @upwardSelectionHoverBoxShadow;
}

/* Active Upward */
.ui.active.upward.selection.dropdown {
  border-radius: @upwardSelectionVisibleBorderRadius !important;
}

/* Visible Upward */
.ui.upward.selection.dropdown.visible {
  box-shadow: @upwardSelectionVisibleBoxShadow;
  border-radius: @upwardSelectionVisibleBorderRadius !important;
}

/* Visible Hover Upward */
.ui.upward.active.selection.dropdown:hover {
  box-shadow: @upwardSelectionActiveHoverBoxShadow;
}
.ui.upward.active.selection.dropdown:hover .menu {
  box-shadow: @upwardSelectionActiveHoverMenuBoxShadow;
}

/*--------------
     Simple
---------------*/

/*  Selection Menu */
.ui.scrolling.dropdown .menu,
.ui.dropdown .scrolling.menu {
  overflow-x: hidden;
  overflow-y: auto;
}

.ui.scrolling.dropdown .menu {
  overflow-x: hidden;
  overflow-y: auto;
  backface-visibility: hidden;
  -webkit-overflow-scrolling: touch;
  min-width: 100% !important;
  width: auto !important;
}

.ui.dropdown .scrolling.menu {
  position: static;
  overflow-y: auto;
  border: none;
  box-shadow: none !important;
  border-radius: 0 !important;
  margin: 0 !important;
  min-width: 100% !important;
  width: auto !important;
  border-top: @menuBorder;
}
.ui.scrolling.dropdown .menu .item.item.item,
.ui.dropdown .scrolling.menu > .item.item.item {
  border-top: @scrollingMenuItemBorder;
  padding-right: @scrollingMenuRightItemPadding !important;
}
.ui.scrolling.dropdown .menu .item:first-child,
.ui.dropdown .scrolling.menu .item:first-child {
  border-top: none;
}
.ui.dropdown > .animating.menu .scrolling.menu,
.ui.dropdown > .visible.menu .scrolling.menu {
  display: block;
}

/* Scrollbar in IE */
@media all and (-ms-high-contrast:none) {
  .ui.scrolling.dropdown .menu,
  .ui.dropdown .scrolling.menu {
    min-width: ~"calc(100% - "@scrollbarWidth~")";
  }
}
@media only screen and (max-width : @largestMobileScreen) {
  .ui.scrolling.dropdown .menu,
  .ui.dropdown .scrolling.menu {
    max-height: @scrollingMobileMaxMenuHeight;
  }
}
@media only screen and (min-width: @tabletBreakpoint) {
  .ui.scrolling.dropdown .menu,
  .ui.dropdown .scrolling.menu {
    max-height: @scrollingTabletMaxMenuHeight;
  }
}
@media only screen and (min-width: @computerBreakpoint) {
  .ui.scrolling.dropdown .menu,
  .ui.dropdown .scrolling.menu {
    max-height: @scrollingComputerMaxMenuHeight;
  }
}
@media only screen and (min-width: @widescreenMonitorBreakpoint) {
  .ui.scrolling.dropdown .menu,
  .ui.dropdown .scrolling.menu {
    max-height: @scrollingWidescreenMaxMenuHeight;
  }
}

/*--------------
     Simple
---------------*/

/* Displays without javascript */

.ui.simple.dropdown .menu:before,
.ui.simple.dropdown .menu:after {
  display: none;
}
.ui.simple.dropdown .menu {
  position: absolute;
  display: block;
  overflow: hidden;
  top: -9999px !important;
  opacity: 0;
  width: 0;
  height: 0;
  transition: @simpleTransition;
}

.ui.simple.active.dropdown,
.ui.simple.dropdown:hover {
  border-bottom-left-radius: 0em !important;
  border-bottom-right-radius: 0em !important;
}

.ui.simple.active.dropdown > .menu,
.ui.simple.dropdown:hover > .menu {
  overflow: visible;
  width: auto;
  height: auto;
  top: 100% !important;
  opacity: 1;
}
.ui.simple.dropdown > .menu > .item:active > .menu,
.ui.simple.dropdown:hover > .menu > .item:hover > .menu {
  overflow: visible;
  width: auto;
  height: auto;
  top: 0% !important;
  left: 100% !important;
  opacity: 1;
}
.ui.simple.disabled.dropdown:hover .menu {
  display: none;
  height: 0px;
  width: 0px;
  overflow: hidden;
}

/* Visible */
.ui.simple.visible.dropdown > .menu {
  display: block;
}

/*--------------
      Fluid
---------------*/

.ui.fluid.dropdown {
  display: block;
  width: 100%;
  min-width: 0em;
}
.ui.fluid.dropdown > .dropdown.icon {
  float: right;
}


/*--------------
    Floating
---------------*/

.ui.floating.dropdown .menu {
  left: 0;
  right: auto;
  box-shadow: @floatingMenuBoxShadow !important;
  border-radius: @floatingMenuBorderRadius !important;
}
.ui.floating.dropdown > .menu {
  margin-top: @floatingMenuDistance !important;
  border-radius: @floatingMenuBorderRadius !important;
}

/*--------------
     Pointing
---------------*/

.ui.pointing.dropdown > .menu {
  top: 100%;
  margin-top: @pointingMenuDistance;
  border-radius: @pointingMenuBorderRadius;
}

.ui.pointing.dropdown > .menu:after {
  display: block;
  position: absolute;
  pointer-events: none;
  content: '';
  visibility: visible;
  transform: rotate(45deg);

  width: @pointingArrowSize;
  height: @pointingArrowSize;
  box-shadow: @pointingArrowBoxShadow;
  background: @pointingArrowBackground;
  z-index: @pointingArrowZIndex;
}

.ui.pointing.dropdown > .menu:after {
  top: @pointingArrowOffset;
  left: 50%;
  margin: 0em 0em 0em @pointingArrowOffset;
}

/* Top Left Pointing */
.ui.top.left.pointing.dropdown > .menu {
  top: 100%;
  bottom: auto;
  left: 0%;
  right: auto;
  margin: @pointingArrowDistanceFromEdge 0em 0em;
}
.ui.top.left.pointing.dropdown > .menu {
  top: 100%;
  bottom: auto;
  left: 0%;
  right: auto;
  margin: @pointingArrowDistanceFromEdge 0em 0em;
}
.ui.top.left.pointing.dropdown > .menu:after {
  top: @pointingArrowOffset;
  left: @pointingArrowDistanceFromEdge;
  right: auto;
  margin: 0em;
  transform: rotate(45deg);
}
/* Top Right  Pointing */
.ui.top.right.pointing.dropdown > .menu {
  top: 100%;
  bottom: auto;
  right: 0%;
  left: auto;
  margin: @pointingArrowDistanceFromEdge 0em 0em;
}
.ui.top.right.pointing.dropdown > .menu:after {
  top: @pointingArrowOffset;
  left: auto;
  right: @pointingArrowDistanceFromEdge;
  margin: 0em;
  transform: rotate(45deg);
}

/* Left Pointing */
.ui.left.pointing.dropdown > .menu {
  top: 0%;
  left: 100%;
  right: auto;
  margin: 0em 0em 0em @pointingArrowDistanceFromEdge;
}
.ui.left.pointing.dropdown > .menu:after {
  top: 1em;
  left: @pointingArrowOffset;
  margin: 0em 0em 0em 0em;
  transform: rotate(-45deg);
}

/* Right Pointing */
.ui.right.pointing.dropdown > .menu {
  top: 0%;
  left: auto;
  right: 100%;
  margin: 0em @pointingArrowDistanceFromEdge 0em 0em;
}
.ui.right.pointing.dropdown > .menu:after {
  top: 1em;
  left: auto;
  right: @pointingArrowOffset;
  margin: 0em 0em 0em 0em;
  transform: rotate(135deg);
}

/* Bottom Pointing */
.ui.bottom.pointing.dropdown > .menu {
  top: auto;
  bottom: 100%;
  left: 0%;
  right: auto;
  margin: 0em 0em @pointingArrowDistanceFromEdge ;
}
.ui.bottom.pointing.dropdown > .menu:after {
  top: auto;
  bottom: @pointingArrowOffset;
  right: auto;
  margin: 0em;
  transform: rotate(-135deg);
}
/* Reverse Sub-Menu Direction */
.ui.bottom.pointing.dropdown > .menu .menu {
  top: auto !important;
  bottom: 0px !important;
}

/* Bottom Left */
.ui.bottom.left.pointing.dropdown > .menu {
  left: 0%;
  right: auto;
}
.ui.bottom.left.pointing.dropdown > .menu:after {
  left: @pointingArrowDistanceFromEdge;
  right: auto;
}

/* Bottom Right */
.ui.bottom.right.pointing.dropdown > .menu {
  right: 0%;
  left: auto;
}
.ui.bottom.right.pointing.dropdown > .menu:after {
  left: auto;
  right: @pointingArrowDistanceFromEdge;
}

/* Upward pointing */
.ui.upward.pointing.dropdown > .menu,
.ui.upward.top.pointing.dropdown > .menu {
  top: auto;
  bottom: 100%;
  margin: 0em 0em @pointingMenuDistance;
  border-radius: @pointingUpwardMenuBorderRadius;
}
.ui.upward.pointing.dropdown > .menu:after,
.ui.upward.top.pointing.dropdown > .menu:after {
  top: 100%;
  bottom: auto;
  box-shadow: @pointingUpwardArrowBoxShadow;
  margin: @pointingArrowOffset 0em 0em;
}


.loadUIOverrides();
