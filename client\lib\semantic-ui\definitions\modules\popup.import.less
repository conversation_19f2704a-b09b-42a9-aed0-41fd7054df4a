/*
  DO NOT MODIFY - This file has been generated and will be regenerated
  Semantic UI v2.2.6
*/
/*!
 * # Semantic UI - Popup
 * http://github.com/semantic-org/semantic-ui/
 *
 *
 * Released under the MIT license
 * http://opensource.org/licenses/MIT
 *
 */


/*******************************
            Theme
*******************************/

@type    : 'module';
@element : 'popup';

@import (multiple) '../../theme.config.import.less';


/*******************************
            Popup
*******************************/

.ui.popup {
  display: none;
  position: absolute;
  top: 0px;
  right: 0px;

  /* Fixes content being squished when inline (moz only) */
  min-width: min-content;
  z-index: @zIndex;

  border: @border;
  line-height: @lineHeight;
  max-width: @maxWidth;
  background: @background;

  padding: @verticalPadding @horizontalPadding;
  font-weight: @fontWeight;
  font-style: @fontStyle;
  color: @color;

  border-radius: @borderRadius;
  box-shadow: @boxShadow;
}
.ui.popup > .header {
  padding: 0em;

  font-family: @headerFont;
  font-size: @headerFontSize;
  line-height: @headerLineHeight;
  font-weight: bold;
}
.ui.popup > .header + .content {
  padding-top: @headerDistance;
}

.ui.popup:before {
  position: absolute;
  content: '';
  width: @arrowSize;
  height: @arrowSize;

  background: @arrowBackground;
  transform: rotate(45deg);

  z-index: @arrowZIndex;
  box-shadow: @arrowBoxShadow;
}

/*******************************
            Types
*******************************/

/*--------------
    Tooltip
---------------*/

/* Content */
[data-tooltip] {
  position: relative;
}

/* Arrow */
[data-tooltip]:before {
  pointer-events: none;
  position: absolute;
  content: '';
  font-size: @medium;
  width: @arrowSize;
  height: @arrowSize;

  background: @tooltipArrowBackground;
  transform: rotate(45deg);

  z-index: @arrowZIndex;
  box-shadow: @tooltipArrowBoxShadow;
}

/* Popup */
[data-tooltip]:after {
  pointer-events: none;
  content: attr(data-tooltip);
  position: absolute;
  text-transform: none;
  text-align: left;
  white-space: nowrap;

  font-size: @tooltipFontSize;

  border: @tooltipBorder;
  line-height: @tooltipLineHeight;
  max-width: @tooltipMaxWidth;
  background: @tooltipBackground;

  padding: @tooltipPadding;
  font-weight: @tooltipFontWeight;
  font-style: @tooltipFontStyle;
  color: @tooltipColor;

  border-radius: @tooltipBorderRadius;
  box-shadow: @tooltipBoxShadow;
  z-index: @tooltipZIndex;
}

/* Default Position (Top Center) */
[data-tooltip]:not([data-position]):before {
  top: auto;
  right: auto;
  bottom: 100%;
  left: 50%;
  background: @tooltipArrowBottomBackground;
  margin-left: @tooltipArrowHorizontalOffset;
  margin-bottom: -@tooltipArrowVerticalOffset;
}
[data-tooltip]:not([data-position]):after {
  left: 50%;
  transform: translateX(-50%);
  bottom: 100%;
  margin-bottom: @tooltipDistanceAway;
}

/* Animation */
[data-tooltip]:before,
[data-tooltip]:after {
  pointer-events: none;
  visibility: hidden;
}
[data-tooltip]:before {
  opacity: 0;
  transform: rotate(45deg) scale(0) !important;
  transform-origin: center top;
  transition:
    all @tooltipDuration @tooltipEasing
  ;
}
[data-tooltip]:after {
  opacity: 1;
  transform-origin: center bottom;
  transition:
    all @tooltipDuration @tooltipEasing
  ;
}
[data-tooltip]:hover:before,
[data-tooltip]:hover:after {
  visibility: visible;
  pointer-events: auto;
}
[data-tooltip]:hover:before {
  transform: rotate(45deg) scale(1) !important;
  opacity: 1;
}

/* Animation Position */
[data-tooltip]:after,
[data-tooltip][data-position="top center"]:after,
[data-tooltip][data-position="bottom center"]:after {
  transform: translateX(-50%) scale(0) !important;
}
[data-tooltip]:hover:after,
[data-tooltip][data-position="bottom center"]:hover:after {
  transform: translateX(-50%) scale(1) !important;
}
[data-tooltip][data-position="left center"]:after,
[data-tooltip][data-position="right center"]:after {
  transform: translateY(-50%) scale(0) !important;
}
[data-tooltip][data-position="left center"]:hover:after,
[data-tooltip][data-position="right center"]:hover:after {
  transform: translateY(-50%) scale(1) !important;
}
[data-tooltip][data-position="top left"]:after,
[data-tooltip][data-position="top right"]:after,
[data-tooltip][data-position="bottom left"]:after,
[data-tooltip][data-position="bottom right"]:after {
  transform: scale(0) !important;
}
[data-tooltip][data-position="top left"]:hover:after,
[data-tooltip][data-position="top right"]:hover:after,
[data-tooltip][data-position="bottom left"]:hover:after,
[data-tooltip][data-position="bottom right"]:hover:after {
  transform: scale(1) !important;
}


/*--------------
    Inverted
---------------*/

/* Arrow */
[data-tooltip][data-inverted]:before {
  box-shadow: none !important;
}

/* Arrow Position */
[data-tooltip][data-inverted]:before {
  background: @invertedArrowBottomBackground;
}

/* Popup  */
[data-tooltip][data-inverted]:after {
  background: @tooltipInvertedBackground;
  color: @tooltipInvertedColor;
  border: @tooltipInvertedBorder;
  box-shadow: @tooltipInvertedBoxShadow;
}
[data-tooltip][data-inverted]:after .header {
  background-color: @tooltipInvertedHeaderBackground;
  color: @tooltipInvertedHeaderColor;
}

/*--------------
    Position
---------------*/

/* Top Center */
[data-position="top center"][data-tooltip]:after {
  top: auto;
  right: auto;
  left: 50%;
  bottom: 100%;
  transform: translateX(-50%);
  margin-bottom: @tooltipDistanceAway;
}
[data-position="top center"][data-tooltip]:before {
  top: auto;
  right: auto;
  bottom: 100%;
  left: 50%;
  background: @tooltipArrowTopBackground;
  margin-left: @tooltipArrowHorizontalOffset;
  margin-bottom: -@tooltipArrowVerticalOffset;
}

/* Top Left */
[data-position="top left"][data-tooltip]:after {
  top: auto;
  right: auto;
  left: 0;
  bottom: 100%;
  margin-bottom: @tooltipDistanceAway;
}
[data-position="top left"][data-tooltip]:before {
  top: auto;
  right: auto;
  bottom: 100%;
  left: @arrowDistanceFromEdge;
  margin-left: @tooltipArrowHorizontalOffset;
  margin-bottom: -@tooltipArrowVerticalOffset;
}

/* Top Right */
[data-position="top right"][data-tooltip]:after {
  top: auto;
  left: auto;
  right: 0;
  bottom: 100%;
  margin-bottom: @tooltipDistanceAway;
}
[data-position="top right"][data-tooltip]:before {
  top: auto;
  left: auto;
  bottom: 100%;
  right: @arrowDistanceFromEdge;
  margin-left: @tooltipArrowHorizontalOffset;
  margin-bottom: -@tooltipArrowVerticalOffset;
}


/* Bottom Center */
[data-position="bottom center"][data-tooltip]:after {
  bottom: auto;
  right: auto;
  left: 50%;
  top: 100%;
  transform: translateX(-50%);
  margin-top: @tooltipDistanceAway;
}
[data-position="bottom center"][data-tooltip]:before {
  bottom: auto;
  right: auto;
  top: 100%;
  left: 50%;
  margin-left: @tooltipArrowHorizontalOffset;
  margin-top: -@tooltipArrowVerticalOffset;
}

/* Bottom Left */
[data-position="bottom left"][data-tooltip]:after {
  left: 0;
  top: 100%;
  margin-top: @tooltipDistanceAway;
}
[data-position="bottom left"][data-tooltip]:before {
  bottom: auto;
  right: auto;
  top: 100%;
  left: @arrowDistanceFromEdge;
  margin-left: @tooltipArrowHorizontalOffset;
  margin-top: -@tooltipArrowVerticalOffset;
}

/* Bottom Right */
[data-position="bottom right"][data-tooltip]:after {
  right: 0;
  top: 100%;
  margin-top: @tooltipDistanceAway;
}
[data-position="bottom right"][data-tooltip]:before {
  bottom: auto;
  left: auto;
  top: 100%;
  right: @arrowDistanceFromEdge;
  margin-left: @tooltipArrowVerticalOffset;
  margin-top: -@tooltipArrowHorizontalOffset;
}

/* Left Center */
[data-position="left center"][data-tooltip]:after {
  right: 100%;
  top: 50%;
  margin-right: @tooltipDistanceAway;
  transform: translateY(-50%);
}
[data-position="left center"][data-tooltip]:before {
  right: 100%;
  top: 50%;
  margin-top: @tooltipArrowVerticalOffset;
  margin-right: @tooltipArrowHorizontalOffset;
}

/* Right Center */
[data-position="right center"][data-tooltip]:after {
  left: 100%;
  top: 50%;
  margin-left: @tooltipDistanceAway;
  transform: translateY(-50%);
}
[data-position="right center"][data-tooltip]:before {
  left: 100%;
  top: 50%;
  margin-top: @tooltipArrowHorizontalOffset;
  margin-left: -@tooltipArrowVerticalOffset;
}

/* Arrow */
[data-position~="bottom"][data-tooltip]:before {
  background: @arrowTopBackground;
  box-shadow: @bottomArrowBoxShadow;
}
[data-position="left center"][data-tooltip]:before {
  background: @arrowCenterBackground;
  box-shadow: @leftArrowBoxShadow;
}
[data-position="right center"][data-tooltip]:before {
  background: @arrowCenterBackground;
  box-shadow: @rightArrowBoxShadow;
}
[data-position~="top"][data-tooltip]:before {
  background: @arrowBottomBackground;
}

/* Inverted Arrow Color */
[data-inverted][data-position~="bottom"][data-tooltip]:before {
  background: @invertedArrowTopBackground;
  box-shadow: @bottomArrowBoxShadow;
}
[data-inverted][data-position="left center"][data-tooltip]:before {
  background: @invertedArrowCenterBackground;
  box-shadow: @leftArrowBoxShadow;
}
[data-inverted][data-position="right center"][data-tooltip]:before {
  background: @invertedArrowCenterBackground;
  box-shadow: @rightArrowBoxShadow;
}
[data-inverted][data-position~="top"][data-tooltip]:before {
  background: @invertedArrowBottomBackground;
}

[data-position~="bottom"][data-tooltip]:before {
  transform-origin: center bottom;
}
[data-position~="bottom"][data-tooltip]:after {
  transform-origin: center top;
}
[data-position="left center"][data-tooltip]:before {
  transform-origin: top center;
}
[data-position="left center"][data-tooltip]:after {
  transform-origin: right center;
}
[data-position="right center"][data-tooltip]:before {
  transform-origin: right center;
}
[data-position="right center"][data-tooltip]:after {
  transform-origin: left center;
}

/*--------------
     Spacing
---------------*/

.ui.popup {
  margin: 0em;
}

/* Extending from Top */
.ui.top.popup {
  margin: 0em 0em @popupDistanceAway;
}
.ui.top.left.popup {
  transform-origin: left bottom;
}
.ui.top.center.popup {
  transform-origin: center bottom;
}
.ui.top.right.popup {
  transform-origin: right bottom;
}

/* Extending from Vertical Center */
.ui.left.center.popup {
  margin: 0em @popupDistanceAway 0em 0em;
  transform-origin: right 50%;
}
.ui.right.center.popup {
  margin: 0em 0em 0em @popupDistanceAway;
  transform-origin: left 50%;
}

/* Extending from Bottom */
.ui.bottom.popup {
  margin: @popupDistanceAway 0em 0em;
}
.ui.bottom.left.popup {
  transform-origin: left top;
}
.ui.bottom.center.popup {
  transform-origin: center top;
}
.ui.bottom.right.popup {
  transform-origin: right top;
}

/*--------------
     Pointer
---------------*/

/*--- Below ---*/
.ui.bottom.center.popup:before {
  margin-left: @arrowOffset;
  top: @arrowOffset;
  left: 50%;
  right: auto;
  bottom: auto;
  box-shadow: @bottomArrowBoxShadow;
}

.ui.bottom.left.popup {
  margin-left: @boxArrowOffset;
}
/*rtl:rename*/
.ui.bottom.left.popup:before {
  top: @arrowOffset;
  left: @arrowDistanceFromEdge;
  right: auto;
  bottom: auto;
  margin-left: 0em;
  box-shadow: @bottomArrowBoxShadow;
}

.ui.bottom.right.popup {
  margin-right: @boxArrowOffset;
}
/*rtl:rename*/
.ui.bottom.right.popup:before {
  top: @arrowOffset;
  right: @arrowDistanceFromEdge;
  bottom: auto;
  left: auto;
  margin-left: 0em;
  box-shadow: @bottomArrowBoxShadow;
}

/*--- Above ---*/
.ui.top.center.popup:before {
  top: auto;
  right: auto;
  bottom: @arrowOffset;
  left: 50%;
  margin-left: @arrowOffset;
}
.ui.top.left.popup {
  margin-left: @boxArrowOffset;
}
/*rtl:rename*/
.ui.top.left.popup:before {
  bottom: @arrowOffset;
  left: @arrowDistanceFromEdge;
  top: auto;
  right: auto;
  margin-left: 0em;
}
.ui.top.right.popup {
  margin-right: @boxArrowOffset;
}
/*rtl:rename*/
.ui.top.right.popup:before {
  bottom: @arrowOffset;
  right: @arrowDistanceFromEdge;
  top: auto;
  left: auto;
  margin-left: 0em;
}

/*--- Left Center ---*/
/*rtl:rename*/
.ui.left.center.popup:before {
  top: 50%;
  right: @arrowOffset;
  bottom: auto;
  left: auto;
  margin-top: @arrowOffset;
  box-shadow: @leftArrowBoxShadow;
}

/*--- Right Center  ---*/
/*rtl:rename*/
.ui.right.center.popup:before {
  top: 50%;
  left: @arrowOffset;
  bottom: auto;
  right: auto;
  margin-top: @arrowOffset;
  box-shadow: @rightArrowBoxShadow;
}

/* Arrow Color By Location */
.ui.bottom.popup:before {
  background: @arrowTopBackground;
}
.ui.right.center.popup:before,
.ui.left.center.popup:before {
  background: @arrowCenterBackground;
}
.ui.top.popup:before {
  background: @arrowBottomBackground;
}

/* Inverted Arrow Color */
.ui.inverted.bottom.popup:before {
  background: @invertedArrowTopBackground;
}
.ui.inverted.right.center.popup:before,
.ui.inverted.left.center.popup:before {
  background: @invertedArrowCenterBackground;
}
.ui.inverted.top.popup:before {
  background: @invertedArrowBottomBackground;
}


/*******************************
            Coupling
*******************************/

/* Immediate Nested Grid */
.ui.popup > .ui.grid:not(.padded) {
  width: @nestedGridWidth;
  margin: @nestedGridMargin;
}

/*******************************
            States
*******************************/

.ui.loading.popup {
  display: block;
  visibility: hidden;
  z-index: @loadingZIndex;
}

.ui.animating.popup,
.ui.visible.popup {
  display: block;
}

.ui.visible.popup {
  transform: translateZ(0px);
  backface-visibility: hidden;
}


/*******************************
            Variations
*******************************/

/*--------------
     Basic
---------------*/

.ui.basic.popup:before {
  display: none;
}


/*--------------
     Wide
---------------*/

.ui.wide.popup {
  max-width: @wideWidth;
}
.ui[class*="very wide"].popup {
  max-width: @veryWideWidth;
}

@media only screen and (max-width: @largestMobileScreen) {
  .ui.wide.popup,
  .ui[class*="very wide"].popup {
    max-width: @maxWidth;
  }
}


/*--------------
     Fluid
---------------*/

.ui.fluid.popup {
  width: 100%;
  max-width: none;
}


/*--------------
     Colors
---------------*/

/* Inverted colors  */
.ui.inverted.popup {
  background: @invertedBackground;
  color: @invertedColor;
  border: @invertedBorder;
  box-shadow: @invertedBoxShadow;
}
.ui.inverted.popup .header {
  background-color: @invertedHeaderBackground;
  color: @invertedHeaderColor;
}
.ui.inverted.popup:before {
  background-color: @invertedArrowColor;
  box-shadow: none !important;
}

/*--------------
     Flowing
---------------*/

.ui.flowing.popup {
  max-width: none;
}


/*--------------
     Sizes
---------------*/

.ui.mini.popup {
  font-size: @mini;
}
.ui.tiny.popup {
  font-size: @tiny;
}
.ui.small.popup {
  font-size: @small;
}
.ui.popup {
  font-size: @medium;
}
.ui.large.popup {
  font-size: @large;
}
.ui.huge.popup {
  font-size: @huge;
}


.loadUIOverrides();
