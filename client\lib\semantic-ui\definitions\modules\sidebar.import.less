/*
  DO NOT MODIFY - This file has been generated and will be regenerated
  Semantic UI v2.2.6
*/
/*!
 * # Semantic UI - Sidebar
 * http://github.com/semantic-org/semantic-ui/
 *
 *
 * Released under the MIT license
 * http://opensource.org/licenses/MIT
 *
 */



/*******************************
            Theme
*******************************/

@type    : 'module';
@element : 'sidebar';

@import (multiple) '../../theme.config.import.less';

/*******************************
            Sidebar
*******************************/

/* Sidebar Menu */
.ui.sidebar {
  position: fixed;
  top: 0;
  left: 0;

  backface-visibility: hidden;
  transition: none;
  will-change: transform;
  transform: translate3d(0, 0, 0);
  visibility: hidden;
  -webkit-overflow-scrolling: touch;

  height: 100% !important;
  max-height: 100%;
  border-radius: 0em !important;
  margin: 0em !important;
  overflow-y: auto !important;
  z-index: @topLayer;
}

/* GPU Layers for Child Elements */
.ui.sidebar > * {
  backface-visibility: hidden;
}


/*--------------
   Direction
---------------*/

.ui.left.sidebar {
  right: auto;
  left: 0px;
  transform: translate3d(-100%, 0, 0);
}
.ui.right.sidebar {
  right: 0px !important;
  left: auto !important;
  transform: translate3d(100%, 0%, 0);
}

.ui.top.sidebar,
.ui.bottom.sidebar {
  width: 100% !important;
  height: auto !important;
}
.ui.top.sidebar {
  top: 0px !important;
  bottom: auto !important;
  transform: translate3d(0, -100%, 0);
}
.ui.bottom.sidebar {
  top: auto !important;
  bottom: 0px !important;
  transform: translate3d(0, 100%, 0);
}


/*--------------
     Pushable
---------------*/

.pushable {
  height: 100%;
  overflow-x: hidden;
  padding: 0em !important;
}

/* Whole Page */
body.pushable {
  background: @canvasBackground !important;
}

/* Page Context */
.pushable:not(body) {
  transform: translate3d(0, 0, 0);
}
.pushable:not(body) > .ui.sidebar,
.pushable:not(body) > .fixed,
.pushable:not(body) > .pusher:after {
  position: absolute;
}


/*--------------
     Fixed
---------------*/

.pushable > .fixed {
  position: fixed;
  backface-visibility: hidden;

  transition: transform @duration @easing;
  will-change: transform;
  z-index: @fixedLayer;
}

/*--------------
     Page
---------------*/

.pushable > .pusher {
  position: relative;
  backface-visibility: hidden;
  overflow: hidden;
  min-height: 100%;
  transition: transform @duration @easing;
  z-index: @middleLayer;
}

body.pushable > .pusher {
  background: @pageBackground;
}

/* Pusher should inherit background from context */
.pushable > .pusher {
  background: inherit;
}

/*--------------
     Dimmer
---------------*/

.pushable > .pusher:after {
  position: fixed;
  top: 0px;
  right: 0px;
  content: '';
  background-color: @dimmerColor;
  overflow: hidden;
  opacity: 0;
  transition: @dimmerTransition;
  will-change: opacity;
  z-index: @dimmerLayer;
}

/*--------------
    Coupling
---------------*/

.ui.sidebar.menu .item {
  border-radius: 0em !important;
}

/*******************************
            States
*******************************/

/*--------------
     Dimmed
---------------*/

.pushable > .pusher.dimmed:after {
  width: 100% !important;
  height: 100% !important;
  opacity: 1 !important;
}

/*--------------
    Animating
---------------*/

.ui.animating.sidebar {
  visibility: visible;
}

/*--------------
     Visible
---------------*/

.ui.visible.sidebar {
  visibility: visible;
  transform: translate3d(0, 0, 0);
}

/* Shadow Direction */
.ui.left.visible.sidebar,
.ui.right.visible.sidebar {
  box-shadow: @horizontalBoxShadow;
}
.ui.top.visible.sidebar,
.ui.bottom.visible.sidebar {
  box-shadow: @verticalBoxShadow;
}

/* Visible On Load */
.ui.visible.left.sidebar ~ .fixed,
.ui.visible.left.sidebar ~ .pusher {
  transform: translate3d(@width, 0, 0);
}
.ui.visible.right.sidebar ~ .fixed,
.ui.visible.right.sidebar ~ .pusher {
  transform: translate3d(-@width, 0, 0);
}
.ui.visible.top.sidebar ~ .fixed,
.ui.visible.top.sidebar ~ .pusher {
  transform: translate3d(0, @height, 0);
}
.ui.visible.bottom.sidebar ~ .fixed,
.ui.visible.bottom.sidebar ~ .pusher {
  transform: translate3d(0, -@height, 0);
}

/* opposite sides visible forces content overlay */
.ui.visible.left.sidebar ~ .ui.visible.right.sidebar ~ .fixed,
.ui.visible.left.sidebar ~ .ui.visible.right.sidebar ~ .pusher,
.ui.visible.right.sidebar ~ .ui.visible.left.sidebar ~ .fixed,
.ui.visible.right.sidebar ~ .ui.visible.left.sidebar ~ .pusher {
  transform: translate3d(0, 0, 0);
}

/*--------------
       iOS
---------------*/

/*
  iOS incorrectly sizes document when content
  is presented outside of view with 2Dtranslate
*/
html.ios {
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
}

html.ios,
html.ios body {
  height: initial !important;
}


/*******************************
          Variations
*******************************/

/*--------------
     Width
---------------*/

/* Left / Right */
.ui.thin.left.sidebar,
.ui.thin.right.sidebar {
  width: @thinWidth;
}
.ui[class*="very thin"].left.sidebar,
.ui[class*="very thin"].right.sidebar {
  width: @veryThinWidth;
}
.ui.left.sidebar,
.ui.right.sidebar {
  width: @width;
}
.ui.wide.left.sidebar,
.ui.wide.right.sidebar {
  width: @wideWidth;
}
.ui[class*="very wide"].left.sidebar,
.ui[class*="very wide"].right.sidebar {
  width: @veryWideWidth;
}

/* Left Visible */
.ui.visible.thin.left.sidebar ~ .fixed,
.ui.visible.thin.left.sidebar ~ .pusher {
  transform: translate3d(@thinWidth, 0, 0);
}
.ui.visible[class*="very thin"].left.sidebar ~ .fixed,
.ui.visible[class*="very thin"].left.sidebar ~ .pusher {
  transform: translate3d(@veryThinWidth, 0, 0);
}
.ui.visible.wide.left.sidebar ~ .fixed,
.ui.visible.wide.left.sidebar ~ .pusher {
  transform: translate3d(@wideWidth, 0, 0);
}
.ui.visible[class*="very wide"].left.sidebar ~ .fixed,
.ui.visible[class*="very wide"].left.sidebar ~ .pusher {
  transform: translate3d(@veryWideWidth, 0, 0);
}

/* Right Visible */
.ui.visible.thin.right.sidebar ~ .fixed,
.ui.visible.thin.right.sidebar ~ .pusher {
  transform: translate3d(-@thinWidth, 0, 0);
}
.ui.visible[class*="very thin"].right.sidebar ~ .fixed,
.ui.visible[class*="very thin"].right.sidebar ~ .pusher {
  transform: translate3d(-@veryThinWidth, 0, 0);
}
.ui.visible.wide.right.sidebar ~ .fixed,
.ui.visible.wide.right.sidebar ~ .pusher {
  transform: translate3d(-@wideWidth, 0, 0);
}
.ui.visible[class*="very wide"].right.sidebar ~ .fixed,
.ui.visible[class*="very wide"].right.sidebar ~ .pusher {
  transform: translate3d(-@veryWideWidth, 0, 0);
}



/*******************************
          Animations
*******************************/

/*--------------
    Overlay
---------------*/

/* Set-up */
.ui.overlay.sidebar {
  z-index: @topLayer;
}

/* Initial */
.ui.left.overlay.sidebar {
  transform: translate3d(-100%, 0%, 0);
}
.ui.right.overlay.sidebar {
  transform: translate3d(100%, 0%, 0);
}
.ui.top.overlay.sidebar {
  transform: translate3d(0%, -100%, 0);
}
.ui.bottom.overlay.sidebar {
  transform: translate3d(0%, 100%, 0);
}

/* Animation */
.animating.ui.overlay.sidebar,
.ui.visible.overlay.sidebar {
  transition: transform @duration @easing;
}

/* End - Sidebar */
.ui.visible.left.overlay.sidebar {
  transform: translate3d(0%, 0%, 0);
}
.ui.visible.right.overlay.sidebar {
  transform: translate3d(0%, 0%, 0);
}
.ui.visible.top.overlay.sidebar {
  transform: translate3d(0%, 0%, 0);
}
.ui.visible.bottom.overlay.sidebar {
  transform: translate3d(0%, 0%, 0);
}

/* End - Pusher */
.ui.visible.overlay.sidebar ~ .fixed,
.ui.visible.overlay.sidebar ~ .pusher {
  transform: none !important;
}



/*--------------
      Push
---------------*/

/* Initial */
.ui.push.sidebar {
  transition: transform @duration @easing;
  z-index: @topLayer;
}

/* Sidebar - Initial */
.ui.left.push.sidebar {
  transform: translate3d(-100%, 0, 0);
}
.ui.right.push.sidebar {
  transform: translate3d(100%, 0, 0);
}
.ui.top.push.sidebar {
  transform: translate3d(0%, -100%, 0);
}
.ui.bottom.push.sidebar {
  transform: translate3d(0%, 100%, 0);
}

/* End */
.ui.visible.push.sidebar {
  transform: translate3d(0%, 0, 0);
}


/*--------------
    Uncover
---------------*/

/* Initial */
.ui.uncover.sidebar {
  transform: translate3d(0, 0, 0);
  z-index: @bottomLayer;
}

/* End */
.ui.visible.uncover.sidebar {
  transform: translate3d(0, 0, 0);
  transition: transform @duration @easing;
}


/*--------------
   Slide Along
---------------*/

/* Initial */
.ui.slide.along.sidebar {
  z-index: @bottomLayer;
}

/* Sidebar - Initial */
.ui.left.slide.along.sidebar {
  transform: translate3d(-50%, 0, 0);
}
.ui.right.slide.along.sidebar {
  transform: translate3d(50%, 0, 0);
}
.ui.top.slide.along.sidebar {
  transform: translate3d(0, -50%, 0);
}
.ui.bottom.slide.along.sidebar {
  transform: translate3d(0%, 50%, 0);
}

/* Animation */
.ui.animating.slide.along.sidebar {
  transition: transform @duration @easing;
}

/* End */
.ui.visible.slide.along.sidebar {
  transform: translate3d(0%, 0, 0);
}


/*--------------
   Slide Out
---------------*/

/* Initial */
.ui.slide.out.sidebar {
  z-index: @bottomLayer;
}

/* Sidebar - Initial */
.ui.left.slide.out.sidebar {
  transform: translate3d(50%, 0, 0);
}
.ui.right.slide.out.sidebar {
  transform: translate3d(-50%, 0, 0);
}
.ui.top.slide.out.sidebar {
  transform: translate3d(0%, 50%, 0);
}
.ui.bottom.slide.out.sidebar {
  transform: translate3d(0%, -50%, 0);
}

/* Animation */
.ui.animating.slide.out.sidebar {
  transition: transform @duration @easing;
}

/* End */
.ui.visible.slide.out.sidebar {
  transform: translate3d(0%, 0, 0);
}

/*--------------
   Scale Down
---------------*/

/* Initial */
.ui.scale.down.sidebar {
  transition: transform @duration @easing;
  z-index: @topLayer;
}

/* Sidebar - Initial  */
.ui.left.scale.down.sidebar {
  transform: translate3d(-100%, 0, 0);
}
.ui.right.scale.down.sidebar {
  transform: translate3d(100%, 0, 0);
}
.ui.top.scale.down.sidebar {
  transform: translate3d(0%, -100%, 0);
}
.ui.bottom.scale.down.sidebar {
  transform: translate3d(0%, 100%, 0);
}

/* Pusher - Initial */
.ui.scale.down.left.sidebar ~ .pusher {
  transform-origin: 75% 50%;
}
.ui.scale.down.right.sidebar ~ .pusher {
  transform-origin: 25% 50%;
}
.ui.scale.down.top.sidebar ~ .pusher {
  transform-origin: 50% 75%;
}
.ui.scale.down.bottom.sidebar ~ .pusher {
  transform-origin: 50% 25%;
}

/* Animation */
.ui.animating.scale.down > .visible.ui.sidebar {
  transition: transform @duration @easing;
}
.ui.visible.scale.down.sidebar ~ .pusher,
.ui.animating.scale.down.sidebar ~ .pusher {
  display: block !important;
  width: 100%;
  height: 100%;
  overflow: hidden !important;
}

/* End */
.ui.visible.scale.down.sidebar {
  transform: translate3d(0, 0, 0);
}
.ui.visible.scale.down.sidebar ~ .pusher {
  transform: scale(0.75);
}

.loadUIOverrides();
