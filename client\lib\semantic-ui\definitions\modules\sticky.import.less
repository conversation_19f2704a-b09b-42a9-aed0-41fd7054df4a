/*
  DO NOT MODIFY - This file has been generated and will be regenerated
  Semantic UI v2.2.6
*/
/*!
 * # Semantic UI - Sticky
 * http://github.com/semantic-org/semantic-ui/
 *
 *
 * Released under the MIT license
 * http://opensource.org/licenses/MIT
 *
 */


/*******************************
            Theme
*******************************/

@type    : 'module';
@element : 'sticky';

@import (multiple) '../../theme.config.import.less';

/*******************************
            Sticky
*******************************/

.ui.sticky {
  position: static;
  transition: @transition;
  z-index: @zIndex;
}

/*******************************
            States
*******************************/

/* Bound */
.ui.sticky.bound {
  position: absolute;
  left: auto;
  right: auto;
}

/* Fixed */
.ui.sticky.fixed {
  position: fixed;
  left: auto;
  right: auto;
}

/* Bound/Fixed Position */
.ui.sticky.bound.top,
.ui.sticky.fixed.top {
  top: 0px;
  bottom: auto;
}
.ui.sticky.bound.bottom,
.ui.sticky.fixed.bottom {
  top: auto;
  bottom: 0px;
}


/*******************************
            Types
*******************************/

.ui.native.sticky {
  position: -webkit-sticky;
  position: -moz-sticky;
  position: -ms-sticky;
  position: -o-sticky;
  position: sticky;
}

.loadUIOverrides();
