/*
  DO NOT MODIFY - This file has been generated and will be regenerated
  Semantic UI v2.2.6
*/
/*!
 * # Semantic UI - Tab
 * http://github.com/semantic-org/semantic-ui/
 *
 *
 * Released under the MIT license
 * http://opensource.org/licenses/MIT
 *
 */


/*******************************
            Theme
*******************************/

@type    : 'module';
@element : 'tab';

@import (multiple) '../../theme.config.import.less';

/*******************************
           UI Tabs
*******************************/

.ui.tab {
  display: none;
}

/*******************************
             States
*******************************/

/*--------------------
       Active
---------------------*/

.ui.tab.active,
.ui.tab.open {
  display: block;
}

/*--------------------
       Loading
---------------------*/

.ui.tab.loading {
  position: relative;
  overflow: hidden;
  display: block;
  min-height: @loadingMinHeight;
}
.ui.tab.loading * {
  position: @loadingContentPosition !important;
  left: @loadingContentOffset !important;
}

.ui.tab.loading:before,
.ui.tab.loading.segment:before {
  position: absolute;
  content: '';
  top: @loaderDistanceFromTop;
  left: 50%;

  margin: @loaderMargin;
  width: @loaderSize;
  height: @loaderSize;

  border-radius: @circularRadius;
  border: @loaderLineWidth solid @loaderFillColor;
}
.ui.tab.loading:after,
.ui.tab.loading.segment:after {
  position: absolute;
  content: '';
  top: @loaderDistanceFromTop;
  left: 50%;

  margin: @loaderMargin;
  width: @loaderSize;
  height: @loaderSize;

  animation: button-spin @loaderSpeed linear;
  animation-iteration-count: infinite;

  border-radius: @circularRadius;

  border-color: @loaderLineColor transparent transparent;
  border-style: solid;
  border-width: @loaderLineWidth;

  box-shadow: 0px 0px 0px 1px transparent;
}

.loadUIOverrides();
