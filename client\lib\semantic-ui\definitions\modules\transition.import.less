/*
  DO NOT MODIFY - This file has been generated and will be regenerated
  Semantic UI v2.2.6
*/
/*!
 * # Semantic UI - Transition
 * http://github.com/semantic-org/semantic-ui/
 *
 *
 * Released under the MIT license
 * http://opensource.org/licenses/MIT
 *
 */


/*******************************
            Theme
*******************************/

@type    : 'module';
@element : 'transition';

@import (multiple) '../../theme.config.import.less';

/*******************************
          Transitions
*******************************/

.transition {
  animation-iteration-count: 1;
  animation-duration: @transitionDefaultDuration;
  animation-timing-function: @transitionDefaultEasing;
  animation-fill-mode: @transitionDefaultFill;
}

/*******************************
            States
*******************************/


/* Animating */
.animating.transition {
  backface-visibility: @backfaceVisibility;
  visibility: visible !important;
}

/* Loading */
.loading.transition {
  position: absolute;
  top: -99999px;
  left: -99999px;
}

/* Hidden */
.hidden.transition {
  display: none;
  visibility: hidden;
}

/* Visible */
.visible.transition {
  display: block !important;
  visibility: visible !important;
/*  backface-visibility: @backfaceVisibility;
  transform: @use3DAcceleration;*/
}

/* Disabled */
.disabled.transition {
  animation-play-state: paused;
}

/*******************************
          Variations
*******************************/

.looping.transition {
  animation-iteration-count: infinite;
}


.loadUIOverrides();
