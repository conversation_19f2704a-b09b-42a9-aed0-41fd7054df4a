/*
  DO NOT MODIFY - This file has been generated and will be regenerated
  Semantic UI v2.2.6
*/
/*!
 * # Semantic UI - Item
 * http://github.com/semantic-org/semantic-ui/
 *
 *
 * Released under the MIT license
 * http://opensource.org/licenses/MIT
 *
 */

/*******************************
            Theme
*******************************/

@type    : 'view';
@element : 'card';

@import (multiple) '../../theme.config.import.less';

/*******************************
            Standard
*******************************/

/*--------------
      Card
---------------*/

.ui.cards > .card,
.ui.card {
  max-width: 100%;
  position: relative;
  display: @display;
  flex-direction: column;

  width: @width;
  min-height: @minHeight;
  background: @background;
  padding: @padding;

  border: @border;
  border-radius: @borderRadius;
  box-shadow: @boxShadow;
  transition: @transition;
  z-index: @zIndex;
}
.ui.card {
  margin: @margin;
}

.ui.cards > .card a,
.ui.card a {
  cursor: pointer;
}

.ui.card:first-child {
  margin-top: 0em;
}
.ui.card:last-child {
  margin-bottom: 0em;
}

/*--------------
      Cards
---------------*/

.ui.cards {
  display: @groupDisplay;
  margin: @groupMargin;
  flex-wrap: wrap;
}

.ui.cards > .card {
  display: @groupCardDisplay;
  margin: @groupCardMargin;
  float: @groupCardFloat;
}

/* Clearing */
.ui.cards:after,
.ui.card:after {
  display: block;
  content: ' ';
  height: 0px;
  clear: both;
  overflow: hidden;
  visibility: hidden;
}


/* Consecutive Card Groups Preserve Row Spacing */
.ui.cards ~ .ui.cards {
  margin-top: @consecutiveGroupDistance;
}


/*--------------
  Rounded Edges
---------------*/

.ui.cards > .card > :first-child,
.ui.card > :first-child {
  border-radius: @borderRadius @borderRadius 0em 0em !important;
  border-top: none !important;
}

.ui.cards > .card > :last-child,
.ui.card > :last-child {
  border-radius: 0em 0em @borderRadius @borderRadius !important;
}

.ui.cards > .card > :only-child,
.ui.card > :only-child {
  border-radius: @borderRadius !important;
}

/*--------------
     Images
---------------*/

.ui.cards > .card > .image,
.ui.card > .image {
  position: relative;
  display: block;
  flex: 0 0 auto;
  padding: @imagePadding;
  background: @imageBackground;
}
.ui.cards > .card > .image > img,
.ui.card > .image > img {
  display: block;
  width: 100%;
  height: auto;
  border-radius: inherit;
}
.ui.cards > .card > .image:not(.ui) > img,
.ui.card > .image:not(.ui) > img {
  border: @imageBorder;
}

/*--------------
     Content
---------------*/

.ui.cards > .card > .content,
.ui.card > .content {
  flex-grow: 1;
  border: @contentBorder;
  border-top: @contentDivider;
  background: @contentBackground;
  margin: @contentMargin;
  padding: @contentPadding;
  box-shadow: @contentBoxShadow;
  font-size: @contentFontSize;
  border-radius: @contentBorderRadius;
}

.ui.cards > .card > .content:after,
.ui.card > .content:after {
  display: block;
  content: ' ';
  height: 0px;
  clear: both;
  overflow: hidden;
  visibility: hidden;
}

.ui.cards > .card > .content > .header,
.ui.card > .content > .header {
  display: block;
  margin: @headerMargin;
  font-family: @headerFont;
  color: @headerColor;
}

/* Default Header Size */
.ui.cards > .card > .content > .header:not(.ui),
.ui.card > .content > .header:not(.ui) {
  font-weight: @headerFontWeight;
  font-size: @headerFontSize;
  margin-top: @headerLineHeightOffset;
  line-height: @headerLineHeight;
}

.ui.cards > .card > .content > .meta + .description,
.ui.cards > .card > .content > .header + .description,
.ui.card > .content > .meta + .description,
.ui.card > .content > .header + .description  {
  margin-top: @descriptionDistance;
}

/*----------------
 Floated Content
-----------------*/

.ui.cards > .card  [class*="left floated"],
.ui.card [class*="left floated"] {
  float: left;
}
.ui.cards > .card [class*="right floated"],
.ui.card [class*="right floated"] {
  float: right;
}

/*--------------
     Aligned
---------------*/

.ui.cards > .card  [class*="left aligned"],
.ui.card [class*="left aligned"] {
  text-align: left;
}
.ui.cards > .card [class*="center aligned"],
.ui.card [class*="center aligned"] {
  text-align: center;
}
.ui.cards > .card [class*="right aligned"],
.ui.card [class*="right aligned"] {
  text-align: right;
}


/*--------------
  Content Image
---------------*/

.ui.cards > .card .content img,
.ui.card .content img {
  display: inline-block;
  vertical-align: @contentImageVerticalAlign;
  width: @contentImageWidth;
}
.ui.cards > .card img.avatar,
.ui.cards > .card .avatar img,
.ui.card img.avatar,
.ui.card .avatar img {
  width: @avatarSize;
  height: @avatarSize;
  border-radius: @avatarBorderRadius;
}


/*--------------
   Description
---------------*/

.ui.cards > .card > .content > .description,
.ui.card > .content > .description {
  clear: both;
  color: @descriptionColor;
}

/*--------------
    Paragraph
---------------*/

.ui.cards > .card > .content p,
.ui.card > .content p {
  margin: 0em 0em @paragraphDistance;
}
.ui.cards > .card > .content p:last-child,
.ui.card > .content p:last-child {
  margin-bottom: 0em;
}

/*--------------
      Meta
---------------*/

.ui.cards > .card .meta,
.ui.card .meta {
  font-size: @metaFontSize;
  color: @metaColor;
}
.ui.cards > .card .meta *,
.ui.card .meta * {
  margin-right: @metaSpacing;
}
.ui.cards > .card .meta :last-child,
.ui.card .meta :last-child {
  margin-right: 0em;
}

.ui.cards > .card .meta [class*="right floated"],
.ui.card .meta [class*="right floated"] {
  margin-right: 0em;
  margin-left: @metaSpacing;
}

/*--------------
      Links
---------------*/

/* Generic */
.ui.cards > .card > .content a:not(.ui),
.ui.card > .content a:not(.ui) {
  color: @contentLinkColor;
  transition: @contentLinkTransition;
}
.ui.cards > .card > .content a:not(.ui):hover,
.ui.card > .content a:not(.ui):hover {
  color: @contentLinkHoverColor;
}

/* Header */
.ui.cards > .card > .content > a.header,
.ui.card > .content > a.header {
  color: @headerLinkColor;
}
.ui.cards > .card > .content > a.header:hover,
.ui.card > .content > a.header:hover {
  color: @headerLinkHoverColor;
}

/* Meta */
.ui.cards > .card .meta > a:not(.ui),
.ui.card .meta > a:not(.ui) {
  color: @metaLinkColor;
}
.ui.cards > .card .meta > a:not(.ui):hover,
.ui.card .meta > a:not(.ui):hover {
  color: @metaLinkHoverColor;
}

/*--------------
     Buttons
---------------*/

.ui.cards > .card > .buttons,
.ui.card > .buttons,
.ui.cards > .card > .button,
.ui.card > .button {
  margin: @buttonMargin;
  width: @buttonWidth;
}

/*--------------
      Dimmer
---------------*/

.ui.cards > .card .dimmer,
.ui.card .dimmer {
  background-color: @dimmerColor;
  z-index: @dimmerZIndex;
}

/*--------------
     Labels
---------------*/

/*-----Star----- */

/* Icon */
.ui.cards > .card > .content .star.icon,
.ui.card > .content .star.icon {
  cursor: pointer;
  opacity: @actionOpacity;
  transition: @actionTransition;
}
.ui.cards > .card > .content .star.icon:hover,
.ui.card > .content .star.icon:hover {
  opacity: @actionHoverOpacity;
  color: @starColor;
}
.ui.cards > .card > .content .active.star.icon,
.ui.card > .content .active.star.icon {
  color: @starActiveColor;
}

/*-----Like----- */

/* Icon */
.ui.cards > .card > .content .like.icon,
.ui.card > .content .like.icon {
  cursor: pointer;
  opacity: @actionOpacity;
  transition: @actionTransition;
}
.ui.cards > .card > .content .like.icon:hover,
.ui.card > .content .like.icon:hover {
  opacity: @actionHoverOpacity;
  color: @likeColor;
}
.ui.cards > .card > .content .active.like.icon,
.ui.card > .content .active.like.icon {
  color: @likeActiveColor;
}

/*----------------
  Extra Content
-----------------*/

.ui.cards > .card > .extra,
.ui.card > .extra {
  max-width: 100%;
  min-height: 0em !important;
  flex-grow: 0;
  border-top: @extraDivider !important;
  position: @extraPosition;
  background: @extraBackground;
  width: @extraWidth;
  margin: @extraMargin;
  padding: @extraPadding;
  top: @extraTop;
  left: @extraLeft;
  color: @extraColor;
  box-shadow: @extraBoxShadow;
  transition: @extraTransition;
}
.ui.cards > .card > .extra a:not(.ui),
.ui.card > .extra a:not(.ui) {
  color: @extraLinkColor;
}
.ui.cards > .card > .extra a:not(.ui):hover,
.ui.card > .extra a:not(.ui):hover {
  color: @extraLinkHoverColor;
}


/*******************************
           Variations
*******************************/

/*-------------------
       Raised
--------------------*/

.ui.raised.cards > .card,
.ui.raised.card {
  box-shadow: @raisedShadow;
}
.ui.raised.cards a.card:hover,
.ui.link.cards .raised.card:hover,
a.ui.raised.card:hover,
.ui.link.raised.card:hover {
  box-shadow: @raisedShadowHover;
}

.ui.raised.cards > .card,
.ui.raised.card {
  box-shadow: @raisedShadow;
}
/*-------------------
       Centered
--------------------*/

.ui.centered.cards {
  justify-content: center;
}
.ui.centered.card {
  margin-left: auto;
  margin-right: auto;
}

/*-------------------
        Fluid
--------------------*/

.ui.fluid.card {
  width: 100%;
  max-width: 9999px;
}

/*-------------------
        Link
--------------------*/

.ui.cards a.card,
.ui.link.cards .card,
a.ui.card,
.ui.link.card {
  transform: none;
}


.ui.cards a.card:hover,
.ui.link.cards .card:hover,
a.ui.card:hover,
.ui.link.card:hover {
  cursor: pointer;
  z-index: @linkHoverZIndex;
  background: @linkHoverBackground;
  border: @linkHoverBorder;
  box-shadow: @linkHoverBoxShadow;
  transform: @linkHoverTransform;
}

/*-------------------
       Colors
--------------------*/

/* Red */
.ui.red.cards > .card,
.ui.cards > .red.card,
.ui.red.card {
  box-shadow:
    @borderShadow,
    0px @coloredShadowDistance 0px 0px @red,
    @shadowBoxShadow
  ;
}
.ui.red.cards > .card:hover,
.ui.cards > .red.card:hover,
.ui.red.card:hover {
  box-shadow:
    @borderShadow,
    0px @coloredShadowDistance 0px 0px @redHover,
    @shadowHoverBoxShadow
  ;
}

/* Orange */
.ui.orange.cards > .card,
.ui.cards > .orange.card,
.ui.orange.card {
  box-shadow:
    @borderShadow,
    0px @coloredShadowDistance 0px 0px @orange,
    @shadowBoxShadow
  ;
}
.ui.orange.cards > .card:hover,
.ui.cards > .orange.card:hover,
.ui.orange.card:hover {
  box-shadow:
    @borderShadow,
    0px @coloredShadowDistance 0px 0px @orangeHover,
    @shadowHoverBoxShadow
  ;
}

/* Yellow */
.ui.yellow.cards > .card,
.ui.cards > .yellow.card,
.ui.yellow.card {
  box-shadow:
    @borderShadow,
    0px @coloredShadowDistance 0px 0px @yellow,
    @shadowBoxShadow
  ;
}
.ui.yellow.cards > .card:hover,
.ui.cards > .yellow.card:hover,
.ui.yellow.card:hover {
  box-shadow:
    @borderShadow,
    0px @coloredShadowDistance 0px 0px @yellowHover,
    @shadowHoverBoxShadow
  ;
}

/* Olive */
.ui.olive.cards > .card,
.ui.cards > .olive.card,
.ui.olive.card {
  box-shadow:
    @borderShadow,
    0px @coloredShadowDistance 0px 0px @olive,
    @shadowBoxShadow
  ;
}
.ui.olive.cards > .card:hover,
.ui.cards > .olive.card:hover,
.ui.olive.card:hover {
  box-shadow:
    @borderShadow,
    0px @coloredShadowDistance 0px 0px @oliveHover,
    @shadowHoverBoxShadow
  ;
}

/* Green */
.ui.green.cards > .card,
.ui.cards > .green.card,
.ui.green.card {
  box-shadow:
    @borderShadow,
    0px @coloredShadowDistance 0px 0px @green,
    @shadowBoxShadow
  ;
}
.ui.green.cards > .card:hover,
.ui.cards > .green.card:hover,
.ui.green.card:hover {
  box-shadow:
    @borderShadow,
    0px @coloredShadowDistance 0px 0px @greenHover,
    @shadowHoverBoxShadow
  ;
}

/* Teal */
.ui.teal.cards > .card,
.ui.cards > .teal.card,
.ui.teal.card {
  box-shadow:
    @borderShadow,
    0px @coloredShadowDistance 0px 0px @teal,
    @shadowBoxShadow
  ;
}
.ui.teal.cards > .card:hover,
.ui.cards > .teal.card:hover,
.ui.teal.card:hover {
  box-shadow:
    @borderShadow,
    0px @coloredShadowDistance 0px 0px @tealHover,
    @shadowHoverBoxShadow
  ;
}

/* Blue */
.ui.blue.cards > .card,
.ui.cards > .blue.card,
.ui.blue.card {
  box-shadow:
    @borderShadow,
    0px @coloredShadowDistance 0px 0px @blue,
    @shadowBoxShadow
  ;
}
.ui.blue.cards > .card:hover,
.ui.cards > .blue.card:hover,
.ui.blue.card:hover {
  box-shadow:
    @borderShadow,
    0px @coloredShadowDistance 0px 0px @blueHover,
    @shadowHoverBoxShadow
  ;
}

/* Violet */
.ui.violet.cards > .card,
.ui.cards > .violet.card,
.ui.violet.card {
  box-shadow:
    @borderShadow,
    0px @coloredShadowDistance 0px 0px @violet,
    @shadowBoxShadow
  ;
}
.ui.violet.cards > .card:hover,
.ui.cards > .violet.card:hover,
.ui.violet.card:hover {
  box-shadow:
    @borderShadow,
    0px @coloredShadowDistance 0px 0px @violetHover,
    @shadowHoverBoxShadow
  ;
}

/* Purple */
.ui.purple.cards > .card,
.ui.cards > .purple.card,
.ui.purple.card {
  box-shadow:
    @borderShadow,
    0px @coloredShadowDistance 0px 0px @purple,
    @shadowBoxShadow
  ;
}
.ui.purple.cards > .card:hover,
.ui.cards > .purple.card:hover,
.ui.purple.card:hover {
  box-shadow:
    @borderShadow,
    0px @coloredShadowDistance 0px 0px @purpleHover,
    @shadowHoverBoxShadow
  ;
}

/* Pink */
.ui.pink.cards > .card,
.ui.cards > .pink.card,
.ui.pink.card {
  box-shadow:
    @borderShadow,
    0px @coloredShadowDistance 0px 0px @pink,
    @shadowBoxShadow
  ;
}
.ui.pink.cards > .card:hover,
.ui.cards > .pink.card:hover,
.ui.pink.card:hover {
  box-shadow:
    @borderShadow,
    0px @coloredShadowDistance 0px 0px @pinkHover,
    @shadowHoverBoxShadow
  ;
}

/* Brown */
.ui.brown.cards > .card,
.ui.cards > .brown.card,
.ui.brown.card {
  box-shadow:
    @borderShadow,
    0px @coloredShadowDistance 0px 0px @brown,
    @shadowBoxShadow
  ;
}
.ui.brown.cards > .card:hover,
.ui.cards > .brown.card:hover,
.ui.brown.card:hover {
  box-shadow:
    @borderShadow,
    0px @coloredShadowDistance 0px 0px @brownHover,
    @shadowHoverBoxShadow
  ;
}

/* Grey */
.ui.grey.cards > .card,
.ui.cards > .grey.card,
.ui.grey.card {
  box-shadow:
    @borderShadow,
    0px @coloredShadowDistance 0px 0px @grey,
    @shadowBoxShadow
  ;
}
.ui.grey.cards > .card:hover,
.ui.cards > .grey.card:hover,
.ui.grey.card:hover {
  box-shadow:
    @borderShadow,
    0px @coloredShadowDistance 0px 0px @greyHover,
    @shadowHoverBoxShadow
  ;
}

/* Black */
.ui.black.cards > .card,
.ui.cards > .black.card,
.ui.black.card {
  box-shadow:
    @borderShadow,
    0px @coloredShadowDistance 0px 0px @black,
    @shadowBoxShadow
  ;
}
.ui.black.cards > .card:hover,
.ui.cards > .black.card:hover,
.ui.black.card:hover {
  box-shadow:
    @borderShadow,
    0px @coloredShadowDistance 0px 0px @blackHover,
    @shadowHoverBoxShadow
  ;
}

/*--------------
   Card Count
---------------*/

.ui.one.cards {
  margin-left: @oneCardOffset;
  margin-right: @oneCardOffset;
}
.ui.one.cards > .card {
  width: @oneCard;
}

.ui.two.cards {
  margin-left: @twoCardOffset;
  margin-right: @twoCardOffset;
}
.ui.two.cards > .card {
  width: @twoCard;
  margin-left: @twoCardSpacing;
  margin-right: @twoCardSpacing;
}

.ui.three.cards {
  margin-left: @threeCardOffset;
  margin-right: @threeCardOffset;
}
.ui.three.cards > .card {
  width: @threeCard;
  margin-left: @threeCardSpacing;
  margin-right: @threeCardSpacing;
}

.ui.four.cards {
  margin-left: @fourCardOffset;
  margin-right: @fourCardOffset;
}
.ui.four.cards > .card {
  width: @fourCard;
  margin-left: @fourCardSpacing;
  margin-right: @fourCardSpacing;
}

.ui.five.cards {
  margin-left: @fiveCardOffset;
  margin-right: @fiveCardOffset;
}
.ui.five.cards > .card {
  width: @fiveCard;
  margin-left: @fiveCardSpacing;
  margin-right: @fiveCardSpacing;
}

.ui.six.cards {
  margin-left: @sixCardOffset;
  margin-right: @sixCardOffset;
}
.ui.six.cards > .card {
  width: @sixCard;
  margin-left: @sixCardSpacing;
  margin-right: @sixCardSpacing;
}

.ui.seven.cards {
  margin-left: @sevenCardOffset;
  margin-right: @sevenCardOffset;
}
.ui.seven.cards > .card {
  width: @sevenCard;
  margin-left: @sevenCardSpacing;
  margin-right: @sevenCardSpacing;
}

.ui.eight.cards {
  margin-left: @eightCardOffset;
  margin-right: @eightCardOffset;
}
.ui.eight.cards > .card {
  width: @eightCard;
  margin-left: @eightCardSpacing;
  margin-right: @eightCardSpacing;
  font-size: 11px;
}

.ui.nine.cards {
  margin-left: @nineCardOffset;
  margin-right: @nineCardOffset;
}
.ui.nine.cards > .card {
  width: @nineCard;
  margin-left: @nineCardSpacing;
  margin-right: @nineCardSpacing;
  font-size: 10px;
}

.ui.ten.cards {
  margin-left: @tenCardOffset;
  margin-right: @tenCardOffset;
}
.ui.ten.cards > .card {
  width: @tenCard;
  margin-left: @tenCardSpacing;
  margin-right: @tenCardSpacing;
}


/*-------------------
      Doubling
--------------------*/

/* Mobile Only */
@media only screen and (max-width : @largestMobileScreen) {
  .ui.two.doubling.cards {
    margin-left: @oneCardOffset;
    margin-right: @oneCardOffset;
  }
  .ui.two.doubling.cards .card {
    width: @oneCard;
    margin-left: @oneCardSpacing;
    margin-right: @oneCardSpacing;
  }
  .ui.three.doubling.cards {
    margin-left: @twoCardOffset;
    margin-right: @twoCardOffset;
  }
  .ui.three.doubling.cards .card {
    width: @twoCard;
    margin-left: @twoCardSpacing;
    margin-right: @twoCardSpacing;
  }
  .ui.four.doubling.cards {
    margin-left: @twoCardOffset;
    margin-right: @twoCardOffset;
  }
  .ui.four.doubling.cards .card {
    width: @twoCard;
    margin-left: @twoCardSpacing;
    margin-right: @twoCardSpacing;
  }
  .ui.five.doubling.cards {
    margin-left: @twoCardOffset;
    margin-right: @twoCardOffset;
  }
  .ui.five.doubling.cards .card {
    width: @twoCard;
    margin-left: @twoCardSpacing;
    margin-right: @twoCardSpacing;
  }
  .ui.six.doubling.cards {
    margin-left: @twoCardOffset;
    margin-right: @twoCardOffset;
  }
  .ui.six.doubling.cards .card {
    width: @twoCard;
    margin-left: @twoCardSpacing;
    margin-right: @twoCardSpacing;
  }
  .ui.seven.doubling.cards {
    margin-left: @threeCardOffset;
    margin-right: @threeCardOffset;
  }
  .ui.seven.doubling.cards .card {
    width: @threeCard;
    margin-left: @threeCardSpacing;
    margin-right: @threeCardSpacing;
  }
  .ui.eight.doubling.cards {
    margin-left: @threeCardOffset;
    margin-right: @threeCardOffset;
  }
  .ui.eight.doubling.cards .card {
    width: @threeCard;
    margin-left: @threeCardSpacing;
    margin-right: @threeCardSpacing;
  }
  .ui.nine.doubling.cards {
    margin-left: @threeCardOffset;
    margin-right: @threeCardOffset;
  }
  .ui.nine.doubling.cards .card {
    width: @threeCard;
    margin-left: @threeCardSpacing;
    margin-right: @threeCardSpacing;
  }
  .ui.ten.doubling.cards {
    margin-left: @threeCardOffset;
    margin-right: @threeCardOffset;
  }
  .ui.ten.doubling.cards .card {
    width: @threeCard;
    margin-left: @threeCardSpacing;
    margin-right: @threeCardSpacing;
  }
}

/* Tablet Only */
@media only screen and (min-width : @tabletBreakpoint) and (max-width : @largestTabletScreen) {
  .ui.two.doubling.cards {
    margin-left: @oneCardOffset;
    margin-right: @oneCardOffset;
  }
  .ui.two.doubling.cards .card {
    width: @oneCard;
    margin-left: @oneCardSpacing;
    margin-right: @oneCardSpacing;
  }
  .ui.three.doubling.cards {
    margin-left: @twoCardOffset;
    margin-right: @twoCardOffset;
  }
  .ui.three.doubling.cards .card {
    width: @twoCard;
    margin-left: @twoCardSpacing;
    margin-right: @twoCardSpacing;
  }
  .ui.four.doubling.cards {
    margin-left: @twoCardOffset;
    margin-right: @twoCardOffset;
  }
  .ui.four.doubling.cards .card {
    width: @twoCard;
    margin-left: @twoCardSpacing;
    margin-right: @twoCardSpacing;
  }
  .ui.five.doubling.cards {
    margin-left: @threeCardOffset;
    margin-right: @threeCardOffset;
  }
  .ui.five.doubling.cards .card {
    width: @threeCard;
    margin-left: @threeCardSpacing;
    margin-right: @threeCardSpacing;
  }
  .ui.six.doubling.cards {
    margin-left: @threeCardOffset;
    margin-right: @threeCardOffset;
  }
  .ui.six.doubling.cards .card {
    width: @threeCard;
    margin-left: @threeCardSpacing;
    margin-right: @threeCardSpacing;
  }
  .ui.eight.doubling.cards {
    margin-left: @threeCardOffset;
    margin-right: @threeCardOffset;
  }
  .ui.eight.doubling.cards .card {
    width: @threeCard;
    margin-left: @threeCardSpacing;
    margin-right: @threeCardSpacing;
  }
  .ui.eight.doubling.cards {
    margin-left: @fourCardOffset;
    margin-right: @fourCardOffset;
  }
  .ui.eight.doubling.cards .card {
    width: @fourCard;
    margin-left: @fourCardSpacing;
    margin-right: @fourCardSpacing;
  }
  .ui.nine.doubling.cards {
    margin-left: @fourCardOffset;
    margin-right: @fourCardOffset;
  }
  .ui.nine.doubling.cards .card {
    width: @fourCard;
    margin-left: @fourCardSpacing;
    margin-right: @fourCardSpacing;
  }
  .ui.ten.doubling.cards {
    margin-left: @fiveCardOffset;
    margin-right: @fiveCardOffset;
  }
  .ui.ten.doubling.cards .card {
    width: @fiveCard;
    margin-left: @fiveCardSpacing;
    margin-right: @fiveCardSpacing;
  }
}

/*-------------------
      Stackable
--------------------*/

@media only screen and (max-width : @largestMobileScreen) {
  .ui.stackable.cards {
    display: block !important;
  }
  .ui.stackable.cards .card:first-child {
    margin-top: 0em !important;
  }
  .ui.stackable.cards > .card {
    display: block !important;
    height: auto !important;
    margin: @stackableRowSpacing @stackableCardSpacing;
    padding: 0 !important;
    width: @stackableMargin !important;
  }
}


/*--------------
      Size
---------------*/

.ui.cards > .card {
  font-size: @medium;
}

.loadUIOverrides();
