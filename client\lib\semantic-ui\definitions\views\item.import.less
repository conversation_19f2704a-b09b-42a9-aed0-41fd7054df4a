/*
  DO NOT MODIFY - This file has been generated and will be regenerated
  Semantic UI v2.2.6
*/
/*!
 * # Semantic UI - Item
 * http://github.com/semantic-org/semantic-ui/
 *
 *
 * Released under the MIT license
 * http://opensource.org/licenses/MIT
 *
 */

/*******************************
            Theme
*******************************/

@type    : 'view';
@element : 'item';

@import (multiple) '../../theme.config.import.less';

/*******************************
            Standard
*******************************/

/*--------------
      Item
---------------*/

.ui.items > .item {
  display: @display;
  margin: @itemSpacing 0em;
  width: @width;
  min-height: @minHeight;
  background: @background;
  padding: @padding;

  border: @border;
  border-radius: @borderRadius;
  box-shadow: @boxShadow;
  transition: @transition;
  z-index: @zIndex;
}
.ui.items > .item a {
  cursor: pointer;
}

/*--------------
      Items
---------------*/

.ui.items {
  margin: @groupMargin;
}

.ui.items:first-child {
  margin-top: 0em !important;
}
.ui.items:last-child {
  margin-bottom: 0em !important;
}

/*--------------
      Item
---------------*/

.ui.items > .item:after {
  display: block;
  content: ' ';
  height: 0px;
  clear: both;
  overflow: hidden;
  visibility: hidden;
}
.ui.items > .item:first-child {
  margin-top: 0em;
}
.ui.items > .item:last-child {
  margin-bottom: 0em;
}



/*--------------
     Images
---------------*/

.ui.items > .item > .image {
  position: relative;
  flex: 0 0 auto;
  display: @imageDisplay;
  float: @imageFloat;
  margin: @imageMargin;
  padding: @imagePadding;
  max-height: @imageMaxHeight;
  align-self: @imageVerticalAlign;
}
.ui.items > .item > .image > img {
  display: block;
  width: 100%;
  height: auto;
  border-radius: @imageBorderRadius;
  border: @imageBorder;
}

.ui.items > .item > .image:only-child > img {
  border-radius: @borderRadius;
}


/*--------------
     Content
---------------*/

.ui.items > .item > .content {
  display: block;
  flex: 1 1 auto;
  background: @contentBackground;
  margin: @contentMargin;
  padding: @contentPadding;
  box-shadow: @contentBoxShadow;
  font-size: @contentFontSize;
  border: @contentBorder;
  border-radius: @contentBorderRadius;
}
.ui.items > .item > .content:after {
  display: block;
  content: ' ';
  height: 0px;
  clear: both;
  overflow: hidden;
  visibility: hidden;
}

.ui.items > .item > .image + .content {
  min-width: 0;
  width: @contentWidth;
  display: @contentDisplay;
  margin-left: @contentOffset;
  align-self: @contentVerticalAlign;
  padding-left: @contentImageDistance;
}

.ui.items > .item > .content > .header {
  display: inline-block;
  margin: @headerMargin;
  font-family: @headerFont;
  font-weight: @headerFontWeight;
  color: @headerColor;
}
/* Default Header Size */
.ui.items > .item > .content > .header:not(.ui) {
  font-size: @headerFontSize;
}

/*--------------
     Floated
---------------*/

.ui.items > .item [class*="left floated"] {
  float: left;
}
.ui.items > .item [class*="right floated"] {
  float: right;
}


/*--------------
  Content Image
---------------*/

.ui.items > .item .content img {
  align-self: @contentImageVerticalAlign;
  width: @contentImageWidth;
}
.ui.items > .item img.avatar,
.ui.items > .item .avatar img {
  width: @avatarSize;
  height: @avatarSize;
  border-radius: @avatarBorderRadius;
}


/*--------------
   Description
---------------*/

.ui.items > .item > .content > .description {
  margin-top: @descriptionDistance;
  max-width: @descriptionMaxWidth;
  font-size: @descriptionFontSize;
  line-height: @descriptionLineHeight;
  color: @descriptionColor;
}

/*--------------
    Paragraph
---------------*/

.ui.items > .item > .content p {
  margin: 0em 0em @paragraphDistance;
}
.ui.items > .item > .content p:last-child {
  margin-bottom: 0em;
}

/*--------------
      Meta
---------------*/

.ui.items > .item .meta {
  margin: @metaMargin;
  font-size: @metaFontSize;
  line-height: @metaLineHeight;
  color: @metaColor;
}
.ui.items > .item .meta * {
  margin-right: @metaSpacing;
}
.ui.items > .item .meta :last-child {
  margin-right: 0em;
}

.ui.items > .item .meta [class*="right floated"] {
  margin-right: 0em;
  margin-left: @metaSpacing;
}

/*--------------
      Links
---------------*/

/* Generic */
.ui.items > .item > .content a:not(.ui) {
  color: @contentLinkColor;
  transition: @contentLinkTransition;
}
.ui.items > .item > .content a:not(.ui):hover {
  color: @contentLinkHoverColor;
}

/* Header */
.ui.items > .item > .content > a.header {
  color: @headerLinkColor;
}
.ui.items > .item > .content > a.header:hover {
  color: @headerLinkHoverColor;
}

/* Meta */
.ui.items > .item .meta > a:not(.ui) {
  color: @metaLinkColor;
}
.ui.items > .item .meta > a:not(.ui):hover {
  color: @metaLinkHoverColor;
}



/*--------------
     Labels
---------------*/

/*-----Star----- */

/* Icon */
.ui.items > .item > .content .favorite.icon {
  cursor: pointer;
  opacity: @actionOpacity;
  transition: @actionTransition;
}
.ui.items > .item > .content .favorite.icon:hover {
  opacity: @actionHoverOpacity;
  color: @favoriteColor;
}
.ui.items > .item > .content .active.favorite.icon {
  color: @favoriteActiveColor;
}

/*-----Like----- */

/* Icon */
.ui.items > .item > .content .like.icon {
  cursor: pointer;
  opacity: @actionOpacity;
  transition: @actionTransition;
}
.ui.items > .item > .content .like.icon:hover {
  opacity: @actionHoverOpacity;
  color: @likeColor;
}
.ui.items > .item > .content .active.like.icon {
  color: @likeActiveColor;
}

/*----------------
  Extra Content
-----------------*/

.ui.items > .item .extra {
  display: @extraDisplay;
  position: @extraPosition;
  background: @extraBackground;
  margin: @extraMargin;
  width: @extraWidth;
  padding: @extraPadding;
  top: @extraTop;
  left: @extraLeft;
  color: @extraColor;
  box-shadow: @extraBoxShadow;
  transition: @extraTransition;
  border-top: @extraDivider;
}
.ui.items > .item .extra > * {
  margin: (@extraRowSpacing / 2) @extraHorizontalSpacing (@extraRowSpacing / 2) 0em;
}
.ui.items > .item .extra > [class*="right floated"] {
  margin: (@extraRowSpacing / 2) 0em (@extraRowSpacing / 2) @extraHorizontalSpacing;
}

.ui.items > .item .extra:after {
  display: block;
  content: ' ';
  height: 0px;
  clear: both;
  overflow: hidden;
  visibility: hidden;
}


/*******************************
          Responsive
*******************************/

/* Default Image Width */
.ui.items > .item > .image:not(.ui) {
  width: @imageWidth;
}


/* Tablet Only */
@media only screen and (min-width: @tabletBreakpoint) and (max-width: @largestTabletScreen) {
  .ui.items > .item {
    margin: @tabletItemSpacing 0em;
  }
  .ui.items > .item > .image:not(.ui) {
    width: @tabletImageWidth;
  }
  .ui.items > .item > .image + .content {
    display: block;
    padding: 0em 0em 0em @tabletContentImageDistance;
  }

}

/* Mobile Only */
@media only screen and (max-width: @largestMobileScreen) {
  .ui.items > .item {
    flex-direction: column;
    margin: @mobileItemSpacing 0em;
  }
  .ui.items > .item > .image {
    display: block;
    margin-left: auto;
    margin-right: auto;
  }
  .ui.items > .item > .image,
  .ui.items > .item > .image > img {
    max-width: 100% !important;
    width: @mobileImageWidth !important;
    max-height: @mobileImageMaxHeight !important;
  }
  .ui.items > .item > .image + .content {
    display: block;
    padding: @mobileContentImageDistance 0em 0em;
  }

}


/*******************************
           Variations
*******************************/


/*-------------------
       Aligned
--------------------*/

.ui.items > .item > .image + [class*="top aligned"].content {
  align-self: flex-start;
}
.ui.items > .item > .image + [class*="middle aligned"].content {
  align-self: center;
}
.ui.items > .item > .image + [class*="bottom aligned"].content {
  align-self: flex-end;
}


/*--------------
     Relaxed
---------------*/

.ui.relaxed.items > .item {
  margin: @relaxedItemSpacing 0em;
}
.ui[class*="very relaxed"].items > .item {
  margin: @veryRelaxedItemSpacing 0em;
}


/*-------------------
      Divided
--------------------*/

.ui.divided.items > .item {
  border-top: @dividedBorder;
  margin: @dividedMargin;
  padding: @dividedPadding;
}
.ui.divided.items > .item:first-child {
  border-top: none;
  margin-top: @dividedFirstLastMargin !important;
  padding-top: @dividedFirstLastPadding !important;
}
.ui.divided.items > .item:last-child {
  margin-bottom: @dividedFirstLastMargin !important;
  padding-bottom: @dividedFirstLastPadding !important;
}

/* Relaxed Divided */
.ui.relaxed.divided.items > .item {
  margin: 0em;
  padding: @relaxedItemSpacing 0em;
}
.ui[class*="very relaxed"].divided.items > .item {
  margin: 0em;
  padding: @veryRelaxedItemSpacing 0em;
}


/*-------------------
        Link
--------------------*/

.ui.items a.item:hover,
.ui.link.items > .item:hover {
  cursor: pointer;
}

.ui.items a.item:hover .content .header,
.ui.link.items > .item:hover .content .header {
  color: @headerLinkHoverColor;
}


/*--------------
      Size
---------------*/

.ui.items > .item {
  font-size: @medium;
}

.loadUIOverrides();
