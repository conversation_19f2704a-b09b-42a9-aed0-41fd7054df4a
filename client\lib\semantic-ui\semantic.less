/*
  DO NOT MODIFY - This file has been generated and will be regenerated
  Semantic UI v2.2.6
*/
/*

███████╗███████╗███╗   ███╗ █████╗ ███╗   ██╗████████╗██╗ ██████╗    ██╗   ██╗██╗
██╔════╝██╔════╝████╗ ████║██╔══██╗████╗  ██║╚══██╔══╝██║██╔════╝    ██║   ██║██║
███████╗█████╗  ██╔████╔██║███████║██╔██╗ ██║   ██║   ██║██║         ██║   ██║██║
╚════██║██╔══╝  ██║╚██╔╝██║██╔══██║██║╚██╗██║   ██║   ██║██║         ██║   ██║██║
███████║███████╗██║ ╚═╝ ██║██║  ██║██║ ╚████║   ██║   ██║╚██████╗    ╚██████╔╝██║
╚══════╝╚══════╝╚═╝     ╚═╝╚═╝  ╚═╝╚═╝  ╚═══╝   ╚═╝   ╚═╝ ╚═════╝     ╚═════╝ ╚═╝

  Import this file into your LESS project to use Semantic UI without build tools
*/

/* Global */
& { @import "definitions/globals/reset.import.less"; }
& { @import "definitions/globals/site.import.less"; }

/* Elements */
& { @import "definitions/elements/button.import.less"; }
& { @import "definitions/elements/container.import.less"; }
& { @import "definitions/elements/divider.import.less"; }
& { @import "definitions/elements/flag.import.less"; }
& { @import "definitions/elements/header.import.less"; }
& { @import "definitions/elements/icon.import.less"; }
& { @import "definitions/elements/image.import.less"; }
& { @import "definitions/elements/input.import.less"; }
& { @import "definitions/elements/label.import.less"; }
& { @import "definitions/elements/list.import.less"; }
& { @import "definitions/elements/loader.import.less"; }
& { @import "definitions/elements/rail.import.less"; }
& { @import "definitions/elements/reveal.import.less"; }
& { @import "definitions/elements/segment.import.less"; }
& { @import "definitions/elements/step.import.less"; }

/* Collections */
& { @import "definitions/collections/breadcrumb.import.less"; }
& { @import "definitions/collections/form.import.less"; }
& { @import "definitions/collections/grid.import.less"; }
& { @import "definitions/collections/menu.import.less"; }
& { @import "definitions/collections/message.import.less"; }
& { @import "definitions/collections/table.import.less"; }

/* Views */
& { @import "definitions/views/ad.import.less"; }
& { @import "definitions/views/card.import.less"; }
& { @import "definitions/views/comment.import.less"; }
& { @import "definitions/views/feed.import.less"; }
& { @import "definitions/views/item.import.less"; }
& { @import "definitions/views/statistic.import.less"; }

/* Modules */
& { @import "definitions/modules/accordion.import.less"; }
& { @import "definitions/modules/checkbox.import.less"; }
& { @import "definitions/modules/dimmer.import.less"; }
& { @import "definitions/modules/dropdown.import.less"; }
& { @import "definitions/modules/embed.import.less"; }
& { @import "definitions/modules/modal.import.less"; }
& { @import "definitions/modules/nag.import.less"; }
& { @import "definitions/modules/popup.import.less"; }
& { @import "definitions/modules/progress.import.less"; }
& { @import "definitions/modules/rating.import.less"; }
& { @import "definitions/modules/search.import.less"; }
& { @import "definitions/modules/shape.import.less"; }
& { @import "definitions/modules/sidebar.import.less"; }
& { @import "definitions/modules/sticky.import.less"; }
& { @import "definitions/modules/tab.import.less"; }
& { @import "definitions/modules/transition.import.less"; }
