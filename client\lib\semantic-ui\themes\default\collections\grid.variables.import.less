/*
  DO NOT MODIFY - This file has been generated and will be regenerated
  Semantic UI v2.2.6
*/
/*******************************
             Grid
*******************************/

/* Inherited From Site */

// @mobileBreakpoint
// @tabletBreakpoint
// @computerBreakpoint
// @largeMonitorBreakpoint
// @widescreenMonitorBreakpoint

/*******************************
            Grid
*******************************/

@minWidth: 320px;

@gutterWidth: 2rem;
@rowSpacing: 2rem;

@tableWidth: ~"calc(100% + "@gutterWidth~")";
@columnMaxImageWidth: 100%;

@consecutiveGridDistance: (@rowSpacing / 2);

/*******************************
           Variations
*******************************/

/*--------------
     Relaxed
---------------*/

@relaxedGutterWidth: 3rem;
@veryRelaxedGutterWidth: 5rem;

/*--------------
     Divided
---------------*/

@dividedBorder: -1px 0px 0px 0px @borderColor;
@verticallyDividedBorder: 0px -1px 0px 0px @borderColor;

@dividedInvertedBorder: -1px 0px 0px 0px @whiteBorderColor;
@verticallyDividedInvertedBorder: 0px -1px 0px 0px @whiteBorderColor;

/*--------------
    Celled
---------------*/

@celledMargin: 1em 0em;
@celledWidth: 1px;
@celledBorderColor: @solidBorderColor;

@celledPadding: 1em;
@celledRelaxedPadding: 1.5em;
@celledVeryRelaxedPadding: 2em;

@celledGridDivider: 0px 0px 0px @celledWidth @celledBorderColor;
@celledRowDivider: 0px (-@celledWidth) 0px 0px @celledBorderColor;
@celledColumnDivider: (-@celledWidth) 0px 0px 0px @celledBorderColor;


/*--------------
    Stackable
---------------*/

@stackableRowSpacing: @rowSpacing;
@stackableGutter: @gutterWidth;
@stackableMobileBorder: 1px solid @borderColor;
@stackableInvertedMobileBorder: 1px solid @whiteBorderColor;


/*******************************
             Legacy
*******************************/

/*--------------
     Page
---------------*/

/* Legacy (DO NOT USE)
 */
@mobileWidth: auto;
@mobileMargin: 0em;
@mobileGutter: 0em;

@tabletWidth: auto;
@tabletMargin: 0em;
@tabletGutter: 2em;

@computerWidth: auto;
@computerMargin: 0em;
@computerGutter: 3%;

@largeMonitorWidth: auto;
@largeMonitorMargin: 0em;
@largeMonitorGutter: 15%;

@widescreenMonitorWidth: auto;
@widescreenMargin: 0em;
@widescreenMonitorGutter: 23%;