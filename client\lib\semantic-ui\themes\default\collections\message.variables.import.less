/*
  DO NOT MODIFY - This file has been generated and will be regenerated
  Semantic UI v2.2.6
*/
/*******************************
            Message
*******************************/

// @textColor

/*-------------------
       Elements
--------------------*/

@verticalMargin: 1em;
@verticalPadding: 1em;
@horizontalPadding: 1.5em;
@padding: @verticalPadding @horizontalPadding;
@background: #F8F8F9;
@lineHeightOffset: ((@lineHeight - 1em) / 2);

@borderRadius: @defaultBorderRadius;
@borderWidth: 1px;
@borderShadow: 0px 0px 0px @borderWidth @strongBorderColor inset;
@shadowShadow: 0px 0px 0px 0px rgba(0, 0, 0, 0);
@boxShadow:
  @borderShadow,
  @shadowShadow
;

@transition:
  opacity @defaultDuration @defaultEasing,
  color @defaultDuration @defaultEasing,
  background @defaultDuration @defaultEasing,
  box-shadow @defaultDuration @defaultEasing
;

/* Header */
@headerFontSize: @relativeLarge;
@headerFontWeight: bold;
@headerDisplay: block;
@headerDistance: 0rem;
@headerMargin: -@headerLineHeightOffset 0em @headerDistance 0em;
@headerParagraphDistance: 0.25em;

/* Paragraph */
@messageTextOpacity: 0.85;
@messageParagraphMargin: 0.75em;

/* List */
@listOpacity: 0.85;
@listStylePosition: inside;
@listMargin: 0.5em;
@listItemIndent: 1em;
@listItemMargin: 0.3em;

/* Icon */
@iconDistance: 0.6em;

/* Close Icon */
@closeTopDistance: @verticalPadding - @lineHeightOffset;
@closeRightDistance: 0.5em;
@closeOpacity: 0.7;
@closeTransition: opacity @defaultDuration @defaultEasing;


/*-------------------
        Types
--------------------*/

/* Icon Message */
@iconSize: 3em;
@iconOpacity: 0.8;
@iconContentDistance: 0rem;
@iconVerticalAlign: middle;

/* Attached */
@attachedXOffset: -1px;
@attachedYOffset: -1px;
@attachedBoxShadow: 0em 0em 0em @borderWidth @borderColor inset;
@attachedBottomBoxShadow:
  @attachedBoxShadow,
  @subtleShadow
;

/* Floating */
@floatingBoxShadow:
  @borderShadow,
  @floatingShadow
;

/* Colors */
@redBoxShadow:
  0px 0px 0px @borderWidth @redBorderColor inset,
  @shadowShadow
;
@orangeBoxShadow:
  0px 0px 0px @borderWidth @orangeBorderColor inset,
  @shadowShadow
;
@yellowBoxShadow:
  0px 0px 0px @borderWidth @yellowBorderColor inset,
  @shadowShadow
;
@oliveBoxShadow:
  0px 0px 0px @borderWidth @oliveBorderColor inset,
  @shadowShadow
;
@greenBoxShadow:
  0px 0px 0px @borderWidth @greenBorderColor inset,
  @shadowShadow
;
@tealBoxShadow:
  0px 0px 0px @borderWidth @tealBorderColor inset,
  @shadowShadow
;
@blueBoxShadow:
  0px 0px 0px @borderWidth @blueBorderColor inset,
  @shadowShadow
;
@violetBoxShadow:
  0px 0px 0px @borderWidth @violetBorderColor inset,
  @shadowShadow
;
@purpleBoxShadow:
  0px 0px 0px @borderWidth @purpleBorderColor inset,
  @shadowShadow
;
@pinkBoxShadow:
  0px 0px 0px @borderWidth @pinkBorderColor inset,
  @shadowShadow
;
@brownBoxShadow:
  0px 0px 0px @borderWidth @brownBorderColor inset,
  @shadowShadow
;

/* Warning / Positive / Negative / Info */
@positiveBoxShadow:
  0px 0px 0px @borderWidth @positiveBorderColor inset,
  @shadowShadow
;
@negativeBoxShadow:
  0px 0px 0px @borderWidth @negativeBorderColor inset,
  @shadowShadow
;
@infoBoxShadow:
  0px 0px 0px @borderWidth @infoBorderColor inset,
  @shadowShadow
;
@warningBoxShadow:
  0px 0px 0px @borderWidth @warningBorderColor inset,
  @shadowShadow
;
@errorBoxShadow:
  0px 0px 0px @borderWidth @errorBorderColor inset,
  @shadowShadow
;
@successBoxShadow:
  0px 0px 0px @borderWidth @successBorderColor inset,
  @shadowShadow
;
