/*
  DO NOT MODIFY - This file has been generated and will be regenerated
  Semantic UI v2.2.6
*/
/*******************************
             Table
*******************************/

/*-------------------
       Element
--------------------*/

@verticalMargin: 1em;
@horizontalMargin: 0em;
@margin: @verticalMargin @horizontalMargin;
@borderCollapse: separate;
@borderSpacing: 0px;
@borderRadius: @defaultBorderRadius;
@transition:
  background @defaultDuration @defaultEasing,
  color @defaultDuration @defaultEasing
;
@background: @white;
@color: @textColor;
@borderWidth: 1px;
@border: @borderWidth solid @borderColor;
@boxShadow: none;
@textAlign: left;

/*--------------
     Parts
---------------*/

/* Table Row */
@rowBorder: 1px solid @internalBorderColor;

/* Table Cell */
@cellVerticalPadding: @relativeMini;
@cellHorizontalPadding: @relativeMini;
@cellVerticalAlign: inherit;
@cellTextAlign: inherit;
@cellBorder: 1px solid @internalBorderColor;

/* Table Header */
@headerBorder: 1px solid @internalBorderColor;
@headerDivider: none;
@headerBackground: @offWhite;
@headerAlign: inherit;
@headerVerticalAlign: inherit;
@headerColor: @textColor;
@headerVerticalPadding: @relativeSmall;
@headerHorizontalPadding: @cellHorizontalPadding;
@headerFontStyle: none;
@headerFontWeight: bold;
@headerTextTransform: none;
@headerBoxShadow: none;

/* Table Footer */
@footerBoxShadow: none;
@footerBorder: 1px solid @borderColor;
@footerDivider: none;
@footerBackground: @offWhite;
@footerAlign: inherit;
@footerVerticalAlign: middle;
@footerColor: @textColor;
@footerVerticalPadding: @cellVerticalPadding;
@footerHorizontalPadding: @cellHorizontalPadding;
@footerFontStyle: normal;
@footerFontWeight: normal;
@footerTextTransform: none;

/* Responsive Size */
@responsiveHeaderDisplay: block;
@responsiveFooterDisplay: block;
@responsiveRowVerticalPadding: 1em;
@responsiveRowBoxShadow: 0px -1px 0px 0px rgba(0, 0, 0, 0.1) inset !important;
@responsiveCellVerticalPadding: 0.25em;
@responsiveCellHorizontalPadding: 0.75em;
@responsiveCellBoxShadow: none !important;

/*-------------------
       Types
--------------------*/

/* Definition */
@definitionPageBackground: @white;

@definitionHeaderBackground: transparent;
@definitionHeaderColor: @unselectedTextColor;
@definitionHeaderFontWeight: normal;

@definitionFooterBackground: @definitionHeaderBackground;
@definitionFooterColor: @definitionHeaderColor;
@definitionFooterFontWeight: @definitionHeaderFontWeight;

@definitionColumnBackground: @subtleTransparentBlack;
@definitionColumnFontWeight: bold;
@definitionColumnColor: @selectedTextColor;
@definitionColumnFontSize: @relativeMedium;
@definitionColumnTextTransform: '';
@definitionColumnBoxShadow: '';
@definitionColumnTextAlign: '';
@definitionColumnHorizontalPadding: '';


/*--------------
    Couplings
---------------*/

@iconVerticalAlign: baseline;

/*--------------
     States
---------------*/

@stateMarkerWidth: 0px;

/* Positive */
@positiveColor: @positiveTextColor;
@positiveBoxShadow: @stateMarkerWidth 0px 0px @positiveBorderColor inset;
@positiveBackgroundHover: darken(@positiveBackgroundColor, 3);
@positiveColorHover: darken(@positiveColor, 3);

/* Negative */
@negativeColor: @negativeTextColor;
@negativeBoxShadow: @stateMarkerWidth 0px 0px @negativeBorderColor inset;
@negativeBackgroundHover: darken(@negativeBackgroundColor, 3);
@negativeColorHover: darken(@negativeColor, 3);

/* Error */
@errorColor: @errorTextColor;
@errorBoxShadow: @stateMarkerWidth 0px 0px @errorBorderColor inset;
@errorBackgroundHover: darken(@errorBackgroundColor, 3);
@errorColorHover: darken(@errorColor, 3);

/* Warning */
@warningColor: @warningTextColor;
@warningBoxShadow: @stateMarkerWidth 0px 0px @warningBorderColor inset;
@warningBackgroundHover: darken(@warningBackgroundColor, 3);
@warningColorHover: darken(@warningColor, 3);

/* Active */
@activeColor: @textColor;
@activeBackgroundColor: #E0E0E0;
@activeBoxShadow: @stateMarkerWidth 0px 0px @activeColor inset;

@activeBackgroundHover: #EFEFEF;
@activeColorHover: @selectedTextColor;

/*--------------
     Types
---------------*/

/* Attached */
@attachedTopOffset: 0px;
@attachedBottomOffset: 0px;
@attachedHorizontalOffset: -@borderWidth;
@attachedWidth: ~"calc(100% + "-@attachedHorizontalOffset * 2~")";
@attachedBoxShadow: none;
@attachedBorder: @borderWidth solid @solidBorderColor;
@attachedBottomBoxShadow:
  @boxShadow,
  @attachedBoxShadow
;

/* Striped */
@stripedBackground: rgba(0, 0, 50, 0.02);
@invertedStripedBackground: rgba(255, 255, 255, 0.05);

/* Selectable */
@selectableBackground: @transparentBlack;
@selectableTextColor: @selectedTextColor;
@selectableInvertedBackground: @transparentWhite;
@selectableInvertedTextColor: @invertedSelectedTextColor;

/* Sortable */
@sortableBackground: '';
@sortableColor: @textColor;

@sortableBorder: 1px solid @borderColor;
@sortableIconWidth: auto;
@sortableIconDistance: 0.5em;
@sortableIconOpacity: 0.8;
@sortableIconFont: 'Icons';
@sortableIconAscending: '\f0d8';
@sortableIconDescending: '\f0d7';
@sortableDisabledColor: @disabledTextColor;

@sortableHoverBackground: @transparentBlack;
@sortableHoverColor: @hoveredTextColor;

@sortableActiveBackground: @transparentBlack;
@sortableActiveColor: @selectedTextColor;

@sortableActiveHoverBackground: @transparentBlack;
@sortableActiveHoverColor: @selectedTextColor;

@sortableInvertedBorderColor: transparent;
@sortableInvertedHoverBackground: @transparentWhite @subtleGradient;
@sortableInvertedHoverColor: @invertedHoveredTextColor;
@sortableInvertedActiveBackground: @strongTransparentWhite @subtleGradient;
@sortableInvertedActiveColor: @invertedSelectedTextColor;

/* Colors */
@coloredBorderSize: 0.2em;
@coloredBorderRadius: 0em 0em @borderRadius @borderRadius;

/* Inverted */
@invertedBackground: #333333;
@invertedBorder: none;
@invertedCellBorderColor: @whiteBorderColor;
@invertedCellColor: @invertedTextColor;

@invertedHeaderBackground: @veryStrongTransparentBlack;
@invertedHeaderColor: @invertedTextColor;
@invertedHeaderBorderColor: @invertedCellBorderColor;

@invertedDefinitionColumnBackground: @subtleTransparentWhite;
@invertedDefinitionColumnColor: @invertedSelectedTextColor;
@invertedDefinitionColumnFontWeight: bold;

/* Basic */
@basicTableBackground: transparent;
@basicTableBorder: @borderWidth solid @borderColor;
@basicBoxShadow: none;

@basicTableHeaderBackground: transparent;
@basicTableCellBackground: transparent;
@basicTableHeaderDivider: none;
@basicTableCellBorder: 1px solid rgba(0, 0, 0, 0.1);
@basicTableCellPadding: '';
@basicTableStripedBackground: @transparentBlack;

/* Padded */
@paddedVerticalPadding: 1em;
@paddedHorizontalPadding: 1em;
@veryPaddedVerticalPadding: 1.5em;
@veryPaddedHorizontalPadding: 1.5em;

/* Compact */
@compactVerticalPadding: 0.5em;
@compactHorizontalPadding: 0.7em;
@veryCompactVerticalPadding: 0.4em;
@veryCompactHorizontalPadding: 0.6em;


/* Sizes */
@small: 0.9em;
@medium: 1em;
@large: 1.1em;
