/*
  DO NOT MODIFY - This file has been generated and will be regenerated
  Semantic UI v2.2.6
*/
/*******************************
            Container
*******************************/

/*-------------------
       Element
--------------------*/

/* Minimum Gutter is used to determine  the maximum container width for a given device */

@maxWidth: 100%;

/* Devices */
@mobileMinimumGutter: 0em;
@mobileWidth: auto;
@mobileGutter: 1em;

@tabletMinimumGutter: (@emSize  * 1);
@tabletWidth: @tabletBreakpoint - (@tabletMinimumGutter * 2) - @scrollbarWidth;
@tabletGutter: auto;

@computerMinimumGutter: (@emSize  * 1.5);
@computerWidth: @computerBreakpoint - (@computerMinimumGutter * 2) - @scrollbarWidth;
@computerGutter: auto;

@largeMonitorMinimumGutter: (@emSize  * 2);
@largeMonitorWidth: @largeMonitorBreakpoint - (@largeMonitorMinimumGutter * 2) - @scrollbarWidth;
@largeMonitorGutter: auto;

/* Coupling (Add Negative Margin to container size) */
@gridGutterWidth: 2rem;
@relaxedGridGutterWidth: 3rem;
@veryRelaxedGridGutterWidth: 5rem;

@mobileGridWidth: @mobileWidth;
@tabletGridWidth: ~"calc("@tabletWidth~" + "@gridGutterWidth~")";
@computerGridWidth: ~"calc("@computerWidth~" + "@gridGutterWidth~")";
@largeMonitorGridWidth: ~"calc("@largeMonitorWidth~" + "@gridGutterWidth~")";

@mobileRelaxedGridWidth: @mobileWidth;
@tabletRelaxedGridWidth: ~"calc("@tabletWidth~" + "@relaxedGridGutterWidth~")";
@computerRelaxedGridWidth: ~"calc("@computerWidth~" + "@relaxedGridGutterWidth~")";
@largeMonitorRelaxedGridWidth: ~"calc("@largeMonitorWidth~" + "@relaxedGridGutterWidth~")";

@mobileVeryRelaxedGridWidth: @mobileWidth;
@tabletVeryRelaxedGridWidth: ~"calc("@tabletWidth~" + "@veryRelaxedGridGutterWidth~")";
@computerVeryRelaxedGridWidth: ~"calc("@computerWidth~" + "@veryRelaxedGridGutterWidth~")";
@largeMonitorVeryRelaxedGridWidth: ~"calc("@largeMonitorWidth~" + "@veryRelaxedGridGutterWidth~")";

/*-------------------
       Types
--------------------*/

/* Text */
@textWidth: 700px;
@textFontFamily: @pageFont;
@textLineHeight: 1.5;
@textSize: @large;