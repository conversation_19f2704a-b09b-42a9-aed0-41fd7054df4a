/*
  DO NOT MODIFY - This file has been generated and will be regenerated
  Semantic UI v2.2.6
*/
/*******************************
            Divider
*******************************/

/*-------------------
       Element
--------------------*/

@margin: 1rem 0rem;

@highlightWidth: 1px;
@highlightColor: @whiteBorderColor;

@shadowWidth: 1px;
@shadowColor: @borderColor;

/* Text */
@letterSpacing: 0.05em;
@fontWeight: bold;
@color: @darkTextColor;
@textTransform: uppercase;

/*-------------------
       Coupling
--------------------*/

/* Icon */
@dividerIconSize: 1rem;
@dividerIconMargin: 0rem;


/*******************************
         Variations
*******************************/

/* Horizontal / Vertical */
@horizontalMargin: '';
@horizontalDividerMargin: 1em;
@horizontalRulerOffset: ~"calc(-50% - "(@horizontalDividerMargin)~")";

@verticalDividerMargin: 1rem;
@verticalDividerHeight: ~"calc(100% - "(@verticalDividerMargin)~")";

/* Inverted */
@invertedTextColor: @white;
@invertedHighlightColor: rgba(255, 255, 255, 0.15);
@invertedShadowColor: @borderColor;

/* Section */
@sectionMargin: 2rem;

/* Sizes */
@medium: 1rem;