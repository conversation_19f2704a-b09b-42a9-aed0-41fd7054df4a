/*
  DO NOT MODIFY - This file has been generated and will be regenerated
  Semantic UI v2.2.6
*/
/*******************************
            Header
*******************************/

/*-------------------
       Element
--------------------*/

@textTransform: none;
@fontFamily: @headerFont;
@fontWeight: @headerFontWeight;
@lineHeight: @headerLineHeight;
@lineHeightOffset: @headerLineHeightOffset;

@topMargin: @headerTopMargin;
@bottomMargin: @headerBottomMargin;
@margin: @topMargin 0em @bottomMargin;

@firstMargin: -@lineHeightOffset;
@lastMargin: 0em;
@horizontalPadding: 0em;
@verticalPadding: 0em;

/* Sub Heading */
@subHeadingDistance: @2px;
@subHeadingFontSize: @relativeTiny;
@subHeadingFontWeight: bold;
@subHeadingTextTransform: uppercase;
@subHeadingColor: '';

@smallSubHeadingSize: @relativeMini;
@largeSubHeadingSize: @relativeSmall;
@hugeSubHeadingSize: @relativeMedium;

/* Sub Header */
@subHeaderMargin: 0em;
@subHeaderLineHeight: 1.2em;
@subHeaderColor: @mutedTextColor;

/* Icon */
@iconOpacity: 1;
@iconSize: 1.5em;
@iconOffset: @lineHeightOffset;
@iconMargin: 0.75rem;
@iconAlignment: middle;

/* Image */
@imageWidth: 2.5em;
@imageHeight: auto;
@imageOffset: @lineHeightOffset;
@imageMargin: @iconMargin;
@imageAlignment: middle;

/* Label */
@labelSize: '';
@labelDistance: 0.5rem;
@labelVerticalAlign: middle;

/* Content */
@contentAlignment: top;
@contentIconAlignment: middle;
@contentImageAlignment: middle;

/* Paragraph after Header */
@nextParagraphDistance: 0em;

/*-------------------
      Variations
--------------------*/

/* Sizing */
@hugeFontSize   : unit(@h1, em);
@largeFontSize  : unit(@h2, em);
@mediumFontSize : unit(@h3, em);
@smallFontSize  : unit(@h4, em);
@tinyFontSize   : unit(@h5, em);

/* Sub Header */
@h1SubHeaderFontSize: @large;
@h2SubHeaderFontSize: @large;
@h3SubHeaderFontSize: @medium;
@h4SubHeaderFontSize: @medium;
@h5SubHeaderFontSize: @small;

@hugeSubHeaderFontSize  : @h1SubHeaderFontSize;
@largeSubHeaderFontSize : @h2SubHeaderFontSize;
@subHeaderFontSize      : @h3SubHeaderFontSize;
@smallSubHeaderFontSize : @h4SubHeaderFontSize;
@tinySubHeaderFontSize  : @h5SubHeaderFontSize;

/* Icon Header */
@iconHeaderSize: 3em;
@iconHeaderOpacity: 1;
@iconHeaderMargin: 0.5rem;
@circularHeaderIconSize: 2em;
@squareHeaderIconSize: 2em;

/* No Line Height Offset */
@iconHeaderTopMargin: 2rem;
@iconHeaderBottomMargin: @bottomMargin;
@iconHeaderFirstMargin: 0em;

/* Divided */
@dividedBorderWidth: 1px;
@dividedBorder: @dividedBorderWidth solid @borderColor;
@dividedColoredBorderWidth: 2px;

@dividedBorderPadding: @3px;
@dividedSubHeaderPadding: @3px;
@dividedIconPadding: 0em;

/* Block */
@blockBackground: @darkWhite;
@blockBoxShadow: none;
@blockBorderWidth: 1px;
@blockBorder: @blockBorderWidth solid @solidBorderColor;
@blockHorizontalPadding: @medium;
@blockVerticalPadding: @mini;
@blockBorderRadius: @defaultBorderRadius;

@tinyBlock: @tiny;
@smallBlock: @small;
@mediumBlock: @medium;
@largeBlock: @large;
@hugeBlock: @huge;

/* Attached */
@attachedOffset: -1px;
@attachedBoxShadow: none;
@attachedBorder: 1px solid @solidBorderColor;
@attachedVerticalPadding: @blockVerticalPadding;
@attachedHorizontalPadding: @blockHorizontalPadding;
@attachedBackground: @white;
@attachedBorderRadius: @blockBorderRadius;

@tinyAttachedSize: @relativeTiny;
@smallAttachedSize: @relativeSmall;
@mediumAttachedSize: @relativeMedium;
@largeAttachedSize: @relativeLarge;
@bigAttachedSize: @relativeBig;
@hugeAttachedSize: @relativeHuge;

/* Inverted */
@invertedColor: @white;
@invertedSubHeaderColor: @invertedMutedTextColor;
@invertedDividedBorderColor: @whiteBorderColor;
@invertedBlockBackground: @lightBlack @subtleGradient;
@invertedAttachedBackground: @invertedBlockBackground;

/* Floated */
@floatedMargin: 0.5em;
