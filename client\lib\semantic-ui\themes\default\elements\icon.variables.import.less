/*
  DO NOT MODIFY - This file has been generated and will be regenerated
  Semantic UI v2.2.6
*/
/*******************************
             Icon
*******************************/

/*-------------------
   Icon Variables
--------------------*/

@fontName: 'icons';
@fallbackSRC: url("@{fontPath}/@{fontName}.eot");
@src:
  url("@{fontPath}/@{fontName}.eot?#iefix") format('embedded-opentype'),
  url("@{fontPath}/@{fontName}.woff2") format('woff2'),
  url("@{fontPath}/@{fontName}.woff") format('woff'),
  url("@{fontPath}/@{fontName}.ttf") format('truetype'),
  url("@{fontPath}/@{fontName}.svg#icons") format('svg')
;

@opacity: 1;
@width: @iconWidth;
@height: 1em;
@distanceFromText: 0.25rem;


/* Variations */

@linkOpacity: 0.8;
@linkDuration: 0.3s;
@loadingDuration: 2s;

@circularSize: 2em;
@circularPadding: 0.5em 0.5em;
@circularShadow: 0em 0em 0em 0.1em rgba(0, 0, 0, 0.1) inset;

@borderedSize: 2em;
@borderedVerticalPadding: ((@borderedSize - @height) / 2);
@borderedHorizontalPadding: ((@borderedSize - @width) / 2);
@borderedShadow: 0em 0em 0em 0.1em rgba(0, 0, 0, 0.1) inset;

@cornerIconSize: 0.45em;
@cornerIconStroke: 1px;
@cornerIconShadow:
  -@cornerIconStroke -@cornerIconStroke 0 @white,
   @cornerIconStroke -@cornerIconStroke 0 @white,
  -@cornerIconStroke  @cornerIconStroke 0 @white,
   @cornerIconStroke  @cornerIconStroke 0 @white
;
@cornerIconInvertedShadow:
  -@cornerIconStroke -@cornerIconStroke 0 @black,
   @cornerIconStroke -@cornerIconStroke 0 @black,
  -@cornerIconStroke  @cornerIconStroke 0 @black,
   @cornerIconStroke  @cornerIconStroke 0 @black
;

@mini: 0.4em;
@tiny: 0.5em;
@small: 0.75em;
@medium: 1em;
@large: 1.5em;
@big: 2em;
@huge: 4em;
@massive: 8em;
