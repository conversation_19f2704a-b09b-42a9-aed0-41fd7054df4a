/*
  DO NOT MODIFY - This file has been generated and will be regenerated
  Semantic UI v2.2.6
*/
/*******************************
            Input
*******************************/

/*-------------------
      Element
--------------------*/

@inputFont: @pageFont;
@verticalPadding: @inputVerticalPadding;
@horizontalPadding: @inputHorizontalPadding;

@lineHeight: @inputLineHeight;
@lineHeightOffset: ((@lineHeight - 1em) / 2);

@padding: (@verticalPadding - @lineHeightOffset) @horizontalPadding;

@textAlign: left;
@background: @inputBackground;
@borderWidth: 1px;
@border: @borderWidth solid @borderColor;
@boxShadow: none;

@borderRadius: @defaultBorderRadius;
@transition:
  box-shadow @defaultDuration @defaultEasing,
  border-color @defaultDuration @defaultEasing
;

/*-------------------
        Types
--------------------*/

/* Icon Input */
@iconWidth: (@verticalPadding * 2) + @glyphWidth;
@iconOpacity: 0.5;
@iconFocusOpacity: 1;
@iconOffset: -0.5em;

@iconDistance: 0em;
@iconMargin: @iconWidth + @iconDistance;
@iconTransition: opacity 0.3s @defaultEasing;

@transparentIconWidth: @glyphWidth;
@transparentIconMargin: 2em;

/* Circular Icon Input */
@circularIconVerticalOffset: 0.35em;
@circularIconHorizontalOffset: 0.5em;

/* Labeled Input */
@labelCornerTop: @borderWidth;
@labelCornerRight: @borderWidth;
@labelCornerSize: @relative9px;
@labelSize: 1em;
@labelVerticalPadding: (@verticalPadding - @lineHeightOffset);

@labeledMargin: 2.5em;
@labeledIconInputMargin: 3.25em;
@labeledIconMargin: 1.25em;

/*-------------------
        States
--------------------*/

/* Placeholder */
@placeholderColor: @inputPlaceholderColor;
@placeholderFocusColor: @inputPlaceholderFocusColor;

/* Down */
@downBorderColor: rgba(0, 0, 0, 0.3);
@downBackground: #FAFAFA;
@downColor: @textColor;
@downBoxShadow: none;

/* Focus */
@focusBorderColor: @focusedFormBorderColor;
@focusBackground: @background;
@focusColor: @hoveredTextColor;
@focusBoxShadow: none;

/* Error */
@errorBackground: @negativeBackgroundColor;
@errorColor: @negativeTextColor;
@errorBorder: @negativeBorderColor;
@errorBoxShadow: none;

@placeholderErrorColor: lighten(@errorColor, 40);
@placeholderErrorFocusColor: lighten(@errorColor, 30);

/* Loader */
@invertedLoaderFillColor: rgba(0, 0, 0, 0.15);

/*-------------------
      Variations
--------------------*/

/* Inverted */
@transparentInvertedPlaceholderColor: @invertedUnselectedTextColor;
@transparentInvertedColor: @white;

