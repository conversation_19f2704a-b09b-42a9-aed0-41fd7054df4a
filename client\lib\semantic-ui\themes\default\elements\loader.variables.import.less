/*
  DO NOT MODIFY - This file has been generated and will be regenerated
  Semantic UI v2.2.6
*/
/*******************************
             Loader
*******************************/

/* Some global loader styles defined in site.variables */
// @loaderSpeed
// @loaderLineWidth
// @loaderFillColor
// @loaderLineColor
// @invertedLoaderFillColor
// @invertedLoaderLineColor

/*-------------------
      Standard
--------------------*/

@loaderTopOffset: 50%;
@loaderLeftOffset: 50%;

@shapeBorderColor: @loaderLineColor transparent transparent;
@invertedShapeBorderColor: @invertedLoaderLineColor transparent transparent;

/*-------------------
        Types
--------------------*/

/* Text */
@textDistance: @relativeMini;
@loaderTextColor: @textColor;
@invertedLoaderTextColor: @invertedTextColor;

/*-------------------
        States
--------------------*/

@indeterminateDirection: reverse;
@indeterminateSpeed: (2 * @loaderSpeed);

/*-------------------
      Variations
--------------------*/

@inlineVerticalAlign: middle;
@inlineMargin: 0em;

/* Exact Sizes (Avoids Rounding Errors) */
@mini    : @14px;
@tiny    : @16px;
@small   : @24px;
@medium  : @32px;
@large   : @48px;
@big     : @52px;
@huge    : @58px;
@massive : @64px;

@miniOffset: 0em 0em 0em -(@mini / 2);
@tinyOffset: 0em 0em 0em -(@tiny / 2);
@smallOffset: 0em 0em 0em -(@small / 2);
@mediumOffset: 0em 0em 0em -(@medium / 2);
@largeOffset: 0em 0em 0em -(@large / 2);
@bigOffset: 0em 0em 0em -(@big / 2);
@hugeOffset: 0em 0em 0em -(@huge / 2);
@massiveOffset: 0em 0em 0em -(@massive / 2);

@tinyFontSize: @relativeTiny;
@miniFontSize: @relativeMini;
@smallFontSize: @relativeSmall;
@mediumFontSize: @relativeMedium;
@largeFontSize: @relativeLarge;
@bigFontSize: @relativeBig;
@hugeFontSize: @relativeHuge;
@massiveFontSize: @relativeMassive;
