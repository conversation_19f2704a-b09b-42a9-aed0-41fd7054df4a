/*
  DO NOT MODIFY - This file has been generated and will be regenerated
  Semantic UI v2.2.6
*/
/*******************************
            Rail
*******************************/

/*-------------------
       Element
--------------------*/

@width: 300px;
@height: 100%;

@distance: 4rem;
@splitDistance: (@distance / 2);

/*-------------------
      Variations
--------------------*/

/* Close */
@closeDistance: 2em;
@veryCloseDistance: 1em;

@splitCloseDistance: (@closeDistance / 2);
@splitVeryCloseDistance: (@veryCloseDistance / 2);

@closeWidth: ~"calc("@width~" + "@splitCloseDistance~")";
@veryCloseWidth: ~"calc("@width~" + "@splitVeryCloseDistance~")";

/* Dividing */
@dividingBorder: 1px solid @borderColor;
@dividingDistance: 5rem;
@splitDividingDistance: (@dividingDistance / 2);
@dividingWidth: @width + @splitDividingDistance;

