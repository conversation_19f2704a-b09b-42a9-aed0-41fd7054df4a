/*
  DO NOT MODIFY - This file has been generated and will be regenerated
  Semantic UI v2.2.6
*/
/*******************************
           Accordion
*******************************/

@boxShadow: none;

/* Title */
@titleFont: @headerFont;
@titlePadding: 0.5em 0em;
@titleFontSize: 1em;
@titleColor: @textColor;

/* Icon */
@iconOpacity: 1;
@iconFontSize: 1em;
@iconFloat: none;
@iconWidth: 1.25em;
@iconHeight: 1em;
@iconDisplay: inline-block;
@iconMargin: 0em 0.25rem 0em 0rem;
@iconPadding: 0em;
@iconTransition:
  transform @defaultDuration @defaultEasing,
  opacity @defaultDuration @defaultEasing
;
@iconVerticalAlign: baseline;
@iconTransform: none;

/* Child Accordion */
@childAccordionMargin: 1em 0em 0em;
@childAccordionPadding: 0em;

/* Content */
@contentMargin: '';
@contentPadding: 0.5em 0em 1em;

/*-------------------
       Coupling
--------------------*/

@menuTitlePadding: 0em;
@menuIconFloat: right;
@menuIconMargin: @lineHeightOffset 0em 0em 1em;
@menuIconTransform: rotate(180deg);


/*-------------------
       States
--------------------*/

@activeIconTransform: rotate(90deg);

/*-------------------
      Variations
--------------------*/

/* Styled */
@styledWidth: 600px;
@styledBackground: @white;
@styledBorderRadius: @defaultBorderRadius;
@styledBoxShadow:
  @subtleShadow,
  0px 0px 0px 1px @borderColor
;

/* Content */
@styledContentMargin: 0em;
@styledContentPadding: 0.5em 1em 1.5em;

/* Child Content */
@styledChildContentMargin: 0em;
@styledChildContentPadding: @styledContentPadding;

/* Styled Title */
@styledTitleMargin: 0em;
@styledTitlePadding: 0.75em 1em;
@styledTitleFontWeight: bold;
@styledTitleColor: @unselectedTextColor;
@styledTitleTransition: background-color @defaultDuration @defaultEasing;
@styledTitleBorder: 1px solid @borderColor;
@styledTitleTransition:
  background @defaultDuration @defaultEasing,
  color @defaultDuration @defaultEasing
;

/* Styled Title States */
@styledTitleHoverBackground: transparent;
@styledTitleHoverColor: @textColor;
@styledActiveTitleBackground: transparent;
@styledActiveTitleColor: @selectedTextColor;

/* Styled Child Title States */
@styledHoverChildTitleBackground: @styledTitleHoverBackground;
@styledHoverChildTitleColor: @styledTitleHoverColor;
@styledActiveChildTitleBackground: @styledActiveTitleBackground;
@styledActiveChildTitleColor: @styledActiveTitleColor;

/* Inverted */
@invertedTitleColor: @invertedTextColor;

