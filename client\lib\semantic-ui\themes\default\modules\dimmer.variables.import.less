/*
  DO NOT MODIFY - This file has been generated and will be regenerated
  Semantic UI v2.2.6
*/
/*******************************
            Dimmer
*******************************/

@dimmablePosition: relative;
@dimmerPosition: absolute;

@backgroundColor: rgba(0, 0, 0 , 0.85);
@lineHeight: 1;
@perspective: 2000px;

@duration: 0.5s;
@transition:
  background-color @duration linear
;
@zIndex: 1000;
@textAlign: center;
@verticalAlign: middle;
@textColor: @white;
@overflow: hidden;

@blurredStartFilter: ~"blur(0px) grayscale(0)";
@blurredEndFilter: ~"blur(5px) grayscale(0.7)";
@blurredTransition: 800ms filter @defaultEasing;

@blurredBackgroundColor: rgba(0, 0, 0, 0.6);
@blurredInvertedBackgroundColor: rgba(255, 255, 255, 0.6);

/* Hidden (Default) */
@hiddenOpacity: 0;

/* Content */
@contentDisplay: table;
@contentChildDisplay: table-cell;

/* Visible */
@visibleOpacity: 1;

/*-------------------
        Types
--------------------*/

/* Page Dimmer*/
@transformStyle: '';
@pageDimmerPosition: fixed;


/*-------------------
      Variations
--------------------*/

/* Inverted */
@invertedBackgroundColor: rgba(255, 255, 255, 0.85);
@invertedTextColor: @textColor;

/* Simple */
@simpleZIndex: 1;
@simpleStartBackgroundColor: rgba(0, 0, 0, 0);
@simpleEndBackgroundColor: @backgroundColor;
@simpleInvertedStartBackgroundColor: rgba(255, 255, 255, 0);
@simpleInvertedEndBackgroundColor: @invertedBackgroundColor;