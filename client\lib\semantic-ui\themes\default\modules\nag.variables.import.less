/*
  DO NOT MODIFY - This file has been generated and will be regenerated
  Semantic UI v2.2.6
*/
/*******************************
             Nag
*******************************/

/*--------------
   Collection
---------------*/

@position: relative;
@width: 100%;
@zIndex: 999;
@margin: 0em;

@background: #555555;
@opacity: 0.95;
@minHeight: 0em;
@padding: 0.75em 1em;
@lineHeight: 1em;
@boxShadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.2);

@fontSize: 1rem;
@textAlign: center;
@color: @textColor;

@transition: 0.2s background ease;


/*--------------
    Elements
---------------*/

/* Title */
@titleColor: @white;
@titleMargin: 0em 0.5em;

@closeSize: 1em;
@closeMargin: (-@closeSize / 2) 0em 0em;
@closeTop: 50%;
@closeRight: 1em;
@closeColor: @white;
@closeTransition: opacity 0.2s ease;
@closeOpacity: 0.4;


/*--------------
      States
---------------*/

/* Hover */
@nagHoverBackground: @background;
@nagHoverOpacity: 1;

@closeHoverOpacity: 1;

/*--------------
   Variations
---------------*/

/* Top / Bottom */
@top: 0em;
@bottom: 0em;
@borderRadius: @defaultBorderRadius;
@topBorderRadius: 0em 0em @borderRadius @borderRadius;
@bottomBorderRadius: @borderRadius @borderRadius 0em 0em;

/* Inverted */
@invertedBackground: @darkWhite;

/*--------------
      Plural
---------------*/

@groupedBorderRadius: 0em;

