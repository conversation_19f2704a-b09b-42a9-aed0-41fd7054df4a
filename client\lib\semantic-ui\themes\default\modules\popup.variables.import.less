/*
  DO NOT MODIFY - This file has been generated and will be regenerated
  Semantic UI v2.2.6
*/
/*******************************
             Popup
*******************************/

/*-------------------
       Element
--------------------*/

@zIndex: 1900;
@background: @white;

@maxWidth: 250px;
@borderColor: @solidBorderColor;
@borderWidth: 1px;
@boxShadow: @floatingShadow;
@color: @textColor;

@verticalPadding: 0.833em;
@horizontalPadding: 1em;
@fontWeight: normal;
@fontStyle: normal;
@borderRadius: @defaultBorderRadius;

/*-------------------
       Parts
--------------------*/

/* Placement */
@arrowSize: @relative10px;
@arrowWidth: 1em;
@arrowDistanceFromEdge: 1em;
@boxArrowOffset: 0em;
@popupDistanceAway: @arrowSize;


/* Header */
@headerFontFamily: @headerFont;
@headerFontSize: @relativeLarge;
@headerDistance: @relative7px;
@headerLineHeight: 1.2;

/* Content Border */
@border: @borderWidth solid @borderColor;

/* Arrow */
@arrowBackground: @white;
@arrowZIndex: 2;
@arrowJitter: 0.05em;
@arrowOffset: -(@arrowSize / 2) + @arrowJitter;

@arrowStroke: @borderWidth;
@arrowColor: darken(@borderColor, 10);

/* Arrow color by position */
@arrowTopBackground: @arrowBackground;
@arrowCenterBackground: @arrowBackground;
@arrowBottomBackground: @arrowBackground;

@arrowBoxShadow: @arrowStroke @arrowStroke 0px 0px @arrowColor;
@leftArrowBoxShadow: @arrowStroke -@arrowStroke 0px 0px @arrowColor;
@rightArrowBoxShadow: -@arrowStroke @arrowStroke 0px 0px @arrowColor;
@bottomArrowBoxShadow: -@arrowStroke -@arrowStroke 0px 0px @arrowColor;

/*-------------------
       Types
--------------------*/

/* Tooltip */
@tooltipBackground: @background;
@tooltipBorderRadius: @borderRadius;
@tooltipPadding: @verticalPadding @horizontalPadding;
@tooltipFontWeight: @fontWeight;
@tooltipFontStyle: @fontStyle;
@tooltipColor: @color;
@tooltipBorder: @border;
@tooltipBoxShadow: @boxShadow;
@tooltipMaxWidth: none;
@tooltipFontSize: @medium;
@tooltipLineHeight: @lineHeight;
@tooltipDistanceAway: @relative7px;
@tooltipZIndex: 1;
@tooltipDuration: @defaultDuration;
@tooltipEasing: @defaultEasing;

/* Inverted */
@tooltipInvertedBackground: @invertedBackground;
@tooltipInvertedColor: @invertedColor;
@tooltipInvertedBorder: @invertedBorder;
@tooltipInvertedBoxShadow: @invertedBoxShadow;
@tooltipInvertedHeaderBackground: @invertedHeaderBackground;
@tooltipInvertedHeaderColor: @invertedHeaderColor;

/* Arrow */
@tooltipArrowVerticalOffset: -@2px;
@tooltipArrowHorizontalOffset: -@1px;
@tooltipArrowBoxShadow: @arrowBoxShadow;
@tooltipArrowBackground: @arrowBackground;
@tooltipArrowTopBackground: @arrowTopBackground;
@tooltipArrowCenterBackground: @arrowCenterBackground;
@tooltipArrowBottomBackground: @arrowBottomBackground;

/*-------------------
       Coupling
--------------------*/

/* Grid Inside Popup */
@nestedGridMargin: -0.7rem -0.875rem; /* (padding * @medium) */
@nestedGridWidth: ~"calc(100% + 1.75rem)";

/*-------------------
       States
--------------------*/

@loadingZIndex: -1;

/*-------------------
       Variations
--------------------*/

/* Wide */
@wideWidth: 350px;
@veryWideWidth: 550px;

/* Inverted */
@invertedBackground: @black;
@invertedColor: @white;
@invertedBorder: none;
@invertedBoxShadow: none;

@invertedHeaderBackground: none;
@invertedHeaderColor: @white;
@invertedArrowColor: @invertedBackground;

/* Arrow color by position */
@invertedArrowTopBackground: @invertedBackground;
@invertedArrowCenterBackground: @invertedBackground;
@invertedArrowBottomBackground: @invertedBackground;
