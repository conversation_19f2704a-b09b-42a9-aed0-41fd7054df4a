/*
  DO NOT MODIFY - This file has been generated and will be regenerated
  Semantic UI v2.2.6
*/
/*******************************
             Rating
*******************************/

@margin: 0em @relativeMini;
@whiteSpace: nowrap;
@verticalAlign: baseline;

@iconCursor: pointer;
@iconWidth: 1.25em;
@iconHeight: auto;
@iconTransition:
  opacity @defaultDuration @defaultEasing,
  background @defaultDuration @defaultEasing,
  text-shadow @defaultDuration @defaultEasing,
  color @defaultDuration @defaultEasing
;


/*-------------------
        Types
--------------------*/

/* Standard */
@inactiveBackground: transparent;
@inactiveColor: rgba(0, 0, 0, 0.15);

@selectedBackground: @inactiveBackground;
@selectedColor: @textColor;

@activeBackground: @inactiveBackground;
@activeColor: @darkTextColor;

/* Star */
@starIconWidth: @iconWidth;
@starIconHeight: @iconHeight;
@starShadowWidth: 1px;

@starInactiveBackground: @inactiveBackground;
@starInactiveColor: @inactiveColor;
@starInactiveTextShadow: none;

@starActiveBackground: @activeBackground;
@starActiveColor: #FFE623;
@starActiveShadowColor: #DDC507;
@starActiveTextShadow:
  0px -@starShadowWidth 0px @starActiveShadowColor,
  -@starShadowWidth 0px 0px @starActiveShadowColor,
  0px @starShadowWidth 0px @starActiveShadowColor,
  @starShadowWidth 0px 0px @starActiveShadowColor
;

@starSelectedBackground: @selectedBackground;
@starSelectedColor: #FFCC00;
@starSelectedShadowColor: #E6A200;
@starSelectedTextShadow:
  0px -@starShadowWidth 0px @starSelectedShadowColor,
  -@starShadowWidth 0px 0px @starSelectedShadowColor,
  0px @starShadowWidth 0px @starSelectedShadowColor,
  @starShadowWidth 0px 0px @starSelectedShadowColor
;

/* Heart */
@heartIconWidth: 1.4em;
@heartIconHeight: @iconHeight;
@heartShadowWidth: 1px;

@heartInactiveBackground: @inactiveBackground;
@heartInactiveColor: @inactiveColor;
@heartInactiveTextShadow: none;

@heartActiveBackground: @activeBackground;
@heartActiveColor: #FF6D75;
@heartActiveShadowColor: #CD0707;
@heartActiveTextShadow:
  0px -@heartShadowWidth 0px @heartActiveShadowColor,
  -@heartShadowWidth 0px 0px @heartActiveShadowColor,
  0px @heartShadowWidth 0px @heartActiveShadowColor,
  @heartShadowWidth 0px 0px @heartActiveShadowColor
;

@heartSelectedBackground: @selectedBackground;
@heartSelectedColor: #FF3000;
@heartSelectedShadowColor: #AA0101;
@heartSelectedTextShadow:
  0px -@heartShadowWidth 0px @heartSelectedShadowColor,
  -@heartShadowWidth 0px 0px @heartSelectedShadowColor,
  0px @heartShadowWidth 0px @heartSelectedShadowColor,
  @heartShadowWidth 0px 0px @heartSelectedShadowColor
;

/*-------------------
        States
--------------------*/

@interactiveActiveIconOpacity: 1;
@interactiveSelectedIconOpacity: 1;

/*-------------------
      Variations
--------------------*/

@massive: 2rem;