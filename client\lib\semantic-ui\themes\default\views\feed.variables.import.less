/*
  DO NOT MODIFY - This file has been generated and will be regenerated
  Semantic UI v2.2.6
*/
/*******************************
             Feed
*******************************/

/*-------------------
        Feed
--------------------*/

@margin: 1em 0em;

/*-------------------
      Elements
--------------------*/

/* Event */
@eventWidth: 100%;
@eventPadding: @3px 0em;
@eventMargin: 0em;
@eventBackground: none;
@eventDivider: none;

/* Event Label */
@labelWidth: 2.5em;
@labelHeight: auto;
@labelAlignSelf: stretch;
@labelTextAlign: left;

/* Icon Label */
@iconLabelOpacity: 1;
@iconLabelWidth: 100%;
@iconLabelSize: 1.5em;
@iconLabelPadding: 0.25em;
@iconLabelBackground: none;
@iconLabelBorderRadius: none;
@iconLabelBorder: none;
@iconLabelColor: rgba(0, 0, 0, 0.6);

/* Image Label */
@imageLabelWidth: 100%;
@imageLabelHeight: auto;
@imageLabelBorderRadius: @circularRadius;

/* Content w/ Label */
@labeledContentMargin: 0.5em 0em @relative5px @relativeLarge;
@lastLabeledContentPadding: 0em;

/* Content */
@contentAlignSelf: stretch;
@contentTextAlign: left;
@contentWordWrap: break-word;

/* Date */
@dateMargin: -0.5rem 0em 0em;
@datePadding: 0em;
@dateColor: @lightTextColor;
@dateFontSize: @relativeMedium;
@dateFontWeight: normal;
@dateFontStyle: normal;

/* Summary */
@summaryMargin: 0em;
@summaryFontSize: @relativeMedium;
@summaryFontWeight: bold;
@summaryColor: @textColor;

/* Summary Image */
@summaryImageWidth: auto;
@summaryImageHeight: 10em;
@summaryImageMargin: -0.25em 0.25em 0em 0em;
@summaryImageVerticalAlign: middle;
@summaryImageBorderRadius: 0.25em;

/* Summary Date */
@summaryDateDisplay: inline-block;
@summaryDateFloat: none;
@summaryDateMargin: 0em 0em 0em 0.5em;
@summaryDatePadding: 0em;
@summaryDateFontSize: @relativeTiny;
@summaryDateFontWeight: @dateFontWeight;
@summaryDateFontStyle: @dateFontStyle;
@summaryDateColor: @dateColor;

/* User */
@userFontWeight: bold;
@userDistance: 0em;
@userImageWidth: @summaryImageWidth;
@userImageHeight: @summaryImageHeight;
@userImageMargin: @summaryImageMargin;
@userImageVerticalAlign: @summaryImageVerticalAlign;

/* Extra Summary Data */
@extraMargin: 0.5em 0em 0em;
@extraBackground: none;
@extraPadding: 0em;
@extraColor: @textColor;

/* Extra Images */
@extraImageMargin: 0em 0.25em 0em 0em;
@extraImageWidth: 6em;

/* Extra Text */
@extraTextPadding: 0em;
@extraTextPointer: none;
@extraTextFontSize: @relativeMedium;
@extraTextLineHeight: @lineHeight;
@extraTextMaxWidth: 500px;

/* Metadata Group */
@metadataDisplay: inline-block;
@metadataFontSize: @relativeTiny;
@metadataMargin: 0.5em 0em 0em;
@metadataBackground: none;
@metadataBorder: none;
@metadataBorderRadius: 0;
@metadataBoxShadow: none;
@metadataPadding: 0em;
@metadataColor: rgba(0, 0, 0, 0.6);

@metadataElementSpacing: 0.75em;

/* Like */
@likeColor: '';
@likeHoverColor: #FF2733;
@likeActiveColor: #EF404A;
@likeTransition: 0.2s color ease;

/* Metadata Divider */
@metadataDivider: '';
@metadataDividerColor: rgba(0, 0, 0, 0.2);
@metadataDividerOffset: -1em;

@metadataActionCursor: pointer;
@metadataActionOpacity: 1;
@metadataActionColor: rgba(0, 0, 0, 0.5);
@metadataActionTransition: color @defaultDuration @defaultEasing;

@metadataActionHoverColor: @selectedTextColor;

/*-------------------
      Variations
--------------------*/
