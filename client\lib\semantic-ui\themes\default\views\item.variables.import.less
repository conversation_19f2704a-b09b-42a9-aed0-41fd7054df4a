/*
  DO NOT MODIFY - This file has been generated and will be regenerated
  Semantic UI v2.2.6
*/
/*******************************
             Item
*******************************/

/*-------------------
         View
--------------------*/

/* Group */
@groupMargin: 1.5em 0em;

/* Item */
@display: flex;
@background: transparent;
@borderRadius: 0rem;
@minHeight: 0px;
@padding: 0em;
@width: 100%;
@boxShadow: none;
@border: none;
@zIndex: '';
@transition: box-shadow @defaultDuration @defaultEasing;

/* Responsive */
@itemSpacing: 1em;
@imageWidth: 175px;
@contentImageDistance: 1.5em;

@tabletItemSpacing: 1em;
@tabletImageWidth: 150px;
@tabletContentImageDistance: 1em;

@mobileItemSpacing: 2em;
@mobileImageWidth: auto;
@mobileImageMaxHeight: 250px;
@mobileContentImageDistance: 1.5em;

/*-------------------
       Content
--------------------*/

/* Image */
@imageDisplay: block;
@imageFloat: none;
@imageMaxHeight: '';
@imageVerticalAlign: top;
@imageMargin: 0em;
@imagePadding: 0em;
@imageBorder: none;
@imageBorderRadius: 0.125rem;
@imageBoxShadow: none;
@imageBorder: none;

/* Content */
@contentDisplay: block;
@contentVerticalAlign: top;

@contentWidth: auto;
@contentOffset: 0em;
@contentBackground: none;
@contentMargin: 0em;
@contentPadding: 0em;
@contentFontSize: 1em;
@contentBorder: none;
@contentBorderRadius: 0em;
@contentBoxShadow: none;

/* Header */
@headerMargin: -@lineHeightOffset 0em 0em;
@headerFontWeight: bold;
@headerFontSize: @relativeBig;
@headerColor: @darkTextColor;

/* Metadata */
@metaMargin: 0.5em 0em 0.5em;
@metaFontSize: 1em;
@metaLineHeight: 1em;
@metaSpacing: 0.3em;
@metaColor: rgba(0, 0, 0, 0.6);

/* Icons */
@actionOpacity: 0.75;
@actionHoverOpacity: 1;
@actionTransition: color @defaultDuration @defaultEasing;

/* Actions */
@favoriteColor: #FFB70A;
@favoriteActiveColor: #FFE623;
@likeColor: #FF2733;
@likeActiveColor: #FF2733;

/* Links */
@headerLinkColor: @headerColor;
@headerLinkHoverColor: @linkHoverColor;
@metaLinkColor: @lightTextColor;
@metaLinkHoverColor: @textColor;
@contentLinkColor: '';
@contentLinkHoverColor: '';
@contentLinkTransition: color @defaultDuration @defaultEasing;


/* Description */
@descriptionDistance: 0.6em;
@descriptionMaxWidth: auto;
@descriptionFontSize: 1em;
@descriptionLineHeight: @lineHeight;
@descriptionColor: @textColor;

/* Content Image */
@contentImageWidth: '';
@contentImageVerticalAlign: middle;

/* Avatar Image */
@avatarSize: @contentImageWidth;
@avatarBorderRadius: @circularRadius;

/* Paragraph */
@paragraphDistance: 0.5em;

/* Additional Content */
@extraDivider: none;
@extraHorizontalSpacing: 0.5rem;
@extraRowSpacing: 0.5rem;

@extraBackground: none;
@extraDisplay: block;
@extraPosition: relative;
@extraMargin: (1rem - @extraRowSpacing) 0em 0em;
@extraTop: 0em;
@extraLeft: 0em;
@extraWidth: 100%;
@extraPadding: 0em 0em 0em;
@extraBoxShadow: none;
@extraColor: @lightTextColor;
@extraTransition: color @defaultDuration @defaultEasing;

/*-------------------
      Variations
--------------------*/

/* Relaxed */
@relaxedItemSpacing: 1.5em;
@veryRelaxedItemSpacing: 2em;

/* Divided */
@dividedBorder: 1px solid @borderColor;
@dividedMargin: 0em;
@dividedPadding: 1em 0em;

@dividedFirstLastMargin: 0em;
@dividedFirstLastPadding: 0em;

/* Sizes */
@medium: 1em;