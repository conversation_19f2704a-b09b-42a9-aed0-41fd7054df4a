@import "{}/imports/ui/stylesheets/not-found.less";

.ui.inverted.menu .item.no-divider:before{
    background: none !important;
}

/* Stop tables from being highlighted when a sub-element is clicked */
.no-select {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: rgba(255, 255, 255, 0);
}

.no-h-pad {
    margin-left: -1rem;
    margin-right: -1rem;
}

/* Table Highlights */
.ui.table.selectable tr.positive, .ui.table.selectable td.positive {
    background: #3791d526 !important;
    color: #000 !important;
}

.ui.selectable.table tr.positive:hover, .ui.table tr td.selectable.positive:hover, .ui.selectable.table tr:hover td.positive {
    background: #3791d536 !important;
    color: #000 !important;
}

.scroll-x {
    overflow-x: auto;
}

.scroll-y {
    overflow-y: auto;
}

@media screen and (orientation: portrait) {
    /* Decrease table size in portrait mode */
    table.portrait {
        font-size: 0.8em;
        
        td {
            vertical-align: top;
        }
    }
}

html.tablet {
    table.tablet-hide-last-column {
        td:last-child:not(.no-results),
        th:last-child {
            display: none;
        }
    }

    .tablet-hide {
        display: none;
    }
}

html.desktop {
    .desktop-hide {
        display: none;
    }
}

table.break-word {
    td.break {
        word-break: break-word;
    }
}

// Searchbox in sidebar
.ui.fluid.input > input.blue-border {
    border: 2px solid #2185d0;
}
