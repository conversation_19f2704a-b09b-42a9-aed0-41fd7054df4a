// Import needed templates
import '../../ui/views/layout/layout';
import '../../ui/views/layout/layout-empty';
import '../../ui/pages/home/<USER>';
import '../../ui/pages/not-found/not-found';
import '../../ui/views/accounts/login';
import '../../ui/views/accounts/change-password';
import '../../ui/views/accounts/enroll-user';

import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Meteor } from 'meteor/meteor';
import { Router } from 'meteor/router';

// Set up routes specific to parent application.
const registerRoutes = () => {
  new Router(null, 'chemicalsLayout')
    .route('App.home', '/', 'Chemicals_home')
    .route('login', '/login', 'login')
    .route('changePassword', '/accounts/change-password')
    .route('enrollUser', '/accounts/enroll-user');

  // Also route to login when the user is not logged in
  FlowRouter.triggers.enter((context, redirect) => {
    if (!Meteor.userId()) redirect('login');
  }, { except: ['login'] });

  // Setup route-not-found page.
  FlowRouter.route('*', {
    action() {
      FlowRouter.go('App.home'); // just go to home page.
    },
  });
};

export const Routes = {
  register: () => registerRoutes(),
};
