import { Accounts } from 'meteor/accounts-base';
import { Meteor } from 'meteor/meteor';
import { Roles } from 'meteor/alanning:roles';
import { _ } from 'meteor/underscore';
import { <PERSON> as log } from 'meteor/wylio:winston-papertrail';
import { CompanyProfiles as Receipt_CompanyProfiles } from 'meteor/receipt/imports/api/company-profiles/company-profiles';
import { CompanySiteProfiles as Receipt_CompanySiteProfiles } from 'meteor/receipt/imports/api/company-site-profiles/company-site-profiles';
import { AppMode } from '../both/app-mode';

const createUserAccounts = (userAccounts) => {
  _.each(userAccounts, (user) => {
    if (Meteor.users.find({ username: user.username }).count() === 0) {
      const id = Accounts.createUser(user);
      if (user.pack_roles
        && user.pack_roles.length > 0
        && user.pack_site_identifier) {
        log.info(`Adding [Pack] roles to user <${user.username}>, <${JSON.stringify(user.pack_roles)}>`);
        Roles.addUsersToRoles(id, user.pack_roles, user.pack_site_identifier);
      }
    } else {
      log.info(`User <${user.username}> already in database - not inserted.`);
    }
  });
};

const createDevUserAccounts = () => {
  // Example account:
  // {
  //   username: 'username',
  //   mail: '<EMAIL>',
  //   password: 'password1',
  //   profile: {},
  //   pack_roles: ['admin'],
  //   pack_company_identifier: 'peterson-nl',
  //   pack_site_identifier: 'peterson-chemicals-dnhr',
  //   receipt_company_name: 'peterson-nl',
  //   receipt_site_identifier: 'peterson-chemicals-dnhr',
  // },
  const devUserAccounts = [];
  createUserAccounts(devUserAccounts);
};

const createProductionUserAccounts = () => {
  const productionUserAccounts = [];
  createUserAccounts(productionUserAccounts);
};

Meteor.startup(() => {
  if (AppMode.inDevMode) {
    log.info('Check and insert Chemicals User Accounts for Development environment..');
    createDevUserAccounts();
    createProductionUserAccounts();
  }
  if (AppMode.inProductionMode) {
    if (Meteor.users.find().count() === 0) {
      createProductionUserAccounts();
    }
  }
});

Accounts.onLogin((user) => {
  log.info(`${user.user.username} logged in`);
});

// Setup customisations on user object before inserting into user collection.
Accounts.onCreateUser((options, user) => {
  log.info(`Creating new user ${options.username}.`);
  log.info(`Options for user ${options.username} are <${JSON.stringify(options)}>.`);

  if (options.profile && !_.isEmpty(options.profile)) {
    log.info(`Setting profile for user <${options.username}> as per supplied options <${JSON.stringify(options.profile)}>.`);
    user.profile = options.profile;
  } else {
    log.info(`Setting default profile for user ${options.username}.`);

    // Setup user profile information to link user to Receipt site/company.
    let receiptCompanyProfileId = 'NOT_DEFINED';
    let receiptCompanySiteProfileId = 'NOT_DEFINED';
    if (options.receipt_company_name && options.receipt_site_identifier) {
      log.info(`Setting user profile to map to specified Receipt site for <${options.username}> <${options.receipt_site_identifier}>.`);

      // Get Ids for relevant company and site profile from the database.
      const vrCompanySiteProfile = Receipt_CompanySiteProfiles
        .find({ identifier: options.receipt_site_identifier })
        .fetch()[0];
      receiptCompanySiteProfileId = vrCompanySiteProfile._id;

      const receiptCompanyProfile = Receipt_CompanyProfiles
        .findOne({ name: options.receipt_company_name });
      receiptCompanyProfileId = receiptCompanyProfile._id;
    } else {
      log.info(`Receipt site not configured for  <${options.username}>.`);
    }

    user.profile = {
      companyProfileId: receiptCompanyProfileId, // Must link to vor-receipt.companyProfile
      sites: [
        {
          companySiteProfileId: receiptCompanySiteProfileId, // Must link to vor-receipt.companySiteProfile
          isDefault: true,
        },
      ],
    };
  }

  log.info(`Setting user profile for <${options.username}> as <${JSON.stringify(user.profile)}>.`);

  log.info(`Adding roles (needed for Pack side) to user ${options.username}`);
  // Create roles array for user object about to be inserted into users collection - Must exist for Pack stuff to work.
  // Does job of Roles.addUsersToRoles() above, but that will only work if user exists in collection, and in this case it doesn't yet.
  const rolesObj = { roles: {} };
  if (options.pack_roles && options.pack_site_identifier) {
    log.info(`Adding [Pack] roles to user ${options.username} as <${JSON.stringify(options.pack_roles)}>.`);
    rolesObj.roles[options.pack_site_identifier] = [];
    rolesObj.roles[options.pack_site_identifier].push(...options.pack_roles);
  }
  const customizedUser = Object.assign(user, rolesObj);

  return customizedUser;
});

// This is to get around Accounts.createUser instantly signing in a newly
// created user. The exception created here is swallowed client-side.
// In the future, we should create users using a backend method so we
// don't have to circumnavigate the exceptions.
Accounts.validateLoginAttempt((attempt) => {
  if (attempt.methodName === 'createUser') {
    return false;
  }
  return true;
});
