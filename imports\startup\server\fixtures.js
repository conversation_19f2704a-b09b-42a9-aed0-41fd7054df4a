/* eslint-disable camelcase */
import { Meteor } from 'meteor/meteor';
import { <PERSON> as log } from 'meteor/wylio:winston-papertrail';

// TODO: Change to use Chemicals Company profiles instead of Pack one.
import { CompanyProfiles as Pack_CompanyProfiles } from 'meteor/pack/imports/api/company-profiles/company-profiles';
import { CompanySiteProfiles as Pack_CompanySiteProfiles } from 'meteor/pack/imports/api/company-site-profiles/company-site-profiles';
import { CompanyProfiles as Receipt_CompanyProfiles } from 'meteor/receipt/imports/api/company-profiles/company-profiles';
import { CompanySiteProfiles as Receipt_CompanySiteProfiles } from 'meteor/receipt/imports/api/company-site-profiles/company-site-profiles';
import { AppMode } from '../both/app-mode';

Meteor.startup(() => {
  // Fill the DB with required data on startup
  log.info('Checking and inserting Peterson NL Chemicals [Pack] company and site profiles.');
  if (Pack_CompanyProfiles.find({ identifier: 'peterson-nl' }).count() === 0
  && !Meteor.isAppTest
  && (AppMode.inDevMode || AppMode.inProductionMode)) {
    // Setup Company and Site Profiles (Pack)
  } else {
    log.info('[Pack] Peterson NL Company profile is already in database - not inserting anything.');
  }

  log.info('Checking and inserting Peterson NL Chemicals [Receipt] company and site profiles.');
  if (Receipt_CompanyProfiles.find({ name: 'peterson-nl' }).count() === 0
    && !Meteor.isAppTest
    && (AppMode.inDevMode || AppMode.inProductionMode)) {
    // Setup Company and Site Profiles (Receipt)
  } else {
    log.info('[Receipt] Peterson NL Company profile is already in database - not inserting anything.');
  }
});
