import './chemicals-welcome.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';

Template.chemicalsWelcome.helpers({
  welcomeMessage() {
    return 'WMS Waste';
  },
});

Template.chemicalsWelcome.events({
  // Something strange about 1st click not working on fresh load in Chrome
  // Added mouseup event as temporary workaround.
  'click/mouseup .receipt-btn': function handleClick(event, instance) {
    FlowRouter.go('workItemOverview', {});
  },
  'click/mouseup .pack-btn': function handleClick(event, instance) {
    FlowRouter.go('unpackCargo', {
      clientId: 'null',
    });
  },
});
