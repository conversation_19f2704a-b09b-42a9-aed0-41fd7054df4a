<template name="changePassword">
  <div class="ui fluid container">
    <div class="ui centered card" style="min-width: 30%;">
      <div class="content">
        <div class="header">
          Change Password
        </div>
        </div>
        <div class="content">
          <form class="ui form" id="pwdChangeForm">
            <div class="field">
              <label>Previous Password</label>
              <input type="password" name="oldPassword" placeholder="Previous Password" />
            </div>
            <div class="field">
              <label>New Password</label>
              <input type="password" name="newPassword" placeholder="New Password" />
            </div>
            <div class="field">
              <label>Confirm New Password</label>
              <input type="password" name="newPasswordConfirm" placeholder="Confirm New Password" />
            </div>
            <button type="submit" class="ui button right floated" {{canSubmit}}>Change Password</button>
            <div class="clearfix"></div>
            <div class="ui error message"></div>
            <div class="ui success message"></div>
          </form>
        </div>
    </div>
  </div>
</template>
