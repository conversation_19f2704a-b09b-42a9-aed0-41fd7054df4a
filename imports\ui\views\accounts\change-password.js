import { Accounts } from 'meteor/accounts-base';
import { Template } from 'meteor/templating';
import { $ } from 'meteor/jquery';
import './change-password.html';

const changePasswordErrorMessage = (error) => {
  const errorMessageContainer = $('.error.message');
  errorMessageContainer.html(`Unable to change password.<br/>Check your username and password. <br/> ${error}`);
  errorMessageContainer.show();
};
const changePasswordSuccessMessage = () => {
  const errorMessageContainer = $('.error.message');
  const successMessageContainer = $('.success.message');
  $('input, button').attr('disabled', 'true');
  successMessageContainer.html('You have succesfully changed your password.');
  errorMessageContainer.hide();
  successMessageContainer.show();
};

Template.changePassword.onRendered(() => {
  $('#pwdChangeForm').form({
    fields: {
      oldPassword: {
        identifier: 'oldPassword',
        rules: [
          {
            type: 'empty',
            prompt: 'Please enter your old password.',
          },
        ],
      },
      newPassword: {
        identifier: 'newPassword',
        rules: [
          {
            type: 'empty',
            prompt: 'Please enter your new password.',
          },
        ],
      },
      newPasswordConfirm: {
        identifier: 'newPasswordConfirm',
        rules: [
          {
            type: 'match[newPassword]',
            prompt: '{name} field must match New Password.',
          },
        ],
      },
    },
  });
});

Template.changePassword.events({
  'submit form': function submit(event) {
    event.preventDefault();
    const oldPassword = $('[name=oldPassword]').val();
    const newPassword = $('[name=newPassword]').val();
    Accounts.changePassword(oldPassword, newPassword, (error) => {
      if (error) {
        changePasswordErrorMessage(error);
      } else {
        changePasswordSuccessMessage();
      }
    });
  },
});
