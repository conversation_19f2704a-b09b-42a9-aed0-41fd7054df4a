<template name="enrollUser">
  <div class="ui fluid container">
    <div class="ui centered card" style="min-width: 30%;">
      <div class="content">
        <div class="header">
          Add User
        </div>
        </div>
        <div class="content">
        {{#if canAddUsers}}
            <form class="ui form" id="pwdChangeForm">
                <div class="field">
                    <label>Username</label>
                    <input type="text" name="username" placeholder="Username" />
                </div>
                <div class="field">
                    <label>Email</label>
                    <input type="text" name="email" placeholder="Email" />
                </div>
                <div class="field">
                    <label>Password</label>
                    <input type="password" name="password" placeholder="Password" />
                </div>
                <div class="field">
                <label>Confirm Password</label>
                <input type="password" name="passwordConfirm" placeholder="Password Confirmation" />
                </div>
                <button type="submit" class="ui button right floated" {{canSubmit}}>Create User</button>
                <div class="clearfix"></div>
                <div class="ui error message"></div>
                <div class="ui success message"></div>
            </form>
        {{else}}
            <div class="ui error message">
                You are not authorized to add new users!
            </div>
        {{/if}}
        </div>
    </div>
  </div>


</template>
