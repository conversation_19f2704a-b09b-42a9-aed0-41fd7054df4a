import './enroll-user.html';
import { $ } from 'meteor/jquery';
import { Accounts } from 'meteor/accounts-base';
import { Meteor } from 'meteor/meteor';
import { Roles } from 'meteor/alanning:roles';
import { Template } from 'meteor/templating';

const enrollUserErrorMessage = (error) => {
  const errorMessageContainer = $('.error.message');
  errorMessageContainer.html(`Unable to create new user. <br/> ${error}`);
  errorMessageContainer.show();
};
const enrollUserSuccessMessage = (username) => {
  const errorMessageContainer = $('.error.message');
  const successMessageContainer = $('.success.message');
  $('input, button').attr('disabled', 'true');
  successMessageContainer.html(`You have succesfully created new user ${username}.`);
  errorMessageContainer.hide();
  successMessageContainer.show();
};

Template.enrollUser.onRendered(() => {
  $('#pwdChangeForm').form({
    fields: {
      username: {
        identifier: 'username',
        rules: [
          {
            type: 'empty',
            prompt: 'Please enter a username.',
          },
        ],
      },
      password: {
        identifier: 'password',
        rules: [
          {
            type: 'empty',
            prompt: 'Please enter a password.',
          },
        ],
      },
      email: {
        identifier: 'email',
        optional: true,
        rules: [
          {
            type: 'email',
            prompt: 'Please enter a valid email.',
          },
        ],
      },
      passwordConfirm: {
        identifier: 'passwordConfirm',
        rules: [
          {
            type: 'match[password]',
            prompt: '{name} field must match password.',
          },
        ],
      },
    },
  });
});

Template.enrollUser.helpers({
  canAddUsers() {
    const user = Meteor.user();
    return (Roles.userIsInRole(user, 'admin', 'peterson-chemicals-dnhr'));
  },
});

Template.enrollUser.events({
  'submit form': function submitForm(event) {
    event.preventDefault();
    const userSettings = {
      username: $('[name=username]').val(),
      email: $('[name=email]').val(),
      password: $('[name=password]').val(),
      // TODO: Currently hardcoded should add functionality to select this on frontend
      receipt_company_name: 'peterson-nl',
      receipt_site_identifier: 'peterson-chemicals-dnhr',
      pack_site_identifier: 'peterson-chemicals-dnhr',
      pack_roles: ['dummyrole'], // Required to associate the user with a Pack Site ID
    };

    Accounts.createUser(userSettings, (error) => {
      if (error) {
        // This is to get around the error thrown by the server, on being
        // forbidden to login on create user events - so the newly created user
        // doesn't instantly log in. In the future, we should create users using a
        // backend method so we don't have to circumnavigate the exceptions.
        if (error.reason !== 'Login forbidden') {
          enrollUserErrorMessage(error);
        } else {
          enrollUserSuccessMessage(userSettings.username);
        }
      } else {
        enrollUserSuccessMessage(userSettings.username);
      }
    });
  },
});
