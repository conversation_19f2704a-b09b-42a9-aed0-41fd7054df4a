import { Meteor } from 'meteor/meteor';
import { Template } from 'meteor/templating';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { $ } from 'meteor/jquery';
import './login.html';

const displayLoginErrorMessage = () => {
  const errorMessageContainer = $('.error.message');
  errorMessageContainer.html('Unable to login.<br/>Check your username and password.');
  errorMessageContainer.show();
};

Template.login.onRendered(() => {
  $('#loginForm').form({
    fields: {
      username: {
        identifier: 'username',
        rules: [
          {
            type: 'empty',
            prompt: 'Please enter your username.',
          },
        ],
      },
      password: {
        identifier: 'password',
        rules: [
          {
            type: 'empty',
            prompt: 'Please enter your password.',
          },
        ],
      },
    },
  });
});

Template.login.events({
  'submit form': function submit(event) {
    event.preventDefault();
    const username = $('[name=username]').val();
    const password = $('[name=password]').val();
    Meteor.loginWithPassword(username, password, (error) => {
      if (error) {
        displayLoginErrorMessage();
      } else {
        FlowRouter.go('App.home');
      }
    });
  },
});
