<template name="chemicalsLayout">

    <div class="ui top fixed inverted menu">
        <a class="header item" href="{{pathFor 'App.home' query=''}}">
            <img class="ui middle aligned tiny image" src="/images/peterson-logo-white.png" />
        </a>
        <div class="left menu">
            <a class="item" href="{{pathFor 'workItemOverview'}}">
                Cargo
            </a>
            <a class="item" href="{{pathFor 'unpackCargo' clientId='null'}}">
                <span style="padding-top:3px;">Waste</span>
            </a>
        </div>

        {{#if Template.subscriptionsReady}}
        <div class="right menu">
            {{#if loggedIn}}
            <div class="item right floated">
                {{> userDropdownChemicals}}
            </div>
            {{/if}}
        </div>
        {{/if}}
    </div>

    <div id="main" style="overflow: auto">
        {{#if Template.subscriptionsReady}} {{> Template.dynamic template=main}} {{else}}
        <div class="ui active loader"></div>
        {{/if}}
    </div>

</template>