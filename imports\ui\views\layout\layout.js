import './layout.html';
import '../accounts/user-dropdown-chemicals';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import { Template } from 'meteor/templating';

Template.chemicalsLayout.onCreated(function onCreated() {
  const template = this;
});

Template.chemicalsLayout.onRendered(function onRendered() { });

Template.chemicalsLayout.helpers({
  loggedIn() {
    const user = Meteor.user();
    return user != null;
  },
});

Template.chemicalsLayout.events({
  'click .ui.dropdown .remove.icon': function handleClick(event, templateInstance) {
    templateInstance.$(event.target).parent('.dropdown').dropdown('clear');
    event.stopPropagation();
  },
});
