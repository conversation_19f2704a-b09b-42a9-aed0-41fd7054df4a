{"name": "chemicals", "private": true, "scripts": {"start": "meteor run --settings ./deployments/development/settings.json", "debug": "meteor run --inspect --settings ./deployments/development/settings.json", "lint": "eslint .", "pretest": "npm run lint --silent", "test:packages": "meteor test-packages --driver-package meteortesting:mocha --port 3100 --settings ./deployments/development/settings.json", "deploy:staging": "set DEPLOY_HOSTNAME=eu-west-1.galaxy-deploy.meteor.com & meteor deploy wmswaste-staging.petersonlighthouse.com --owner peterson --settings ./deployments/staging/settings.json", "deploy:production": "set DEPLOY_HOSTNAME=eu-west-1.galaxy-deploy.meteor.com & meteor deploy wmswaste.petersonlighthouse.com --owner peterson --settings ./deployments/production/settings.json"}, "dependencies": {"@babel/runtime": "^7.24.1", "basic-auth": "^2.0.0", "core-js": "^2.5.3", "glob": "^7.1.2", "meteor-node-stubs": "^1.2.9", "moment-timezone": "^0.5.45", "simpl-schema": "^0.4.2"}, "devDependencies": {"@meteorjs/eslint-config-meteor": "^1.0.5", "autoprefixer": "^6.3.1", "babel-eslint": "^8.2.6", "eslint": "^8.57.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-base": "^15.0.0", "eslint-import-resolver-meteor": "^0.4.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-meteor": "^7.3.0", "eslint-plugin-mocha": "^10.4.1", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.3.0", "postcss": "^6.0.22", "postcss-load-config": "^1.2.0"}, "postcss": {"plugins": {"autoprefixer": {"browsers": ["last 2 versions"]}}}, "volta": {"node": "14.21.3"}}