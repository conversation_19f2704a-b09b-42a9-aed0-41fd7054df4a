/* eslint-disable max-classes-per-file */
import { Meteor } from 'meteor/meteor';

class _CollectionCreator {
  constructor(options = {}) {
    this.prefix = options.namePrefix;
  }

  get collectionNamePrefix() {
    return this.prefix;
  }

  set collectionNamePrefix(prefix) {
    this.prefix = prefix;
  }

  newCollection(name) {
    return new Meteor.Collection(this._collectionName(name));
  }

  _collectionName(name) {
    if (this.prefix && this.prefix.length) {
      return `${this.prefix}.${name}`;
    }

    return name;
  }
}

class _CollectionManager {
  constructor() {
    this.collectionCreator = new _CollectionCreator();
  }

  initCreator(options) {
    this.collectionCreator = new _CollectionCreator(options);
  }

  get create() {
    return this.collectionCreator;
  }
}

export const CollectionManager = new _CollectionManager();
export const name = 'collection-manager';
