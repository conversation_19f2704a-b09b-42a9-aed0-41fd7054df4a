import { Meteor } from 'meteor/meteor';

const EventEmitter = require('events');

const mediatorEventEmitter = new EventEmitter();

const events = {
  CONTAINER_UPDATE: 'CONTAINER_UPDATE', // Container ready for waste removal, or container info has been updated from Flow.
  CONTAINER_COMPLETED_WASTE_REMOVAL: 'CONTAINER_COMPLETED_WASTE_REMOVAL', // Container now processed and moved to 'Awaiting Collection'
  CONTAINER_RETURNED_TO_PREP: 'CONTAINER_RETURNED_TO_PREP',
  ITEM_IN_CARGO_RECEIPTED: 'ITEM_IN_CARGO_RECEIPTED', // Item has been removed from container
};

class _Mediator {
  constructor() {}

  get events() {
    return events;
  }

  subscribe(eventType, callback) {
    console.log('info: Subscribing to event', eventType);
    if (events[eventType]) {
      mediatorEventEmitter.on(eventType, callback);
    } else {
      throw new Error('Subscribing for an event type that does not exist');
    }
  }

  publish(eventType, eventData) {
    if (events[eventType]) {
      mediatorEventEmitter.emit(eventType, eventData);
    } else {
      throw new Error('Pushlishing for an event type that does not exist');
    }
  }
}

export const Mediator = new _Mediator();
export const name = 'mediator';
