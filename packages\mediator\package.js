Package.describe({
  name: 'mediator',
  version: '0.0.1',
  // Brief, one-line summary of the package.
  summary: '',
  // URL to the Git repository containing the source code for this package.
  git: '',
  // By default, Meteor will default to using README.md for documentation.
  // To avoid submitting documentation, set this field to null.
  documentation: 'README.md',
});

Package.onUse((api) => {
  api.versionsFrom('2.6.1');
  api.use(['ecmascript']);
  api.mainModule('mediator.js');
});

Package.onTest((api) => {
  api.use(['ecmascript', 'meteortesting:mocha']);
  api.use('mediator');
  api.mainModule('mediator-tests.js');
});
