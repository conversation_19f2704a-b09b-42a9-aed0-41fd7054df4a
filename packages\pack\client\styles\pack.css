/* CSS Declarations */

html,
body{
  height: 100%;
  background-color: whitesmoke;
}

html{
  overflow: hidden;
}
body{
  overflow: auto;
}

#main{
  height: 100%;
  z-index:1;
  padding-top:65px;
}

#__blaze-root{
  height: 100%;
}

#historyTable {
  height: 70%;
  overflow:auto;
  display:block;
}

.whitespace-nowrap{
  white-space: nowrap;
}

.bold{
  font-weight: bold;
}

.clearfix {
  zoom: 1;
  clear: both;
}

.clearfix:before, .clearfix:after {
  content: "\0020";
  display: block;
  height: 0;
  overflow: hidden;
}

.clearfix:after {
  clear: both;
}

.ui.dropdown .remove.icon {
  font-size: 0.857143em;
  float:left;
  margin:0;
  padding:0;
  left:-0.7em;
  top:0;
  position:relative;
  opacity: 0.5;
}

.ui.dropdown input[value=''] ~ .remove.icon, .ui.dropdown input:not([value]) ~ .remove.icon
{
   opacity: 0.0;
}

.ui.dropdown input.search {
  margin-left:15px;
}
.ui.dropdown .text.default{
  margin-left:-15px;
}
.ui.dropdown input[value=''] ~ input.search, .ui.dropdown input:not([value]) ~ input.search {
  margin-left:0;
}
.ui.dropdown.loading .remove.icon:after, .ui.dropdown.loading .remove.icon:before {
  content:none;
}
.ui.dropdown.loading .text {
  margin-left:-15px;
}

.receiptFormHeaderRule {
  margin-bottom: 1rem;
  border-style: groove;
  border-width: 0;
  border-bottom-width: 1px;
}

.truncate-with-ellipsis {
  max-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
} 

.truncate-with-ellipsis:hover {
  overflow: visible; 
  white-space: normal;
  height:auto;
}

.toggle.label {
  cursor: pointer;
  display: inline-block;
  margin-right: 1em;
}

.bordered.button {
  border: 0.1rem solid !important;
}

.shadowed.buttons {
  box-shadow: 0px 1px 2px 0 rgba(34, 36, 38, 0.15) !important;
}

.default-padding.fluid.container {
  padding-top: 1rem !important;
  padding-left: 1rem !important;
  padding-right: 1rem !important;
}

.padded-top.column {
  padding-top: 2.4rem !important;
}

.no-padding {
  padding: 0 !important;
}

.no-side-padding {
  padding-left: 0 !important;
  padding-right: 0 !important;
}
.scan-item{
    animation: fadein 0.5s;
    margin-bottom: 10px !important;
}
.scan-item h3{
    display: inline !important;
}

.scan-item-stats .segment{
    padding-bottom: 10px !important;
    padding-top: 10px !important;
}

#scan-progress-bottom{
    position: fixed;
    bottom: 0;
    width: 100%;
}

.selectable{
    cursor: pointer;
}
@keyframes fadein {
    from { opacity: 0; }
    to   { opacity: 1; }
}