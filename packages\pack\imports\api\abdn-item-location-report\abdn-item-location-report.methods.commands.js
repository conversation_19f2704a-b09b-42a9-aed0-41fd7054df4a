import { Match, check } from 'meteor/check';
import { AbdnItems } from '../abdn-items/abdn-items';
import { CompanySiteProfiles } from '../company-site-profiles/company-site-profiles';
import { ItemLocationReports } from '../abdn-item-location-report/abdn-item-location-report';
import { Roles } from 'meteor/alanning:roles';
import { <PERSON> as log } from 'meteor/wylio:winston-papertrail';
import moment from 'moment';

const checkAndGetUser = () => {
  const user = Meteor.user();
  if (!user) {
    throw new Meteor.Error('not-authorized');
  }
  return user;
};

const getStoredItemLocationReport = (siteIdentifier, clientId) => {
  check(siteIdentifier, String);
  check(clientId, Match.Maybe(String));

  const siteProfile = CompanySiteProfiles.findOne({ identifier: siteIdentifier });
  const storageLocations = siteProfile.configuration.storageLocations;

  const itemLocationsReport = {
    reportRunDateTime: moment().toDate(),
    locationReportData: {
      locationCounts: [],
    },
  };

  const selector = {
    siteId: siteIdentifier,
    isDeleted: false,
    isStored: true,
  };

  if (clientId) {
    selector['client._id'] = clientId;
  }

  _.each(storageLocations, (location) => {
    const locationCount = {
      location: location.name,
      itemCount: null,
    };

    selector.location = location.name;

    locationCount.itemCount = AbdnItems.find(selector).count();

    itemLocationsReport.locationReportData.locationCounts.push(locationCount);
  });

  return itemLocationsReport;
};

const upsertDocumentInItemLocationReport = (siteId, client, itemLocationReport) => {
  log.info('updateDocumentInItemLocationReport called - ' +
    `itemLocationReport: <${JSON.stringify(itemLocationReport, null, 2)}>.`);
  const clientName = client.name;
  const clientId = client._id;
  ItemLocationReports.update(
    { clientId, siteId, clientName },
    { $set: { itemLocationReport } },
    { upsert: true },
    (error) => {
      if (error) {
        log.error('updateDocumentInItemLocationReport failed - ' +
            `${JSON.stringify(error, null, 2)}`);
      } else {
        log.info('updateDocumentInItemLocationReport completed.');
      }
    },
  );
};

const updateLocationReportForSitesClient = (client, siteId) => {
  log.info('updateLocationReportFromCollectionEvent called - ' +
    `clientId: <${JSON.stringify(client, null, 2)}>, siteId: <${siteId}>.`);

  upsertDocumentInItemLocationReport(
    siteId,
    client,
    getStoredItemLocationReport(siteId, client._id),
  );
};

export const AbdnItemLocationReport = {
  getStoredItemLocationReport,
  updateLocationReportForSitesClient,
  upsertDocumentInItemLocationReport,
};

Meteor.methods({
  updateLocationReportForSitesClient: function handleUpdate(clientId, siteId) {
    log.info('MeteorMethod updateLocationReportForSitesClient called - ' +
      `clientId: <${clientId}>, siteId: <${siteId}>.`);

    check(clientId, String);
    check(siteId, String);

    const user = checkAndGetUser();
    const userSites = Roles.getGroupsForUser(user);
    const siteProfile = CompanySiteProfiles
      .findOne({ $and: [{ identifier: siteId }, { identifier: { $in: userSites } }] });

    if (!siteProfile) {
      throw new Meteor.Error(500, 'Error 500: Not Found', 'the site profile does not exits');
    }

    const client = siteProfile.configuration.clients.find((c) => c._id === clientId);
    updateLocationReportForSitesClient(client, siteProfile.identifier);
  },
});
