import { CompanySiteProfiles } from '../../company-site-profiles/company-site-profiles';
import { ItemLocationReports } from '../abdn-item-location-report';
import { Meteor } from 'meteor/meteor';
import { Roles } from 'meteor/alanning:roles';
import { check } from 'meteor/check';
import { <PERSON> as log } from 'meteor/wylio:winston-papertrail';

const checkAndGetSiteProfile = (siteId, userSites = null) => {
  const siteProfile = CompanySiteProfiles.findOne({ identifier: siteId });

  if (!siteProfile) {
    throw new Meteor.Error(500, 'Error 500: Site Not Found', 'The siteId does not exist');
  } else if (userSites) {
    // If usersites provided check user can access site
    const noAccessToSite = userSites.indexOf(siteProfile.identifier) === -1;
    if (noAccessToSite) {
      throw new Meteor.Error(500, 'Error 500: User Not Authorized For Site', 'The siteId does not exist for user profile.');
    }
  }
  return siteProfile;
};

const checkAndGetSiteClient = (siteProfile, clientId) => {
  const client = siteProfile.configuration.clients.find(c => c._id === clientId);

  if (!client) {
    throw new Meteor.Error(500, 'Error 500: Client Not found', 'The clientId does not exist for the specified site.');
  }

  return client;
};

Meteor.publish('itemLocationReports.storedLocationCounts', function storedLocationCounts(clientId, siteId) {
  log.info('itemLocationReports.storedLocationCounts subscription - ' +
    `clientId: <${clientId}>, siteId: <${siteId}>.`);

  check(clientId, String);
  check(siteId, String);
  if (this.userId) {
    const userSites = Roles.getGroupsForUser(this.userId);
    const siteProfile = checkAndGetSiteProfile(siteId, userSites);
    const client = checkAndGetSiteClient(siteProfile, clientId);

    return ItemLocationReports.find({ clientId: client._id, siteId: siteProfile.identifier });
  }

  return [];
});
