import { Match, check } from 'meteor/check';
import { AbdnItems } from './abdn-items';
import { AltensEventFactory } from '../../shared/event-factory';
import { CompanySiteProfiles } from '../company-site-profiles/company-site-profiles';
import { Meteor } from 'meteor/meteor';
import { <PERSON> as log } from 'meteor/wylio:winston-papertrail';
import { moment } from 'meteor/momentjs:moment';

const createNewEventAndSetExistingEventToDeleted = (
  itemId,
  eventType,
  newEventDateTime,
  userName,
  eventData = null,
) => {
  const now = moment();

  // Set previous event of this type on this item TO DELETED.
  AbdnItems.update(
    { _id: itemId, 'events.eventType': eventType },
    {
      $set: {
        'events.$.isDeleted': true,
        'events.$.updatedBy': userName,
        'events.$.updatedAt': now.toDate(),
      },
    },
  );

  // Create the new event.
  AltensEventFactory.createSingularItemEventWithData(
    itemId,
    eventType,
    newEventDateTime,
    userName,
    eventData,
  );
};

const updateItem = (itemId, updatesFromClient, userName, userSiteIdentifiers) => {
  check(itemId, String);
  check(updatesFromClient, Object);
  check(userName, String);
  check(userSiteIdentifiers, Array);

  log.info(`AbdnItemUpdator.updateItem called - ItemId<${itemId}>
            updates ${JSON.stringify(updatesFromClient, null, 2)}`);

  // Load current Item.
  const currentItem = AbdnItems.findOne(itemId);

  if (!currentItem) {
    throw new Meteor.Error(500, 'Error 500: Not found', 'The item does not exist');
  }

  // Verify Item loaded has a site identifier that matches user's site.
  if (!(userSiteIdentifiers.indexOf(currentItem.siteId) > -1)) {
    throw new Meteor.Error(
      'not-authorized',
      'User\'s associated site does not match Item\'s site.',
      `UserSite: ${userSiteIdentifiers}, itemSite: ${currentItem.siteId}.`,
    );
  }

  const updates = _.clone(updatesFromClient); // Shallow copy - ok as shallow source object.
  // Remove properties we do not need for the update.
  delete updates.receivedAtDateTime;
  delete updates.receiptCategoryId;
  delete updates.offshoreLocationId;
  delete updates.dgClassificationId;
  delete updates.vendorId;
  delete updates.packageTypeId;
  delete updates.contentTypeId;
  delete updates.unitCostCurrency;

  // Fix Received Date.
  const receivedAtDateTime = moment(updatesFromClient.receivedAtDateTime);
  updates.receivedDateStr = receivedAtDateTime.format('YYYY-MM-DD');
  updates.receivedDate = receivedAtDateTime.toDate();

  log.info(`Updates to apply ${JSON.stringify(updates, null, 2)}`);
  // TODO: Handle cases where dates are reset to null, e.g. 'undeliver' an item.
  // Or handle elsewhere e.g., via a dedicated 'undeliver' button.

  log.info(`Current Item ${JSON.stringify(currentItem, null, 2)}`);

  // List of keys that can be set from string to null(object).
  const keysOfStringsAllowedToBeSetToNull = [];
  keysOfStringsAllowedToBeSetToNull.push('dgClassification');
  keysOfStringsAllowedToBeSetToNull.push('comments');
  keysOfStringsAllowedToBeSetToNull.push('materialNo');
  keysOfStringsAllowedToBeSetToNull.push('workOrderNo');
  keysOfStringsAllowedToBeSetToNull.push('packageContent');
  keysOfStringsAllowedToBeSetToNull.push('offshoreLocation');

  const valuesToSet = {};
  const auditChanges = [];
  Object.keys(updates).forEach(function (key, index) {
    console.log(`Checking <${key}>`);
    if (!currentItem.hasOwnProperty(key)) {
      console.log(`<${key}> property does not exist on item - Not updating this property.`);
    } else {
      if (((typeof updates[key]) !== (typeof currentItem[key])) &&
          (currentItem[key] != null) &&
          (!_.contains(keysOfStringsAllowedToBeSetToNull, key))) {
        console.log(`Property type <${typeof updates[key]}> does not match stored type` +
                    ` <${typeof currentItem[key]}> for <${key}> - Not updating this property.`);
      } else if (updates[key] === currentItem[key]) {
        console.log('No change to ' + key);
      } else {
        if ((updates[key] instanceof Date) &&
            (currentItem[key] !== null) &&
            (updates[key].getTime() === currentItem[key].getTime())) {
          console.log('No change to Date value ' + key);
        } else {
          console.log(`<${key}> changed from <${currentItem[key]}> to <${updates[key]}>.`);
          valuesToSet[key] = updates[key];

          // Record change in values.
          const change = { key, from: currentItem[key], to: updates[key] };
          auditChanges.push(change);
        }
      }
    }
  });

  console.log(`Compacted Changes: ${JSON.stringify(valuesToSet)}.`);

  if (Object.keys(valuesToSet).length === 0) {
    // Nothing to update - so don't perform update.
    log.info('No updates found - item will not be updated.');
  } else {
    // Add any dependent updates.

    // Set item audit info.
    const now = moment();
    const setAuditValues = {
      updatedBy: userName,
      updatedAt: now.toDate(),
    };
    _.extend(valuesToSet, setAuditValues);

    log.info(`Item Changes with Audit info: ${JSON.stringify(valuesToSet, null, 2)}.`);

    // Update Item.
    AbdnItems.update(itemId, { $set: valuesToSet });

    // Record Item Edited event - for audit purposes only.
    const eventData = { edits: auditChanges };
    AltensEventFactory.createSingularItemEventWithData(
      itemId,
      AltensEventFactory.AltensItemEvents.EDITED,
      now.toDate(),
      userName,
      eventData,
    );

    // Update any item events // Add new events.
    if (_.contains(Object.keys(valuesToSet), 'receivedDate')) {
      createNewEventAndSetExistingEventToDeleted(
        itemId,
        AltensEventFactory.AltensItemEvents.RECEIVED,
        valuesToSet.receivedDate,
        userName,
      );
    }
  }

  // Return the updated item.
  const updatedItem = AbdnItems.findOne(itemId);
  return updatedItem;
};

export const AbdnItemUpdator = {
  updateItem,
};
