import { Meteor } from 'meteor/meteor';
import { AbdnItemsSchema,
  AbdnItemsAddItemMethodSchema,
  AbdnItemsUpdatePackedPropertiesMethodSchema } from './abdn-items.schemas';

export const AbdnItems = new Meteor.Collection('abdnItems');

export const AbdnItemsSchemas = {
  AbdnItemsSchema,
  AbdnItemsAddItemMethodSchema,
  AbdnItemsUpdatePackedPropertiesMethodSchema,
};

/*  Attach Schema to Collection for Validating Inserts/Updates  */
AbdnItems.attachSchema(AbdnItemsSchema);

const escapeRegExp = (query) => query.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&');

const getItemsSelector = (
  clientId,
  storedItemsOnly,
  restrictToLocation,
  query,
  ignorePacked = true,
) => {
  const selector = {
    $and: [{
      'client._id': clientId,
    }],
  };

  if (ignorePacked) selector.$and.push({ isPacked: false });

  if (storedItemsOnly) {
    selector.$and.push(restrictToLocation ?
      { isStored: true, location: restrictToLocation } :
      { isStored: true });
  } else {
    selector.$and.push({ isStored: false });
  }

  if (query && query.length > 2) {
    const querySelector = {
      $or: [
        { receiptNo: { $regex: escapeRegExp(query), $options: 'i' } },
        { poNo: { $regex: escapeRegExp(query), $options: 'i' } },
        { deliveryNo: { $regex: escapeRegExp(query), $options: 'i' } },
        { packageType: { $regex: escapeRegExp(query), $options: 'i' } },
        { description: { $regex: escapeRegExp(query), $options: 'i' } },
        { vendor: { $regex: escapeRegExp(query), $options: 'i' } },
        { receiptCategory: { $regex: escapeRegExp(query), $options: 'i' } },
      ],
    };
    selector.$and.push(querySelector);
  }

  return selector;
};

export const ItemsSelector = {
  getItemsSelector,
};
