import { AbdnItems, AbdnItemsSchemas } from '../abdn-items/abdn-items';
import { Match, check } from 'meteor/check';
import { AbdnItemLocationReport } from '../abdn-item-location-report/abdn-item-location-report.methods.commands';
import { AbdnItemUpdator } from './abdn-item-updator';
import { AbdnRequestsSchemas } from '../abdn-requests/abdn-requests';
import { AltensEventFactory } from '../../shared/event-factory';
import { CompanySiteProfiles } from '../company-site-profiles/company-site-profiles';
import { DB_DATE_STR_FORMAT } from '../../shared/lib/constants';
import { Meteor } from 'meteor/meteor';
import { Roles } from 'meteor/alanning:roles';
import { <PERSON> as log } from 'meteor/wylio:winston-papertrail';

const checkAndGetUser = () => {
  const user = Meteor.user();
  if (!user) {
    throw new Meteor.Error('not-authorized');
  }
  return user;
};

const verifyReceiptNo = (receiptNo) => {
  const duplicates = AbdnItems.find({ receiptNo }).fetch;

  return duplicates && duplicates.length > 0;
};

function getSiteIdentifiersForUser(user) {
  const userSites = [];
  const usersSite = user.profile.siteIdentifier;
  userSites.push(usersSite);
  return userSites;
}

const convertItemPropertyIdsToObjects = (itemProperties, siteProfile) => {
  // Receipt Category
  const receiptCategory = siteProfile
    .configuration
    .receiptCategories
    .find((x) => x._id === itemProperties.receiptCategoryId);

  // Offshore Location
  const offshoreLocation = siteProfile
    .configuration
    .offshoreLocations
    .find((x) => x._id === itemProperties.offshoreLocationId);
  // Vendor
  const vendor = siteProfile
    .configuration
    .vendors
    .find((x) => x._id === itemProperties.vendorId);

  // Package Type
  const packageType = siteProfile
    .configuration
    .packageTypes
    .find((x) => x._id === itemProperties.packageTypeId);
  // Content Type
  const contentType = siteProfile
    .configuration
    .packageTypes
    .find((x) => x._id === itemProperties.contentTypeId);
  // DG Classification
  const dgClassification = siteProfile
    .configuration
    .dgClassifications
    .find((x) => x._id === itemProperties.dgClassificationId);
  // Unit Cost Currency
  const unitCostCurrency = siteProfile
    .configuration
    .currencies
    .find((x) => x._id === itemProperties.unitCostCurrencyId);

  return {
    receiptCategory,
    offshoreLocation,
    vendor,
    packageType,
    contentType,
    dgClassification,
    unitCostCurrency,
  };
};

const convertToNames = (input) => {
  const packageType = input.packageType ? input.packageType.name : null;
  const packageContent = input.contentType ? input.contentType.name : null;

  const receiptCategory = input.receiptCategory ? input.receiptCategory.name : null;
  const offshoreLocation = input.offshoreLocation ? input.offshoreLocation.name : null;
  const vendor = input.vendor ? input.vendor.name : null;
  const dgClassification = input.dgClassification ? input.dgClassification.name : null;
  const unitCostCurrency = input.unitCostCurrency ? input.unitCostCurrency.name : null;

  // Return properties that match the names of the mongodb item properties
  return {
    receiptCategory,
    offshoreLocation,
    vendor,
    packageType,
    packageContent,
    dgClassification,
    unitCostCurrency,
  };
};

const updateItemToInsertFromCategoryAutoStoreOnReceipt = (receiptCategory, itemToInsert, siteProfile) => {
  log.info('updateItemToInsertFromCategoryAutoStoreOnReceipt called - ' +
    `receiptCategory: <${JSON.stringify(receiptCategory, null, 2)}>, ` +
    `itemToInsert: <${JSON.stringify(itemToInsert, null, 2)}>.`);

  // if item is backload the storage location is always set to backload
  const storageLocationName = itemToInsert.isBackload ? 'Backload' : receiptCategory.name;

  const updatedItemToInsert = itemToInsert;
  const siteStorageLocations = siteProfile.configuration.storageLocations;
  const itemLocation = siteStorageLocations
    .find((location) => location.name === storageLocationName);

  if (itemLocation) {
    updatedItemToInsert.isStored = true;
    updatedItemToInsert.location = itemLocation.name;
    updatedItemToInsert.storedDate = updatedItemToInsert.receivedDate;
    updatedItemToInsert.storedDateStr = updatedItemToInsert.receivedDateStr;

    return updatedItemToInsert;
  }
  log.info('updateItemToInsertFromCategoryAutoStoreOnReceipt failed to find ' +
    'corresponding storage location itemToInsert was not updated.');

  return itemToInsert;
};

Meteor.methods({
  'abdnItems.add': function handleAdd(itemProperties) {
    log.info(`abdnItems.add Called. ${JSON.stringify(itemProperties, null, 2)}`);

    // Ensure user is logged in before inserting
    const user = checkAndGetUser();
    const userSites = Roles.getGroupsForUser(user);
    const siteProfile = CompanySiteProfiles.findOne({ identifier: { $in: userSites } });
    if (!siteProfile) {
      throw new Meteor.Error(500, 'Error 500: Not Found', 'the site profile does not exits');
    }

    // Validate object before inserting
    check(itemProperties, AbdnItemsSchemas.AbdnItemsAddItemMethodSchema);

    const client = siteProfile
      .configuration
      .clients
      .find((x) => x._id === itemProperties.clientId);

    const receivedAtDateTime = moment(itemProperties.receivedAtDateTime);

    // Verify Receipt Number isn't duplicate
    if (verifyReceiptNo(itemProperties.receiptNo)) {
      throw new Meteor.Error(500, 'Error 500: Dupicate Receipt No', 'the item receipt no is already present in the system');
    }

    const verifiedReceiptNo = itemProperties.receiptNo;

    const itemValuesFromIds = convertItemPropertyIdsToObjects(itemProperties, siteProfile);
    const receiptCategory = itemValuesFromIds.receiptCategory;
    const offshoreLocation = itemValuesFromIds.offshoreLocation;
    const vendor = itemValuesFromIds.vendor;
    const packageType = itemValuesFromIds.packageType;
    const contentType = itemValuesFromIds.contentType;
    const dgClassification = itemValuesFromIds.dgClassification;
    const unitCostCurrency = itemValuesFromIds.unitCostCurrency;

    const quantity = itemProperties.quantity;
    const weightKg = itemProperties.weightKg === 0.0 ? null : itemProperties.weightKg;
    const unitCost = itemProperties.unitCost === 0.0 ? null : itemProperties.unitCost;
    const contentQuantity = itemProperties.contentQuantity === 0 ?
      null : itemProperties.contentQuantity;
    // Create Item Received Event
    const now = moment();
    const itemEvents = [];
    itemEvents.push(AltensEventFactory.createItemEvent(
      AltensEventFactory.AltensItemEvents.RECEIVED,
      receivedAtDateTime.toDate(),
      user.username,
    ));

    let itemToInsert = {
      siteId: siteProfile.identifier,
      client,
      receiptNo: verifiedReceiptNo,
      receivedDateStr: receivedAtDateTime.format(DB_DATE_STR_FORMAT),
      receivedDate: receivedAtDateTime.toDate(),
      receiptCategory: receiptCategory.name,
      vendor: vendor.name,
      deliveryNo: itemProperties.deliveryNo,
      poNo: itemProperties.poNo,
      workOrderNo: itemProperties.workOrderNo.length ? itemProperties.workOrderNo : null,
      offshoreLocation: offshoreLocation ? offshoreLocation.name : null,
      isBackload: itemProperties.isBackload,
      materialNo: itemProperties.materialNo.length ? itemProperties.materialNo : null,
      packageType: packageType.name,
      quantity,
      packageContent: contentType ? contentType.name : null,
      packageContentQuantity: contentQuantity,
      weightKg,
      unitCost,
      unitCostCurrency: unitCostCurrency ? unitCostCurrency.name : null,
      description: itemProperties.description,
      comments: itemProperties.comments.length ? itemProperties.comments : null,
      isDangerousGoods: itemProperties.isDG,
      dgClassification: dgClassification ? dgClassification.name : null,
      createdBy: user.username,
      createdAt: now.toDate(),
      updatedBy: null,
      updatedAt: null,
      events: itemEvents,
    };

    let itemIsAutoStored = false;
    // If receiptCategory is AutoStore or item isBackload then set item to stored
    // during creation
    if (receiptCategory.doAutoStoreOnReceipt || itemToInsert.isBackload) {
      itemToInsert =
        updateItemToInsertFromCategoryAutoStoreOnReceipt(
          receiptCategory,
          itemToInsert,
          siteProfile,
        );
      itemIsAutoStored = itemToInsert.isStored;
    }

    if (itemProperties.isAutomaticReceiptNumber) {
      CompanySiteProfiles
        .update({ _id: siteProfile._id }, { $inc: { 'configuration.receiptNoSequence': 1 } });
      log.info(`Incremented receipt number sequence for site profile ${siteProfile.identifier}`);
    } else {
      log.info('Manually entered receipt no, - skip incrementing receipt number sequence.');
    }

    log.info(`Registering new item ${JSON.stringify(itemToInsert, null, 2)}`);

    const insertedItemId = AbdnItems.insert(itemToInsert, (error) => {
      if (error) {
        log.error(`Insert of new item failed: error: <${error}>.`);
      } else if (itemIsAutoStored) {
        AbdnItemLocationReport.updateLocationReportForSitesClient(client, siteProfile.identifier);
      }
    });

    return (AbdnItems.findOne(insertedItemId));
  },
  'abdnItems.edit': function handleEdit(itemId, itemUpdates) {
    check(itemId, String);
    check(itemUpdates, Object);

    // Make sure the user is logged in before editing an item.
    const user = checkAndGetUser();
    const userSiteIdentifiers = getSiteIdentifiersForUser(user);

    log.info(`Item.Edit called by <${user.username}>- ItemId<${itemId}> updates${JSON.stringify(itemUpdates, null, 2)}`);

    // Load current Item to get the site-identifier.
    const currentItem = AbdnItems.findOne(itemId);
    const itemSiteIdentifier = currentItem.siteId;
    if (userSiteIdentifiers.indexOf(itemSiteIdentifier) < 0) {
      throw new Meteor.Error(
        'Not Authorised',
        'User is not authorised to edit this item.',
        `User site does not match item site <${itemSiteIdentifier}>.`,
      );
    }

    const siteProfile = CompanySiteProfiles.findOne({ identifier: itemSiteIdentifier });
    if (!siteProfile) {
      throw new Meteor.Error(
        'Not Found',
        'The site profile was not found.',
        `Site profile not found for <${itemSiteIdentifier}>. `,
      );
    }

    // Convert PropertyIds to string names.
    const itemValues = convertToNames(convertItemPropertyIdsToObjects(itemUpdates, siteProfile));
    log.info(`itemValues ${JSON.stringify(itemValues, null, 2)}`);
    _.extend(itemUpdates, itemValues);

    const updatedItem = AbdnItemUpdator.updateItem(
      itemId,
      itemUpdates,
      user.username,
      userSiteIdentifiers);

    return (updatedItem); // return the updated item.
  },
  'abdnItems.updatePackedProperties': function handleUpdate(packedProperties) {
    log.info('abdnItems.updatePackedProperties called - ' +
      `packedProperties: ${JSON.stringify(packedProperties, null, 2)}`);
    check(packedProperties, AbdnItemsSchemas.AbdnItemsUpdatePackedPropertiesMethodSchema);

    let updateProperties = null;

    if (packedProperties.isPacked) {
      const now = moment().utc();
      updateProperties = {
        $set: {
          isPacked: packedProperties.isPacked,
          packingUnit: packedProperties.packingUnit,
          packedDate: now.toDate(),
          packedDateStr: now.format(DB_DATE_STR_FORMAT),
        },
      };
    } else {
      updateProperties = {
        $set: {
          isPacked: packedProperties.isPacked,
          packingUnit: null,
          packedDate: null,
          packedDateStr: null,
        },
      };
    }
    AbdnItems.update(
      { _id: packedProperties.itemId },
      updateProperties,
      (error) => {
        if (error) {
          log.error('abdnItems.updatePackedProperties failed - '
            + `error: ${JSON.stringify(error, null, 2)}`);
        } else {
          log.info('abdnItems.updatePackedProperties completed.');
        }
      },
    );
  },
  'abdnItems.setPackedEvents': function handleSetPackedEvents(itemIds, requestId, packingUnitId) {
    log.info('abdnItems.setPackedEvents called - ' +
      `itemIds: <${JSON.stringify(itemIds, null, 2)}>.`);

    check(itemIds, [String]);
    check(requestId, String);
    check(packingUnitId, String);
    const user = checkAndGetUser();

    const items = AbdnItems.find({ _id: { $in: itemIds } }).fetch();
    _.each(items, (item) => {
      const previousPackedEvents = item.events
        .filter((event) =>
          event.eventType === AltensEventFactory.AltensItemEvents.PACKED
            && event.isDeleted === false);

      /* If item has previous PACKED event soft delete this */
      if (previousPackedEvents && previousPackedEvents.length > 0) {
        log.info(`Item <${item._id}>, `
          + 'has previous packed events. Soft deleting previous packed events.');
        AbdnItems.update(
          {
            _id: item._id,
            'events.eventType': AltensEventFactory.AltensItemEvents.PACKED,
            'events.isDeleted': false,
          },
          { $set: { 'events.$.isDeleted': true } },
        );
      }

      /* Update Item Events */
      const eventData = {
        requestId,
        packingUnitId,
      };

      AltensEventFactory.createSingularItemEventWithData(
        item._id,
        AltensEventFactory.AltensItemEvents.PACKED,
        item.packedDate,
        user.username,
        eventData,
      );
    });
  },
});
