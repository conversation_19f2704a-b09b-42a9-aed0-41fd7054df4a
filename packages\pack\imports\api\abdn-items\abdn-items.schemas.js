import { ItemEventSchema } from '../../shared/event-factory';
import SimpleSchema from 'simpl-schema';

export const AbdnItemsSchema = new SimpleSchema({
  siteId: {
    type: String,
    label: 'Site Identifier',
  },
  client: {
    type: Object,
    label: 'Client',
  },
  'client._id': String,
  'client.name': String,
  'client.logo': String,
  receiptNo: {
    type: String,
    label: 'Receipt Number',
  },
  receivedDate: {
    type: Date,
    label: 'Received Date',
  },
  receivedDateStr: {
    type: String,
    label: 'Received DateString',
  },
  receiptCategory: {
    type: String,
    label: 'Receipt Category', // Indicates what will happen to item after receipt stage
  },
  vendor: {
    type: String,
    label: 'Vendor',
  },
  deliveryNo: {
    type: String,
    label: 'Delivery Number',
  },
  poNo: {
    type: String,
    label: 'PO Number',
  },
  workOrderNo: {
    type: String,
    label: 'Work Order Number',
    optional: true,
  },
  offshoreLocation: {
    type: String,
    label: 'Offshore Location',
    optional: true,
  },
  isBackload: {
    type: Boolean,
    label: 'Is Backload',
    defaultValue: false,
  },
  materialNo: {
    type: String,
    label: 'Material No.',
    optional: true,
  },
  packageType: {
    type: String,
    label: 'Package Unit Reference',
  },
  quantity: {
    type: SimpleSchema.Integer,
    label: 'Quantity of Package',
  },
  packageContent: {
    type: String,
    label: 'Content of Package Reference',
    optional: true,
  },
  packageContentQuantity: {
    type: SimpleSchema.Integer,
    label: 'Package Contents Quantity',
    optional: true,
  },
  weightKg: {
    type: Number,
    label: 'Weight (in Kg)',
    optional: true,
  },
  unitCost: {
    type: Number,
    label: 'Unit Cost',
    optional: true,
  },
  unitCostCurrency: {
    type: String,
    label: 'Unit Cost Currency',
    optional: true,
  },
  description: {
    type: String,
    label: 'Item Description',
  },
  comments: {
    type: String,
    label: 'Comments',
    optional: true,
  },
  isDangerousGoods: {
    type: Boolean,
    label: 'Is Dangerous Goods',
  },
  dgClassification: {
    type: String,
    label: 'Dangerous Goods Classification',
    optional: true,
  },
  createdBy: {
    type: String,
    label: 'Created By',
  },
  createdAt: {
    type: Date,
    label: 'Created At',
  },
  updatedBy: {
    type: String,
    label: 'Updated By',
    optional: true,
  },
  updatedAt: {
    type: Date,
    label: 'Updated At',
    optional: true,
  },
  isStored: {
    type: Boolean,
    label: 'Is Stored',
    defaultValue: false,
  },
  location: {
    type: String,
    label: 'Location Item Stored At',
    optional: true,
  },
  subLocation: {
    type: String,
    label: 'Sub Location',
    optional: true,
  },
  storedDate: {
    type: Date,
    label: 'Stored Date',
    optional: true,
  },
  storedDateStr: {
    type: String,
    label: 'Stored DateString',
    optional: true,
  },
  isDeleted: {
    type: Boolean,
    label: 'Is Deleted',
    defaultValue: false,
  },
  isPacked: {
    type: Boolean,
    label: 'Is Packed',
    defaultValue: false,
  },
  packingUnit: {
    type: String,
    label: 'Packing Unit',
    optional: true,
  },
  packedDate: {
    type: Date,
    label: 'Packed Date',
    optional: true,
  },
  packedDateStr: {
    type: String,
    label: 'Packed DateString',
    optional: true,
  },
  requestId: {
    type: String,
    label: 'Request Id',
    optional: true,
  },
  isDispatched: {
    type: Boolean,
    label: 'Is Dispatched',
    defaultValue: false,
  },
  dispatchedDate: {
    type: Date,
    label: 'Dispatched Date',
    optional: true,
  },
  dispatchedDateStr: {
    type: String,
    label: 'Dispatched DateString',
    optional: true,
  },
  manifestNo: {
    type: String,
    label: 'Manifest Number',
    optional: true,
  },
  events: {
    type: Array,
    label: 'Events',
  },
  'events.$': {
    type: Object,
    blackbox: true,
  },
  importIndex: {
    type: Number,
    label: 'Index from data import',
    optional: true,
  },
  importFilename: {
    type: String,
    label: 'Filename from data import',
    optional: true,
  },
});

export const AbdnItemsAddItemMethodSchema = new SimpleSchema({
  clientId: {
    type: String,
    label: 'Client Id',
    min: 1,
  },
  receivedAtDateTime: {
    type: Date,
    label: 'Received At DateTime',
  },
  receiptNo: {
    type: String,
    label: 'Receipt Number',
    min: 1,
  },
  isAutomaticReceiptNumber: {
    type: Boolean,
    label: 'Is Automatic Receipt Number',
  },
  receiptCategoryId: {
    type: String,
    label: 'Receipt Category Id',
    min: 1,
  },
  vendorId: {
    type: String,
    label: 'Vendor Id',
    min: 1,
  },
  deliveryNo: {
    type: String,
    label: 'Delivery Number',
    min: 1,
  },
  poNo: {
    type: String,
    label: 'PO Number',
    min: 1,
  },
  workOrderNo: {
    type: String,
    label: 'Work Order Number',
    optional: true,
  },
  offshoreLocationId: {
    type: String,
    label: 'Offshore Location Id',
    optional: true,
  },
  isBackload: {
    type: Boolean,
    label: 'Is Backload',
  },
  materialNo: {
    type: String,
    label: 'Material Number',
    optional: true,
  },
  packageTypeId: {
    type: String,
    label: 'Package Type Id',
    min: 1,
  },
  quantity: {
    type: SimpleSchema.Integer,
    label: 'Quantity',
  },
  weightKg: {
    type: Number,
    label: 'Weight Kg',
    optional: true,
  },
  unitCost: {
    type: Number,
    label: 'Unit Cost',
    optional: true,
  },
  unitCostCurrencyId: {
    type: String,
    label: 'Unit Cost Currency Id',
    optional: true,
  },
  contentTypeId: {
    type: String,
    label: 'Content Type Id',
    optional: true,
  },
  contentQuantity: {
    type: SimpleSchema.Integer,
    label: 'Content Quantity',
    optional: true,
  },
  isDG: {
    type: Boolean,
    label: 'Is DG',
  },
  dgClassificationId: {
    type: String,
    label: 'DG Classification Id',
    optional: true,
    custom: function custom() {
      if (this.field('isDG').value && !this.value) {
        return 'missingDGClassification';
      }
    },
  },
  description: {
    type: String,
    label: 'Description',
    min: 1,
  },
  comments: {
    type: String,
    label: 'Comments',
    optional: true,
  },
});

export const AbdnItemsUpdatePackedPropertiesMethodSchema = new SimpleSchema({
  itemId: {
    type: String,
    label: 'LabelId',
  },
  isPacked: {
    type: Boolean,
    label: 'Is Packed',
  },
  packingUnit: {
    type: String,
    label: 'Packing Unit',
    optional: true,
  },
});
