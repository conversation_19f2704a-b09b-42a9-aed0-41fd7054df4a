import { AbdnItems, ItemsSelector } from '../abdn-items';
import { Match, check } from 'meteor/check';
import { Meteor } from 'meteor/meteor';
import { ReactiveTable } from 'meteor/fcallem:reactive-table-semantic';
import { Roles } from 'meteor/alanning:roles';
import { <PERSON> as log } from 'meteor/wylio:winston-papertrail';
import { utils } from '../../../shared/utils';

ReactiveTable.publish('abdnItems.receiptHistory', function publish() {
  return this.userId ? AbdnItems : [];
});

Meteor.publish('abdnItems.recentItems', function recentItems(pageSize, skipCount, clientId) {
  log.info('abdnItems.recentItems subscribed - ' +
    `pageSize: <${pageSize}>, skipCount: <${skipCount}>, clientId: <${clientId}>.`);
  check(pageSize, Number);
  check(skipCount, Number);
  check(clientId, Match.Maybe(String));

  if (this.userId) {
    const userSites = Roles.getGroupsForUser(this.userId);

    const selector = {
      isPacked: false,
      siteId: { $in: userSites },
    };

    // if clientId provided limit results to client
    if (clientId) {
      selector['client._id'] = clientId;
    }

    return AbdnItems.find(
      selector,
      {
        sort: { receivedDate: -1 },
        limit: pageSize,
        skip: skipCount,
      });
  }
  return [];
});

Meteor.publish('abdnItems.storedOrCanStore', function storedOrCanStore(
  clientId,
  query,
  limit,
  ignorePacked = false,
  storedItemsOnly = false,
  restrictToLocation = null,
) {
  log.info('abdnItems.storedOrCanStore subscribed - ' +
    `clientId: <${clientId}>, query: <${query}>, limit: <${limit}>, ` +
    `ignorePacked: <${ignorePacked}>, storedItemsOnly: <${storedItemsOnly}>, ` +
    `restrictToLocation: <${restrictToLocation}>.`);
  check(clientId, String);
  check(query, Match.Maybe(String));
  check(limit, Number);
  check(ignorePacked, Boolean);

  if (this.userId) {
    // TODO: Confirm user is allowed to see items for this clientId

    const selector = ItemsSelector.getItemsSelector(
      clientId,
      storedItemsOnly,
      restrictToLocation,
      query,
      ignorePacked,
    );

    return AbdnItems.find(selector, { sort: { receivedDate: -1 }, limit });
  }
  return [];
});

// Used in abdn-item-edit screen.
Meteor.publish('abdnItems.selectedItemForEdit', function (itemId) {
  log.info('abdnItems.selectedItemForEdit subscription - ' +
    `itemId: <${itemId}>.`);
  check(itemId, String);

  if (this.userId) {
    // Only return item if item is for this users site.
    const user = Meteor.users.findOne(this.userId);
    const userSites = [];
    const usersSite = user.profile.siteIdentifier;
    userSites.push(usersSite);

    return AbdnItems.find({
      _id: itemId,
      siteId: { $in: userSites },
    });
  }
  return [];
});

Meteor.publish(
  'abdnItems.forClientFiltered',
  function forClientFiltered(clientId, query, limit, ignoreDispatched = false) {
    log.info('abdnItems.forClientFiltered subscription - ' +
      `clientId: <${clientId}>, query: <${query}>, ` +
      ` limit: <${limit}>, ignoreDispatched: <${ignoreDispatched}>.`);
    check(clientId, String);
    check(query, String);
    check(limit, Number);
    check(ignoreDispatched, Boolean);

    if (this.userId) {
      const userSites = Roles.getGroupsForUser(this.userId);

      const selector = {
        $and: [{
          'client._id': clientId,
          siteId: { $in: userSites },
        }],
      };

      if (ignoreDispatched) selector.$and.push({ isDispatched: false });

      if (query && query.length > 2) {
        const querySelector = {
          $or: [
            { description: { $regex: utils.escapeRegExp(query), $options: 'i' } },
            { vendor: { $regex: utils.escapeRegExp(query), $options: 'i' } },
            { receiptNo: { $regex: utils.escapeRegExp(query), $options: 'i' } },
            { packageType: { $regex: utils.escapeRegExp(query), $options: 'i' } },
            { offshoreLocation: { $regex: utils.escapeRegExp(query), $options: 'i' } },
            { poNo: { $regex: utils.escapeRegExp(query), $options: 'i' } },
            { receiptCategory: { $regex: utils.escapeRegExp(query), $options: 'i' } },
          ],
        };
        // Push the $or operation onto the selector
        selector.$and.push(querySelector);
      }
      return AbdnItems.find(selector, { sort: { receivedDate: -1 }, limit });
    }

    return [];
  },
);
