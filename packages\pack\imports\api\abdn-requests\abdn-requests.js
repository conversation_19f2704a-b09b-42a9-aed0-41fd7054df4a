import { Meteor } from 'meteor/meteor';
import { AbdnRequestsSchema, DestinationSchema, AbdnRequestsCreateMethodSchema } from './abdn-requests.schemas';

export const AbdnRequests = new Meteor.Collection('abdnRequests');

export const AbdnRequestsSchemas = {
  AbdnRequestsSchema,
  DestinationSchema,
  AbdnRequestsCreateMethodSchema,
};

export const AbdnRequestsTimePeriodFilters = {
  ALL: 'ALL',
  LAST_THREE_DAYS: 'LAST_THREE_DAYS',
  NEXT_SEVEN_DAYS: 'NEXT_SEVEN_DAYS',
  THIS_MONTH: 'THIS_MONTH',
};

export const AbdnRequestsDestinationTypes = {
  VENDOR_WAREHOUSE: 'vendor-warehouse',
  OFFSHORE_LOCATION: 'offshore-location',
};

export const AbdnRequestsPackingUnitTypes = {
  VEHICLE: 'vehicle',
  CCU: 'ccu',
  CMR: 'cmr',
  BEGELEIDINGSBRIEF: 'begeleidingsbrief',
};

AbdnRequests.attachSchema(AbdnRequestsSchema);
