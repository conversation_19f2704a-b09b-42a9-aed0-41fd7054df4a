import { AbdnRequests, AbdnRequestsDestinationTypes, AbdnRequestsSchemas } from './abdn-requests';
import { Match, check } from 'meteor/check';
import { AltensEventFactory } from '../../shared/event-factory';
import { CompanySiteProfiles } from '../company-site-profiles/company-site-profiles';
import { DB_DATE_STR_FORMAT } from '../../shared/lib/constants';
import { Meteor } from 'meteor/meteor';
import { Roles } from 'meteor/alanning:roles';
import { <PERSON> as log } from 'meteor/wylio:winston-papertrail';
import { moment } from 'meteor/momentjs:moment';
import { utils } from '../../shared/utils';

const checkAndGetUser = () => {
  const user = Meteor.user();
  if (!user) {
    throw new Meteor.Error('not-authorized');
  }
  return user;
};

function getSiteIdentifiersForUser(user) {
  const userSites = [];
  const usersSite = user.profile.siteIdentifier;
  userSites.push(usersSite);
  return userSites;
}

const generateAndUpdateRequestIdentifiers = (scheduledDateTime, siteId) => {
  const scheduledDateStr = moment(scheduledDateTime).format(DB_DATE_STR_FORMAT);
  const requestsOnSameDay = AbdnRequests.find(
    {
      scheduledDateStr,
      siteId,
    },
    { sort: { scheduledDateTime: 1 } },
  ).fetch();
  if (requestsOnSameDay.length > 0) {
    return requestsOnSameDay.length + 1;
  }
  return 1;
};

const addDaysIgnoringWeekends = (date) => {
  date.add(1, 'days');
  while (date.isoWeekday() === 6 || date.isoWeekday() === 7) {
    date.add(1, 'days');
  }
};

const addOccurencesInDaysPerWeek = (date, daysPerWeek) => {
  switch (daysPerWeek) {
    case 1:
      date.add(7, 'days');
      break;
    case 5:
      addDaysIgnoringWeekends(date);
      break;
    case 7:
      date.add(1, 'days');
      break;
    // no default
  }
};

const padToTwo = (no) => (no <= 99 ? ('0' + no).slice(-2) : no);

const createPackingRequestRefNo = (date, requestNo) => {
  const dateObj = moment(date);
  const requestNoStr = utils.isNumeric(requestNo) ? padToTwo(requestNo) : requestNo.slice(1);
  return `PRF${dateObj.format('YY')}/${dateObj.format('MM/DD')}-${requestNoStr}`;
};

const getDestination = (destinationId, destinationType, siteProfile) => {
  switch (destinationType) {
    case AbdnRequestsDestinationTypes.VENDOR_WAREHOUSE: {
      // Get a list of warehouses
      const warehouses = _.flatten(_.map(
        siteProfile.configuration.vendors,
        (vendor) => vendor.warehouses,
      ));
      return _.find(warehouses, (warehouse) => warehouse._id === destinationId);
    }
    case AbdnRequestsDestinationTypes.OFFSHORE_LOCATION: {
      const offshoreLocations = siteProfile.configuration.offshoreLocations;
      return _.find(
        offshoreLocations,
        (offshoreLocation) => offshoreLocation._id === destinationId,
      );
    }
    // no default
  }
};

Meteor.methods({
  'abdnRequests.create': function handleCreate(requestProperties) {
    check(requestProperties, AbdnRequestsSchemas.AbdnRequestsCreateMethodSchema);

    // Make sure the user is logged in before adding an item.
    const user = checkAndGetUser();
    const userSiteIdentifiers = getSiteIdentifiersForUser(user);

    log.info(`abdnRequests.create called by <${user.username}> - `
             + `request properties ${JSON.stringify(requestProperties, null, 2)}`);

    // Get site for which request should be created.
    const providedSiteIdentifier = requestProperties.siteIdentifier;
    if (userSiteIdentifiers.indexOf(providedSiteIdentifier) < 0) {
      throw new Meteor.Error(
        'Not Authorised',
        'User is not authorised to create a request for this site.',
        `User site does not request site <${providedSiteIdentifier}>.`,
      );
    }

    const siteProfile = CompanySiteProfiles.findOne({ identifier: { $in: userSiteIdentifiers } });

    if (!siteProfile) {
      throw new Meteor.Error(500, 'Error 500: Not found', 'the site does not exist');
    }

    const siteDestination = getDestination(
      requestProperties.destinationId,
      requestProperties.destinationType,
      siteProfile,
    );
    if (!siteDestination) {
      log.info(JSON.stringify(siteDestination, null, 2));
      throw new Meteor.Error(500, 'Error 500: Not found', 'the destination does not exist');
    }
    const destination = {
      name: siteDestination.name,
      destinationType: requestProperties.destinationType,
    };

    const client = siteProfile.configuration.clients
      .find((clients) => clients._id === requestProperties.clientId);

    const scheduledDateTime = moment(requestProperties.scheduledDateTime);

    const siteId = providedSiteIdentifier;
    const requestIdentifier = generateAndUpdateRequestIdentifiers(scheduledDateTime, siteId);

    const packingRequestRefNo = createPackingRequestRefNo(scheduledDateTime, requestIdentifier);

    const requestToCreate = {
      siteId,
      client,
      requestIdentifier,
      repeatOfRequestId: null,
      packingRequestRefNo,
      destinations: [destination],
      scheduledDate: scheduledDateTime.toDate(),
      scheduledDateStr: scheduledDateTime.format(DB_DATE_STR_FORMAT),
      packingUnits: [],
      createdAt: moment().utc().toDate(),
      createdBy: user.username,
      updatedAt: null,
      updatedBy: null,
    };

    log.info('abdnRequests.create - generated object to insert'
    + `${JSON.stringify(requestToCreate, null, 2)}`);

    AbdnRequests.insert(requestToCreate, (error, createdAbdnRequestId) => {
      if (!error) {
        log.info(`abdnRequests.create - Inserted new request <${createdAbdnRequestId}>.`);
        if (requestProperties.isRepeated) {
          const daysBetweenOccurences = requestProperties.occurenceInDaysPerWeek;
          const repeatUntil = requestProperties.repeatUntil;
          addOccurencesInDaysPerWeek(scheduledDateTime, daysBetweenOccurences);
          while (!scheduledDateTime.isAfter(repeatUntil)) {
            requestToCreate.requestIdentifier =
              generateAndUpdateRequestIdentifiers(scheduledDateTime);
            requestToCreate.scheduledDate = scheduledDateTime.toDate();
            requestToCreate.repeatOfRequestId = createdAbdnRequestId;
            AbdnRequests.insert(requestToCreate);
            addOccurencesInDaysPerWeek(scheduledDateTime, daysBetweenOccurences);
          }
        }
      } else {
        log.error(`abdnRequests.create - Failed to insert request: ${error}`);
      }
    });
  },
  'abdnRequests.assignUnit': function handleAssignUnit(requestId, unitType, unitIdentifier) {
    log.info('abdnRequests.assignUnit called - '
      + `requestId: <${requestId}>, unitType: <${unitType}>, unitIdentifier: <${unitIdentifier}>.`);

    check(requestId, String);
    check(unitType, String);
    check(unitIdentifier, String);

    const user = checkAndGetUser();
    const userSites = Roles.getGroupsForUser(user);

    const packingUnit = {
      _id: new Mongo.ObjectID()._str,
      identifier: unitIdentifier,
      unitType,
      items: [],
      isClosed: false,
    };

    AbdnRequests.update(
      { _id: requestId, siteId: { $in: userSites } },
      { $push: { packingUnits: packingUnit } },
      (error) => {
        if (error) {
          log.error(`abdnRequests.assignUnit failed - ${error}`);
        } else {
          log.info(`abdnRequests.assignUnit - successfully assign unit to request <${requestId}>.`);
        }
      },
    );
  },
  'abdnRequests.openUnit': function handleOpenUnit(requestId, packingUnitId) {
    log.info('abdnRequests.openUnit called - '
      + `requestId: <${requestId}>, packingUnitId: <${packingUnitId}>.`);

    check(requestId, String);
    check(packingUnitId, String);

    const user = checkAndGetUser();
    const userSites = Roles.getGroupsForUser(user);

    AbdnRequests.update(
      {
        _id: requestId,
        'packingUnits.identifier': packingUnitId,
        siteId: { $in: userSites },
      },
      { $set: { 'packingUnits.$.isClosed': false } },
      (error) => {
        if (error) {
          log.error(`abdnRequests.openUnit failed - ${error}`);
        } else {
          log.info(`abdnRequests.openUnit completed - ${packingUnitId} now open.`);
        }
      },
    );
  },
  'abdnRequests.closeUnit': function handleCloseUnit(requestId, packingUnitId) {
    log.info('abdnRequests.closeUnit called - '
      + `requestId: <${requestId}>, packingUnitId: <${packingUnitId}>.`);

    check(requestId, String);
    check(packingUnitId, String);

    const user = checkAndGetUser();
    const userSites = Roles.getGroupsForUser(user);

    AbdnRequests.update(
      {
        _id: requestId,
        'packingUnits.identifier': packingUnitId,
        siteId: { $in: userSites },
      },
      { $set: { 'packingUnits.$.isClosed': true } },
      (error) => {
        if (error) {
          log.error(`abdnRequests.closeUnit failed - ${error}`);
        } else {
          log.info(`abdnRequests.close Unit completed - ${packingUnitId} now closed.`);
          const request = AbdnRequests.findOne({ _id: requestId });
          const unit = request.packingUnits
            .find((unit) => unit.identifier === packingUnitId);
          Meteor.call('abdnItems.setPackedEvents', unit.items, requestId, unit.identifier);
        }
      },
    );
  },
  'abdnRequests.packItem': function handlePackItem(requestId, packingUnitId, itemId) {
    log.info('abdnRequests.packItem called - '
      + `requestId: <${requestId}>, packingUnitId: <${packingUnitId}>, itemId: <${itemId}>.`);

    check(requestId, String);
    check(packingUnitId, String);
    check(itemId, String);

    const user = checkAndGetUser();
    const userSites = Roles.getGroupsForUser(user);

    AbdnRequests.update(
      {
        _id: requestId,
        siteId: { $in: userSites },
        'packingUnits.identifier': packingUnitId,
      },
      { $push: { 'packingUnits.$.items': itemId } },
      (error) => {
        if (error) {
          log.error(`abdnRequests.packItem failed - ${error}.`);
        } else {
          log.info(`abdnRequests.packItem completed - ${itemId} now packed.`);
          const updateItemProperties = {
            itemId,
            isPacked: true,
            packingUnit: packingUnitId,
          };
          Meteor.call('abdnItems.updatePackedProperties', updateItemProperties);
        }
      },
    );
  },
  'abdnRequests.unpackItem': function handleUnpackItem(requestId, packingUnitId, itemId) {
    log.info('abdnRequests.unpackItem called - '
      + `requestId: <${requestId}>, packingUnitId: <${packingUnitId}>, itemId: <${itemId}>.`);

    check(requestId, String);
    check(packingUnitId, String);
    check(itemId, String);

    const user = checkAndGetUser();
    const userSites = Roles.getGroupsForUser(user);

    AbdnRequests.update(
      {
        _id: requestId,
        siteId: { $in: userSites },
        'packingUnits.identifier': packingUnitId,
      },
      { $pull: { 'packingUnits.$.items': itemId } },
      (error) => {
        if (error) {
          log.error(`abdnRequests.unpackItem failed - ${error}.`);
        } else {
          log.info(`abdnRequests.unpackItem completed - ${itemId} now unpacked.`);
          const updateItemProperties = {
            itemId,
            isPacked: false,
          };
          Meteor.call('abdnItems.updatePackedProperties', updateItemProperties);
        }
      },
    );
  },
});
