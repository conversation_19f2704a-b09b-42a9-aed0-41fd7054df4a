import SimpleSchema from 'simpl-schema';
import { AbdnRequestsDestinationTypes } from '../../api/abdn-requests/abdn-requests';

export const DestinationSchema = new SimpleSchema({
  name: {
    type: String,
    label: 'Destination Name',
  },
  destinationType: {
    type: String,
    label: 'Destination Type',
  },
});

export const PackingUnitSchema = new SimpleSchema({
  _id: {
    type: String,
    label: 'Name',
    defaultValue: new Mongo.ObjectID()._str,
  },
  identifier: {
    type: String,
    label: 'Name',
  },
  unitType: {
    type: String,
    label: 'Unit Type',
  },
  isClosed: {
    type: Boolean,
    label: 'Is Closed',
    defaultValue: false,
  },
  items: {
    type: Array,
    label: 'Items',
  },
  'items.$': String,
});

export const AbdnRequestsSchema = new SimpleSchema({
  siteId: {
    type: String,
    label: 'Site Identifier',
  },
  client: {
    type: Object,
    label: 'Client',
  },
  'client._id': String,
  'client.name': String,
  'client.logo': String,
  requestIdentifier: {
    type: String,
    label: 'Request Identifier',
  },
  repeatOfRequestId: {
    type: String,
    label: 'Repeat of Request Id',
    optional: true,
  },
  packingRequestRefNo: {
    type: String,
    label: 'Packing Request Ref No',
  },
  destinations: {
    type: Array,
    label: 'Destinations',
  },
  'destinations.$': DestinationSchema,
  scheduledDate: {
    type: Date,
    label: 'Scheduled Date',
  },
  scheduledDateStr: {
    type: String,
    label: 'Scheduled Date Str',
  },
  packingUnits: {
    type: Array,
    label: 'Packing Units',
  },
  'packingUnits.$': PackingUnitSchema,
  createdAt: {
    type: Date,
    label: 'Created At',
  },
  createdBy: {
    type: String,
    label: 'Created By',
  },
  updatedAt: {
    type: Date,
    label: 'Updated At',
    optional: true,
  },
  updatedBy: {
    type: String,
    label: 'Updated By',
    optional: true,
  },
  isDeleted: {
    type: Boolean,
    label: 'Is Deleted',
    defaultValue: false,
  },
});

export const AbdnRequestsCreateMethodSchema = new SimpleSchema({
  siteIdentifier: {
    type: String,
    label: 'Site Identifier',
  },
  clientId: {
    type: String,
    label: 'Client Id',
  },
  destinationId: {
    type: String,
    label: 'Destination Id',
  },
  destinationType: {
    type: String,
    label: 'Destination Type',
    custom: function custom() {
      const destTypeValues = _.values(AbdnRequestsDestinationTypes);
      if (!_.contains(destTypeValues, this.value)) {
        return 'invalidDestinationType';
      }
    },
  },
  scheduledDateTime: {
    type: Date,
    label: 'Scheduled DateTime',
  },
  isRepeated: {
    type: Boolean,
    label: 'Is Repeated',
  },
  occurenceInDaysPerWeek: {
    type: Number,
    label: 'Occurence In Days Per Week',
  },
  repeatUntilDate: {
    type: Date,
    label: 'Repeat Until Date',
    optional: true,
  },
});
