import { AbdnRequests, AbdnRequestsTimePeriodFilters } from '../abdn-requests';
import { Match, check } from 'meteor/check';
import { AbdnItems } from '../../abdn-items/abdn-items';
import { Meteor } from 'meteor/meteor';
import { Roles } from 'meteor/alanning:roles';
import { <PERSON> as log } from 'meteor/wylio:winston-papertrail';
import { utils } from '../../../shared/utils';

Meteor.publish('siteAbdnRequests', function siteAbdnRequests(timePeriod, clientId, refNoOrDestination) {
  log.info('siteAbdnRequests <subscription> - ' +
    `parameters: timePeriod<${timePeriod}>, refNo<${refNoOrDestination}>.`);
  check(timePeriod, String);
  check(clientId, String);
  check(refNoOrDestination, Match.Maybe(String));

  if (this.userId) {
    const userSites = Roles.getGroupsForUser(this.userId);

    const endOfToday = moment().utc().endOf('day').toDate();
    const threeDaysAgo = moment(endOfToday).subtract(3, 'd').toDate();
    const sevenDaysFromNow = moment(endOfToday).add(7, 'd').toDate();
    const endOfMonth = moment(endOfToday).add(1, 'M').toDate();

    const selector = {
      siteId: { $in: userSites },
      'client._id': clientId,
    };

    switch (timePeriod) {
      case AbdnRequestsTimePeriodFilters.ALL: break;
      case AbdnRequestsTimePeriodFilters.LAST_THREE_DAYS:
        selector.scheduledDate = { $gte: threeDaysAgo, $lte: endOfToday };
        break;
      case AbdnRequestsTimePeriodFilters.NEXT_SEVEN_DAYS:
        selector.scheduledDate = { $gte: threeDaysAgo, $lte: sevenDaysFromNow };
        break;
      case AbdnRequestsTimePeriodFilters.THIS_MONTH:
        selector.scheduledDate = { $gte: threeDaysAgo, $lte: endOfMonth };
        break;
      // no default
    }

    if (refNoOrDestination) {
      selector.$or = [
        { packingRequestRefNo: { $regex: utils.escapeRegExp(refNoOrDestination), $options: 'i' } },
        { 'destinations.name': { $regex: utils.escapeRegExp(refNoOrDestination), $options: 'i' } },
      ];
    }

    return AbdnRequests.find(selector);
  }
  return [];
});

Meteor.publish('activeAbdnRequest', function activeAbdnRequest(requestId) {
  check(requestId, String);

  if (this.userId) {
    const userSites = Roles.getGroupsForUser(this.userId);

    return AbdnRequests.find({ _id: requestId, siteId: { $in: userSites } });
  }
  return [];
});

Meteor.publishComposite('activeAbdnRequestAndItems', function activeAbdnRequestAndItems(requestId, packingUnitId) {
  check(requestId, String);
  log.info(`activeAbdnRequestAndItems subsription - requestId: <${requestId}>.`);
  if (this.userId) {
    const userSites = Roles.getGroupsForUser(this.userId);

    return {
      find: function getActiveAbdnRequest() {
        return AbdnRequests.find({
          _id: requestId,
          siteId: { $in: userSites },
        }, { limit: 1 });
      },
      children: [
        {
          find: function getItemsPackedWithAbdnRequest(abdnRequest) {
            const unit = abdnRequest.packingUnits
              .find((packingUnit) => packingUnit.identifier === packingUnitId);
            const items = unit ? unit.items : [];
            return AbdnItems.find(
              {
                _id: { $in: items },
                siteId: { $in: userSites },
              },
            );
          },
        },
      ],
    };
  }
  return [];
});
