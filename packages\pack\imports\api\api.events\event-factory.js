/* eslint-disable max-classes-per-file */
import { Errors } from '../api.helpers/errors';
import { EventsHelpers } from './events.helpers';
import { Meteor } from 'meteor/meteor';
import moment from 'moment';

const Events = {
  Item: {
    RECEIVED: 'RECEIVED',
    RECEIPTED: 'RECEIPTED',
    STORED: 'STORED',
    UNSTORED: 'UNSTORED',
    PACKED: 'PACKED',
    UNPACKED: 'UNPACKED',
    DISPATCHED: 'DISPATCHED',
    DELETED: 'DELETED',
    EDITED: 'EDITED',
  },
  PurchaseOrders: {
    RECEIVED: 'RECEIVED',
    ITEMRECEIPTED: 'ITEMRECEIPTED',
    EDITED: 'EDITED',
  },
};

class Event {
  constructor(eventType, dateTime, createdBy, createdAt) {
    if (!EventsHelpers.isValidEventType(eventType)) {
      throw new Meteor.Error(Errors.notFound, `The eventType is not recognised: ${eventType}`);
    }

    this.eventType = eventType;
    this.eventDateTime = dateTime;
    this.createdBy = createdBy;
    this.createdAt = createdAt;
    this.isDeleted = false;
  }
}

class ItemEvent extends Event {
  constructor(eventType, dateTime, createdBy, createdAt, eventData = null) {
    super(eventType, dateTime, createdBy, createdAt);
    this.eventData = {};

    if (eventData) {
      Object.keys(eventData).forEach((key) => { this.eventData[key] = eventData[key]; });
    }
  }
}

class ItemEventCreator {
  constructor(eventType, dateTime, createdBy, eventData = null) {
    this.eventType = eventType;
    this.eventDateTime = dateTime;
    this.createdBy = createdBy;
    this.eventData = eventData;
  }

  createEvent() {
    const createdAt = moment().utc().toDate();

    const event = new ItemEvent(
      this.eventType,
      this.eventDateTime,
      this.createdBy,
      createdAt,
      this.eventData,
    );

    return Object.assign({}, event);
  }
}

const createItemEvent = (eventType, dateTime, createdBy, eventData = null) => {
  const event = new ItemEventCreator(eventType, dateTime, createdBy, eventData)
    .createEvent();

  return event;
};

export const EventFactory = {
  Events,
  createItemEvent,
};
