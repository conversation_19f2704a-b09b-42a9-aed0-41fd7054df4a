import { EventFactory } from './event-factory';

const checkEventTypes = (goal, eventTypes, key = null) => {
  if (key) {
    const isGoal = eventTypes[key] === goal;

    if (isGoal) {
      return true;
    }
    if (typeof eventTypes[key] === 'object') {
      // If the property is an object: go deeper
      return checkEventTypes(goal, eventTypes[key]);
    }
    // Otherwise property is a value but not the goal
    return false;
  }

  // If no key is provided get the objects keys
  for (const pub of Object.keys(eventTypes)) {
    // Loop through keys looking to see if goal exists
    if (checkEventTypes(goal, eventTypes, pub)) {
      // Break immediately if true
      return true;
    }
  }

  // Worse case doesn't exist
  return false;
};

export const EventsHelpers = {
  isValidEventType(eventType) {
    return checkEventTypes(eventType, EventFactory.Events);
  },
};
