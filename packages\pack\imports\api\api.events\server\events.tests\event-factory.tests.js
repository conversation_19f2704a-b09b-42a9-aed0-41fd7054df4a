/* eslint-env mocha */
/* eslint-disable func-names, prefer-arrow-callback */
import { EventFactory } from '../../event-factory';
import { TestUtils } from '../../../api.helpers/test-utils';
import { chai } from 'meteor/practicalmeteor:chai';
import moment from 'moment';

const createItemEvent = (params = {}) => EventFactory.createItemEvent(
  params.eventType || EventFactory.Events.Item.RECEIVED,
  params.eventDateTime || moment().utc().toDate(),
  params.createdBy || 'TEST-USER',
  params.eventData,
);

describe('EventFactory', function () {
  describe('createItemEvent', function () {
    beforeEach(function () {
      TestUtils
        .resetMockedDate();
    });

    it('creates item event with specified event type', function () {
      // Arrange
      const eventType = EventFactory.Events.Item.PACKED;

      // Act
      const event = createItemEvent({ eventType });

      // Assert
      chai.assert.equal(event.eventType, eventType);
    });

    it('creates item event with specified datetime', function () {
      // Arrange
      const eventDateTime = moment().utc().toDate();

      // Act
      const event = createItemEvent({ eventDateTime });

      // Assert
      chai.assert.equal(event.eventDateTime, eventDateTime);
    });

    it('creates item event with specified createdBy user', function () {
      // Arrange
      const createdBy = 'SOME-RANDOM-EVENT-CREATOR';

      // Act
      const event = createItemEvent({ createdBy });

      // Assert
      chai.assert.equal(event.createdBy, createdBy);
    });

    it('sets the createdAt time to the current time', function () {
      // Arrange
      const now = moment().utc().toDate();

      TestUtils
        .stubMomentAndJSDateNow(now);

      // Act
      const event = createItemEvent();

      // Assert
      chai.assert.isTrue(moment(event.createdAt).isSame(moment(now)));
    });

    it('creates item event with specified eventData', function () {
      // Arrange
      const eventData = {
        location: 'SOME-TEST-LOCATION',
      };

      // Act
      const event = createItemEvent({ eventData });

      // Assert
      chai.assert.isTrue(event.eventData.location !== null && event.eventData.location !== undefined, 'Expect eventData.location to exist.');
      chai.assert.equal(event.eventData.location, eventData.location);
    });

    it('throws error if the eventType is not valid', function () {
      // Arrange
      const eventType = 'SOME-DEFOS-NOT-VALID-EVENT-TYPE';

      // Act & Assert
      chai.assert.throws(() => createItemEvent({ eventType }), Meteor.Error);
    });
  });
});
