import { CompanySiteProfiles } from '../company-site-profiles/company-site-profiles';
import { Factory } from 'meteor/dburles:factory';
import { TestUtils } from '../api.helpers/test-utils';

export const testSiteProfile = Object.freeze({
  identifier: 'test-company-site-profile',
});

export const testVendor = Object.freeze({
  _id: TestUtils.id(),
  name: '80:20',
  warehouses: [
    {
      _id: TestUtils.id(),
      name: '80:20 PROCUREMENT SERVICES NL B.V.',
      address: null,
    },
    {
      _id: TestUtils.id(),
      name: '80:20 PROCUREMENT SERVICES NL B.V.',
      address: {
        addressLine1: 'Handelsweg 1',
        city: 'Den Helder',
        country: null,
        postCode: '1785AE',
      },
    },
  ],
});

export const testClient = Object.freeze({
  _id: TestUtils.id(),
  name: 'Shell',
  logo: 'shell.png',
});

export const testReceiptCategory = Object.freeze({
  _id: TestUtils.id(),
  name: 'Stock',
  doAutoStoreOnReceipt: false,
});

export const testOffshoreLocation = Object.freeze({
  _id: TestUtils.id(),
  name: 'Test Offshore Location',
  company: 'Shell',
});

export const testCustomsStatus = Object.freeze({
  _id: TestUtils.id(),
  name: 'FCG',
});

export const testPackageType = Object.freeze({
  _id: TestUtils.id(),
  name: 'Box',
});

export const testReceiptLocation = Object.freeze({
  _id: TestUtils.id(),
  name: 'Test Receipt Location',
});

Factory.define('companySiteProfile', CompanySiteProfiles, {
  identifier: testSiteProfile.identifier,
  displayName: 'Test Company Site Profile',
  configuration: {
    receiptNoSequence: 0,
    receiptNoFormatStr: 'DDMMYY',
    receipting: {
      preReceiptByPo: false,
      receiptStages: [
        {
          templateName: 'aobPreReceipt',
          position: 0,
          type: 'pre-receipt',
          canSubmit: true,
        },
        {
          templateName: 'aobReceipt',
          position: 1,
          type: 'partial-receipt',
          canSubmit: true,
        },
      ],
    },
    clients: [
      testClient,
    ],
    packageTypes: [
      testPackageType,
    ],
    offshoreLocations: [
      testOffshoreLocation,
    ],
    vendors: [
      testVendor,
    ],
    receiptCategories: [
      testReceiptCategory,
    ],
    storageLocations: [
      {
        _id: TestUtils.id(),
        name: 'Stock',
        hasShelfLocations: true,
      },
    ],
    currencies: [
      {
        _id: TestUtils.id(),
        name: 'GBP',
      },
    ],
    dgClassifications: [
      {
        _id: TestUtils.id(),
        name: 'Explosive',
      },
    ],
    customsStatuses: [
      testCustomsStatus,
    ],
    ncrs: [
      {
        _id: TestUtils.id(),
        name: 'No dangerous goods label',
      },
    ],
    printers: [
      {
        _id: TestUtils.id(),
        printerIp: 'Test Printer Ip',
        name: 'Test Printer Name',
      },
    ],
    receiptLocations: [
      testReceiptLocation,
    ],
  },
});
