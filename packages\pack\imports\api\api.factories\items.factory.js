import {
  testClient,
  testCustomsStatus,
  testOffshoreLocation,
  testPackageType,
  testReceiptCategory,
  testReceiptLocation,
  testSiteProfile,
  testVendor,
} from
  './company-site-profile.factory';
import { Factory } from 'meteor/dburles:factory';
import { Items } from '../items/items';
import { ReceiptTypes } from '../items/receipt.types';
import faker from 'faker';

Factory.define('aobPreReceipt', Items, {
  receiptNo: faker.random.alphaNumeric(),
  receiptLocation: testReceiptLocation.name,
  receivedDate: new Date(),
  poNo: faker.random.alphaNumeric(),
  vendor: testVendor.name,
  vendorDeliveryNo: faker.random.alphaNumeric(),
  receiptType: ReceiptTypes.aobPreReceipt,
  siteId: testSiteProfile.identifier,
  client: () => testClient,
  events: [],
  noOfPoLinesReceived: 1,
});

Factory.define('aobReceipt', Items, {
  receiptCategory: testReceiptCategory.name,
  offshoreLocation: testOffshoreLocation.name,
  isBackload: false,
  isQa: false,
  isYard: false,
  poNo: faker.random.alphaNumeric(),
  poLineNo: faker.random.number(),
  deliveryNo: faker.random.alphaNumeric(),
  customsStatus: testCustomsStatus.name,
  workOrderNo: faker.random.alphaNumeric(),
  packageType: testPackageType.name,
  quantity: faker.random.number(),
  weightKg: faker.random.number(),
  description: faker.lorem.sentence(),
  ncrs: [],
  receiptType: ReceiptTypes.aobReceipt,
});

Factory.define('aobFullReceipt', Items, {
  receiptNo: faker.random.alphaNumeric(),
  receiptLocation: testReceiptLocation.name,
  receivedDate: new Date(),
  poNo: faker.random.alphaNumeric(),
  vendor: testVendor.name,
  vendorDeliveryNo: faker.random.alphaNumeric(),
  receiptType: ReceiptTypes.aobReceipt,
  siteId: testSiteProfile.identifier,
  client: () => testClient,
  events: [],
  noOfPoLinesReceived: 1,
  receiptCategory: testReceiptCategory.name,
  offshoreLocation: testOffshoreLocation.name,
  isBackload: false,
  isQa: false,
  isYard: false,
  poLineNo: faker.random.number(),
  deliveryNo: faker.random.alphaNumeric(),
  customsStatus: testCustomsStatus.name,
  workOrderNo: faker.random.alphaNumeric(),
  packageType: testPackageType.name,
  quantity: faker.random.number(),
  weightKg: faker.random.number(),
  description: faker.lorem.sentence(),
  ncrs: [],
});

Factory.define('chemPreReceipt', Items, {
  receiptNo: faker.random.alphaNumeric(),
  receiptLocation: testReceiptLocation.name,
  receivedDate: new Date(),
  poNo: faker.random.alphaNumeric(),
  vendor: testVendor.name,
  vendorDeliveryNo: faker.random.alphaNumeric(),
  receiptType: ReceiptTypes.chemPreReceipt,
  siteId: testSiteProfile.identifier,
  client: () => testClient,
  events: [],
  noOfPoLinesReceived: 1,
});

Factory.define('chemReceipt', Items, {
  receiptCategory: testReceiptCategory.name,
  offshoreLocation: testOffshoreLocation.name,
  isBackload: false,
  isQa: false,
  isYard: false,
  poNo: faker.random.alphaNumeric(),
  poLineNo: faker.random.number(),
  deliveryNo: faker.random.alphaNumeric(),
  customsStatus: testCustomsStatus.name,
  workOrderNo: faker.random.alphaNumeric(),
  packageType: testPackageType.name,
  quantity: faker.random.number(),
  weightKg: faker.random.number(),
  description: faker.lorem.sentence(),
  ncrs: [],
  receiptType: ReceiptTypes.chemReceipt,
});
