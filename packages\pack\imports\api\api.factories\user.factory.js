import { Factory } from 'meteor/dburles:factory';
import { Meteor } from 'meteor/meteor';
import faker from 'faker';
import { testSiteProfile } from './company-site-profile.factory';

Factory.define('testAdminUser', Meteor.users, {
  username: 'default-test-user',
  profile: {
    siteIdentifier: testSiteProfile.identifier,
  },
  roles: {
    [testSiteProfile.identifier]: [
      'admin',
    ],
  },
  emails: [{
    address: faker.internet.email(),
    verified: false,
  }],
});
