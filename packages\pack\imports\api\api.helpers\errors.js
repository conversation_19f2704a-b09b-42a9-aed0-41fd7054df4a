import { Log } from './log';
import { Meteor } from 'meteor/meteor';

export const Errors = {
  types: {
    notLoggedIn: 'The user is not logged in.',
    noAccessToSite: 'The user is not allowed to access the site.',
    notFound: 'The entity was not found.',
    commandFailed: 'The command failed.',
  },

  throw(error, details) {
    Log.error(`${error} - ${details}`);
    throw new Meteor.Error(error, details);
  },
};
