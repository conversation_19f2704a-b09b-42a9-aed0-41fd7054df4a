import { <PERSON><PERSON><PERSON><PERSON> } from 'meteor/ejson';
import { <PERSON> as log } from 'meteor/wylio:winston-papertrail';

const logMsg = (level, msg, obj = null) => {
  if (obj) {
    let msgWithObj = msg + ' <' + JSON.stringify(obj) + '>';
    if (Object.keys(obj).length < 20) {
      msgWithObj = msg + '<' + JSON.stringify(obj, null, 3) + '>';
    }
    log[level](msgWithObj);
  } else {
    log[level](msg);
  }
};

const argsToStr = (args) => {
  if (args) {
    return EJSON.stringify(args);
  }
  return 'NO ARGS';
};

export const Log = {
  error(msg, obj = null) {
    logMsg('error', msg, obj);
  },
  debug(msg, obj = null) {
    logMsg('debug', msg, obj);
  },
  info(msg, obj = null) {
    logMsg('info', msg, obj);
  },
  command(name, args) {
    this.info(`Command: <${name}> called with args: <${argsToStr(args)}>.`);
  },
  query(name, args) {
    this.info(`Query: <${name}> called with args: <${argsToStr(args)}>.`);
  },
  publication(name, args) {
    this.info(`Publication: <${name}> called with args: <${argsToStr(args)}>.`);
  },
};
