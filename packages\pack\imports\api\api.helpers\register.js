import { EventDispatcher } from '../api.events/event-dispatcher';
import { EventsHelpers } from '../api.events/events.helpers';
import { Log } from './log';
import { Meteor } from 'meteor/meteor';
import { PublicationHelpers } from '../api.publications/helpers';
import SimpleSchema from 'simpl-schema';

const registerPublicationCommand = {
  publication: Object,
  'publication.name': {
    type: String,
    custom: function isRegisteredPublication() {
      if (!PublicationHelpers.isRegistered(this.value)) {
        return 'publication_not_registered';
      }

      return undefined;
    },
  },
  'publication.validate': Function,
  'publication.run': Function,
};

const registerPublication = {
  validate(args) {
    new SimpleSchema(registerPublicationCommand)
      .validate(args);
  },

  run(publication) {
    Log.info(`Registering Publication ${publication.name}.`);

    this.validate({ publication });

    Meteor.publish(publication.name, function wrapPublication(pubQuery) {
      Log.publication(publication.name, pubQuery);

      if (Meteor.userId()) {
        publication.validate(pubQuery);
        return publication.run(pubQuery);
      }

      Log.error(`Unknown user attempted to subscribe to publication: ${publication.name}, ignoring request.`);

      return [];
    });
  },
};

const registerCmdCommand = {
  command: Object,
  'command.name': String,
  'command.validate': Function,
  'command.run': Function,
  'command.call': Function,
  'command.allowInBackground': {
    type: Boolean,
    optional: true,
  },
};

const registerCommand = {
  validate(args) {
    new SimpleSchema(registerCmdCommand)
      .validate(args);
  },

  run(command) {
    Log.info(`Registering Command ${command.name}.`);

    this.validate({ command });

    Meteor.methods({
      [command.name](args) {
        Log.command(command.name, args);

        if (Meteor.userId() || command.allowInBackground) {
          command.validate.call(this, args);
          return command.run.call(this, args);
        }

        Log.error(`Unknown user attempted to run command: ${command.name}.`);
      },
    });
  },
};

const registerQueryCommand = {
  query: Object,
  'query.name': String,
  'query.validate': Function,
  'query.run': Function,
  'query.call': Function,
  'query.allowInBackground': {
    type: Boolean,
    optional: true,
  },
};

const registerQuery = {
  validate(args) {
    new SimpleSchema(registerQueryCommand)
      .validate(args);
  },

  run(query) {
    Log.info(`Registering Query ${query.name}.`);

    this.validate({ query });

    Meteor.methods({
      [query.name](args) {
        Log.query(query.name, args);

        if (Meteor.userId() || query.allowInBackground) {
          query.validate.call(this, args);
          return query.run.call(this, args);
        }

        Log.error(`Unknown user attempted to run query: ${query.name}.`);

        return null;
      },
    });
  },
};

const registerEventHandlerCommand = {
  eventTypes: Array,
  'eventTypes.$': {
    type: String,
    custom: function isValidEventType() {
      if (!EventsHelpers.isValidEventType(this.value)) {
        return 'invalid_event_type';
      }

      return undefined;
    },
  },
  handler: Function,
};

const registerEventHandler = {
  validate(args) {
    new SimpleSchema(registerEventHandlerCommand)
      .validate(args);
  },

  run({ eventTypes, handler }) {
    this.validate({ eventTypes, handler });
    EventDispatcher.register(handler, ...eventTypes);
  },
};

export const Register = {
  publication(pub) {
    registerPublication.run(pub);

    return this;
  },
  command(cmd) {
    registerCommand.run(cmd);

    return this;
  },
  query(qry) {
    registerQuery.run(qry);

    return this;
  },
  eventHandler(handler, ...eventTypes) {
    registerEventHandler.run({ eventTypes, handler });

    return this;
  },
};
