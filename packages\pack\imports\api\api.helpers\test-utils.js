import { Factory } from 'meteor/dburles:factory';
import MockDate from 'mockdate';
import { resetDatabase } from 'meteor/xolvio:cleaner';
import sinon from 'sinon';

export const TestUtils = {
  userStubs: [],
  id() {
    return new Mongo.ObjectID()._str;
  },
  setupCommonStaticData(setupUser = true) {
    Factory.create('companySiteProfile');
    if (setupUser) {
      this.stubUser(Factory.create('testAdminUser'));
    }
    return this;
  },
  stubUser(user) {
    this.resetUserStub();
    this.userStubs.push(...[
      sinon.stub(this, 'userId').returns(user._id),
      sinon.stub(Meteor, 'userId').returns(user._id),
      sinon.stub(Meteor, 'user').returns(user)]);

    return this;
  },
  resetWholeDb() {
    resetDatabase();

    return this;
  },
  resetDbIgnoringStaticData() {
    resetDatabase({ excludedCollections: ['companySiteProfiles', 'users'] });

    return this;
  },
  resetUserStub() {
    this.userStubs.forEach(stub => stub.restore());
    this.userStubs.splice(0, this.userStubs.length);

    return this;
  },
  stubApiMethod(sandbox, method, retVal = null, ...yields) {
    const stub = sandbox.stub(method, 'call');

    if (retVal) stub.returns(retVal);

    if (yields.length) stub.yields(...yields);

    return this;
  },
  stubMomentAndJSDateNow(dateTime) {
    MockDate.set(dateTime);
  },
  resetMockedDate() {
    MockDate.reset();
  },
  restoreStubApiMethod(method) {
    method.call.restore();

    return this;
  },
  mock(sandbox, method, expects) {
    expects(sandbox.mock(method));

    return this;
  },
  resetSandbox(sandbox) {
    sandbox.restore();

    return this;
  },
};
