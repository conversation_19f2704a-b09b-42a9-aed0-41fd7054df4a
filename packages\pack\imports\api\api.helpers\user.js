import { Errors } from './errors';
import { Meteor } from 'meteor/meteor';
import { PackRoles } from '../api.roles/pack-roles';

const throwNoSitesError = (userId) => {
  throw new Meteor.Error('No site assigned to user.', `UserId: ${userId}`);
};

const getUserId = () => {
  const userId = Meteor.userId();

  if (!userId) {
    Errors.throw(Errors.types.notLoggedIn);
  }

  return userId;
};

export const User = {
  activeSites() {
    const userId = getUserId();
    const sites = PackRoles.getGroupsForUser(userId);

    return sites || throwNoSitesError(userId);
  },
  activeSite() {
    const sites = this.activeSites();

    return sites[0] || throwNoSitesError(getUserId());
  },
  hasAccessToSite(siteIdentifier) {
    const userId = getUserId();

    const activeSites = this.activeSites(userId);

    return activeSites.some((site) => site === siteIdentifier);
  },
};
