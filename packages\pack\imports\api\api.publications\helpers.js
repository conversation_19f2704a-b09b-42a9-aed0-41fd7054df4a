import { Publications } from './publications';

export const PublicationHelpers = {
  isRegistered(publicationName) {
    // Recursive function for finding the publication name within the
    // publication object.
    const checkPublications = (goal, publications, key = null) => {
      // If a key is provided check object property equals the goal
      if (key) {
        const isGoal = publications[key] === goal;

        if (isGoal) {
          return true;
        }
        if (typeof publications[key] === 'object') {
          // If the property is an object: go deeper
          return checkPublications(goal, publications[key]);
        }
        // Otherwise property is a value but not the goal
        return false;
      }

      // If no key is provided get the objects keys
      for (const pub of Object.keys(publications)) {
        // Loop through keys looking to see if goal exists
        if (checkPublications(goal, publications, pub)) {
          // Break immediately if true
          return true;
        }
      }

      // Worse case doesn't exist
      return false;
    };

    return checkPublications(publicationName, Publications);
  },
};
