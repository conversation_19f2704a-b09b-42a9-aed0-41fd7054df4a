export const Publications = Object.freeze({
  companySiteProfiles: {
    companySiteProfileForUser: 'companySiteProfiles.companySiteProfileForUser',
  },
  itemLocationReports: {
    storedItemsLocationCounts: 'itemLocationReports.storedItemsLocationCounts',
  },
  items: {
    recentItems: 'items.recentItems',
    storedOrCanStore: 'items.storedOrCanStore',
    selectedItem: 'items.selectedItem',
    selectedItemForEdit: 'items.selectedItemForEdit',
    forClientFiltered: 'items.forClientFiltered',
    preReceiptForPoRequiringReceipting: 'items.preReceiptForPoRequiringReceipting',
    itemsFromListOfIds: 'items.fromListOfIds',
    itemsFromListOfReceiptNos: 'items.fromListOfReceiptNos',
    packedOrCanPack: 'items.packOrCanPack',
    itemsForCargoId: 'items.itemsForCargoId',
    itemsHistory: 'items.history',
  },
  purchaseOrders: {
    posRequiringReceipting: 'purchaseOrders.posRequiringReceipting',
    po: 'purchaseOrders.po',
  },
  cargo: {
    cargoItemsRequiringReceipting: 'cargo.cargoItemsRequiringReceipting',
    cargoItem: 'cargo.cargoItem',
    cargoHistory: 'cargo.history',
  },
  reports: {
    reportForClient: 'reports.reportForClient',
  },
  requests: {
    request: 'requests.request',
    activeRequestsForClient: 'requests.activeRequestsForClient',
  },
});
