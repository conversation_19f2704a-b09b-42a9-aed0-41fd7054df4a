import { Roles } from 'meteor/alanning:roles';

/**
 * This is a wrapper for the Meteor Roles package so we can include
 * the application specific roles as a property on the package. Means one
 * import instead of two where roles are referenced in the application.
 */

const applicationRoles = {
  ADMIN: 'admin',
};

export const PackRoles = (function ApiRoles(roles) {
  const rolesModule = roles;

  rolesModule.type = applicationRoles;

  return rolesModule;
}(Roles));
