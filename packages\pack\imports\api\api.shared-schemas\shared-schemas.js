import SimpleSchema from 'simpl-schema';
import moment from 'moment';

const _idSchema = new SimpleSchema({
  _id: String,
});

const isActiveSchema = new SimpleSchema({
  isActive: {
    type: Boolean,
    optional: false,
  },
});

const SiteIdSchema = new SimpleSchema({
  siteId: {
    type: String,
    max: 50,
  },
});

const TimestampsSchema = new SimpleSchema({
  createdBy: {
    type: String,
    max: 50,
    optional: true,
    autoValue: function getUser() {
      if (this.isInsert) {
        return Meteor.user() ? Meteor.user().username : 'SERVER INITIATED';
      }
      // Don't let users change this after intial create
      this.unset();

      return undefined;
    },
  },
  createdAt: {
    type: Date,
    optional: true,
    autoValue: function getDatetimeNow() {
      if (this.isInsert) {
        return moment.utc().toDate();
      }

      this.unset();

      return undefined;
    },
  },
  updatedBy: {
    type: String,
    max: 50,
    optional: true,
    autoValue: function updatedByUser() {
      if (this.isUpdate) {
        return Meteor.user() ? Meteor.user().username : 'SERVER INITIATED';
      }

      this.unset();

      return undefined;
    },
  },
  updatedAt: {
    type: Date,
    optional: true,
    autoValue: function getDatetimeNow() {
      if (this.isUpdate) {
        return moment.utc().toDate();
      }

      this.unset();

      return undefined;
    },
  },
});

export {
  _idSchema,
  isActiveSchema,
  SiteIdSchema,
  TimestampsSchema,
};
