import { AddCargoItem } from './commands/add-cargo-item';
import { EditCargoItem } from './commands/edit-cargo-item';
import { Register } from '../api.helpers/register';
import { UpdateNoOfLinesReceived } from './commands/update-no-of-lines-received';
import { UpdateSetCompletedWasteRemoval } from './commands/update-set-completed-waste-removal';
import { UpdateReceiptedItems } from './commands/update-receipted-items';
import { GetCargoAsCsvForDateRange } from './commands/get-cargo-items-as-csv-for-date-range';
import { AuditCargoUpdate } from './commands/audit-cargo-update';
import { UpdateCargoItem } from './commands/external-cargo-updates/update-cargo-item';
import { ReinstateCargoItem } from './commands/reinstate-cargo-item';
import { HideCargoItem } from './commands/hide-received-cargo-item';
import { ItemInCargoReceiptedEvent } from './commands/emit-receipted-event';

Register
  .command(AddCargoItem)
  .command(UpdateReceiptedItems)
  .command(UpdateNoOfLinesReceived)
  .command(GetCargoAsCsvForDateRange)
  .command(UpdateSetCompletedWasteRemoval)
  .command(AuditCargoUpdate)
  .command(EditCargoItem)
  .command(UpdateCargoItem)
  .command(ReinstateCargoItem)
  .command(HideCargoItem)
  .command(ItemInCargoReceiptedEvent);
