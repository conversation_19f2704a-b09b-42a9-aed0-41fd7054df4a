import { GetCountForRecentItems } from './queries/get-count-for-recent-items';
import { GetCargoItemById } from './queries/get-cargo-item-by-id';
import { GetCargoItemByExternalCargoLineId } from './queries/get-cargo-item-by-externalcargolineid';
import { GetCargoAsCsvForOffshoreClientAndDateRange } from './queries/get-cargo-as-csv-for-offshore-client-and-date-range';
import { CheckCargoItemExists } from './queries/check-cargo-item-exists';
import { Register } from '../api.helpers/register';

Register
  .query(CheckCargoItemExists)
  .query(GetCountForRecentItems)
  .query(GetCargoItemByExternalCargoLineId)
  .query(GetCargoItemById)
  .query(GetCargoAsCsvForOffshoreClientAndDateRange);
