import { ClientSchema } from '../../company-site-profiles/configuration/client.schema';
import { EventFactory } from '../../api.events/event-factory';
import { Meteor } from 'meteor/meteor';
import { Cargo } from '../cargo';
import { CargoSchema } from '../cargo.schema';
import SimpleSchema from 'simpl-schema';
import moment from 'moment';

const command = {
  id: {
    type: String,
    optional: true,
  },
  receivedDate: Date,
  noOfLines: {
    type: SimpleSchema.Integer,
    optional: true,
  },
  noOfWasteLines: {
    type: SimpleSchema.Integer,
    optional: true,
  },
  siteIdentifier: String,
  client: ClientSchema,
  receiptLocation: String,
  receiptNo: String,
  description: {
    type: String,
    optional: true,
  },
  offshoreClient: String,
  ccu: String,
  offshoreLocation: String,
  voyageNo: String,
  manifestNo: String,
  ecargoCargoLine: {
    type: Object,
    blackbox: true,
    optional: true,
  },
  externalCargoLineId: String,
};

export const AddCargoItem = {
  name: 'cargo.addCargoItem',

  validate(args) {
    new SimpleSchema(command)
      .validate(args);
  },

  run({
    id,
    receivedDate,
    noOfLines,
    noOfWasteLines,
    siteIdentifier,
    client,
    receiptLocation,
    description,
    receiptNo,
    offshoreClient,
    ccu,
    offshoreLocation,
    voyageNo,
    manifestNo,
    ecargoCargoLine,
    externalCargoLineId,
  }) {
    console.log('AddCargoItem.run() called...');

    const cargoItem = {
      ccu,
      offshoreClient,
      offshoreLocation,
      voyageNo,
      manifestNo,
      ecargoCargoLine,
      externalCargoLineId,
      receivedDate,
      identifier: ccu, // TODO: Take out later
      noOfLines,
      noOfWasteLines,
      vendor: 'VendorNotUsed',
      siteId: siteIdentifier,
      client,
      receiptLocation,
      description,
      receiptNo,
      startWasteRemoval: true,
      events: [
        EventFactory.createItemEvent(
          EventFactory.Events.PurchaseOrders.RECEIVED,
          moment().utc().toDate(),
          Meteor.user().username,
        ),
      ],
    };

    if (id && id.length) {
      cargoItem._id = id;
    }

    console.log('Inserting cargo item into cargo collection');
    Cargo.insert(cargoItem, { filter: false });
    console.log('... Done inserting cargo item into cargo collection');
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
