import { Meteor } from 'meteor/meteor';
import { Cargo } from '../cargo';
import SimpleSchema from 'simpl-schema';
import moment from 'moment';

const command = {
  externalCargoLineId: String,
  allDiffs: {
    type: Object,
    blackbox: true,
    optional: false,
  },
};

// Keep a list of all cargo (and material line changes)
// that happen after cargo is received in Pack.
export const AuditCargoUpdate = {
  name: 'cargo.auditCargoUpdate',
  allowInBackground: true,
  validate(args) {
    new SimpleSchema(command)
      .validate(args);
  },

  run({ externalCargoLineId, allDiffs }) {
    const nowUtc = moment().utc().toDate();
    const cargoUpdate = {
      timestamp: nowUtc,
      changes: allDiffs,
    };

    Cargo.update(
      { externalCargoLineId }, // Flow Cargo Line Guid.
      { $push: { cargoUpdates: cargoUpdate } },
    );
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
