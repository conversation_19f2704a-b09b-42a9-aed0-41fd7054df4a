import { Errors } from '../../api.helpers/errors';
import { EventFactory } from '../../api.events/event-factory';
import { GetaCargoItemById } from '../queries/get-cargo-item-by-id';
import { Meteor } from 'meteor/meteor';
import { Cargo } from '../cargo';
import SimpleSchema from 'simpl-schema';
import moment from 'moment';

const command = {
  _id: {
    type: String,
    optional: true,
  },
  receivedDate: Date,
  identifier: String,
  vendor: String,
  vendorDeliveryNo: {
    type: String,
    optional: true,
  },
  receiptLocation: String,
  description: {
    type: String,
    optional: true,
  },
};

export const EditCargoItem = {
  name: 'cargo.editCargoItem',

  validate(args) {
    new SimpleSchema(command)
      .validate(args);
  },

  run(cargoItemToEdit) {
    const cargoItem = GetPoById.call({ id: cargoItemToEdit._id });

    if (!cargoItem) {
      throw new Meteor.Error(Errors.types.notFound, `Couldn't find CargoItem with Id: ${poToEdit._id}`);
    }

    const propertiesToCheck = Object.keys(command);
    const audit = [];

    const update = propertiesToCheck.reduce((acc, prop) => {
      if (cargoItemToEdit[prop]
        && (cargoItemToEdit[prop] !== cargoItem[prop])
        && (!(cargoItemToEdit[prop] instanceof Date)
          || !moment(cargoItemToEdit[prop]).isSame(moment(cargoItem[prop])))) {
        acc.$set[prop] = cargoItemToEdit[prop];
        audit.push({
          propertyName: prop,
          originalVal: cargoItem[prop],
          newVal: cargoItemToEdit[prop],
        });
      }

      return acc;
    }, { $set: {} });

    if (audit.length) {
      update.$push = {
        events: EventFactory.createItemEvent(
          EventFactory.Events.Cargo.EDITED,
          moment().utc().toDate(),
          Meteor.user().username,
          { edits: audit },
        ),
      };

      Cargo.update({ _id: cargoItemToEdit._id }, update);
    }
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
