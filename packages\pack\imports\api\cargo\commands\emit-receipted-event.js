import { Meteor } from 'meteor/meteor';
import { Mediator } from "meteor/mediator";
import SimpleSchema from 'simpl-schema';

const command = {
  externalCargoLineId: String,
  materialLineId: String,
};

export const ItemInCargoReceiptedEvent = {
  name: 'ItemInCargo.emitReceiptedEvent',
  validate(args) {
    new SimpleSchema(command)
      .validate(args);
  },
  run({ externalCargoLineId, materialLineId }) {
    Mediator.publish(
      Mediator.events.ITEM_IN_CARGO_RECEIPTED,
      { cargoLineId: externalCargoLineId, materialLineId },
    );
  },
  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
