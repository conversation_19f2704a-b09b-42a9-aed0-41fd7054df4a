
import { Meteor } from 'meteor/meteor';
import { Cargo } from '../../cargo';
import { CargoSchema } from '../../cargo.schema';
import SimpleSchema from 'simpl-schema';

const command = {
  id: {
    type: String,
    optional: true,
  },
  updateDto: {
    type: Object,
    blackbox: true,
    optional: true,
  },
};

export const UpdateCargoItem = {
  name: 'cargo.updateCargoItem',
  allowInBackground: true,
  validate(args) {
    new SimpleSchema(command)
      .validate(args);
  },

  run({
    id,
    updateDto,
  }) {
    console.log('UpdateCargoItem.run() called...');

    Cargo.update({ _id: id }, {
      $set: {
        ecargoCargoLine: updateDto,
        noOfLines: updateDto.materials.length,
        noOfWasteLines: updateDto.materials.filter((x) => x.isWaste).length,
      },
    });
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
