import { Meteor } from 'meteor/meteor';
import SimpleSchema from 'simpl-schema';
import { Cargo } from '../cargo';

const command = {
  id: String,
};

export const HideCargoItem = {
  name: 'cargo.hideCargoItem',

  validate(args) {
    new SimpleSchema(command)
      .validate(args);
  },

  run({ id }) {
    Cargo.update(
      { _id: id },
      {
        $set: {
          startWasteRemoval: false,
        },
      },
    );
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
