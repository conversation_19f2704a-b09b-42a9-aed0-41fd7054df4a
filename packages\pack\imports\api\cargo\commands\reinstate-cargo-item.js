import { Meteor } from 'meteor/meteor'
import SimpleSchema from 'simpl-schema';
import { Cargo } from '../cargo';

const command = {
  id: String,
};

export const ReinstateCargoItem = {
  name: 'cargo.reinstateCargoItem',
  validate(args) {
    new SimpleSchema(command)
      .validate(args);
  },

  run({ id }) {
    console.log('ReinstateCargoItem.run() called...');
    Cargo.update(
      { _id: id },
      {
        $set: {
          isWasteRemovalCompleted: false,
          startWasteRemoval: true,
        },
      },
    );
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
