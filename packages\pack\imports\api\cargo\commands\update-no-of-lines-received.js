import { Meteor } from 'meteor/meteor';
import { Cargo } from '../cargo';
import SimpleSchema from 'simpl-schema';

const command = {
  cargoItemId: String,
  noOfLinesReceived: SimpleSchema.Integer,
};

export const UpdateNoOfLinesReceived = {
  name: 'cargo.updateNoOfLinesReceived',

  validate(args) {
    new SimpleSchema(command)
      .validate(args);
  },

  run({ cargoItemId, noOfLinesReceived }) {
    Cargo.update({ _id: cargoItemId }, {
      $set: {
        noOfLines: noOfLinesReceived,
      },
    });
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
