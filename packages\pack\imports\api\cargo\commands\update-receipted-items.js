import { Meteor } from 'meteor/meteor';
import { Cargo } from '../cargo';
import SimpleSchema from 'simpl-schema';
import { Winston as log } from 'meteor/wylio:winston-papertrail';

// Validation schema for parameters passed to command.
const command = {
  cargoItemId: String,
  receiptedItemId: String,
};

export const UpdateReceiptedItems = {
  name: 'cargo.updateReceiptedItems',

  validate(args) {
    log.info(`cargo.updateReceiptedItems Validating args: ${JSON.stringify(args)}`);

    try {
      new SimpleSchema(command).validate(args);
    } catch (e) {
      console.log(`Exception happened when updating cargo item. ${e}`);
      throw e;
    }
  },

  run({ cargoItemId, receiptedItemId }) {
    log.info(`Updating number of lines receipted on Cargo Item <${cargoItemId}>.`);
    Cargo.update({ _id: cargoItemId }, {
      $push: {
        linesReceipted: receiptedItemId,
      },
      $inc: {
        noOfLinesReceipted: 1,
      },
    });

    const cargoItem = Cargo.findOne({ _id: cargoItemId });
    log.info(`Updated Cargo Item <${cargoItem.ccu}>, <${cargoItem.receiptNo}> to have <${cargoItem.noOfLinesReceipted}> lines receipted. <${cargoItemId}>`);

    // TODO: Maybe change/adapt to set all Waste Lines receipted
    if (cargoItem.noOfLines === cargoItem.noOfLinesReceipted) {
      log.info(`Cargo Item - All <${cargoItem.noOfLinesReceipted}> lines now receipted <${cargoItem.ccu}>, <${cargoItem.receiptNo}>`);
      Cargo.update({ _id: cargoItemId }, {
        $set: {
          allLinesReceipted: true,
        },
      });
    }
  },

  call(args, callback) {
    log.info(`Running Meteor.call for ${this.name}`);
    Meteor.call(this.name, args, callback);
  },
};
