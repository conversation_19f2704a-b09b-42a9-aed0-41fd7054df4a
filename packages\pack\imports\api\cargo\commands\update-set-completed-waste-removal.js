import { Meteor } from 'meteor/meteor';
import { Cargo } from '../cargo';
import SimpleSchema from 'simpl-schema';

const command = {
  cargoItemId: String,
  isWasteRemovalCompleted: Boolean,
};

export const UpdateSetCompletedWasteRemoval = {
  name: 'cargo.updateSetCompletedWasteRemoval',

  validate(args) {
    new SimpleSchema(command)
      .validate(args);
  },

  run({ cargoItemId, isWasteRemovalCompleted }) {
    Cargo.update({ _id: cargoItemId }, {
      $set: {
        isWasteRemovalCompleted,
        startWasteRemoval: false,
      },
    });
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
