import { Cargo } from '../cargo';
import { Meteor } from 'meteor/meteor';
import SimpleSchema from 'simpl-schema';

const query = {
  externalCargoLineId: String,
};

export const CheckCargoItemExists = {
  name: 'cargo.checkCargoItemExists',
  allowInBackground: true,

  validate(args) {
    new SimpleSchema(query)
      .validate(args);
  },

  run({ externalCargoLineId }) {
    const cargoItem = Cargo.findOne({ externalCargoLineId });
    return cargoItem !== undefined;
  },

  call(args, callback) {
    return Meteor.call(this.name, args, callback);
  },
};
