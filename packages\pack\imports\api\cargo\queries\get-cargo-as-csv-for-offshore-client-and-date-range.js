import { CSV } from 'meteor/clinical:csv';
import { Cargo } from '../cargo';
import { Meteor } from 'meteor/meteor';
import SimpleSchema from 'simpl-schema';
import { Winston as log } from 'meteor/wylio:winston-papertrail';
import moment from 'moment';
import { User } from '../../api.helpers/user';

const params = {
  offshoreClient: {
    type: String,
    optional: true,
  },
  reportStartDateStr: {
    type: String,
    optional: true,
  },
  reportEndDateStr: {
    type: String,
    optional: true,
  },
  dateToFilterBy: {
    type: String,
    optional: true,
  },
};

export const GetCargoAsCsvForOffshoreClientAndDateRange = {
  name: 'cargo.getCargoAsCsvForOffshoreClientAndDateRange',

  validate(args) {
    new SimpleSchema(params)
      .validate(args);
  },

  run({ reportStartDateStr, reportEndDateStr, dateToFilterBy, offshoreClient }) {
    console.log('get cargo export');
    const query = {
      $and: [
        { siteId: User.activeSite() },
      ],
    };

    if (reportStartDateStr) {
      query.$and
        .push({ [dateToFilterBy]: { $gte: moment(reportStartDateStr).startOf('day').toDate() } });
    }

    if (reportEndDateStr) {
      query.$and
        .push({ [dateToFilterBy]: { $lte: moment(reportEndDateStr).endOf('day').toDate() } });
    }

    if (offshoreClient) {
      query.$and
        .push({ offshoreClient: offshoreClient });
    }
    const cargoForIntakeExport = Cargo.find(query, { sort: { receivedDate: 1 } }).fetch();

    const cargoForIntakeExportFormatted = cargoForIntakeExport.map((x) => ({
      'Receipt No': x.receiptNo,
      'Received Date': moment(x.receivedDate).format('DD/MM/YYYY HH:mm'),
      'Offshore Client': x.offshoreClient,
      'Offshore Location': x.offshoreLocation,
      'Voyage No': x.voyageNo,
      'Manifest No': x.manifestNo,
      CCU: x.ccu,
    }));

    const heading = true; // Optional, defaults to true
    const delimiter = ','; // Optional, defaults to ",";
    return CSV.unparse(cargoForIntakeExportFormatted, heading, delimiter);
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
