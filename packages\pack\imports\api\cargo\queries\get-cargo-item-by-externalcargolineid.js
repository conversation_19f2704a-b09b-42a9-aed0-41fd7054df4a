import { Meteor } from 'meteor/meteor';
import { Cargo } from '../cargo';
import SimpleSchema from 'simpl-schema';

const query = {
  externalCargoLineId: String, // Flow CargoLineId
};

export const GetCargoItemByExternalCargoLineId = {
  name: 'cargo.getCargoItemByExternalCargoLineId',
  allowInBackground: true,
  validate(args) {
    new SimpleSchema(query)
      .validate(args);
  },

  run({ externalCargoLineId }) {
    return Cargo.findOne({ externalCargoLineId });
  },

  call(args, callback) {
    return Meteor.call(this.name, args, callback);
  },
};
