import { Meteor } from 'meteor/meteor';
import { Cargo } from '../cargo';
import SimpleSchema from 'simpl-schema';

const query = {
  id: String,
};

export const GetCargoItemById = {
  name: 'cargo.getCargoItemById',

  validate(args) {
    new SimpleSchema(query)
      .validate(args);
  },

  run({ id }) {
    return Cargo.findOne({ _id: id });
  },

  call(args, callback) {
    return Meteor.call(this.name, args, callback);
  },
};
