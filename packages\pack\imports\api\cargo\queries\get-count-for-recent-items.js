import { Meteor } from 'meteor/meteor';
import { Cargo } from '../cargo';
import SimpleSchema from 'simpl-schema';
import { User } from '../../api.helpers/user';
import { utils } from '../../../shared/utils';

const q = {
  query: {
    type: String,
    optional: true,
  },
  clientId: String,
};

export const GetCountForRecentItems = {
  name: 'cargo.getCountForRecentItems',

  validate(args) {
    new SimpleSchema(q)
      .validate(args);
  },

  run({ query, clientId }) {
    const selector = {
      $and: [{
        siteId: User.activeSite(),
        allLinesReceipted: false,
      }],
    };

    if (query && query.length) {
      const querySelector = {
        $or: [
          { identifier: { $regex: utils.escapeRegExp(query), $options: 'i' } },
        ],
      };

      selector.$and.push(querySelector);
    }

    if (clientId) {
      selector.$and.push({ 'client._id': clientId });
    }

    return Cargo
      .find(selector, { sort: { receivedDate: -1 } })
      .count();
  },

  call(args, callback) {
    return Meteor.call(this.name, args, callback);
  },
};
