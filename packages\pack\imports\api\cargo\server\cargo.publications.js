import { CargoItem } from './publications/cargo-item';
import { CargoItemsRequiringReceipting } from './publications/cargo-items-requiring-receipting';
import { CargoHistory } from './publications/cargo-history';
import { Cargo } from '../cargo';
import { ReactiveTable } from 'meteor/fcallem:reactive-table-semantic';
import { Register } from '../../api.helpers/register';

ReactiveTable.publish('cargo.receiptHistory', function publish() {
  return this.userId ? Cargo : [];
});

Register
  .publication(CargoItemsRequiringReceipting)
  .publication(CargoItem)
  .publication(CargoHistory);
