import { Cargo } from '../../cargo';
import { Publications } from '../../../api.publications/publications';
import SimpleSchema from 'simpl-schema';
import { User } from '../../../api.helpers/user';

const pubQuery = {
  pageSize: SimpleSchema.Integer,
  page: SimpleSchema.Integer,
};

export const CargoHistory = {
  name: Publications.cargo.cargoHistory,

  validate(args) {
    new SimpleSchema(pubQuery)
      .validate({ pageSize: args.pageSize, page: args.page });
  },

  run({ pageSize, page, filter }) {
    const userSite = User.activeSite();

    // Build query
    const query = {
      'client._id': filter.clientId,
      siteId: userSite,
    };

    // Build Date Filter
    const dateFilter = {};

    if (filter.fromDate) {
      dateFilter.$gte = filter.fromDate;
    }

    if (filter.toDate) {
      dateFilter.$lte = filter.toDate;
    }

    if (Object.keys(dateFilter).length) {
      query[filter.dateToFilter] = dateFilter;
    }

    if (filter.offshoreClient) {
      query.offshoreClient = filter.offshoreClient;
    }

    const sortDirection = filter.sortDirection === 'desc' ? -1 : 1;

    const limit = pageSize;
    const skip = pageSize * (page - 1);

    return Cargo.find(query, { limit, skip, sort: { [filter.sortBy]: sortDirection } });
  },
};
