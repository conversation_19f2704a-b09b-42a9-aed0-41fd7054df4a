import { Publications } from '../../../api.publications/publications';
import { Cargo } from '../../cargo';
import SimpleSchema from 'simpl-schema';
import { User } from '../../../api.helpers/user';

const pubQuery = {
  id: String,
};

export const CargoItem = {
  name: Publications.cargo.cargoItem,

  validate(args) {
    new SimpleSchema(pubQuery)
      .validate(args);
  },

  run({ id }) {
    const selector = {
      siteId: User.activeSite(),
      _id: id,
    };

    return Cargo.find(selector);
  },
};
