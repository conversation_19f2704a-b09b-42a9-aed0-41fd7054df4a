import { Match, check } from 'meteor/check';
import { Catalogue } from './catalogue';
import { Meteor } from 'meteor/meteor';
import { Winston as log } from 'meteor/wylio:winston-papertrail';

Meteor.methods({
  'catalogue.search': function handleSearch(searchText) {
    check(searchText, String);

    log.info(`catalogue.search - search request for the string: ${searchText}`);

    if (!searchText) {
      log.error('Catalogue.search - searchText is undefined returning empty list');
      return [];
    }
    const catalogueEntries = Catalogue.find(
      { $text: { $search: searchText } },
      {
        // `fields` is where we can add MongoDB projections. Here we're causing
        // each document published to include a property named `score`, which
        // contains the document's search rank, a numerical value, with more
        // relevant documents having a higher score.
        fields: {
          score: { $meta: 'textScore' },
        },
        // This indicates that we wish the publication to be sorted by the
        // `score` property specified in the projection fields above.
        sort: {
          score: { $meta: 'textScore' },
        },
        limit: 10,
      },
    ).fetch();

    log.info(`Catalgoue.search - ${catalogueEntries.length} items found.`);

    return catalogueEntries.map((entry) => ({ title: entry.name, description: entry.modelNo }));
  },
});
