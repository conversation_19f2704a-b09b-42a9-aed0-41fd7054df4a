import { CompanySiteProfiles } from '../company-site-profiles';
import { Errors } from '../../api.helpers/errors';
import { Meteor } from 'meteor/meteor';
import SimpleSchema from 'simpl-schema';
import { User } from '../../api.helpers/user';

const command = {
  siteIdentifier: String,
};

export const IncrementItemRefCounter = {
  name: 'companySiteProfiles.incrementItemRefCounter',
  allowInBackground: true,

  validate(args) {
    new SimpleSchema(command)
      .validate(args);
  },

  run({ siteIdentifier }) {
    if (User) {
      if (!User.hasAccessToSite(siteIdentifier)) {
        Errors.throw(Errors.types.noAccessToSite, `UserId: ${this.userId}, `
          + `SiteIdentifier: ${siteIdentifier}`);
      }
    } else {
      console.log('System is Incremenenting Receipt Counter'); // New Cargo received from Receipt side (no user available)
    }
    CompanySiteProfiles.update({ identifier: siteIdentifier },
      { $inc: { 'configuration.receiptNoSequence': 1 } },
    );
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
