import { CompanySiteProfiles } from '../company-site-profiles';
import { GetSiteFromIdentifier } from '../queries/get-site-from-identifier';
import { Log } from '../../api.helpers/log';
import { Meteor } from 'meteor/meteor';
import SimpleSchema from 'simpl-schema';

const command = {
  resetValue: SimpleSchema.Integer,
  siteIdentifier: String,
};

export const ResetItemRefCounter = {
  name: 'companySiteProfiles.resetItemRefCounter',
  allowInBackground: true,

  validate(args) {
    new SimpleSchema(command)
      .validate(args);
  },

  run({ resetValue, siteIdentifier }) {
    const siteProfile = GetSiteFromIdentifier.call({ siteIdentifier });

    CompanySiteProfiles.update(
      { _id: siteProfile._id },
      {
        $set: {
          'configuration.receiptNoSequence': resetValue,
        },
      },
      (error) => {
        if (error) {
          Log.error(`ResetItemRefCounter failed with error: ${JSON.stringify(error)}`);
        }
      },
    );
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
