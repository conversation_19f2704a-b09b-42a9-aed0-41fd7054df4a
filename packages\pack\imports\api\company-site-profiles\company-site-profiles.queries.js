import { ConfigurationRetrieval } from './queries/configuration-retrieval';
import { EntitiesExistInConfiguration } from './queries/entities-exist-in-configuration';
import { GetClientFromId } from './queries/get-client-from-id';
import { GetDestinationFromId } from './queries/get-destination-from-id';
import { GetPackingUnitFromIdentifier } from './queries/get-packing-unit-from-identifier';
import { GetSiteFromIdentifier } from './queries/get-site-from-identifier';
import { GetVendorFromId } from './queries/get-vendor-from-id';
import { Register } from '../api.helpers/register';

Register
  .query(GetClientFromId)
  .query(GetSiteFromIdentifier)
  .query(GetVendorFromId)
  .query(ConfigurationRetrieval)
  .query(EntitiesExistInConfiguration)
  .query(GetDestinationFromId)
  .query(GetPackingUnitFromIdentifier);
