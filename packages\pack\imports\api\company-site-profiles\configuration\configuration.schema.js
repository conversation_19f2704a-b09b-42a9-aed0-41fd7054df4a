import { ClientSchema } from './client.schema';
import { CurrencySchema } from './currency.schema';
import { CustomStatusSchema } from './custom-status.schema';
import { DgClassificationSchema } from './dg-classification.schema';
import { NcrSchema } from './ncr.schema';
import { OffshoreLocationSchema } from './offshore-location.schema';
import { OffshoreClientSchema } from './offshore-client.schema';
import { PackageTypeSchema } from './package-type.schema';
import { PackingUnitSchema } from './packing-unit.schema';
import { PrinterSchema } from './printer.schema';
import { ReceiptCategorySchema } from './receipt-category.schema';
import { ReceiptLocationSchema } from './receipt-location.schema';
import { ReceiptingSchema } from './receipting.schema';
import SimpleSchema from 'simpl-schema';
import { StorageLocationSchema } from './storage-location.schema';
import { VendorSchema } from './vendor.schema';
import { DestinationVendorSchema } from './destination-vendor.schema';
import { MaterialTransportCompanySchema } from './material-transport-company.schema';

export const ConfigurationSchema = new SimpleSchema({
  receiptNoSequence: SimpleSchema.Integer,
  receiptNoFormatStr: String,
  receipting: {
    type: ReceiptingSchema,
    optional: true,
    defaultValue: {},
  },
  clients: Array,
  'clients.$': ClientSchema,
  packageTypes: Array,
  'packageTypes.$': PackageTypeSchema,
  offshoreLocations: Array,
  'offshoreLocations.$': OffshoreLocationSchema,
  offshoreClients: Array,
  'offshoreClients.$': OffshoreClientSchema,
  vendors: Array,
  'vendors.$': VendorSchema,
  destinationVendors: Array,
  'destinationVendors.$': DestinationVendorSchema,
  materialTransportCompanies: Array,
  'materialTransportCompanies.$': MaterialTransportCompanySchema,
  receiptCategories: Array,
  'receiptCategories.$': ReceiptCategorySchema,
  storageLocations: Array,
  'storageLocations.$': StorageLocationSchema,
  currencies: Array,
  'currencies.$': CurrencySchema,
  dgClassifications: Array,
  'dgClassifications.$': DgClassificationSchema,
  customsStatuses: Array,
  'customsStatuses.$': CustomStatusSchema,
  ncrs: Array,
  'ncrs.$': NcrSchema,
  printers: Array,
  'printers.$': PrinterSchema,
  receiptLocations: Array,
  'receiptLocations.$': ReceiptLocationSchema,
  packingUnits: Array,
  'packingUnits.$': PackingUnitSchema,
});
