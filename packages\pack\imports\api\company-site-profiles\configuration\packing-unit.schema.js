import { PackingUnitTypes } from './packing-unit.types';
import SimpleSchema from 'simpl-schema';
import { _idSchema } from '../../api.shared-schemas/shared-schemas';

export const PackingUnitSchema = new SimpleSchema({
  identifier: String,
  unitType: {
    type: String,
    custom: function isValidPackingUnitType() {
      if (this.isSet) {
        const isValid = Object.keys(PackingUnitTypes)
          .some((type) => PackingUnitTypes[type] === this.value);

        return isValid ? undefined : 'invalid_packing_unit_type';
      }

      return undefined;
    },
  },
}).extend(_idSchema);
