import { ReceiptStageTypes } from './receipt-stage.types';
import SimpleSchema from 'simpl-schema';

export const ReceiptStageSchema = new SimpleSchema({
  templateName: String,
  type: {
    type: String,
    custom: function isValidReceiptStage() {
      if (!this.isSet) {
        return SimpleSchema.ErrorTypes.REQUIRED;
      }

      const isValid = Object.keys(ReceiptStageTypes)
        .find((type) => ReceiptStageTypes[type] === this.value);

      return isValid ? undefined : 'invalid_receipt_stage_type';
    },
  },
  position: SimpleSchema.Integer,
  canSubmit: Boolean,
  dependsOn: {
    // Should only enter stage if properties of item set
    type: Array,
    optional: true,
  },
  'dependsOn.$': String,
  dependsOnNull: {
    // Should only enter stage if properties of item aren't set
    type: Array,
    optional: true,
  },
  'dependsOnNull.$': String,
});
