import { CompanySiteProfiles } from '../company-site-profiles';
import { Errors } from '../../api.helpers/errors';
import { Meteor } from 'meteor/meteor';
import SimpleSchema from 'simpl-schema';

const params = {
  siteIdentifier: String,
  packageType: {
    type: String,
    optional: true,
  },
  offshoreLocation: {
    type: String,
    optional: true,
  },
  vendor: {
    type: String,
    optional: true,
  },
  receiptCategory: {
    type: String,
    optional: true,
  },
  storageLocation: {
    type: String,
    optional: true,
  },
  currency: {
    type: String,
    optional: true,
  },
  dgClassication: {
    type: String,
    optional: true,
  },
  client: {
    type: String,
    optional: true,
  },
  receiptLocation: {
    type: String,
    optional: true,
  },
};

const configPropertyMappings = {
  packageType: 'packageTypes',
  offshoreLocation: 'offshoreLocations',
  vendor: 'vendors',
  receiptCategory: 'receiptCategories',
  storageLocation: 'storageLocations',
  currency: 'currencies',
  dgClassication: 'dgClassifications',
  client: 'clients',
  receiptLocation: 'receiptLocations',
};

/**
 * Retrieve whole configuration for SiteProfile or provide
 * id's for specific configuration entities to retrieve from configuration.
 */
export const ConfigurationRetrieval = {
  name: 'companySiteProfiles.configurationRetrieval',

  validate(args) {
    new SimpleSchema(params)
      .validate(args);
  },

  run(query) {
    const siteProfile = CompanySiteProfiles.findOne({ identifier: query.siteIdentifier });
    const configuration = siteProfile.configuration;

    if (!siteProfile) {
      throw new Meteor.Error(Errors.noAccessToSite, `${query.siteIdentifier}`);
    }

    const optionalParams = Object.keys(params).filter((param) => params[param].optional);

    const result = optionalParams.reduce((acc, param) => {
      // Ignore if query doesn't have optional param
      if (!query[param]) {
        return acc;
      }

      // Accumulator init as null to allow for || when returning result
      const newAcc = acc || {};
      const configPropName = configPropertyMappings[param];

      const configResult = configuration[configPropName]
        .find((x) => x._id === query[param] || x.name === query[param]);

      if (!configResult) {
        return acc;
      }

      newAcc[param] = configResult;

      return newAcc;
    }, null);

    return result || configuration;
  },

  call(args, callback) {
    return Meteor.call(this.name, args, callback);
  },
};

export const ConfigurationRetrievalResult = {
  isValid(query, result) {
    if (!result) {
      return false;
    }

    const keys = Object.keys(query)
      .filter(x => x !== 'siteIdentifier');

    if (!keys || keys.length === 0) {
      return result !== null;
    }

    let isValid = true;

    for (const key of keys) {
      if (!result[key]) {
        isValid = false;
        break;
      }
    }

    return isValid;
  },
};
