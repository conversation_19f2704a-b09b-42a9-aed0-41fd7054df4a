import { ConfigurationRetrieval } from './configuration-retrieval';
import { Meteor } from 'meteor/meteor';
import SimpleSchema from 'simpl-schema';

const query = {
  siteIdentifier: String,
  entities: Array,
  'entities.$': Object,
  'entities.$.name': {
    type: String,
    optional: true,
    custom: function isRequiredIfIdIsNull() {
      if (!this.value) {
        const id = this.siblingField('id');

        if (!id.isSet) {
          return SimpleSchema.ErrorTypes.REQUIRED;
        }
      }

      return undefined;
    },
  },
  'entities.$.id': {
    type: String,
    optional: true,
    custom: function isRequiredIfNameIsNull() {
      if (!this.value) {
        const name = this.siblingField('name');

        if (!name.isSet) {
          return SimpleSchema.ErrorTypes.REQUIRED;
        }
      }
      return undefined;
    },
  },
  'entities.$.configProperty': String,
};

export const EntitiesExistInConfiguration = {
  name: 'companySiteProfiles.entitiesExistInConfiguration',

  validate(args) {
    new SimpleSchema(query)
      .validate(args);
  },

  run({ siteIdentifier, entities }) {
    const result = {};
    const configQuery = { siteIdentifier };

    entities.forEach((entity) => {
      configQuery[entity.configProperty] = entity.name || entity.id;
    });

    const config = ConfigurationRetrieval.call(configQuery);

    entities.forEach((entity) => {
      result[entity.configProperty] = (config[entity.configProperty] !== null ||
          config[entity.configProperty] !== undefined);
    });

    return result;
  },

  call(args, callback) {
    return Meteor.call(this.name, args, callback);
  },
};
