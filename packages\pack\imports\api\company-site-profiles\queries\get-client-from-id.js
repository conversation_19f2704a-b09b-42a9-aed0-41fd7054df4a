import { Errors } from '../../api.helpers/errors';
import { GetSiteFromIdentifier } from './get-site-from-identifier';
import { Meteor } from 'meteor/meteor';
import SimpleSchema from 'simpl-schema';
import { User } from '../../api.helpers/user';

const query = {
  clientId: String,
  siteIdentifier: String,
};

export const GetClientFromId = {
  name: 'companySiteProfiles.getClientFromId',

  validate(args) {
    new SimpleSchema(query)
      .validate(args);
  },

  run({ clientId, siteIdentifier }) {
    if (!User.hasAccessToSite(siteIdentifier)) {
      Errors.throw(Errors.types.noAccessToSite, `UserId: ${this.userId}, ` +
        `SiteIdentifier: ${siteIdentifier}`);
    }

    const companySiteProfile = GetSiteFromIdentifier.call({ siteIdentifier });

    const clients = companySiteProfile.configuration.clients;

    if (!clients) {
      Errors.throw(Errors.types.notFound, `Clients configuration for Site: ${siteIdentifier}`);
    }

    const client = clients.find((c) => c._id === clientId);

    if (!client) {
      Errors.throw(Errors.types.notFound, `ClientId: ${clientId} for Site: ${siteIdentifier}`);
    }

    return client;
  },

  call(args, callback) {
    return Meteor.call(this.name, args, callback);
  },
};
