import { DestinationTypes } from '../configuration/destination.types';
import { Errors } from '../../api.helpers/errors';
import { GetSiteFromIdentifier } from './get-site-from-identifier';
import { Meteor } from 'meteor/meteor';
import SimpleSchema from 'simpl-schema';
import { User } from '../../api.helpers/user';

const query = {
  destinationId: String,
};

export const GetDestinationFromId = {
  name: 'companySiteProfiles.getDestinationFromId',

  validate(args) {
    new SimpleSchema(query)
      .validate(args);
  },

  run({ destinationId }) {
    const buildDestinationObj = (destination, destinationType ) => {
      return {
        _id: destination._id,
        name: destination.name,
        destinationType,
      };
    };

    const siteIdentifier = User.activeSite();
    
    const siteProfile = GetSiteFromIdentifier.call({ siteIdentifier });

    const configuration = siteProfile.configuration;

    const destination = configuration
      .destinationVendors // was offshoreLoctions - changed for Chemicals.
      .find((dest) => dest._id === destinationId);

    if (destination) {
      return buildDestinationObj(destination, DestinationTypes.offshoreLocation);
    }

    // Insert query for other destination options here when applicable
    // e.g. Vendor Warehouses

    if (!destination) {
      Errors.throw(Errors.types.notFound, `Destination with Id: ${destinationId}, not found.`);
    }

    return undefined;
  },

  call(args, callback) {
    return Meteor.call(this.name, args, callback);
  },
};
