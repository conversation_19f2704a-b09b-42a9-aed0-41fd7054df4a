import { Errors } from '../../api.helpers/errors';
import { GetSiteFromIdentifier } from './get-site-from-identifier';
import { Meteor } from 'meteor/meteor';
import SimpleSchema from 'simpl-schema';
import { User } from '../../api.helpers/user';
import { Log } from '../../api.helpers/log';

const query = {
  packingUnitIdentifier: String,
};

const isEmptyObject = (obj) => Object.keys(obj).length === 0;

export const GetPackingUnitFromIdentifier = {
  name: 'requests.getPackingUnitFromIdentifier',

  validate(args) {
    new SimpleSchema(query)
      .validate(args);
  },

  run({ packingUnitIdentifier }) {
    const siteIdentifier = User.activeSite();

    const siteProfile = GetSiteFromIdentifier.call({ siteIdentifier });

    const packingUnits = siteProfile.configuration.packingUnits;

    Log.info('Packing Units found for site:', { packingUnits });

    if (!(packingUnits && packingUnits.length)) {
      Log.info('No Packing Units found for site:', { siteIdentifier });
      Errors.throw(Errors.types.notFound, `No Packing Units found for the Site: ${siteIdentifier}`);
    }

    const packingUnit = packingUnits
      .filter((unit) => unit.identifier === packingUnitIdentifier);

    if (!packingUnit || isEmptyObject(packingUnit)) {
      Log.info('No Packing Unit found for identifier:', { packingUnitIdentifier });
      Errors.throw(Errors.types.notFound, `No Packing Unit found for identifier: ${packingUnitIdentifier}`);
    } else {
      Log.info('Packing Unit was found for identifier:', { packingUnitIdentifier });
    }

    return packingUnit;
  },

  call(args, callback) {
    return Meteor.call(this.name, args, callback);
  },
};
