import { CompanySiteProfiles } from '../company-site-profiles';
import { Errors } from '../../api.helpers/errors';
import { Meteor } from 'meteor/meteor';
import SimpleSchema from 'simpl-schema';
import { User } from '../../api.helpers/user';

const query = {
  siteIdentifier: String,
};

export const GetSiteFromIdentifier = {
  name: 'companySiteProfiles.getSiteFromIdentifier',
  allowInBackground: true,

  validate(args) {
    new SimpleSchema(query)
      .validate(args);
  },

  run({ siteIdentifier }) {
    if (Meteor.userId()) { // null if no user logged in - running as background task.
      if (!User.hasAccessToSite(siteIdentifier)) {
        Errors.throw(Errors.types.noAccessToSite, `UserId: ${this.userId}, `
          + `SiteIdentifier: ${siteIdentifier}`);
      }
    }

    const companySiteProfile = CompanySiteProfiles.findOne({ identifier: siteIdentifier });

    if (!companySiteProfile) {
      Errors.throw(Errors.types.notFound, `SiteIdentifier: ${siteIdentifier}`);
    }

    return companySiteProfile;
  },

  call(args, callback) {
    return Meteor.call(this.name, args, callback);
  },
};
