import { Errors } from '../../api.helpers/errors';
import { GetSiteFromIdentifier } from './get-site-from-identifier';
import { Meteor } from 'meteor/meteor';
import SimpleSchema from 'simpl-schema';
import { User } from '../../api.helpers/user';

const command = {
  siteIdentifier: String,
  vendorId: String,
};

export const GetVendorFromId = {
  name: 'companySiteProfiles.getVendorFromId',

  validate(args) {
    new SimpleSchema(command)
      .validate(args);
  },

  run({ siteIdentifier, vendorId }) {
    if (!User.hasAccessToSite(siteIdentifier)) {
      Errors.throw(Errors.types.noAccessToSite, `UserId: ${this.userId}, ` +
        `SiteIdentifier: ${siteIdentifier}`);
    }

    const companySiteProfile = GetSiteFromIdentifier.call({ siteIdentifier });

    const vendors = companySiteProfile.configuration.vendors;

    if (!vendors) {
      Errors.throw(Errors.types.notFound, `Vendors configuration for Site: ${siteIdentifier}`);
    }

    const vendor = vendors.find(v => v._id === vendorId);

    if (!vendor) {
      Errors.throw(Errors.types.notFound, `VendorId: ${vendorId} for Site: ${siteIdentifier}`);
    }

    return vendor;
  },

  call(args, callback) {
    return Meteor.call(this.name, args, callback);
  },
};
