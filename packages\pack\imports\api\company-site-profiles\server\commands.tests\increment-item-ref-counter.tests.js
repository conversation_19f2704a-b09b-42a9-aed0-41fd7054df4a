/* eslint-env mocha */
/* eslint-disable func-names, prefer-arrow-callback */

import { Factory } from 'meteor/dburles:factory';
import { IncrementItemRefCounter } from '../../commands/increment-item-ref-counter';
import { TestUtils } from '../../../api.helpers/test-utils';
import { chai } from 'meteor/practicalmeteor:chai';
import sinon from 'sinon';

const sandbox = sinon.createSandbox();

describe('IncrementItemRefCounter', function () {
  before(function () {
    TestUtils
      .resetWholeDb()
      .setupCommonStaticData();
  });

  afterEach(function () {
    TestUtils
      .resetSandbox(sandbox)
      .resetDbIgnoringStaticData();
  });

  it('increments the receipt no counter by 1 for the specified site profile', function () {

  });

  it('throws error if user does not have access to site', function () {

  });
});
