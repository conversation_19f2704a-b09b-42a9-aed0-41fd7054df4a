/* eslint-env mocha */
/* eslint-disable func-names, prefer-arrow-callback */

import { Factory } from 'meteor/dburles:factory';
import { IncrementItemRefCounter } from '../../commands/increment-item-ref-counter';
import { TestUtils } from '../../../api.helpers/test-utils';
import { chai } from 'meteor/practicalmeteor:chai';
import sinon from 'sinon';

const sandbox = sinon.createSandbox();

describe('ResetItemRefCounter', function () {
  before(function () {
    TestUtils
      .resetWholeDb()
      .setupCommonStaticData();
  });

  afterEach(function () {
    TestUtils
      .resetSandbox(sandbox)
      .resetDbIgnoringStaticData();
  });

  it('resets the item ref counter for the specified site to the specified number.', function () {

  });
});
