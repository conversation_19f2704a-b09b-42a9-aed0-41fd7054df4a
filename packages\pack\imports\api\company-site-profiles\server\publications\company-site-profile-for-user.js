import { CompanySiteProfiles } from '../../company-site-profiles';
import { Publications } from '../../../api.publications/publications';
import { User } from '../../../api.helpers/user';

export const CompanySiteProfileForUser = {
  name: Publications.companySiteProfiles.companySiteProfileForUser,

  validate() {
    // Nothing to validate
  },

  run() {
    const siteId = User.activeSite();

    return CompanySiteProfiles.find({ identifier: siteId });
  },
};
