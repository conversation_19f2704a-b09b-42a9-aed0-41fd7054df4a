import { Errors } from '../../api.helpers/errors';
import { GetClientFromId } from '../../company-site-profiles/queries/get-client-from-id';
import { ItemLocationReports } from '../item-location-reports';
import { Log } from '../../api.helpers/log';
import { Meteor } from 'meteor/meteor';
import SimpleSchema from 'simpl-schema';
import { StoredItemLocationReport } from '../queries/stored-item-location-report';

const command = {
  clientId: String,
  siteIdentifier: String,
};

export const UpdateLocationReportForSitesClient = {
  name: 'itemLocationReports.updateLocationReportForSitesClient',

  validate(args) {
    new SimpleSchema(command)
      .validate(args);
  },

  run({ clientId, siteIdentifier }) {
    const itemLocationReport = StoredItemLocationReport
      .call({ siteIdentifier, clientId });
    const client = GetClientFromId.call({ siteIdentifier, clientId });

    ItemLocationReports
      .update(
        { clientId, clientName: client.name, siteId: siteIdentifier },
        { $set: { itemLocationReport } },
        { upsert: true },
        (error) => {
          if (error) {
            Log.error(
              Errors.types.commandFailed,
              `UpdateLocationReportForSitesClient failed with error: ${JSON.stringify(error)}`,
            );
          }
        },
      );
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
