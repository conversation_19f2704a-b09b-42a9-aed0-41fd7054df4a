import SimpleSchema from 'simpl-schema';
import { SiteIdSchema } from '../api.shared-schemas/shared-schemas';

const LocationCountSchema = new SimpleSchema({
  location: {
    type: String,
  },
  itemCount: {
    type: SimpleSchema.Integer,
  },
});

const LocationReportDataSchema = new SimpleSchema({
  reportRunDateTime: {
    type: Date,
  },
  locationCounts: {
    type: Array,
  },
  'locationCounts.$': {
    type: LocationCountSchema,
  },
});

export const ItemLocationReportSchema = new SimpleSchema({
  locationReportData: {
    type: LocationReportDataSchema,
  },
  clientId: {
    type: String,
  },
});

ItemLocationReportSchema.extend(SiteIdSchema);
