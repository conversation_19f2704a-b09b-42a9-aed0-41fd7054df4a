import { Errors } from '../../api.helpers/errors';
import { GetClientFromId } from '../../company-site-profiles/queries/get-client-from-id';
import { GetSiteFromIdentifier } from
  '../../company-site-profiles/queries/get-site-from-identifier';
import { Items } from '../../items/items';
import { Meteor } from 'meteor/meteor';
import SimpleSchema from 'simpl-schema';
import moment from 'moment';

const query = {
  siteIdentifier: String,
  clientId: String,
};

export const StoredItemLocationReport = {
  name: 'itemLocationReports.reports.storedItemLocationReport',

  validate(args) {
    new SimpleSchema(query)
      .validate(args);
  },

  run({ siteIdentifier, clientId }) {
    const siteProfile = GetSiteFromIdentifier.call({ siteIdentifier });
    const client = GetClientFromId.call({ clientId, siteIdentifier });
    const storageLocations = siteProfile.configuration.storageLocations;

    if (!storageLocations) {
      Errors.throw(Errors.types.notFound, `No Storage Locations for Site: ${siteIdentifier}`);
    }

    const itemLocationsReport = {
      reportRunDateTime: moment().toDate(),
      locationReportData: {
        locationCounts: [],
      },
    };

    const selector = {
      siteId: siteProfile.identifier,
      clientId: client._id,
      isDeleted: false,
      isStored: true,
    };

    for (const storageLocation of storageLocations) {
      const locationCount = {
        location: location.name,
        itemCount: null,
      };

      selector.location = storageLocation.name;

      locationCount.itemCount = Items.find(selector).count();

      itemLocationsReport.locationReportData.locationCounts.push(locationCount);
    }

    return itemLocationsReport;
  },

  call(args, callback) {
    return Meteor.call(this.name, args, callback);
  },
};
