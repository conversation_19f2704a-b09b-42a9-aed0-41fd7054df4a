import { GetClientFromId } from '../../../company-site-profiles/queries/get-client-from-id';
import { GetSiteFromIdentifier } from
  '../../../company-site-profiles/queries/get-site-from-identifier';
import { ItemLocationReports } from '../../../item-location-reports/item-location-reports';
import { Publications } from '../../../api.publications/publications';
import SimpleSchema from 'simpl-schema';

const pubQuery = {
  clientId: String,
  siteIdentifier: String,
};

export const StoredItemsLocationCounts = {
  name: Publications.itemLocationReports.storedItemsLocationCounts,

  validate(args) {
    new SimpleSchema(pubQuery)
      .validate(args);
  },

  run({ clientId, siteIdentifier }) {
    const siteProfile = GetSiteFromIdentifier.call({ siteIdentifier });
    const client = GetClientFromId.call({ clientId, siteIdentifier });

    return ItemLocationReports.find({
      clientId: client._id,
      siteId: siteProfile.identifier,
    });
  },
};
