import { Errors } from '../../api.helpers/errors';
import { EventFactory } from '../../api.events/event-factory';
import { Items } from '../../items/items';
import { Meteor } from 'meteor/meteor';
import { ReceiptTypes } from '../receipt.types';
import SimpleSchema from 'simpl-schema';
import { User } from '../../api.helpers/user';
import moment from 'moment';
import { Log } from '../../api.helpers/log';
import { CompanySiteProfiles } from '../../company-site-profiles/company-site-profiles';
import { ReceiptNumberCalculator } from '../../../shared/receiptnumber-calculator';

const addClientToSiteConfiguration = (itemOffshoreClientUppercase, siteProfile) => {
  Log.info(`Adding new offshoreClient name to site configuration offshoreClients: <${itemOffshoreClientUppercase}>`);
  const offshoreClient = {
    _id: new Mongo.ObjectID()._str,
    name: itemOffshoreClientUppercase,
    isActive: false,
  };

  CompanySiteProfiles.update(
    { _id: siteProfile._id },
    { $push: { 'configuration.offshoreClients': offshoreClient } },
  );
};

const addNewOffshoreClientIfNotInSiteConfiguation = (item, siteIdentifier) => {
  const itemOffshoreClientUppercase = item.offshoreClient.toUpperCase();
  const siteProfile = CompanySiteProfiles.findOne({ identifier: siteIdentifier });
  const config = siteProfile.configuration;
  const currentOffshoreClients = config.offshoreClients;
  if (!currentOffshoreClients.find(x => x.name === itemOffshoreClientUppercase)) {
    addClientToSiteConfiguration(itemOffshoreClientUppercase, siteProfile);
  }
};

const getItemToInsert = (itemPropertiesFromClient, siteIdentifier) => {
  const props = itemPropertiesFromClient;

  const nowUtcDateTime = moment().utc().toDate();

  const itemToInsert =
  {
    receiptNo: null, // Set when saved.
    itemLineIndex: 0, // Only relevant if from Flow material line.
    cargoItemId: null, // Item is being created without a CCU (Cargo Item so this stays null). Use this to identify manual item creations
    externalCargoLineId: null,
    receiptType: null,
    receivedDate: null, // set when saved.
    siteId: null, // Set dynamically based on user's site.
    client: {}, // Set dynamically
    receiptLocation: null,
    offshoreClient: null, // Set in UI.
    ccu: null, // keep as null, this has not come from a (Flow) CCU.
    offshoreLocation: null, // Set in UI.
    voyageNo: null, // Leave as null.
    ccuManifestNo: null, // Leave as null.
    weightKg: 0, // Set in UI.
    externalMaterialLineId: null,
    ecargoMaterialInfo: null, // Copy of Flow material info (not relevant for manual material insert)
    euralCode: null, // Set in UI.
    description: null, // Set in UI.
    imoHazardClass: null, // Set in UI.
    imoSubClass: null, // Set in UI.
    unNo: null, // Set in UI.
    imoCode: null, // Usually 'IMDG or null' for Flow items.
    isWaste: null, // Set in UI.
    materialManifestNo: null,
    packingUnit: null,
    marinePollutant: null, // (bool) Set in UI.
    wasteDescription: null, // Set in UI.
    quantity: 1,
    events: [], // Set first receipted event when saved.
    isStored: false,
    isPacked: false,
    isDispatched: false,
    receiptCategory: 'Standard Receipt',
    ncrs: [],
    materialReceiptDateTime: null, // ISODate('2018-11-21T09:50:34.435Z'),
    packageType: null, // Set in UI.
  };

  const siteProfile = CompanySiteProfiles.findOne({ identifier: siteIdentifier });
  const config = siteProfile.configuration;
  const receiptLocation = config.receiptLocations[0].name;
  const clientObject = config.clients.find(x => x.name === 'Peterson');
  const receiptNo = ReceiptNumberCalculator.getReceiptNoAndIncrement(config, siteIdentifier);
  const itemLineIndex = 1; // Normally this is the material-line-index within the cargo item.
  const receiptNoWithLineIndex = `${receiptNo}-${itemLineIndex}`;
  const defaultReceiptCategory = config.receiptCategories[0].name; // "Standard Receipt".

  // Set values from server.
  itemToInsert.siteId = siteIdentifier;
  itemToInsert.receiptNo = receiptNoWithLineIndex;
  itemToInsert.receiptType = ReceiptTypes.chemReceipt;
  itemToInsert.materialReceiptDateTime = nowUtcDateTime;
  itemToInsert.receivedDate = nowUtcDateTime; // Used as (CCU) cargo item received date - just set as same as material receipt date for now. NOTE THIS IS USED FOR REPORT FILTERING SO NEEDS A VALUE
  itemToInsert.receiptLocation = receiptLocation;
  itemToInsert.client = clientObject;
  itemToInsert.itemLineIndex = itemLineIndex;
  itemToInsert.receiptCategory = defaultReceiptCategory;
  itemToInsert.externalMaterialLineId = 'N/A';
  itemToInsert.externalCargoLineId = 'N/A';

  // Set values from client input form.
  itemToInsert.offshoreClient = props.offshoreClient.trim(); // Ensure trailing leading spaces removed.
  itemToInsert.ccu = props.ccu;
  itemToInsert.offshoreLocation = props.offshoreLocation;
  itemToInsert.voyageNo = props.voyageNo;
  itemToInsert.description = props.description; // material description.
  itemToInsert.wasteDescription = props.wasteDescription;
  itemToInsert.marinePollutant = props.isMarinePollutant;
  itemToInsert.weightKg = props.weightKg;
  itemToInsert.unNo = props.unNo;
  itemToInsert.imoHazardClass = props.imoHazardClass;
  itemToInsert.imoSubClass = props.imoSubClass;
  itemToInsert.euralCode = props.euralCode;
  itemToInsert.isWaste = props.isWaste;
  itemToInsert.quantity = props.quantity;
  itemToInsert.packageType = props.packageType;
  itemToInsert.ncrs = props.ncrs;

  // Set RECEIPTED event on item.
  const receiptDateTime = nowUtcDateTime;
  const receiptedEvent = EventFactory.createItemEvent(
    EventFactory.Events.Item.RECEIPTED,
    receiptDateTime,
    Meteor.user().username,
  );
  itemToInsert.events = [];
  itemToInsert.events.push(receiptedEvent);

  return itemToInsert;
};

// Use for validation of command arguments.
const command = {
  packageType: String,
  quantity: Number,
  weightKg: {
    type: Number,
    optional: true,
  },
  description: String,
  wasteDescription: String,
  isWaste: Boolean,
  isMarinePollutant: Boolean,
  offshoreLocation: String,
  offshoreClient: String,
  ccu: String,
  voyageNo: String,
  imoHazardClass: String,
  imoSubClass: String,
  euralCode: String,
  unNo: String,
  ncrs: Array,
  'ncrs.$': String,
};

export const CreateAndReceiptItem = {
  name: 'items.createAndReceipt',

  validate(args) {
    new SimpleSchema(command)
      .validate(args);
  },

  run(itemProperties) {
    Log.info('CreateAndReceiptItem called', itemProperties);
    const siteIdentifier = User.activeSite(); // peterson-chemicals-dnhr

    const itemToInsert = getItemToInsert(itemProperties, siteIdentifier);

    Log.info(`Registering new item ${JSON.stringify(itemToInsert, null, 2)}`);
    const itemId = Items.insert(itemToInsert); // Insert it and record Id.

    if (!itemId) {
      Errors.throw(Errors.types.commandFailed, 'Material Item create and insert failed.');
    } else {
      Log.info(`Material Item created and inserted: <${itemId}>`);
    }

    const item = Items.findOne(itemId);

    /* If new offshoreClient is specified - add to list of offshoreClients in site configuration with isActive false
    so that it doesn't become a valid operator but can still be filtered against. */
    addNewOffshoreClientIfNotInSiteConfiguation(item, siteIdentifier);

    return (item); // return the item.
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
