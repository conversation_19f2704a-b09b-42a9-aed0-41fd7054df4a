import { Errors } from '../../api.helpers/errors';
import { EventFactory } from '../../api.events/event-factory';
import { GetSiteFromIdentifier } from '../../company-site-profiles/queries/get-site-from-identifier';
import { Items } from '../items';
import { Meteor } from 'meteor/meteor';
import { ReceiptTypes } from '../receipt.types';
import SimpleSchema from 'simpl-schema';
import { User } from '../../api.helpers/user';
import moment from 'moment';
import { Log } from '../../api.helpers/log';

const command = {
  packingUnit: {
    type: String,
    optional: false,
  },
  setDispatched: {
    type: Boolean,
    optional: false,
  },
};

export const SetItemsInPackingUnitDispatched = {
  name: 'items.setDispatchedByPackingUnit',

  validate(args) {
    new SimpleSchema(command)
      .validate(args);
  },

  run({ packingUnit, setDispatched = true }) {
    if(setDispatched) {
      Log.info('Dispatching item(s) in packing unit', packingUnit);
      var matchedDocuments = dispatch(packingUnit);
      var message = `<${matchedDocuments}> Material Item(s) were just dispatched.`;
    }
    else {
      Log.info('Un-Dispatching item(s) in packing unit', packingUnit);
      var matchedDocuments = unDispatch(packingUnit);
      var message = `<${matchedDocuments}> Material Item(s) were just undispatched.`;
    }

    if (matchedDocuments === 0) {
      throw new Meteor.Error(Errors.types.notFound, `No items found for packing unit: ${packingUnit}`);
    } else {
      Log.info(message);
    }
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};

const dispatch = (packingUnit) => {
  const dispatchedDate = moment().utc();

  const dispatchEvent = EventFactory.createItemEvent(
    EventFactory.Events.Item.DISPATCHED,
    dispatchedDate.toDate(),
    Meteor.user().username,
    {
      packingUnit,
    },
  );

  const matchedDocuments = Items.update({
    receiptType: ReceiptTypes.chemReceipt, // Updated for Chemicals.
    isPacked: true,
    packingUnit,
  }, {
    $set: {
      isDispatched: true,
      dispatchedDate: dispatchedDate.toDate(),
    },
    $push: {
      events: dispatchEvent,
    },
  }, {
    multi: true,
  });

  return matchedDocuments;
};

const unDispatch = (packingUnit) => {
  // Get all items in packing unit
  const query = {
    receiptType: ReceiptTypes.chemReceipt,
    isDispatched: true,
    packingUnit,
  };

  const items = Items.find(query, {
    _id: 1,
    events: 1,
  }).fetch();

  items.forEach((item) => {
    // Cycle through events and mark any with eventType == "dispatched" as deleted
    const dispatchEvents = item.events.map((event) => {
      if (event.eventType == EventFactory.Events.Item.DISPATCHED) {
        event.isDeleted = true;
      }

      return event;
    });

    Items.update(
      {
        _id: item._id,
        receiptType: ReceiptTypes.chemReceipt,
      },
      {
        $set: {
          isDispatched: false,
          events: dispatchEvents, // Write the updated list of events back.
        },
        $unset: {
          dispatchedDate: 1,
          dispatchedDateStr: 1,
        },
      },
    );
  });

  return items.length;
};
