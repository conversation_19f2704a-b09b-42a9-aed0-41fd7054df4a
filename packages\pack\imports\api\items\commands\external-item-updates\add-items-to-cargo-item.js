import { Items } from '../../items';
import { Cargo } from '../../../cargo/cargo';
import SimpleSchema from 'simpl-schema';
import { User } from '../../../api.helpers/user';
import generateNextItemLineIndex from '../generate-next-item-line-index';
import { mapEcargoMaterialToNewItem } from '../item-mappers';

const command = {
  cargoItemId: String,
  newItemDtos: [{
    type: Array,
    blackbox: true,
  }],
};

export const AddItemsToCargoItem = {
  name: 'items.addItemsToCargoItem',
  allowInBackground: true,
  validate(args) {

  },

  run({
    cargoItemId,
    newItemDtos,
  }) {
    const cargoItem = Cargo.findOne(cargoItemId);
    const existingItemsBelongingToCargoItem = Items.find({ cargoItemId: cargoItem._id}).fetch();

    const itemsToInsert = [];

    newItemDtos.forEach((newItemDto) => {
      itemsToInsert.push(
        mapEcargoMaterialToNewItem(
          cargoItemId,
          cargoItem,
          newItemDto,
          generateNextItemLineIndex(cargoItem, [...existingItemsBelongingToCargoItem, ...itemsToInsert]),
        ),
      );
    });

    itemsToInsert.forEach((itemToInsert) => Items.insert(itemToInsert));
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
