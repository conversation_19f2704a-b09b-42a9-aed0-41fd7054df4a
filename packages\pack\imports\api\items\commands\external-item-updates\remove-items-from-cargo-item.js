import { Items } from '../../items';
import { Cargo } from '../../../cargo/cargo';
import SimpleSchema from 'simpl-schema';
import { User } from '../../../api.helpers/user';
import { ReceiptTypes } from '../../receipt.types';
import { Log } from '../../../api.helpers/log';

const command = {
  cargoItemId: String,
  idsOfItemsToRemove: [String],
};

export const RemoveItemsFromCargoItem = {
  name: 'items.removeItemsFromCargoItem',
  allowInBackground: true,
  validate(args) {

  },

  run({
    cargoItemId,
    idsOfItemsToRemove,
  }) {
    const cargoItem = Cargo.findOne(cargoItemId);
    const itemsToRemove = Items.find({ _id: { $in: idsOfItemsToRemove } }).fetch();

    itemsToRemove.forEach((itemToRemove) => {
      const canBeRemoved = itemToRemove.receiptType === ReceiptTypes.chemPreReceipt;
      if (canBeRemoved) {
        Items.remove(
          { _id: itemToRemove._id },
        );
      } else {
        Log.info(`Item ${itemToRemove._id} removal ignored as item has been receipted.`);
      }
    });
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
