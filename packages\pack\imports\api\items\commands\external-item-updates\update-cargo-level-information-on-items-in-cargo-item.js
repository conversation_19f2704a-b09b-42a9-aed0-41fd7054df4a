import { Items } from '../../items';
import { Cargo } from '../../../cargo/cargo';
import SimpleSchema from 'simpl-schema';
import { User } from '../../../api.helpers/user';
import { mergeEcargoDeckCargoItemIntoItem } from '../item-mappers'
import { ReceiptTypes } from '../../receipt.types';
import { Log } from '../../../api.helpers/log';

const command = {
  cargoItemId: String,
  cargoUpdateDto: {
    type: Object,
    blackbox: true,
  },
};

export const UpdateCargoLevelInformationOnItemsInCargoItem = {
  name: 'items.updateCargoLevelInformationOnItemsInCargoItem',
  allowInBackground: true,
  validate(args) {

  },

  run({
    cargoItemId,
    cargoUpdateDto,
  }) {
    const cargoItem = Cargo.findOne(cargoItemId);
    const existingItemsBelongingToCargoItem = Items.find({ cargoItemId }).fetch();

    existingItemsBelongingToCargoItem.forEach((item) => {
      const canBeUpdated = item.receiptType === ReceiptTypes.chemPreReceipt;

      if (canBeUpdated) {
        mergeEcargoDeckCargoItemIntoItem(
          item,
          cargoUpdateDto);

        Items.update(
          { _id: item._id },
          { $set: item },
        );
      } else {
        Log.info(`Item ${item._id} update (cargo level info) ignored as item has been receipted.`);
      }
    });
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
