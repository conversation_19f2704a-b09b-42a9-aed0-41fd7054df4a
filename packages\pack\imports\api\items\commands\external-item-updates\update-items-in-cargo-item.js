import { Items } from '../../items';
import { Cargo } from '../../../cargo/cargo';
import SimpleSchema from 'simpl-schema';
import { User } from '../../../api.helpers/user';
import { mergeEcargoMaterialIntoItem } from '../item-mappers'
import { Log } from '../../../api.helpers/log';
import { ReceiptTypes } from '../../receipt.types';

const command = {
  cargoItemId: String,
  itemUpdateDtos: [{
    type: Array,
    blackbox: true,
  }],
};

export const UpdateItemsInCargoItem = {
  name: 'items.updateItemsInCargoItem',
  allowInBackground: true,
  validate(args) {

  },

  run({
    cargoItemId,
    itemUpdateDtos,
  }) {
    const idsOfItemsToUpdate = itemUpdateDtos.map((x) => x.itemId);
    const itemsToUpdate = Items.find({ _id: { $in: idsOfItemsToUpdate } }).fetch();

    itemUpdateDtos.forEach(({ itemId, itemUpdateDto }) => {
      const itemToUpdate = itemsToUpdate.find((x) => x._id === itemId);
      const canBeUpdated = itemToUpdate.receiptType === ReceiptTypes.chemPreReceipt;

      if (canBeUpdated) {
        mergeEcargoMaterialIntoItem(
          itemToUpdate,
          itemUpdateDto,
        );
        Items.update(
          { _id: itemToUpdate._id },
          { $set: itemToUpdate },
        );
      } else {
        Log.info(`Item ${itemToUpdate._id} update ignored as item has been receipted.`);
      }
    });
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
