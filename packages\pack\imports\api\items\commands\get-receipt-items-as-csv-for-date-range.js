import { CSV } from 'meteor/clinical:csv';
import { Items } from '../items';
import { Meteor } from 'meteor/meteor';
import SimpleSchema from 'simpl-schema';
import { dtoMapper } from '../../../shared/chem-dto-mapper';
import { User } from '../../api.helpers/user';
import moment from 'moment';

const params = {
  reportStartDateStr: {
    type: String,
    optional: true,
  },
  reportEndDateStr: {
    type: String,
    optional: true,
  },
  dateToFilterBy: {
    type: String,
    optional: true,
  },
};

export const GetItemsAsCsvForDateRange = {
  name: 'items.getItemsAsCsvForDateRange',

  validate(args) {
    new SimpleSchema(params)
      .validate(args);
  },

  run({ reportStartDateStr, reportEndDateStr, dateToFilterBy }) {
    const siteId = User.activeSite();

    const query = {
      $and: [
        { siteId: siteId },
      ],
    };

    if (reportStartDateStr) {
      query.$and
        .push({ [dateToFilterBy]: { $gte: moment(reportStartDateStr).startOf('day').toDate() } });
    }

    if (reportEndDateStr) {
      query.$and
        .push({ [dateToFilterBy]: { $lte: moment(reportEndDateStr).endOf('day').toDate() } });
    }

    const collection = Items.find(query, { sort: { receivedDate: 1 } }).fetch();

    const displayCollection = collection.map(
      (item) => dtoMapper.mapItemObject(item, null, dtoMapper.materialsFields),
    );

    const heading = false; // Optional, defaults to true
    const delimiter = ','; // Optional, defaults to ",";
    return CSV.unparse(displayCollection, heading, delimiter);
  },

  call(args, callback) {
    console.log('Calling', this.name);
    Meteor.call(this.name, args, callback);
  },
};
