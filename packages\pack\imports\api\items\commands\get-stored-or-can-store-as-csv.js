import { CSV } from 'meteor/clinical:csv';
import { Items } from '../items';
import { Meteor } from 'meteor/meteor';
import SimpleSchema from 'simpl-schema';
import { dtoMapper } from '../../../shared/chem-dto-mapper';
import { User } from '../../api.helpers/user';
import { ReceiptTypes } from '../receipt.types';

const params = {
  clientId: String,
  query: {
    type: String,
    optional: true,
  },
  ignorePacked: {
    type: Boolean,
    optional: true,
  },
  receiptCategory: {
    type: String,
    optional: true,
  },
  storedItemsOnly: {
    type: Boolean,
    optional: true,
  },
  location: {
    type: String,
    optional: true,
  },
  fromDate: {
    type: Date,
    optional: true,
  },
  toDate: {
    type: Date,
    optional: true,
  },
  offshoreLocation: {
    type: String,
    optional: true,
  },
  offshoreClient: {
    type: String,
    optional: true,
  },
  limit: {
    type: SimpleSchema.Integer,
    optional: true,
  },
};

const queryBuilder = ({
  clientId,
  query,
  ignorePacked = true,
  storedItemsOnly,
  receiptCategory = null,
  offshoreLocation = null,
  offshoreClient = null,
  location = null,
  fromDate = null,
  toDate = null,
})=>{
  const siteId = User.activeSite();
  const selector = {
    $and: [
      {
        'client._id': clientId,
        siteId,
        isStored: storedItemsOnly,
        isPacked: !ignorePacked,
        isDispatched: false, // Exclude Dispatched items (no longer stored)
        receiptType: ReceiptTypes.chemReceipt, // Chemicals receipt type when material is receipted.
      },
    ],
  };

  if (receiptCategory) {
    selector.$and.push({ receiptCategory });
  }

  if (location) {
    selector.$and.push({ location });
  }

  if (offshoreLocation) {
    selector.$and.push({ offshoreLocation });
  }

  if (fromDate) {
    selector.$and.push({ receivedDate: { $gte:fromDate } });
  }

  if (toDate) {
    selector.$and.push({ receivedDate: { $lte:toDate } });
  }

  if (offshoreClient) {
    // Edited to be case insensitive search - note this may have perf impact as it won't use indexes.
    selector.$and.push({ offshoreClient: { $regex: new RegExp(offshoreClient, 'i') } });
  }

  if (query && query.length) {
    selector.$and.push({ query });
  }

  return selector;
};

export const GetStoredOrCanStoreAsCsv = {
  name: 'items.getStoredOrCanStoreAsCsv',

  validate(args) {
    new SimpleSchema(params)
      .validate(args);
  },

  run(args) {
    const query = queryBuilder(args);
    const collection = Items.find(query).fetch();
    const displayCollection = collection.map(
      (item) => dtoMapper.mapItemObject(item, null, dtoMapper.materialsFields),
    );

    const heading = false; // Optional, defaults to true
    const delimiter = ','; // Optional, defaults to ",";
    return CSV.unparse(displayCollection, heading, delimiter);
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
