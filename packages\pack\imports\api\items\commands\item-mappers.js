import { ReceiptTypes } from '../receipt.types';

const initItemWithCargoLevelInformation = (item, ecargoDeckCargoItem) => {
  item.receivedDate = ecargoDeckCargoItem.receivedDate;
};

const initItemWithMaterialInformation = (item, ecargoMaterial) => {
  item.weightKg = 0.00;
  item.externalMaterialLineId = ecargoMaterial.lineId;
};

export const mergeEcargoDeckCargoItemIntoItem = (item, ecargoDeckCargoItem) => {
  item.siteId = ecargoDeckCargoItem.siteId;
  item.client = ecargoDeckCargoItem.client;
  item.receiptLocation = ecargoDeckCargoItem.receiptLocation;
  item.ccuDescription = ecargoDeckCargoItem.description;
  item.offshoreClient = ecargoDeckCargoItem.offshoreClient;
  item.ccu = ecargoDeckCargoItem.ccu;
  item.offshoreLocation = ecargoDeckCargoItem.offshoreLocation;
  item.voyageNo = ecargoDeckCargoItem.voyageNo;
  item.ccuManifestNo = ecargoDeckCargoItem.manifestNo; // Assume this is 'voyage level' manifest containing list of cargo.
};

export const mergeEcargoMaterialIntoItem = (item, ecargoMaterial) => {
  // Set material specific fields.
  item.ecargoMaterialInfo = ecargoMaterial;
  item.euralCode = ecargoMaterial.euralcode;
  item.description = ecargoMaterial.description;
  item.imoHazardClass = ecargoMaterial.imoHazardClass;
  item.imoSubClass = ecargoMaterial.imoSubclass;
  item.unNo = ecargoMaterial.unNo;
  item.imoCode = ecargoMaterial.imoCode;
  item.isWaste = ecargoMaterial.isWaste;
  item.materialManifestNo = ecargoMaterial.manifestNo; // ManifestNo on Flow material - TODO: Confirm that this is the same value as ccu.ManifestNo.
  item.packingUnit = ecargoMaterial.packingUnit;
  item.marinePollutant = ecargoMaterial.marinePollutant;
  item.wasteDescription = ecargoMaterial.wasteDescription;
  // Set fields to be set/confirmed during receipt of item..
  item.quantity = ecargoMaterial.quantity;
};

export const mapEcargoMaterialToNewItem = (cargoItemId, ecargoDeckCargoItem, ecargoMaterial, itemLineIndex) => {
  let item = {};

  // Set receiptNo on material based on receipt no of Cargo Item.
  item.receiptNo = `${ecargoDeckCargoItem.receiptNo}-${itemLineIndex}`;
  item.itemLineIndex = itemLineIndex;

  // Set link back to CargoItem
  item.cargoItemId = cargoItemId; // GUID.

  // Set the external (Flow) CargoLineId on the material item.
  item.externalCargoLineId = ecargoDeckCargoItem.externalCargoLineId;
  // MUST set receipt type - to indicate pre-receipt, prior to main receipt of material.
  item.receiptType = ReceiptTypes.chemPreReceipt;

  // Set fields on material that are direct copies from cargo item.
  initItemWithCargoLevelInformation(item, ecargoDeckCargoItem);
  mergeEcargoDeckCargoItemIntoItem(item, ecargoDeckCargoItem);
  initItemWithMaterialInformation(item, ecargoMaterial);
  mergeEcargoMaterialIntoItem(item, ecargoMaterial);

  return item;
};
