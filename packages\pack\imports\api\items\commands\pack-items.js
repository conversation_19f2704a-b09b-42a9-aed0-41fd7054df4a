import { Errors } from '../../api.helpers/errors';
import { EventFactory } from '../../api.events/event-factory';
import { Items } from '../../items/items';
import { Meteor } from 'meteor/meteor';
import { ReceiptTypes } from '../receipt.types';
import SimpleSchema from 'simpl-schema';
import { User } from '../../api.helpers/user';
import moment from 'moment';
import { Log } from '../../api.helpers/log';

const command = {
  itemIds: Array,
  'itemIds.$': String,
  requestId: String,
  packingUnitId: String,
  packingUnitIdentifier: String,
};

export const PackItems = {
  name: 'items.packItems',

  validate(args) {
    new SimpleSchema(command)
      .validate(args);
  },

  run({
    itemIds, requestId, packingUnitId, packingUnitIdentifier,
  }) {
    Log.info('Pack Items called', { itemIds, requestId, packingUnitId, packingUnitIdentifier });
    const siteId = User.activeSite();

    const selector = {
      _id: { $in: itemIds },
      siteId,
      receiptType: ReceiptTypes.chemReceipt, // Edited for Chemicals.
    };

    const packedDateTime = moment().utc().toDate();
    const packedEvent = EventFactory.createItemEvent(
      EventFactory.Events.Item.PACKED,
      packedDateTime,
      Meteor.user().username,
      {
        requestId,
        packingUnitId,
        packingUnitIdentifier,
      },
    );

    const update = {
      $set: {
        requestId,
        packingUnit: packingUnitId, // Required for filtering
        packingUnitIdentifier,
        isPacked: true,
        packedDate: packedDateTime,
      },
      $push: {
        events: packedEvent,
      },
    };

    const updated = Items.update(selector, update, { multi: true });

    if (updated === 0) {
      Errors.throw(Errors.types.commandFailed, 'Update failed.');
    } else {
      Log.info(`PackItems numItems updated: <${updated}>`);
    }
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
