import { AddPo } from '../../purchase-orders/commands/add-po';
import { AobPreReceiptSchema } from '../receipt.schemas/aob-pre-receipt.schema';
import { GetVendorFromId } from
  '../../company-site-profiles/queries/get-vendor-from-id';
import { IncrementItemRefCounter } from
  '../../company-site-profiles/commands/increment-item-ref-counter';
import { Items } from '../items';
import SimpleSchema from 'simpl-schema';
import { User } from '../../api.helpers/user';

const command = {
  receipt: AobPreReceiptSchema,
  isManualReceiptNo: {
    type: Boolean,
    optional: true,
  },
};

export const PreReceiptItem = {
  name: 'items.preReceiptItem',

  validate(args) {
    new SimpleSchema(command)
      .validate(args);
  },

  run({ receipt, isManualReceiptNo = false }) {
    const siteIdentifier = User.activeSite();

    const receiptToInsert = receipt;

    // Don't trust client
    receiptToInsert.siteId = siteIdentifier;

    // Get Vendor from Id
    const vendor = GetVendorFromId.call({ siteIdentifier, vendorId: receipt.vendor });
    receiptToInsert.vendor = vendor.name;

    AddPo.call({
      poIdentifier: receiptToInsert.poNo,
      noOfLines: receiptToInsert.noOfPoLinesReceived,
      receivedAt: receiptToInsert.receivedDate,
      vendor: receiptToInsert.vendor,
      vendorDeliveryNo: receiptToInsert.vendorDeliveryNo,
      siteIdentifier,
    });
    const originalReceiptNo = receiptToInsert.receiptNo;

    for (let i = 0; i < receiptToInsert.noOfPoLinesReceived; i++) {
      receiptToInsert.receiptNo = `${originalReceiptNo}-${i + 1}`;
      Items.insert(receiptToInsert);
    }

    IncrementItemRefCounter.call({ siteIdentifier });
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
