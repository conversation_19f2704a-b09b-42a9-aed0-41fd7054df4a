import { AobNoOfLines } from '../commands/receipt-item.strategies/aob-no-of-lines';
import { AobPreReceipt } from '../commands/receipt-item.strategies/aob-pre-receipt';
import { AobPreReceiptSchema } from '../receipt.schemas/aob-pre-receipt.schema';
import { AobReceipt } from '../commands/receipt-item.strategies/aob-receipt';
import { AobReceiptSchema } from '../receipt.schemas/aob-receipt.schema';
import { ChemPreReceipt } from './receipt-item.strategies/chem-pre-receipt';
import { ChemPreReceiptSchema } from '../receipt.schemas/chem-pre-receipt.schema';
import { ChemReceipt } from './receipt-item.strategies/chem-receipt';
import { ChemReceiptSchema } from '../receipt.schemas/chem-receipt.schema';

import { IncrementItemRefCounter } from
  '../../company-site-profiles/commands/increment-item-ref-counter';
import { Meteor } from 'meteor/meteor';
import { ReceiptTypes } from '../receipt.types';
import SimpleSchema from 'simpl-schema';
import { User } from '../../api.helpers/user';

const command = {
  // There is a bug with function, uncomment when fixed. https://github.com/aldeed/node-simple-schema/issues/112
  // item: SimpleSchema.oneOf(AobPreReceiptSchema, AobReceiptSchema),
  item: {
    type: Object,
    blackbox: true,
  },
};

export const ReceiptItem = {
  name: 'items.receiptItem',

  validate(args) {
    new SimpleSchema(command)
      .validate(args);
  },

  run({ item }) {
    const incrementItemRefCounter = (siteIdentifier) =>
      IncrementItemRefCounter.call({ siteIdentifier });

    // Incrementing counter needs to happen ASAP.
    incrementItemRefCounter(User.activeSite());

    switch (item.receiptType) {
      /* Chemicals */
      case ReceiptTypes.chemPreReceipt:
        ChemPreReceipt.call({ item }, (err) => {
          if (!err) {
            console.log('Finished pre-receipt of CCU cargo item.');
          } else {
            console.log(err);
          }
        });
        break;
      case ReceiptTypes.chemReceipt: // Not used anymore as unable to use callback
        break;

      default:
        throw new Meteor.Error('Receipt Type not recognised.', item.receiptType);
    }
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
