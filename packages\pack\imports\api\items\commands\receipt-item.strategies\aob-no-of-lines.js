import { AobNoOfLinesSchema } from '../../receipt.schemas/aob-no-of-lines.schema';
import { Errors } from '../../../api.helpers/errors';
import { EventFactory } from '../../../api.events/event-factory';
import { GetPoById } from '../../../purchase-orders/queries/get-po-by-id';
import { Items } from '../../items';
import { Log } from '../../../api.helpers/log';
import { Meteor } from 'meteor/meteor';
import { ReceiptTypes } from '../../receipt.types';
import { SimpleSchema } from 'simpl-schema/dist/SimpleSchema';
import { UpdateNoOfLinesReceived } from '../../../purchase-orders/commands/update-no-of-lines-received';
import { User } from '../../../api.helpers/user';
import moment from 'moment';

const command = {
  item: AobNoOfLinesSchema,
};

const initReceipt = (po) => {
  const poId = po._id;
  const siteIdentifier = User.activeSite(); // Don't trust client

  const initialisedReceipt = {
    siteId: siteIdentifier,
    poId,
    poNo: po.identifier,
    client: po.client,
    receiptType: ReceiptTypes.aobPreReceipt,
    receiptNo: po.receiptNo,
    receiptLocation: po.receiptLocation,
    receivedDate: po.receivedDate,
    vendor: po.vendor,
    vendorDeliveryNo: po.vendorDeliveryNo,
  };

  // Create received event
  const receivedEvent = EventFactory.createItemEvent(
    EventFactory.Events.Item.RECEIVED,
    moment().utc().toDate(),
    Meteor.user().username,
  );

  initialisedReceipt.events = [receivedEvent];

  return initialisedReceipt;
};

const receiptItems = (item, noOfLines) => {
  const originalReceiptNo = item.receiptNo;
  const receiptToInsert = item;

  try {
    for (let i = 0; i < noOfLines; i++) {
      receiptToInsert.receiptNo = `${originalReceiptNo}-${i + 1}`;
      receiptToInsert.batchInsertIndex = i + 1;

      Items.insert(receiptToInsert);
    }

    Log.info(`${item.noOfPoLinesReceived} AobPreReceipts inserted into db.`);
  } catch (e) {
    Log.error(e);
    throw e;
  }
};

export const AobNoOfLines = {
  name: 'items.aobNoOfLines',

  validate(args) {
    new SimpleSchema(command)
      .validate(args);
  },

  run({ item }) {
    const po = GetPoById.call({ id: item._id });

    if (!po) {
      throw new Meteor.Error(Errors.types.notFound, `Couldn't find PO with id ${item._id}`);
    }

    const baseReceipt = initReceipt(po);
    try {
      receiptItems(baseReceipt, item.noOfLines);
      UpdateNoOfLinesReceived.call({ poId: item._id, noOfLinesReceived: item.noOfLines });
    } catch (e) {
      Log.error(e);
      throw e;
    }
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
