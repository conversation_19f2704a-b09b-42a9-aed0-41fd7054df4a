import { AddPo } from '../../../purchase-orders/commands/add-po';
import { AobPreReceiptSchema } from '../../receipt.schemas/aob-pre-receipt.schema';
import { EntitiesExistInConfiguration } from '../../../company-site-profiles/queries/entities-exist-in-configuration';
import { Errors } from '../../../api.helpers/errors';
import { EventFactory } from '../../../api.events/event-factory';
import { Items } from '../../../items/items';
import { Log } from '../../../api.helpers/log';
import { Meteor } from 'meteor/meteor';
import SimpleSchema from 'simpl-schema';
import { User } from '../../../api.helpers/user';
import moment from 'moment';

const command = {
  item: AobPreReceiptSchema,
};

const receiptConfigurationValuesAreValid = (receipt, siteIdentifier) => {
  const result = EntitiesExistInConfiguration
    .call({
      siteIdentifier,
      entities: [
        { name: receipt.vendor, configProperty: 'vendor' },
        { name: receipt.client.name, configProperty: 'client' },
        { name: receipt.receiptLocation, configProperty: 'receiptLocation' },
      ],
    });

  if (!result.vendor) {
    Errors.throw(Errors.types.notFound, `Can't find vendor: ${receipt.vendor}, in configuration for site: ${siteIdentifier}.`);
  }

  if (!result.client) {
    Errors.throw(Errors.types.notFound, `Can't find client: ${receipt.client.name}, in configuration for site: ${siteIdentifier}.`);
  }

  if (!result.receiptLocation) {
    Errors.throw(Errors.types.notFound, `Can't find receiptLocation: ${receipt.receiptLocation}, in configuration for site: ${siteIdentifier}.`);
  }
};

const initReceipt = (receipt) => {
  const poId = new Mongo.ObjectID()._str;
  const siteIdentifier = User.activeSite(); // Don't trust client
  receiptConfigurationValuesAreValid(receipt, siteIdentifier);

  const initialisedReceipt = Object
    .assign({ siteId: siteIdentifier, poId }, receipt);

  // Create received event
  const receivedEvent = EventFactory.createItemEvent(
    EventFactory.Events.Item.RECEIVED,
    moment().utc().toDate(),
    Meteor.user().username,
  );

  initialisedReceipt.events = [receivedEvent];

  return initialisedReceipt;
};

const addPo = (
  id,
  identifier,
  noOfLines,
  receivedDate,
  vendor,
  vendorDeliveryNo,
  siteIdentifier,
  client,
  description,
  receiptLocation,
  receiptNo,
) =>
  AddPo.call({
    id,
    identifier,
    noOfLines,
    receivedDate,
    vendor,
    vendorDeliveryNo,
    siteIdentifier,
    client,
    receiptLocation,
    description,
    receiptNo,
  });

const receiptItems = (item) => {
  const originalReceiptNo = item.receiptNo;
  const receiptToInsert = item;
  const noOfPoLinesReceivedInputed = !!item.noOfPoLinesReceived;

  if (noOfPoLinesReceivedInputed && item.noOfPoLinesReceived < 0) {
    Log.debug('Attempted to AobPreReceipt with number of lines received <0 ignoring request.');
    return;
  }

  try {
    addPo(
      receiptToInsert.poId,
      receiptToInsert.poNo,
      receiptToInsert.noOfPoLinesReceived,
      receiptToInsert.receivedDate,
      receiptToInsert.vendor,
      receiptToInsert.vendorDeliveryNo,
      receiptToInsert.siteId,
      receiptToInsert.client,
      receiptToInsert.description,
      receiptToInsert.receiptLocation,
      receiptToInsert.receiptNo,
    );

    if (noOfPoLinesReceivedInputed) {
      // If number of lines received inputed, create lines now
      for (let i = 0; i < item.noOfPoLinesReceived; i++) {
        receiptToInsert.receiptNo = `${originalReceiptNo}-${i + 1}`;
        receiptToInsert.batchInsertIndex = i + 1;

        Items.insert(receiptToInsert);
      }
    }

    Log.info(`${item.noOfPoLinesReceived} AobPreReceipts inserted into db.`);
  } catch (e) {
    Log.error(e);
    throw e;
  }
};

export const AobPreReceipt = {
  name: 'items.aobPreReceipt',

  validate(args) {
    new SimpleSchema(command)
      .validate(args);
  },

  run({ item }) {
    receiptItems(
      initReceipt(item),
    );
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
