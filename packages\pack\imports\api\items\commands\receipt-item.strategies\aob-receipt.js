import { AobReceiptSchema } from '../../receipt.schemas/aob-receipt.schema';
import { EntitiesExistInConfiguration } from '../../../company-site-profiles/queries/entities-exist-in-configuration';
import { Errors } from '../../../api.helpers/errors';
import { EventFactory } from '../../../api.events/event-factory';
import { Items } from '../../items';
import { Meteor } from 'meteor/meteor';
import { ReceiptTypes } from '../../receipt.types';
import SimpleSchema from 'simpl-schema';
import { UpdateReceiptedItems } from '../../../purchase-orders/commands/update-receipted-items';
import { User } from '../../../api.helpers/user';
import moment from 'moment';

const command = {
  item: AobReceiptSchema,
};

const validateConfigurationEntitiesExistForSite = (receipt, siteIdentifier) => {
  const existsInSiteConfigQuery = {
    siteIdentifier,
    entities: [
      { configProperty: 'packageType', name: receipt.packageType },
      { configProperty: 'receiptCategory', name: receipt.receiptCategory },
    ],
  };

  if (receipt.offshoreLocation) {
    existsInSiteConfigQuery.entities.push({
      configProperty: 'offshoreLocation',
      name: receipt.offshoreLocation,
    });
  }

  const entitesExist = EntitiesExistInConfiguration.call(existsInSiteConfigQuery);

  Object.keys(entitesExist).forEach((key) => {
    if (!entitesExist[key]) {
      Errors.throw(Errors.types.notFound, `Can't find ${key}, in configuration for site: ${siteIdentifier}.`);
    }
  });
};

export const AobReceipt = {
  name: 'items.aobReceipt',

  validate(args) {
    new SimpleSchema(command)
      .validate(args);
  },

  run({ item }) {
    const siteIdentifier = User.activeSite();

    validateConfigurationEntitiesExistForSite(item, siteIdentifier);

    const itemToUpdate = Items.findOne({
      poNo: item.poNo,
      receiptType: ReceiptTypes.aobPreReceipt,
      receiptNo: item.receiptNo,
    });

    if (!itemToUpdate) {
      throw new Meteor.Error('Unable to find item to update for po');
    }

    const receiptedEvent = EventFactory.createItemEvent(
      EventFactory.Events.Item.RECEIPTED,
      moment().utc().toDate(),
      Meteor.user().username,
    );

    Items.update({ _id: itemToUpdate._id }, {
      $set: {
        receiptCategory: item.receiptCategory,
        offshoreLocation: item.offshoreLocation,
        offshoreLocationStorageBin: item.offshoreLocationStorageBin,
        isBackload: item.isBackload,
        isQa: item.isQa,
        isYard: item.isYard,
        poLineNo: item.poLineNo,
        deliveryNo: item.deliveryNo,
        customsStatus: item.customsStatus,
        workOrderNo: item.workOrderNo,
        materialNo: item.materialNo,
        packageType: item.packageType,
        quantity: item.quantity,
        weight: item.weight,
        description: item.description,
        ncrs: item.ncrs,
        receiptType: ReceiptTypes.aobReceipt,
      },
      $push: {
        events: receiptedEvent,
      },
    });

    UpdateReceiptedItems.call({ identifier: item.poNo, receiptedItem: itemToUpdate._id });
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
