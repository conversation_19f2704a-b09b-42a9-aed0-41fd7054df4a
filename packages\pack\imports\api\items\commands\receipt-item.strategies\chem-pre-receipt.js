import { AddCargoItem } from '../../../cargo/commands/add-cargo-item';
import { ChemPreReceiptFromClientSchema } from '../../receipt.schemas/chem-pre-receipt.schema';
import { EntitiesExistInConfiguration } from '../../../company-site-profiles/queries/entities-exist-in-configuration';
import { Errors } from '../../../api.helpers/errors';
import { EventFactory } from '../../../api.events/event-factory';
import { Items } from '../../items';
import { Log } from '../../../api.helpers/log';
import { Meteor } from 'meteor/meteor';
import SimpleSchema from 'simpl-schema';
import { User } from '../../../api.helpers/user';
import moment from 'moment';
import generateNextItemLineIndex from '../generate-next-item-line-index';
import { mapEcargoMaterialToNewItem } from '../item-mappers';
import { ReceiptTypes } from '../../receipt.types';

const command = {
  item: ChemPreReceiptFromClientSchema, // Use to validate data coming from browser.
};

const receiptConfigurationValuesAreValid = (receipt, siteIdentifier) => {
  console.log('chem-prereceipt- receiptConfigurationValuesAreValid ...');
  const result = EntitiesExistInConfiguration
    .call({
      siteIdentifier,
      entities: [
        { name: receipt.receiptLocation, configProperty: 'receiptLocation' },
      ],
    });

  if (!result.receiptLocation) {
    Errors.throw(Errors.types.notFound, `Can't find receiptLocation: ${receipt.receiptLocation}, in configuration for site: ${siteIdentifier}.`);
  }
};

const initReceipt = (receipt) => {
  console.log('chem-prereceipt- initialising received cargo item...');
  const cargoItemId = new Mongo.ObjectID()._str;
  const siteIdentifier = User.activeSite(); // Don't trust client

  console.log(`chem-prereceipt- siteIdentifier ${siteIdentifier}`);

  receiptConfigurationValuesAreValid(receipt, siteIdentifier);

  const initialisedReceipt = Object
    .assign({ siteId: siteIdentifier, cargoItemId }, receipt);

  // Create received event
  const receivedEvent = EventFactory.createItemEvent(
    EventFactory.Events.Item.RECEIVED,
    moment().utc().toDate(),
    Meteor.user().username,
  );

  initialisedReceipt.events = [receivedEvent];

  return initialisedReceipt;
};

const addCargoItem = (
  id,
  noOfLines,
  noOfWasteLines,
  receivedDate,
  siteIdentifier,
  client,
  description,
  receiptLocation,
  receiptNo,
  offshoreClient,
  ccu,
  offshoreLocation,
  voyageNo,
  manifestNo,
  ecargoCargoLine,
  externalCargoLineId,
) =>
  AddCargoItem.call({
    id,
    noOfLines,
    noOfWasteLines,
    receivedDate,
    siteIdentifier,
    client,
    receiptLocation,
    description,
    receiptNo,
    offshoreClient,
    ccu,
    offshoreLocation,
    voyageNo,
    manifestNo,
    ecargoCargoLine,
    externalCargoLineId,
  });

const getMaterialItemsFromCargoItem = (cargoItem) => {
  const materialItems = [];

  const numMaterials = (cargoItem.ecargoCargoLine.materials !== null)
    ? cargoItem.ecargoCargoLine.materials.length : 0;

  const itemsCreated = [];
  cargoItem.ecargoCargoLine.materials.forEach((ecargoMaterial) => {
    const item = mapEcargoMaterialToNewItem(
      cargoItem.cargoItemId,
      cargoItem,
      ecargoMaterial,
      generateNextItemLineIndex(cargoItem, itemsCreated),
    );
    itemsCreated.push(item);
  });
  return itemsCreated;
};

const receiptItems = (cargoItem) => {
  console.log(`chem-prereceipt  receiptItems called with cargoItem ${JSON.stringify(cargoItem)}`);
  const noOfMaterialLinesReceived = (cargoItem.ecargoCargoLine.materials !== null) ?
    cargoItem.ecargoCargoLine.materials.length : 0;
  const noOfWasteMaterialLinesReceived = (cargoItem.ecargoCargoLine.materials !== null) ?
    cargoItem.ecargoCargoLine.materials.filter(x => x.isWaste).length : 0;
  const originalReceiptNo = cargoItem.receiptNo;
  const receiptToInsert = cargoItem;
  console.log(`chem-prereceipt  number of material lines: ${noOfMaterialLinesReceived}`);

  try {
    console.log(`chem-prereceipt Adding cargo Item to DB. ${receiptToInsert.ccu}, ${receiptToInsert.receiptNo}.`);
    // Insert and Receive Cargo Item
    addCargoItem(
      receiptToInsert.cargoItemId,
      noOfMaterialLinesReceived,
      noOfWasteMaterialLinesReceived,
      receiptToInsert.receivedDate,
      receiptToInsert.siteId,
      receiptToInsert.client,
      receiptToInsert.description,
      receiptToInsert.receiptLocation,
      receiptToInsert.receiptNo,
      receiptToInsert.offshoreClient,
      receiptToInsert.ccu,
      receiptToInsert.offshoreLocation,
      receiptToInsert.voyageNo,
      receiptToInsert.manifestNo,
      receiptToInsert.ecargoCargoLine,
      receiptToInsert.externalCargoLineId,
    );

    // Insert and 'pre-receipt' Material Items
    Log.info('Inserting material items....');
    const materialItems = getMaterialItemsFromCargoItem(cargoItem);
    if (materialItems && materialItems.length > 0) {
      for (let i = 0; i < materialItems.length; i++) {
        let materialItem = materialItems[i];
        Log.info(`Inserting material item ${materialItems[i].receiptNo}`);

        Items.insert(materialItem);
      }
    }

    Log.info(`${materialItems.length} Materials inserted into db for CCU ${receiptToInsert.ccu} - ready for receipting.`);
  } catch (e) {
    console.log(`Exception happened when inserting cargo item and associated materials. ${e} ${e.stack}`);
    console.log(`${JSON.stringify(Items.simpleSchema({ receiptType: ReceiptTypes.chemPreReceipt }).namedContext().validationErrors())}`);
    throw e;
  }
};

export const ChemPreReceipt = {
  name: 'items.chemPreReceipt',
  allowInBackground: true,
  validate(args) {
    new SimpleSchema(command)
      .validate(args);
  },

  run({ item }) {
    receiptItems(
      initReceipt(item),
    );
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
