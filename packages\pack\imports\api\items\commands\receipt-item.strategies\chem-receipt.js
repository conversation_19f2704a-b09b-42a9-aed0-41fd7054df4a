import { ChemReceiptFromClientSchema } from '../../receipt.schemas/chem-receipt.schema';
import { EntitiesExistInConfiguration } from '../../../company-site-profiles/queries/entities-exist-in-configuration';
import { Errors } from '../../../api.helpers/errors';
import { EventFactory } from '../../../api.events/event-factory';
import { Items } from '../../items';
import { Meteor } from 'meteor/meteor';
import { ReceiptTypes } from '../../receipt.types';
import SimpleSchema from 'simpl-schema';
import { UpdateReceiptedItems } from '../../../cargo/commands/update-receipted-items';
import { User } from '../../../api.helpers/user';
import moment from 'moment';
import { Winston as log } from 'meteor/wylio:winston-papertrail';
import { Log } from '../../../api.helpers/log';
import { Mediator } from 'meteor/mediator';

const command = {
  item: ChemReceiptFromClientSchema, // Schema used to validate update info from browser.
};

const validateConfigurationEntitiesExistForSite = (receipt, siteIdentifier) => {
  const existsInSiteConfigQuery = {
    siteIdentifier,
    entities: [
      { configProperty: 'receiptCategory', name: receipt.receiptCategory },
    ],
  };

  const entitesExist = EntitiesExistInConfiguration.call(existsInSiteConfigQuery);

  Object.keys(entitesExist).forEach((key) => {
    if (!entitesExist[key]) {
      Errors.throw(Errors.types.notFound, `Can't find ${key}, in configuration for site: ${siteIdentifier}.`);
    }
  });
};

export const ChemReceipt = {
  name: 'items.chemReceipt',

  validate(args) {
    new SimpleSchema(command) // Validate material item update args from client using client schema.
      .validate(args);
  },

  // Called when Material Item is receipted.
  run({ item }) {
    console.log(`chem-receipt: run() called for <${JSON.stringify(item)}>`);
    const siteIdentifier = User.activeSite();

    validateConfigurationEntitiesExistForSite(item, siteIdentifier);

    console.log('chem-receipt - Configuration entities validated.');

    const itemToUpdate = Items.findOne({
      _id: item._id,
      ccu: item.ccu,
      receiptType: ReceiptTypes.chemPreReceipt,
      receiptNo: item.receiptNo,
    });

    if (!itemToUpdate) {
      throw new Meteor.Error('Unable to find item to update for material receipt.');
    }

    let receiptDateTime = moment().utc().toDate();

    const receiptedEvent = EventFactory.createItemEvent(
      EventFactory.Events.Item.RECEIPTED,
      receiptDateTime,
      Meteor.user().username,
    );

    console.log('chem-receipt - Updating Material Item to \'receipted\'.');
    // 1) TEMPORARILY UPDATE THE MATERIAL LINE ITEM TO RECEIPTED - DON'T SET WEIGHT YET
    Items.update({ _id: itemToUpdate._id }, {
      $set: {
        receiptCategory: item.receiptCategory,
        quantity: item.quantity,
        description: item.description,
        ncrs: item.ncrs,
        receiptType: ReceiptTypes.chemReceipt,
        materialReceiptDateTime: receiptDateTime, // [Chemicals] - Added to record time of material receipt (as opposed to cargo item received date time)
      },
      $push: {
        events: receiptedEvent,
      },
    });

    // 2) READ THE MATERIAL *LINE* ITEM BACK - going to use it to clone individual material items.
    console.log('chem-receipt - Reading back material line');
    const itemToClone = Items.findOne({
      _id: item._id,
      ccu: item.ccu,
      receiptType: ReceiptTypes.chemReceipt,
      receiptNo: item.receiptNo,
    });
    console.log(`chem-receipt - Got material line ${JSON.stringify(itemToClone)}.`);

    // Record the original material line quantity.
    let materialLineQuantity = itemToClone.quantity;
    try {
      // 3) For each individual item receipted, create a new entry in the Material Items table
      console.log('chem-receipt - starting to insert individual material items.');
      for (let i = 0; i < item.weights.length; i++) {
        delete itemToClone._id; // Remove mongo _id to allow us to insert as a new object.
        itemToClone.receiptNo = item.weights[i].itemRefNo;
        itemToClone.weightKg = item.weights[i].weight;
        itemToClone.quantity = (item.weights.length > 1) ? 1 : materialLineQuantity; // If single item use material line quantity.
        itemToClone.packageType = item.packageTypes[i].packageType; // Set individully configured package type.

        Items.insert(itemToClone);
        console.log(`chem-receipt: Inserted Material Item ${i + 1} of ${item.weights.length}.`);
      }
    } catch (e) {
      console.log(`Exception happened when inserting individual material items. ${e}`);
      console.log(`${JSON.stringify(Items.simpleSchema({ receiptType: ReceiptTypes.chemReceipt }).namedContext().validationErrors())}`);
      throw e;
    }

    // 3) Delete the Material-Line entry in the Items table - now that we have created an entry for each individual physical item.
    Items.remove(item._id);
    console.log(`chem-receipt: Deleted Material Line entry in items table <${item.receiptNo}>.`);

    console.log('chem-receipt: Update CargoItem following receipt of Material Item.');
    UpdateReceiptedItems.call({ cargoItemId: itemToUpdate.cargoItemId, receiptedItemId: itemToUpdate._id });

    return { receiptDateTime: receiptDateTime };
  },

  call(args, callback) {
    console.log(`chem-receipt: doing Meteor.call to ${this.name}`);
    Meteor.call(this.name, args, callback);
  },
};
