import { GetSiteFromIdentifier } from
  '../../company-site-profiles/queries/get-site-from-identifier';
import { Items } from '../items';
import { Meteor } from 'meteor/meteor';
import { PurchaseOrders } from '../../purchase-orders/purchase-orders';
import { ReceiptTypes } from '../receipt.types';
import SimpleSchema from 'simpl-schema';
import { User } from '../../api.helpers/user';

const command = {
  itemProperties: {
    type: Object,
    blackbox: true,
  },
};

const convertItemPropertyIdsToObjects = (itemProperties, siteProfile) => {
  // Receipt Category
  const receiptCategory = siteProfile
    .configuration
    .receiptCategories
    .find((x) => x._id === itemProperties.receiptCategoryId);

  // Offshore Location
  const offshoreLocation = siteProfile
    .configuration
    .offshoreLocations
    .find((x) => x._id === itemProperties.offshoreLocationId);
  // Vendor
  const vendor = siteProfile
    .configuration
    .vendors
    .find((x) => x._id === itemProperties.vendorId);

  // Package Type
  const packageType = siteProfile
    .configuration
    .packageTypes
    .find((x) => x._id === itemProperties.packageTypeId);
  // Content Type
  const contentType = siteProfile
    .configuration
    .packageTypes
    .find((x) => x._id === itemProperties.contentTypeId);
  // DG Classification
  const dgClassification = siteProfile
    .configuration
    .dgClassifications
    .find((x) => x._id === itemProperties.dgClassificationId);
  // Unit Cost Currency
  const unitCostCurrency = siteProfile
    .configuration
    .currencies
    .find((x) => x._id === itemProperties.unitCostCurrencyId);

  return {
    receiptCategory,
    offshoreLocation,
    vendor,
    packageType,
    contentType,
    dgClassification,
    unitCostCurrency,
  };
};

export const ReceiptItem = {
  name: 'items.receiptItem',

  validate(args) {
    new SimpleSchema(command)
      .validate(args);
  },

  run({ itemProperties }) {
    const siteId = User.activeSite();

    const itemId = itemProperties.itemId;

    const siteProfile = GetSiteFromIdentifier.call({ siteIdentifier: siteId });

    const itemValuesFromIds = convertItemPropertyIdsToObjects(itemProperties, siteProfile);
    const receiptCategory = itemValuesFromIds.receiptCategory;
    const offshoreLocation = itemValuesFromIds.offshoreLocation;
    const vendor = itemValuesFromIds.vendor;
    const packageType = itemValuesFromIds.packageType;
    const contentType = itemValuesFromIds.contentType;
    const dgClassification = itemValuesFromIds.dgClassification;
    const unitCostCurrency = itemValuesFromIds.unitCostCurrency;

    const quantity = itemProperties.quantity;
    const weightKg = itemProperties.weightKg === 0.0 ? null : itemProperties.weightKg;
    const unitCost = itemProperties.unitCost === 0.0 ? null : itemProperties.unitCost;
    const contentQuantity = itemProperties.contentQuantity === 0 ?
      null : itemProperties.contentQuantity;

    Items.update({ _id: itemId }, {
      $set: {
        receiptCategory,
        workOrderNo: itemProperties.workOrderNo,
        offshoreLocation,
        isBackload: itemProperties.isBackload,
        isQa: itemProperties.isQa,
        isYard: itemProperties.isYard,
        poLineNo: itemProperties.poLineNo,
        materialNo: itemProperties.materialNo,
        packageType: packageType.name,
        quantity,
        weight: weightKg,
        description: itemProperties.description,
        ncrs: [],
        receiptType: ReceiptTypes.aobReceipt,
      },
    });

    PurchaseOrders.update({ identifier: itemProperties.poNo }, {
      $push: {
        linesReceipt: itemId,
      },
      $inc: {
        noOfLinesReceipted: 1,
      },
    });
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
