import { Errors } from '../../api.helpers/errors';
import { EventFactory } from '../../api.events/event-factory';
import { GetSiteFromIdentifier } from '../../company-site-profiles/queries/get-site-from-identifier';
import { Items } from '../items';
import { Meteor } from 'meteor/meteor';
import { ReceiptTypes } from '../receipt.types';
import SimpleSchema from 'simpl-schema';
import { User } from '../../api.helpers/user';
import moment from 'moment';
import { Log } from '../../api.helpers/log';

const command = {
  itemIds: Array,
  'itemIds.$': String,
  location: String,
  subLocation: {
    optional: true,
    type: String,
  },
};

export const StoreItems = {
  name: 'items.store',

  validate(args) {
    new SimpleSchema(command)
      .validate(args);
  },

  run({ itemIds, location, subLocation }) {
    Log.info('Storing item(s) to location.', { itemIds, location, subLocation });
    const storedDateTime = moment().utc();

    const storedEvent = EventFactory.createItemEvent(
      EventFactory.Events.Item.STORED,
      storedDateTime.toDate(),
      Meteor.user().username,
      {
        location,
        subLocation,
      },
    );

    const matchedDocuments = Items.update({
      _id: { $in: itemIds },
      receiptType: ReceiptTypes.chemReceipt, // Updated for Chemicals.
    }, {
      $set: {
        storedDate: storedDateTime.toDate(),
        location,
        subLocation,
        isStored: true,
      },
      $push: {
        events: storedEvent,
      },
    }, {
      multi: true,
    });

    if (matchedDocuments === 0) {
      throw new Meteor.Error(Errors.types.notFound, `No items were found for ids: ${itemIds}`);
    } else {
      Log.info(`<${matchedDocuments}> Material Item(s) just had their storage location updated.`, itemIds);
    }
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
