import { Errors } from '../../api.helpers/errors';
import { EventFactory } from '../../api.events/event-factory';
import { Items } from '../items';
import { Meteor } from 'meteor/meteor';
import { ReceiptTypes } from '../receipt.types';
import SimpleSchema from 'simpl-schema';
import moment from 'moment';
import { Log } from '../../api.helpers/log';

const command = {
  itemIds: Array,
  'itemIds.$': String,
};

export const UnStoreItems = {
  name: 'items.unStoreItem',

  validate(args) {
    new SimpleSchema(command)
      .validate(args);
  },

  run({ itemIds }) {
    Log.info('Unstoring item(s).', { itemIds });
    const unstoredDateTime = moment().utc();

    const unstoredEvent = EventFactory.createItemEvent(
      EventFactory.Events.Item.UNSTORED,
      unstoredDateTime.toDate(),
      Meteor.user().username,
    );

    const matchedDocuments = Items
      .update({ _id: { $in: itemIds }, receiptType: ReceiptTypes.chemReceipt }, {
        $set: {
          isStored: false,
        },
        $push: {
          events: unstoredEvent,
        },
      }, { multi: true });

    if (matchedDocuments === 0) {
      throw new Meteor.Error(Errors.types.notFound, `No items were found for ids: ${itemIds}`);
    } else {
      Log.info(`<${matchedDocuments}> Material Item(s) were un-stored.`, itemIds);
    }
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
