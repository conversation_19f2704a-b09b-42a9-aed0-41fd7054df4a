import { Errors } from '../../api.helpers/errors';
import { EventFactory } from '../../api.events/event-factory';
import { Items } from '../items';
import { Meteor } from 'meteor/meteor';
import { ReceiptTypes } from '../receipt.types';
import SimpleSchema from 'simpl-schema';
import { User } from '../../api.helpers/user';
import moment from 'moment';

const command = {
  itemIds: Array,
  'itemIds.$': String,
  requestId: String,
  packingUnitId: String,
};

export const UnpackItems = {
  name: 'items.unpackItems',

  validate(args) {
    new SimpleSchema(command)
      .validate(args);
  },

  run({ itemIds, requestId, packingUnitId }) {
    const siteId = User.activeSite();

    const selector = {
      _id: { $in: itemIds },
      siteId,
      requestId,
      packingUnit: packingUnitId,
      receiptType: ReceiptTypes.chemReceipt, // Edited for Chemicals.
    };

    const unpackedEvent = EventFactory.createItemEvent(
      EventFactory.Events.Item.UNPACKED,
      moment().utc().toDate(),
      Meteor.user().username,
      {
        requestId,
        packingUnitId,
      });

    const update = {
      $set: {
        requestId: null,
        packingUnit: null,
        packingUnitIdentifier: null, // Added for Chemicals.
        packedDate: null,
        packedDateStr: null,
        isPacked: false,
      },
      $push: {
        events: unpackedEvent,
      },
    };

    const updated = Items.update(selector, update, { multi: true });

    if (updated === 0) {
      Errors.throw(Errors.types.commandFailed, 'Update failed.');
    }
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
