import { EventDispatcher } from '../../api.events/event-dispatcher';

const ifEventsUpdatedFire = (doc, modifier) => {
  const fireEvent = (event) => EventDispatcher.fire(event, doc);

  if (modifier.$push) {
    const events = modifier.$push.events;
    if (events) {
      if (events.$each) {
        events.$each.forEach((event) => fireEvent(event));
      } else {
        fireEvent(events);
      }
    }
  }
};

export const AfterUpdate = {
  do(userId, doc, fieldNames, modifier) {
    ifEventsUpdatedFire(doc, modifier);
  },
};
