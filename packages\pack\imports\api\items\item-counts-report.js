import { Match, check } from 'meteor/check';
import { Items } from './items';
import { moment } from 'meteor/momentjs:moment';

const getItemCountsReport = (siteIdentifier, fromDate = null, toDate = null) => {
  check(siteIdentifier, String);
  check(fromDate, String);
  check(toDate, String);

  const fromDateSelector = fromDate;
  const toDateSelector = toDate;

  const matchOn = {
    siteId: siteIdentifier,
    isDeleted: false,
    receivedDateStr: {
      $gte: fromDateSelector,
      $lte: toDateSelector,
    },
  };

  const totalCounts = Items.aggregate(
    { $match: matchOn },
    { $group: { _id: '$receivedDateStr', count: { $sum: 1 } } },
    { $sort: { _id: -1 } },
  );

  matchOn.destCategory = 'SL RECEIPT';
  const slReceiptCounts = Items.aggregate(
    { $match: matchOn },
    { $group: { _id: '$receivedDateStr', count: { $sum: 1 } } },
    { $sort: { _id: -1 } },
  );

  matchOn.destCategory = 'Contractor Goods';
  const contrGoodsCounts = Items.aggregate(
    { $match: matchOn },
    { $group: { _id: '$receivedDateStr', count: { $sum: 1 } } },
    { $sort: { _id: -1 } },
  );

  const totalDeliveredMatchOn = {
    siteId: siteIdentifier,
    isDeleted: false,
    deliveredDateStr: {
      $ne: null,
      $gte: fromDateSelector,
      $lte: toDateSelector,
    },
  };
  const totalDeliveredCounts = Items.aggregate(
    { $match: totalDeliveredMatchOn },
    { $group: { _id: '$deliveredDateStr', count: { $sum: 1 } } },
    { $sort: { _id: -1 } },
  );

  const dayCountsReportDataUnsorted = [];
  _.each(totalCounts, (element, index, list) => {
    const dateStr = element._id;
    const totalItemsReceived = element.count;

    const slItemsReceived = (_.find(
      slReceiptCounts,
      function (x) { return x._id === dateStr; }) || { count: 0 }).count;

    const contractorItemsReceived = (_.find(
      contrGoodsCounts,
      function (x) { return x._id === dateStr; }) || { count: 0 }).count;

    // Assumes no dates where items were delivered and no items were received.
    const totalItemsDelivered = (_.find(
      totalDeliveredCounts,
      function (x) { return x._id === dateStr; }) || { count: 0 }).count;

    const weekNumber = moment(dateStr, 'YYYY-MM-DD').isoWeek();
    const yearWeekNumber = moment(dateStr, 'YYYY-MM-DD').year() * 100 + weekNumber;
    const dayCounts = {
      date: dateStr,
      yearWeekNumber,
      totalItemsReceived,
      slItemsReceived,
      contractorItemsReceived,
      totalItemsDelivered,
    };

    dayCountsReportDataUnsorted.push(dayCounts);
  });

  // Order report data by date ascending.
  const dayCountsReportData = _.sortBy(dayCountsReportDataUnsorted, 'date');

  // Create Weekly aggregations from daily data.
  const groupedByWeekNo = _.groupBy(dayCountsReportData, 'yearWeekNumber');
  const weekCountsReportData = [];
  _.each(groupedByWeekNo, (element, index, list) => {
    const dayCountsForWeek = element;
    const yearWeekNumber = dayCountsForWeek[0].yearWeekNumber;
    const dateStr = moment()
      .year(Math.floor(yearWeekNumber / 100))
      .isoWeek(yearWeekNumber % 100)
      .isoWeekday('Monday')
      .format('YYYY-MM-DD');

    const totalItemsReceived = _.pluck(dayCountsForWeek, 'totalItemsReceived').reduce(function(a, b) { return a + b; }, 0);
    const slItemsReceived = _.pluck(dayCountsForWeek, 'slItemsReceived').reduce(function(a, b) { return a + b; }, 0);
    const contractorItemsReceived = _.pluck(dayCountsForWeek, 'contractorItemsReceived').reduce(function(a, b) { return a + b; }, 0);
    const totalItemsDelivered = _.pluck(dayCountsForWeek, 'totalItemsDelivered').reduce(function(a, b) { return a + b; }, 0);

    const weekCounts = {
      date: dateStr,
      yearWeekNumber,
      totalItemsReceived,
      slItemsReceived,
      contractorItemsReceived,
      totalItemsDelivered,
    };

    weekCountsReportData.push(weekCounts);
  });

  const itemCountsReport = {
    reportName: 'Item Counts Report',
    fromDate: fromDateSelector,
    toDate: toDateSelector,
    reportRunDateTime: moment().toDate(),
    site: siteIdentifier,
    dayCountsReportData,
    weekCountsReportData,
  };

  return itemCountsReport;
};

export const ItemCountsReport = {
  getItemCountsReport,
};
