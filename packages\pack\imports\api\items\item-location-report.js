import { Match, check } from 'meteor/check';
import { Items } from './items';
import { moment } from 'meteor/momentjs:moment';

// Get current counts of items at each loations for site.
const getItemLocationReport = (siteIdentifier) => {
  check(siteIdentifier, String);

  const itemLocationsReport = {
    reportRunDateTime: moment().toDate(),
    site: siteIdentifier,
  };

  if (siteIdentifier === 'peterson-lillyhall') {
    // Get count of items currently in SL Stores.
    const matchOnSlStores = {
      siteId: siteIdentifier,
      isDeleted: false,
      isReceivedAtClientStores: true,
      isDeliveredToDestination: false,
    };
    const itemsAtSlStoresCount = Items.find(matchOnSlStores).count();

    // Get count of items currently on contractor shelves.
    const matchOnContractorShelves = {
      siteId: siteIdentifier,
      isDeleted: false,
      isReceivedAtContractorShelf: true,
      isDeliveredToDestination: false,
    };
    const itemsAtContrShelvesCount = Items.find(matchOnContractorShelves).count();

    const locationReportData = {
      locationCounts: [
        {
          location: 'Contractor Shelves',
          itemCount: itemsAtContrShelvesCount,
        },
        {
          location: 'SL Stores',
          itemCount: itemsAtSlStoresCount,
        },
      ],
    };

    itemLocationsReport.locationReportData = locationReportData;
  }

  console.log(JSON.stringify(itemLocationsReport, null, 2));
  return itemLocationsReport;
};

export const ItemLocationReport = {
  getItemLocationReport,
};
