import { Match, check } from 'meteor/check';
import { CompanySiteProfiles } from '../company-site-profiles/company-site-profiles';
import { EventFactory } from '../../shared/event-factory';
import { Items } from './items';
import { Meteor } from 'meteor/meteor';
import { <PERSON> as log } from 'meteor/wylio:winston-papertrail';
import { moment } from 'meteor/momentjs:moment';
import { ReceiptTypes } from '../items/receipt.types';

const updateItem = (itemId, updatesFromClient, userName, userSiteIdentifier) => {
  check(itemId, String);
  check(updatesFromClient, Object);
  check(userName, String);
  check(userSiteIdentifier, String);

  log.info(`ItemUpdator.updateItem called - ItemId<${itemId}>
            updates ${JSON.stringify(updatesFromClient, null, 2)}`);

  // Load current Item.
  const currentItem = Items.findOne(itemId);

  if (!currentItem) {
    throw new Meteor.Error(500, 'Error 500: Not found', 'The item does not exist');
  }

  // Verify Item loaded has a site identifier that matches user's site.
  if (!(currentItem.siteId === userSiteIdentifier)) {
    throw new Meteor.Error(
      'not-authorized',
      'Users associated site does not match Items site.',
      `UserSite: ${userSiteIdentifier}, itemSite: ${currentItem.siteId}.`);
  }

  const updates = _.clone(updatesFromClient); // Shallow copy - ok as shallow source object.
  // Remove properties we do not need for the update.
  delete updates.receivedAtDateTime;

  // Fix Received Date if exists on list of updated fields.
  if (updatesFromClient.receivedAtDateTime) {
    const receivedAtDateTime = moment(updatesFromClient.receivedAtDateTime);
    updates.receivedDateStr = receivedAtDateTime.format('YYYY-MM-DD');
    updates.receivedDate = receivedAtDateTime.toDate();
  }

  log.info(`Updates to apply ${JSON.stringify(updates, null, 2)}`);

  const keysAllowedToBeInsertedOnUpdate = [
    'packageType',
    'wasteDescription',
    'offshoreClient',
    'offshoreLocation',
    'isWaste',
    'marinePollutant',
    'ccu',
    'voyageNo',
    'materialManifestNo',
    'imoHazardClass',
    'imoSubClass',
    'euralCode',
    'unNo',
    'weightKg',
    'quantity',
    'description',
  ];

  const valuesToSet = {};
  const auditChanges = [];
  Object.keys(updates).forEach(function (key, index) {
    const isAllowedToBeInserted = keysAllowedToBeInsertedOnUpdate.includes(key);

    console.log(`Checking <${key}>`);
    if (!isAllowedToBeInserted) {
      console.log(`<${key}> property does not exist on item - Not updating this property.`);
    } else {
      if (((typeof updates[key]) !== (typeof currentItem[key])) && (currentItem[key] != null)) {
        console.log(`Property type <${typeof updates[key]}> does not match stored type` +
                    ` <${typeof currentItem[key]}> for <${key}> - Not updating this property.`);
      } else if (updates[key] === currentItem[key]) {
        console.log('No change to ' + key);
      } else if ((updates[key] == null || updates[key] == "") && 
        (currentItem[key] == null || currentItem[key] == "")) {
        console.log("Can't update null or empty with null or empty")
      } else {
        if ((updates[key] instanceof Date) &&
            (currentItem[key] !== null) &&
            (updates[key].getTime() === currentItem[key].getTime())) {
          console.log('No change to Date value ' + key);
        } else {
          console.log(`<${key}> changed from <${currentItem[key]}> to <${updates[key]}>.`);
          valuesToSet[key] = updates[key];

          // Record change in values.
          const change = { key, from: currentItem[key], to: updates[key] };
          auditChanges.push(change);
        }
      }
    }
  });

  console.log(`Compacted Changes: ${JSON.stringify(valuesToSet)}.`);

  if (Object.keys(valuesToSet).length === 0) {
    // Nothing to update - so don't perform update.
    log.info('No updates found - item will not be updated.');
  } else {
    // Add any dependent updates.

    // Set item audit info.
    const now = moment();
    const setAuditValues = {
      updatedBy: userName,
      updatedAt: now.toDate(),
    };
    _.extend(valuesToSet, setAuditValues);

    log.info(`Item Changes with Audit info: ${JSON.stringify(valuesToSet, null, 2)}.`);

    // Update Item.
    Items.update({
      _id: itemId,
      receiptType: ReceiptTypes.chemReceipt, // Updated for Chemicals.
    }, { $set: valuesToSet });

    // Record Item Edited event - for audit purposes only.
    const eventData = { edits: auditChanges };
    const newEditedEvent = EventFactory.createSingularItemEventWithData(
      itemId,
      EventFactory.ItemEvents.EDITED,
      now.toDate(),
      userName,
      eventData,
    );

    Items.update(
      {
        _id: itemId,
        receiptType: ReceiptTypes.chemReceipt, // Updated for Chemicals.
      },
      { $push: { events: newEditedEvent } },
    );
  }

  // Return the updated item.
  const updatedItem = Items.findOne(itemId);
  return updatedItem;
};

export const ItemUpdator = {
  updateItem,
};
