import { AobNoOfLines } from './commands/receipt-item.strategies/aob-no-of-lines';
import { AobPreReceipt } from './commands/receipt-item.strategies/aob-pre-receipt';
import { Aob<PERSON><PERSON>eipt } from './commands/receipt-item.strategies/aob-receipt';
import { ChemPreReceipt } from './commands/receipt-item.strategies/chem-pre-receipt';
import { ChemReceipt } from './commands/receipt-item.strategies/chem-receipt';
import { PackItems } from './commands/pack-items';
import { ReceiptItem } from './commands/receipt-item';
import { Register } from '../api.helpers/register';
import { StoreItems } from './commands/store-items';
import { UnStoreItems } from './commands/un-store-items';
import { UnpackItems } from './commands/unpack-items';
import { RemoveItemsFromCargoItem } from './commands/external-item-updates/remove-items-from-cargo-item';
import { AddItemsToCargoItem } from './commands/external-item-updates/add-items-to-cargo-item';
import { UpdateItemsInCargoItem } from './commands/external-item-updates/update-items-in-cargo-item';
import { UpdateCargoLevelInformationOnItemsInCargoItem } from './commands/external-item-updates/update-cargo-level-information-on-items-in-cargo-item';
import { GetItemsAsCsvForDateRange } from './commands/get-receipt-items-as-csv-for-date-range';
import { GetStoredOrCanStoreAsCsv } from './commands/get-stored-or-can-store-as-csv';
import { SetItemsInPackingUnitDispatched } from './commands/dispatch-items';
import { CreateAndReceiptItem } from './commands/create-and-receipt-item';

Register
  .command(ReceiptItem)
  .command(AobReceipt)
  .command(AobPreReceipt)
  .command(AobNoOfLines)
  .command(StoreItems)
  .command(UnStoreItems)
  .command(PackItems)
  .command(UnpackItems)
  .command(ChemReceipt)
  .command(ChemPreReceipt)
  .command(RemoveItemsFromCargoItem)
  .command(AddItemsToCargoItem)
  .command(UpdateItemsInCargoItem)
  .command(UpdateCargoLevelInformationOnItemsInCargoItem)
  .command(GetItemsAsCsvForDateRange)
  .command(GetStoredOrCanStoreAsCsv)
  .command(SetItemsInPackingUnitDispatched)
  .command(CreateAndReceiptItem);
