import { EventFactory } from '../api.events/event-factory';
import { Register } from '../api.helpers/register';
import { ReportTypes } from '../reports/report.types';
import { UpdateReport } from '../reports/commands/update-report';

const Events = EventFactory.Events.Item;

const updateUndeliveredItemsReport = (doc) => {
  const reportType = ReportTypes.UNDELIVERED_ITEMS;
  const reportData = { clientId: doc.client._id };

  UpdateReport.call({ reportType, reportData });
};

Register
  .eventHandler(
    updateUndeliveredItemsReport,
    Events.RECEIPTED,
    Events.STORED,
    Events.UNSTORED,
    Events.PACKED,
    Events.EDITED,
  );
