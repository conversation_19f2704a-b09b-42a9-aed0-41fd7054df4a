import { AfterInsert } from './hooks/after-insert';
import { AfterUpdate } from './hooks/after-update';
import { AobPreReceiptSchema } from './receipt.schemas/aob-pre-receipt.schema';
import { AobReceiptSchema } from './receipt.schemas/aob-receipt.schema';
import { ChemPreReceiptSchema } from './receipt.schemas/chem-pre-receipt.schema';
import { ChemReceiptSchema } from './receipt.schemas/chem-receipt.schema';
import { ItemsSchema } from './items.schema';
import { Meteor } from 'meteor/meteor';
import { ReceiptTypes } from './receipt.types';

export const Items = new Meteor.Collection('items');

// Validate against the Chemicals pre receipt schema when one is added to the collection
Items.attachSchema(ChemPreReceiptSchema, { selector: { receiptType: ReceiptTypes.chemPreReceipt } });

// Validate against the Chemicals receipt schema when one is added to the collection
Items.attachSchema(ChemReceiptSchema, { selector: { receiptType: ReceiptTypes.chemReceipt } });

if (Meteor.isServer) {
  // Add collection hooks
  Items.after.update(AfterUpdate.do);
  Items.after.insert(AfterInsert.do);

  Items.before.insert((userId, doc) => {
    console.log(`Item hook - About to insert new item ${JSON.stringify(doc.receiptNo)}`);
  });

  Items.before.update((userId, doc) => {
    console.log(`Item hook - About to update an item ${JSON.stringify(doc.receiptNo)}`);
  });
}
