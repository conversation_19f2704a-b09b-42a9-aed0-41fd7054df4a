//////////////////////
// DEPRECATED ////////
// NOT USED ANYMORE //
//////////////////////
import { Match, check } from 'meteor/check';

import { CompanySiteProfiles } from '../company-site-profiles/company-site-profiles';
import { EventFactory } from '../../shared/event-factory';
import { ItemCountsReport } from '../items/item-counts-report';
import { ItemUpdator } from '../items/item-updator';
import { Items } from './items';
import { Meteor } from 'meteor/meteor';
import { VehicleRuns } from '../vehicle-runs/vehicle-runs';
import { _ } from 'meteor/underscore';
import { Winston as log } from 'meteor/wylio:winston-papertrail';
import { moment } from 'meteor/momentjs:moment';
import { utils } from '../../shared/utils';

import { User } from '../../api/api.helpers/user';

function getSiteIdentifierForUser(user) {
  // TODO: get siteIdentifier from user association to site.
  const siteId = User.activeSite();
  return siteId;
}

function checkAndGetUser() {
  const user = Meteor.user();
  if (!user) {
    throw new Meteor.Error('not-authorized');
  }
  return user;
}

Meteor.methods({
  'items.add': function addItem(itemProperties) {
    console.info(`items.add Called. ${JSON.stringify(itemProperties, null, 2)}`);

    // Make sure the user is logged in before inserting an item
    const user = checkAndGetUser();
    const userSiteIdentifier = getSiteIdentifierForUser(user)

    const siteProfile = CompanySiteProfiles.findOne({ identifier: userSiteIdentifier });
    if (!siteProfile) {
      throw new Meteor.Error(500, 'Error 500: Not found', 'the site profile does not exist');
    }

    // TODO: Add validation function here

    // Package type: Bag, Box etc.
    const packageType = siteProfile
      .configuration
      .packingUnits
      .find((x) => x._id === itemProperties.packageTypeId)
      .name;

    // SL or Contractor. (Destination)
    const destCategory = siteProfile
      .configuration
      .destinationOptions
      .find((x) => x._id === itemProperties.destCategoryId)
      .name;

    const contractPhase = siteProfile
      .configuration
      .contractPhases
      .find((x) => x._id === itemProperties.contractPhaseId)
      .name;

    // Description / Weight category
    const description = siteProfile
      .configuration
      .weightCategories
      .find((x) => x._id === itemProperties.descriptionId)
      .name;

    const deliveryCompany = siteProfile
      .configuration
      .deliveryCompanies
      .find((x) => x._id === itemProperties.deliveryCompanyId)
      .name;

    const client = siteProfile
      .configuration
      .clients
      .find((x) => x._id === itemProperties.clientId);

    const verifiedItemRef = itemProperties.itemRef; // TODO: Verify Item ref is not duplicate.
    const receivedAtDateTime = moment(itemProperties.receivedAtDateTime);
    const userName = Meteor.user().username;
    const now = moment();

    // Create item to insert into collection.
    const itemEvents = [];
    const createdBy = userName;
    const eventDateTime = receivedAtDateTime.toDate();
    const receiptEvent = EventFactory.createItemEvent(
      EventFactory.ItemEvents.RECEIVED,
      eventDateTime,
      createdBy);
    itemEvents.push(receiptEvent);

    // Convert strings to numbers, but allow them to be passed as numbers.
    let quantity = itemProperties.quantity;
    if (typeof itemProperties.quantity === 'string') {
      quantity = parseInt(itemProperties.quantity, 10); // Int
    }

    let weightKg = itemProperties.weightKg;
    if (typeof itemProperties.weightKg === 'string') {
      weightKg = parseFloat(itemProperties.weightKg); // Float
    }

    check(quantity, Number);
    check(weightKg, Number);

    const itemToInsert = {
      client,
      siteId: siteProfile.identifier,
      itemRef: verifiedItemRef,
      receivedDateStr: receivedAtDateTime.format('YYYY-MM-DD'),
      receivedDate: receivedAtDateTime.toDate(), // Will be in event list as well
      contractPhase,
      destCategory,
      destSublocation: itemProperties.destSublocation,
      quantity,
      weightKg,
      packageType,
      description,
      deliveryCompany,
      vendorRef: itemProperties.vendorRef,
      comments: itemProperties.comments,

      deliveredDateStr: null,
      deliveredDate: null,
      contractorShelfDateStr: null,
      vehicle: null,
      vehicleRun: null,
      isItemDamaged: false,
      isDeliveryReturned: false,
      isDeliveredToDestination: false,
      isReceivedAtClientStores: false,
      receivedAtClientStoresDateStr: null,
      isDeleted: false,

      createdBy: userName,
      createdAt: now.toDate(),
      updatedBy: null,
      updatedAt: null,
      events: itemEvents,
    };

    if (itemProperties.isAutomaticReceiptnumber) {
      CompanySiteProfiles.update({ _id: siteProfile._id }, { $inc: { 'configuration.receiptNoSequence': 1 } });
      console.info('Incremented receipt number sequence.');
    } else {
      console.info('Manually entered receipt no, - skip incrementing receipt number sequence.');
    }

    console.info(`Registering new item ${JSON.stringify(itemToInsert, null, 2)}`);

    const itemId = Items.insert(itemToInsert); // Insert it and record Id.

    return (Items.findOne(itemId)); // return the object - Could do this via subscription.
  },
  'items.edit': function editItem(itemId, itemUpdates) {
    check(itemId, String);
    check(itemUpdates, Object);

    // Make sure the user is logged in before editing an item.
    const user = checkAndGetUser();
    const userSiteIdentifier = getSiteIdentifierForUser(user);

    log.info(`Item.Edit called by <${user.username}>- ItemId<${itemId}> updates${JSON.stringify(itemUpdates, null, 2)}`);

    const updatedItem = ItemUpdator.updateItem(itemId, itemUpdates, user.username, userSiteIdentifier);

    return (updatedItem); // return the updated item.
  },
  'items.removeItemFromStore': function removeItemFromStore(itemId, storageLocationId) {
    const user = checkAndGetUser();
    check(itemId, String);
    check(storageLocationId, String);

    const actionDateTime = moment().utc();

    const siteProfile = CompanySiteProfiles.findOne(
      { 'configuration.storageLocations._id': storageLocationId },
    );

    const storageLocation = _.find(
      siteProfile.configuration.storageLocations,
      (location) => location._id === storageLocationId,
    );

    if (storageLocation.name === 'Contractor Shelves') {
      Items.update({ _id: itemId }, { $set: { isReceivedAtContractorShelf: false } });
    }

    if (storageLocation.name === 'SL Stores') {
      Items.update({ _id: itemId }, { $set: { isReceivedAtClientStores: false } });
    }

    const eventData = {location: storageLocation.name};
    EventFactory.createSingularItemEventWithData(
      itemId,
      EventFactory.ItemEvents.REMOVED_FROM_LOCATION,
      actionDateTime.toDate(),
      user.username,
      eventData,
    );
  },
  'items.updateLoadedItem': function updateLoadedItem(updateType, itemId, vehicleRunId, deliveryDateTime, splitQuantity) {
    check(updateType, String);
    check(itemId, String);
    check(vehicleRunId, Match.Maybe(String));
    check(deliveryDateTime, Match.Maybe(Date));
    check(splitQuantity, Match.Maybe(Number));

    const user = checkAndGetUser();

    let vehicleRun = null;
    let deliveredDate = deliveryDateTime;
    if (!deliveredDate) {
      // If no delivery time provided retrieve the time from item's vehicle run
      vehicleRun = VehicleRuns.findOne({ _id: vehicleRunId });
      deliveredDate = vehicleRun.scheduledDateTime;
    }

    // Ensure that delivery date is formatted correctly and introduce dateStr
    const deliveredDateStr = moment(deliveredDate).format('YYYY-MM-DD');
    deliveredDate = moment(deliveredDate).utc().toDate();

    switch (updateType) {
      case EventFactory.ItemEvents.DELIVERED:
        Items.update({ _id: itemId },
          { $set: { deliveredDate, deliveredDateStr, isDeliveredToDestination: true } });
        break;
      case EventFactory.ItemEvents.PARTIAL_DELIVERY:
        Items.update({ _id: itemId },
          { $set: { deliveredDate, deliveredDateStr, partialQtyInNewVehicleRunId: null },
            $inc: { quantityDelivered: splitQuantity } });
        break;
      case EventFactory.ItemEvents.RETURNED:
        Items.update({ _id: itemId },
          { $set: { isDeliveryReturned: true } });
        break;
    }

    const eventData = {
      quantity: splitQuantity,
      vehicleRunId,
    };
    EventFactory.createSingularItemEventWithData(
      itemId,
      updateType,
      deliveredDate,
      user.username,
      eventData,
    );
  },
});
