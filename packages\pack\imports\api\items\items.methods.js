import { Match, check } from 'meteor/check';
import { CSV } from 'meteor/clinical:csv';
import { CompanySiteProfiles } from '../company-site-profiles/company-site-profiles';
import { EventFactory } from '../../shared/event-factory';
import { ItemCountsReport } from '../items/item-counts-report';
import { ItemLocationReport } from '../items/item-location-report';
import { ItemUpdator } from '../items/item-updator';
import { Items } from '../items/items';
import { Meteor } from 'meteor/meteor';
import { VehicleRuns } from '../vehicle-runs/vehicle-runs';
import { dtoMapper } from '../../shared/dto-mapper';
import { Winston as log } from 'meteor/wylio:winston-papertrail';
import { moment } from 'meteor/momentjs:moment';
import { utils } from '../../shared/utils';

Meteor.methods({
  'items.getDayCountsReport': function getReport(reportParams) {
    check(reportParams, Object);

    log.info(`items.getDayCountsReport called ${reportParams.clientId}, ${reportParams.fromDate}, ${reportParams.toDate}`);

    // Make sure the user is logged in before returning day count report.
    if (!this.userId) {
      throw new Meteor.Error('not-authorized');
    }

    // TODO: Get this from userId.
    const siteIdentifier = 'peterson-lillyhall';

    let fromDateStr = reportParams.fromDate;
    let toDateStr = reportParams.toDate;
    if (!fromDateStr || !toDateStr) {
      //  TODO: This is currently implemented to use most recent 8 weeeks.
      //  However the parameters should be used for date selectors
      //        on the report pages in future.
      const today = moment();
      const todayMinus8Weeks = moment().subtract(8, 'weeks');
      fromDateStr = todayMinus8Weeks.format('YYYY-MM-DD');
      toDateStr = today.format('YYYY-MM-DD');
    }

    const start = utils.clock();

    const itemCountsReport = ItemCountsReport.getItemCountsReport(siteIdentifier, fromDateStr, toDateStr);

    const end = utils.clock(start);

    log.info(`Ran day counts report for ${siteIdentifier} in ${end}ms`);

    return itemCountsReport;
  },
  'items.getItemLocationReport': function getReport() {
    // Make sure the user is logged in before returning the report.
    if (!this.userId) {
      throw new Meteor.Error('not-authorized');
    }

    // TODO: Get this from userId.
    const siteIdentifier = 'peterson-lillyhall';

    const start = utils.clock();

    const itemLocationReport = ItemLocationReport.getItemLocationReport(siteIdentifier);

    const end = utils.clock(start);

    log.info(`Ran ItemLocation report for ${siteIdentifier} in ${end}ms`);

    return itemLocationReport;
  },
});
