import { GetReceiptByPoLineNo } from './queries/get-receipt-by-po-line-no';
import { GetItemByReceiptNumber } from './queries/get-item-by-receipt-no';
import { GetItemsByExternalCargoLineId } from './queries/get-items-by-external-cargo-line-id';
import { GetItemsAsCsvForOffshoreClientAndDateRange } from './queries/get-items-as-csv-for-offshore-client-and-date-range';
import { GetOffshoreLocationsForNonDispatchedReceiptedItems } from './queries/get-offshore-locations-for-non-dispatched-receipted-items';
import { CheckItemExists } from './queries/check-item-exists';
import { Register } from '../api.helpers/register';
import { StoredOrCanStoreCounts } from './server/publications/stored-or-can-store';
import { GetItemsInPackingUnit } from './queries/get-items-in-packing-unit';
import { PackedOrCanPackCounts } from './server/publications/packed-or-can-pack';

Register
  .query(GetReceiptByPoLineNo)
  .query(GetItemByReceiptNumber)
  .query(CheckItemExists)
  .query(GetItemsAsCsvForOffshoreClientAndDateRange)
  .query(GetItemsByExternalCargoLineId)
  .query(StoredOrCanStoreCounts)
  .query(GetOffshoreLocationsForNonDispatchedReceiptedItems)
  .query(GetItemsInPackingUnit)
  .query(PackedOrCanPackCounts);
