import { SiteIdSchema, TimestampsSchema, _idSchema } from '../api.shared-schemas/shared-schemas';
import { ClientSchema } from '../company-site-profiles/configuration/client.schema';
import { DB_DATE_STR_FORMAT } from '../../shared/lib/constants';
import { DefaultProcessSchema } from './receipt-process.schemas/default-process.schema';
import { ReceiptTypes } from './receipt.types';
import SimpleSchema from 'simpl-schema';
import moment from 'moment';

export const ItemsSchema = new SimpleSchema({
  client: ClientSchema,
  receiptNo: String,
  itemLineIndex: {
    type: SimpleSchema.Integer,
    optional: true,
    defaultValue: 0,
  },
  receivedDate: Date,
  receivedDateStr: {
    type: String,
    optional: true,
    autoValue: function receivedDateToStr() {
      const receivedDate = this.field('receivedDate');

      if (receivedDate.isSet) {
        return moment(receivedDate.value).format(DB_DATE_STR_FORMAT);
      }

      return undefined;
    },
  },
  receiptCategory: String,
  receiptLocation: String,
  vendor: String,
  vendorDeliveryNo: {
    type: String,
    optional: true,
  },
  deliveryNo: String,
  poNo: String,
  poLineNo: {
    type: SimpleSchema.Integer,
    optional: true,
  },
  poId: {
    type: String,
    optional: true,
  },
  workOrderNo: {
    type: String,
    optional: true,
  },
  materialNo: {
    type: String,
    optional: true,
  },
  offshoreLocationStorageBin: {
    type: String,
    optional: true,
  },
  offshoreLocation: {
    type: String,
    optional: true,
  },
  isBackload: {
    type: Boolean,
    optional: true,
    defaultValue: false,
  },
  isQa: {
    type: Boolean,
    optional: true,
    defaultValue: false,
  },
  isYard: {
    type: Boolean,
    optional: true,
    defaultValue: false,
  },
  customsStatus: {
    type: String,
    optional: true,
  },
  packageType: String,
  quantity: SimpleSchema.Integer,
  packageContent: {
    type: String,
    optional: true,
  },
  packageContentQuantity: {
    type: SimpleSchema.Integer,
    optional: true,
  },
  weightKg: {
    type: Number,
    optional: true,
    defaultValue: 0.0,
  },
  unitCost: {
    type: Number,
    optional: true,
    defaultValue: 0.00,
  },
  unitCostCurrency: {
    type: String,
    optional: true,
  },
  description: {
    type: String,
    optional: true,
  },
  comments: {
    type: String,
    optional: true,
  },
  isDangerousGoods: Boolean,
  dgClassification: {
    type: String,
    optional: true,
  },
  ncrs: {
    type: Array,
    optional: true,
    defaultValue: [],
  },
  'ncrs.$': String,
  isDeleted: {
    type: Boolean,
    optional: true,
    defaultValue: false,
  },
  manifestNo: {
    type: String,
    optional: true,
  },
  events: {
    type: Array,
    optional: true,
    defaultValue: [],
  },
  'events.$': {
    type: Object,
    blackbox: true,
  },
  importIndex: {
    type: SimpleSchema.Integer,
    optional: true,
  },
  importFilename: {
    type: String,
    optional: true,
  },
  receiptType: {
    type: String,
    custom: function isValidReceiptType() {
      if (this.isSet) {
        const isValid = Object.keys(ReceiptTypes)
          .some((type) => ReceiptTypes[type] === this.value);

        return isValid ? undefined : 'invalid_receipt_type';
      }

      return undefined;
    },
  },
  materialReceiptDateTime: {
    type: Date,
    optional: true,
  },
})
  .extend(_idSchema)
  .extend(SiteIdSchema)
  .extend(DefaultProcessSchema)
  .extend(TimestampsSchema);
