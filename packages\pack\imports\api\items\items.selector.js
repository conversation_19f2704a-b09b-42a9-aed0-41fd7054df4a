import SimpleSchema from 'simpl-schema';
import { utils } from '../../shared/utils';

const params = {
  clientId: String,
  siteIdentifier: String,
  storedItemsOnly: {
    type: Boolean,
    optional: true,
  },
  ignorePacked: <PERSON><PERSON><PERSON>,
  ignoreDispatched: <PERSON><PERSON><PERSON>,
  restrictToLocation: {
    type: String,
    optional: true,
  },
  query: {
    type: String,
    optional: true,
  },
  receiptType: {
    type: String,
    optional: true,
  },
};

export const ItemsSelector = {
  validate(args) {
    console.log(args);
    new SimpleSchema(params)
      .validate(args);
  },

  // Used when querying for items based on single string (used in Store screen filtering)
  getStringQuerySelector({ query }) {
    // Logical Or. Has exclusivity problem
    // but may be fixable. Left in for reference.
    // let terms = query.split(" ").map(function(item) {
    //   return item.trim();
    // }).join("|");

    // Logical And. All words must exist within a field.
    // matches partial words. Case insensitive. Words in
    // any order.
    let terms = query.split(' ').map(function(item) {
      return '(?=.*' + item.trim() + ')';
    }).join('');

    const regex = new RegExp(terms, 'gi');

    return {
      $or: [
        { receiptNo: { $regex: regex } },
        { ccu: { $regex: regex } },
        { packageType: { $regex: regex } },
        { description: { $regex: regex } },
        { receiptCategory: { $regex: regex } },
        { location: { $regex: regex } },
        { offshoreClient: { $regex: regex } },
        { offshoreLocation: { $regex: regex } },
      ]
    };
  },

  get({
    clientId,
    siteIdentifier,
    storedItemsOnly,
    restrictToLocation,
    query,
    ignorePacked,
    ignoreDispatched,
    receiptType,
  }) {
    this.validate({
      clientId,
      siteIdentifier,
      storedItemsOnly,
      restrictToLocation,
      query,
      ignorePacked,
      ignoreDispatched,
    });

    const selector = {
      $and: [{
        'client._id': clientId,
        siteId: siteIdentifier,
      }],
    };

    if (receiptType) {
      selector.$and.push({ receiptType });
    }

    if (ignorePacked) {
      selector.$and.push({ isPacked: false });
    }

    if (ignoreDispatched) {
      selector.$and.push({ isDispatched: false });
    }

    if (typeof storedItemsOnly !== 'undefined') {
      if (storedItemsOnly) {
        selector.$and.push(restrictToLocation ?
          { isStored: true, location: restrictToLocation } :
          { isStored: true });
      } else {
        selector.$and.push({ isStored: false });
      }
    }

    if (query && query.length > 2) {
      const querySelector = this.getStringQuerySelector({ query });
      selector.$and.push(querySelector);
    }

    return selector;
  },
};
