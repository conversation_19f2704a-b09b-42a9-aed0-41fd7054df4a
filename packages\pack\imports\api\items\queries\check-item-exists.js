import { Items } from '../items';
import { Meteor } from 'meteor/meteor';
import SimpleSchema from 'simpl-schema';

const query = {
  receiptNo: String,
};

export const CheckItemExists = {
  name: 'items.checkItemExists',

  validate(args) {
    new SimpleSchema(query)
      .validate(args);
  },

  run({ receiptNo }) {
    const item = Items.findOne({ receiptNo });
    return item !== undefined;
  },

  call(args, callback) {
    return Meteor.call(this.name, args, callback);
  },
};
