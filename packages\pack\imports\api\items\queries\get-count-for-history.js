import { Items } from '../items';
import { ItemsSelector } from '../items.selector';
import { Meteor } from 'meteor/meteor';
import SimpleSchema from 'simpl-schema';

const params = {
  resultsPerPage: SimpleSchema.Integer,
  filter: Object,
  'filter.query': {
    type: String,
    optional: true,
  },
  'filter.fromDate': {
    type: Date,
    optional: true,
  },
  'filter.toDate': {
    type: Date,
    optional: true,
  },
  'filter.dateToFilter': String,
  'filter.clientId': String,
  'filter.sortBy': String,
  'filter.sortDirection': String,
};

export const GetCountForHistory = {
  name: 'items.getCountForHistory',

  validate(args) {
    new SimpleSchema(params)
      .validate(args);
  },

  run({ filter, resultsPerPage }) {
    if (Meteor.isServer) {
      const query = ItemsSelector.getItemsSelector(
        filter.clientId,
        false,
        null,
        filter.query,
        false);

      // Build Date Filter
      const dateFilter = {};

      if (filter.fromDate) {
        dateFilter.$gte = filter.fromDate;
      }

      if (filter.toDate) {
        dateFilter.$lte = filter.toDate;
      }

      if (Object.keys(dateFilter).length) {
        query[filter.dateToFilter] = dateFilter;
      }

      const limit = resultsPerPage * 50;

      return Items.find(query, { limit, fields: { _id: 1 } }).count(true);
    }

    return 0;
  },

  call(args, callback) {
    return Meteor.call(this.name, args, callback);
  },
};
