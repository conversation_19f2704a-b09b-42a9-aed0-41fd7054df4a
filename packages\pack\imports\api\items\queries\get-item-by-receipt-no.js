import { Items } from '../items';
import { Meteor } from 'meteor/meteor';
import SimpleSchema from 'simpl-schema';

const query = {
  receiptNo: String,
};

export const GetItemByReceiptNumber = {
  name: 'items.getItemByReceiptNumber',

  validate(args) {
    new SimpleSchema(query)
      .validate(args);
  },

  run({ receiptNo }) {
    return Items.findOne({ receiptNo });
  },

  call(args, callback) {
    return Meteor.call(this.name, args, callback);
  },
};
