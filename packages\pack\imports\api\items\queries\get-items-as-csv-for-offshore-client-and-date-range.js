import { CSV } from 'meteor/clinical:csv';
import { Items } from '../items';
import { Meteor } from 'meteor/meteor';
import { _ } from 'meteor/underscore';
import SimpleSchema from 'simpl-schema';
import { <PERSON> as log } from 'meteor/wylio:winston-papertrail';
import moment from 'moment';
import { User } from '../../api.helpers/user';
import { ReceiptTypes } from '../receipt.types';

const params = {
  offshoreClient: {
    type: String,
    optional: true,
  },
  reportStartDateStr: {
    type: String,
    optional: true,
  },
  reportEndDateStr: {
    type: String,
    optional: true,
  },
  dateToFilterBy: {
    type: String,
    optional: true,
  },
};

export const GetItemsAsCsvForOffshoreClientAndDateRange = {
  name: 'items.getItemsAsCsvForOffshoreClientAndDateRange',

  validate(args) {
    new SimpleSchema(params)
      .validate(args);
  },

  run({ reportStartDateStr, reportEndDateStr, dateToFilterBy, offshoreClient }) {
    const query = {
      receiptType: ReceiptTypes.chemReceipt,
      $and: [
        { siteId: User.activeSite() },
      ],
    };

    if (reportStartDateStr) {
      query.$and
        .push({ [dateToFilterBy]: { $gte: moment(reportStartDateStr).startOf('day').toDate() } });
    }

    if (reportEndDateStr) {
      query.$and
        .push({ [dateToFilterBy]: { $lte: moment(reportEndDateStr).endOf('day').toDate() } });
    }

    if (offshoreClient) {
      query.$and
        .push({ offshoreClient: offshoreClient });
    }

    const itemsForIntakeExport = Items.find(query, { sort: { receivedDate: 1 } }).fetch();

    const itemsForIntakeExportFormatted = itemsForIntakeExport.map(x => ({
      'Receipt No': x.receiptNo,
      'Received Date': moment(x.receivedDate).format('DD/MM/YYYY HH:mm'),
      'Offshore Client': x.offshoreClient,
      'Offshore Location': x.offshoreLocation,
      'Voyage No': x.voyageNo,
      'Manifest No': x.materialManifestNo,
      CCU: x.ccu,
      Qty: x.quantity,
      'Package Type': x.packageType,
      Weight: x.weightKg,
      Description: _.unescape(x.description)
        .replace(/&GT;/ig, '>')
        .replace(/&LT;/ig, '<')
        .replace(/&gt/ig, '>')
        .replace(/&lt/ig, '<'),
      'EURAL/EWC Code': x.euralCode,
      'Hazard Class': x.imoHazardClass,
      'Hazard Subclass': x.imoSubClass,
      UnNo: x.unNo,
    }));

    const heading = true; // Optional, defaults to true
    const delimiter = ','; // Optional, defaults to ",";
    return CSV.unparse(itemsForIntakeExportFormatted, heading, delimiter);
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
