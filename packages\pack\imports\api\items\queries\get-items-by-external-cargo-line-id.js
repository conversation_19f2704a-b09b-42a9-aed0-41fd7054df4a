import { Items } from '../items';
import { Meteor } from 'meteor/meteor';
import SimpleSchema from 'simpl-schema';

const query = {
  externalCargoLineId: String,
};

export const GetItemsByExternalCargoLineId = {
  name: 'items.GetItemsByExternalCargoLineId',
  allowInBackground: true,
  validate(args) {
    new SimpleSchema(query)
      .validate(args);
  },

  run({ externalCargoLineId }) {
    return Items.find({ externalCargoLineId }).fetch();
  },

  call(args, callback) {
    return Meteor.call(this.name, args, callback);
  },
};
