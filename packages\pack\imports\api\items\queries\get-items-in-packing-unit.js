import { Items } from '../items';
import { ItemsSelector } from '../items.selector';
import { ReceiptTypes } from '../receipt.types';
import SimpleSchema from 'simpl-schema';
import { User } from '../../api.helpers/user';
import { queryBuilder } from '../shared/packed-or-can-pack';

const pubQuery = {
  clientId: String,
  packedItemsOnly: Boolean,
  query: {
    type: String,
    optional: true,
  },
  requestId: {
    type: String,
    optional: true,
  },
  packingUnitId: {
    type: String,
    optional: true,
  },
  selectedItemsOnly: {
    type: Array,
    optional: true,
  },
  'selectedItemsOnly.$': String,
  receiptNo: {
    type: Array,
    optional: true,
  },
  'receiptNo.$': String,
  offshoreClient: {
    type: String,
    optional: true,
  },
};

export const GetItemsInPackingUnit = {
  name: 'items.getItemsInPackingUnit',

  validate(args) {
    new SimpleSchema(pubQuery)
      .validate(args);
  },

  run(args) {
    const runQuery = (selector) =>
      Items.find(selector, { sort: { receivedDate: -1 } }); // Possibly remove limit

    const selector = queryBuilder(args);

    console.log(`GetItemsInPackingUnit Selector ${JSON.stringify(selector)}`);
    return runQuery(selector).fetch();
  },

  call(args, callback) {
    return Meteor.call(this.name, args, callback);
  },
};
