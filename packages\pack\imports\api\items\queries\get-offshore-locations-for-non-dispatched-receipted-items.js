import { Items } from '../items';
import SimpleSchema from 'simpl-schema';
import { User } from '../../api.helpers/user';
import { ReceiptTypes } from '../receipt.types';

const params = {

};

// NOTE THAT THIS DOES PULL BACK ALL NON-DISPATCHED, RECEIPTED ITEMS (on server)
// This should be limited by real-life storage in the warehouse. (say few 1000 - guess)
// However we may need to the distinct operation on the server in future if this
// becomes a performance problem.
// The results of this are used on the Store screen filtering.
export const GetOffshoreLocationsForNonDispatchedReceiptedItems = {
  name: 'items.getOffshoreLocationsForNonDispatchedReceiptedItems',

  validate(args) {
    new SimpleSchema(params)
      .validate(args);
  },

  run() {
    const siteId = User.activeSite();

    const query = {
      receiptType: ReceiptTypes.chemReceipt,
      $and: [
        { siteId },
        { isDispatched: false },
      ],
    };

    const fieldsToReturn = { offshoreLocation: 1 };

    const itemsNonDispatchedAndReceipted = Items.find(query, { fields: fieldsToReturn }).fetch();

    const allOffshoreLocations = itemsNonDispatchedAndReceipted.map((x) => (x.offshoreLocation));

    const distinctOffshoreLocations = [...new Set(allOffshoreLocations)];

    // Build object list to return
    const distinctOffshoreLocationsObjs = [];
    distinctOffshoreLocations.forEach((x, index) => {
      distinctOffshoreLocationsObjs.push({
        _id: index, // 1,2,3,4 etc.
        name: x, // offshore location name
      });
    });
    return distinctOffshoreLocationsObjs.sort((a, b) => a.name.localeCompare(b.name))
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
