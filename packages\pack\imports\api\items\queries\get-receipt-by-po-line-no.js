import { Items } from '../items';
import { Meteor } from 'meteor/meteor';
import SimpleSchema from 'simpl-schema';

const query = {
  poNo: String,
  poLineNo: SimpleSchema.Integer,
};

export const GetReceiptByPoLineNo = {
  name: 'items.getReceiptByPoLineNo',

  validate(args) {
    new SimpleSchema(query)
      .validate(args);
  },

  run({ poNo, poLineNo }) {
    return Items.findOne({ poNo, poLineNo });
  },

  call(args, callback) {
    return Meteor.call(this.name, args, callback);
  },
};
