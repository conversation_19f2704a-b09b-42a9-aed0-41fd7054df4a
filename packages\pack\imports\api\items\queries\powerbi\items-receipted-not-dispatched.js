import { Items } from '../../items';
import { ReceiptTypes } from '../../receipt.types';
import { utils } from '../../../../shared/utils';
import moment from 'moment-timezone';

const isNonEmptyString = (str) => {
  let isNonEmptyStr = false;
  if (str !== undefined &&
      str !== null &&
      (typeof str) === 'string' &&
      str.trim().length !== 0) {
    isNonEmptyStr = true;
  }
  return isNonEmptyStr;
};

const isDangerous = (item) => {
  const unNo = utils.getFromDotNotation(item, 'unNo');
  const imoHazardClass = utils.getFromDotNotation(item, 'imoHazardClass');
  const imoSubClass = utils.getFromDotNotation(item, 'imoSubClass');

  const isDangerous = (
    isNonEmptyString(unNo) ||
    isNonEmptyString(imoHazardClass) ||
    isNonEmptyString(imoSubClass)
  );

  return isDangerous;
};

const marinePollutant = (item) => {
  var marinePollutant;

  if (!utils.hasFromDotNotation(item, 'marinePollutant')) {
    return null;
  } else {
    marinePollutant = utils.getFromDotNotation(item, 'marinePollutant');
  }

  return (marinePollutant) ? 'Yes' : 'No';
};

const receiveDateInDenHelderTime = (item) => {
  var dateTime;

  if (!utils.hasFromDotNotation(item, 'receivedDate')) {
    return null;
  } else {
    dateTime = utils.getFromDotNotation(item, 'receivedDate');
  }

  var format = 'YYYY-MM-DDTHH:mm:ss ZZ';
  return moment(dateTime).tz('Etc/GMT-1').format(format);
};

// Map of item properties to desired output fields as of 16/03/2018
const fields = {
  IsDangerousGoods: isDangerous,
  IsWaste: 'isWaste', // Correct
  DateTimeOfArrivalUtc: 'receivedDate', // Correct
  DateTimeOfArrivalLocal: receiveDateInDenHelderTime, // Correct
  DateTimeOfVoyageDepartureOrArrival: null, // For alignment with Flow API
  Client: 'offshoreClient', // Correct
  ClientAddress: '', // To be addded to material item from cargo item
  OffshoreInstallation: 'offshoreLocation', // Correct
  VoyageNumber: 'voyageNo', // Correct
  ManifestNumber: 'ccuManifestNo', // Correct
  CCU: 'ccu', // Correct
  EuralEwcCode: 'euralCode', // Correct
  WasteDescription: 'wasteDescription', // Correct
  MaterialDescription: 'description', // Correct
  IMOHazardClass: 'imoHazardClass', // Correct
  IMOSubClass: 'imoSubClass', // Correct
  PackingUnit: 'packingUnit', // Correct
  PackingGroup: 'ecargoMaterialInfo.packingGroup', // Correct
  GrossWeight: 'weightKg', // Correct
  GrossWeightUnit: '', // Hardcode kg or Kg or KG (TODO: confirm casing)
  Quantity: 'quantity', // Correct
  DestinationOfWasteMaterial: 'ecargoMaterialInfo.destination', // Correct
  DestinationAddress: '',
  StockLocation: 'location', // Correct
  Direction: '', // Hardcode "inbound"
  UnNo: 'unNo', // Correct
  ShippingName: 'ecargoMaterialInfo.properShippingName', // Correct
  TechnicalName: '',
  MarinePollutant: marinePollutant, // Correct
  DbSource: '', // For alignment with Flow API
  IsCancelled: 'ecargoMaterialInfo.isCancelled', // For alignment with Flow API
  MaterialLineId: 'externalMaterialLineId', // For differentiation between this and Flow item in PowerBi
};

// Values to be hardcoded
const hardcodes = {
  GrossWeightUnit: 'kg',
  Direction: 'inbound',
  DestinationAddress: null,
  ClientAddress: null,
  TechnicalName: null,
  DateTimeOfVoyageDepartureOrArrival: null,
  DbSource: 'Vor-Chemicals',
};

export const getPowerBiReceiptedNotDispatched = () => {
  const items = Items.find({
    receiptType: ReceiptTypes.chemReceipt,
    isDispatched: false,
  }).fetch();

  const result = items.map((item) => {
    const formatted = {};
    _.mapObject(fields, (val, key) => {
      if(typeof val === 'function') {
        formatted[key] = val(item);
      } else {
        if (!utils.hasFromDotNotation(item, val)) {
          formatted[key] = null;
        } else {
          formatted[key] = utils.getFromDotNotation(item, val);
        }
      }
    });

    _.mapObject(hardcodes, (val, key) => {
      formatted[key] = val;
    });

    return formatted;
  });

  return result;
};
