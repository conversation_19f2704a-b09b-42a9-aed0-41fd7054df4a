import { DB_DATE_STR_FORMAT } from '../../../shared/lib/constants';
import SimpleSchema from 'simpl-schema';
import moment from 'moment';

export const DefaultProcessSchema = new SimpleSchema({
  isStored: {
    type: Boolean,
    optional: true,
    defaultValue: false,
  },
  location: {
    type: String,
    optional: true,
  },
  subLocation: {
    type: String,
    optional: true,
  },
  storedDate: {
    type: Date,
    optional: true,
  },
  storedDateStr: {
    type: String,
    optional: true,
    autoValue: function storedDateToStr() {
      const storedDate = this.field('storedDate');

      if (storedDate.isSet && storedDate) {
        const pDtStr = moment(storedDate.value).format(DB_DATE_STR_FORMAT);
        if (pDtStr === 'Invalid date') {
          return null;
        }
        return pDtStr;
      }
      return undefined;
    },
  },
  isPacked: {
    type: Boolean,
    optional: true,
    defaultValue: false,
  },
  packedDate: {
    type: Date,
    optional: true,
  },
  packedDateStr: {
    type: String,
    optional: true,
    autoValue: function packedDateToStr() {
      const packedDate = this.field('packedDate');

      if (packedDate.isSet && packedDate) {
        const pDtStr = moment(packedDate.value).format(DB_DATE_STR_FORMAT);
        if (pDtStr === 'Invalid date') {
          return null;
        }
        return pDtStr;
      }
      return undefined;
    },
  },
  packingUnit: {
    type: String,
    optional: true,
  },
  packingUnitIdentifier: {
    type: String,
    optional: true,
  },
  requestId: {
    type: String,
    optional: true,
  },
  isDispatched: {
    type: Boolean,
    optional: true,
    defaultValue: false,
  },
  dispatchedDate: {
    type: Date,
    optional: true,
  },
  dispatchedDateStr: {
    type: String,
    optional: true,
    autoValue: function dispatchedDateToStr() {
      const dispatchedDate = this.field('dispatchedDate');

      if (dispatchedDate.isSet) {
        return moment(dispatchedDate.value).format(DB_DATE_STR_FORMAT);
      }

      return undefined;
    },
  },
});
