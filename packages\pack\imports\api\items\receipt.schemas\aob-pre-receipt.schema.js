import { DefaultProcessSchema } from '../receipt-process.schemas/default-process.schema';
import { ItemsSchema } from '../items.schema';
import SimpleSchema from 'simpl-schema';
import { TimestampsSchema } from
  '../../api.shared-schemas/shared-schemas';

export const AobPreReceiptSchema = ItemsSchema.pick(
  'receiptNo',
  'batchInsertIndex',
  'receivedDate',
  'receivedDateStr',
  'poNo',
  'poId',
  'vendor',
  'vendorDeliveryNo',
  'receiptType',
  'receiptLocation',
  'siteId',
  'client',
  'events')
  .extend(DefaultProcessSchema)
  .extend(TimestampsSchema)
  .extend({
    noOfPoLinesReceived: {
      type: SimpleSchema.Integer,
      min: 1,
      optional: true,
    },
    description: {
      type: String,
      max: 750,
      optional: true,
    },
  });
