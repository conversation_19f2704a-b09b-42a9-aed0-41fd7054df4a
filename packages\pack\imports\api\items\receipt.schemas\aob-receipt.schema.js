import { DefaultProcessSchema } from '../receipt-process.schemas/default-process.schema';
import { ItemsSchema } from '../items.schema';
import { ReceiptTypes } from '../receipt.types';
import { TimestampsSchema } from
  '../../api.shared-schemas/shared-schemas';

export const AobReceiptSchema = ItemsSchema.pick(
  'receiptNo',
  'receiptCategory',
  'offshoreLocation',
  'isBackload',
  'isQa',
  'isYard',
  'poNo',
  'offshoreLocationStorageBin',
  'poLineNo',
  'poId',
  'deliveryNo',
  'customsStatus',
  'workOrderNo',
  'materialNo',
  'packageType',
  'quantity',
  'weightKg',
  'description',
  'ncrs',
  'receiptType',
  'events',
)
  .extend(DefaultProcessSchema)
  .extend(TimestampsSchema)
  .extend({
    receiptType: {
      autoValue: function setToFullReceipt() {
        if (this.isSet && this.value === ReceiptTypes.aobReceipt) {
          return ReceiptTypes.fullReceipt;
        }

        return undefined;
      },
    },
  });
