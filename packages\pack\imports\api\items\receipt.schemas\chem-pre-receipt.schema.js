import { DefaultProcessSchema } from '../receipt-process.schemas/default-process.schema';
import { ItemsSchema } from '../items.schema';
import SimpleSchema from 'simpl-schema';
import { TimestampsSchema } from
  '../../api.shared-schemas/shared-schemas';

// TODO: NEED SEPARATE SCHEMAS FOR CARGOITEM (CCU) and FOR Material ITEM Insert
// CURRENTLY USING SAME SCHEMA FOR BOTH WHICH MEANS LOTS OF FIELDS ARE OPTIONAL AND SHOULDN'T BE.

// Specifies what fields are in the cargo item that is pre-receipted.
const CargoSchemaExtension = new SimpleSchema({
  // Chemicals-related properties
  offshoreClient: String,
  ccu: String,
  offshoreLocation: String,
  voyageNo: String,
  manifestNo: {
    type: String,
    optional: true,
  },
  ecargoCargoLine: {
    type: Object,
    blackbox: true,
    optional: true,
  },
  externalCargoLineId: String,
  noOfMaterialLinesReceived: {
    type: SimpleSchema.Integer,
    min: 1,
    optional: true,
  },
  cargoId: {
    type: String,
    optional: true,
  },
});

// Speciify what fields from cargo level need to also be in the material item
export const CargoFieldsInMaterialItemSchema = CargoSchemaExtension.pick(
  'offshoreClient',
  'ccu',
  'offshoreLocation',
  'voyageNo',
  'manifestNo',
  'externalCargoLineId',
  'cargoId',
);

const MaterialItemSchemaExtension = new SimpleSchema({
  // Fields for Material Item.
  ccuManifestNo: {
    type: String,
    optional: true,
  },
  materialManifestNo: {
    type: String,
    optional: true,
  },
  isCancelled: {
    type: Boolean,
    optional: true,
  },
});

// This schema used to validate initial material item insert into items collection.
export const ChemPreReceiptSchema = ItemsSchema.pick(
  'receiptNo',
  'itemLineIndex',
  'client',
  'receiptLocation',
  'receivedDate',
  'receiptType',
  'siteId',
  'events',
)
  .extend(CargoFieldsInMaterialItemSchema)
  .extend(MaterialItemSchemaExtension)
  .extend(DefaultProcessSchema)
  .extend(TimestampsSchema)
  .extend(
    {
      cargoItemId: { // Guid
        type: String,
        optional: true, // Not set until just before insert.
      },
      externalMaterialLineId: { // Guid
        type: String,
        optional: true, // Not set until just before insert.
      },
      description: {
        type: String,
        max: 500,
        optional: true,
      },
      wasteDescription: {
        type: String,
        max: 500,
        optional: true,
      },
      ecargoMaterialInfo: {
        type: Object,
        blackbox: true,
        optional: true,
      },
      euralCode: {
        type: String,
        max: 50,
        optional: true,
      },
      imoHazardClass: {
        type: String,
        max: 50,
        optional: true,
      },
      imoSubClass: {
        type: String,
        max: 50,
        optional: true,
      },
      imoCode: {
        type: String,
        max: 50,
        optional: true,
      },
      unNo: {
        type: String,
        max: 50,
        optional: true,
      },
      quantity: {
        type: Number,
        optional: true,
      },
      isWaste: {
        type: Boolean,
        optional: true,
        defaultValue: false,
      },
      weightKg: {
        type: Number,
        optional: true,
        defaultValue: 0.00,
      },
      marinePollutant: {
        type: Boolean,
        optional: false,
      },
    },
  );

export const ChemPreReceiptFromClientSchema = ItemsSchema.pick(
  'receiptNo',
  'itemLineIndex',
  'client',
  'receiptLocation',
  'receivedDate',
  'receiptType',
  'siteId',
  'events',
)
  .extend(CargoSchemaExtension)
  .extend(MaterialItemSchemaExtension)
  .extend(DefaultProcessSchema)
  .extend(TimestampsSchema)
  .extend(
    {
      cargoItemId: { // Guid
        type: String,
        optional: true, // Not set until just before insert.
      },
      description: {
        type: String,
        max: 500,
        optional: true,
      },
      ecargoMaterialInfo: {
        type: Object,
        blackbox: true,
        optional: true,
      },
      euralCode: {
        type: String,
        max: 50,
        optional: true,
      },
      imoHazardClass: {
        type: String,
        max: 50,
        optional: true,
      },
      imoSubClass: {
        type: String,
        max: 50,
        optional: true,
      },
      unNo: {
        type: String,
        max: 50,
        optional: true,
      },
      imoCode: {
        type: String,
        max: 50,
        optional: true,
      },
      quantity: {
        type: Number,
        optional: true,
      },
      isWaste: {
        type: Boolean,
        optional: true,
        defaultValue: false,
      },
      weightKg: {
        type: Number,
        optional: true,
        defaultValue: 0.00,
      },
    },
  );
