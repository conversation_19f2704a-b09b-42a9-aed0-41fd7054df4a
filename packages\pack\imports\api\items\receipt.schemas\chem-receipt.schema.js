// import { DefaultProcessSchema } from '../receipt-process.schemas/default-process.schema';
import { ChemPreReceiptSchema } from './chem-pre-receipt.schema';

import { _idSchema } from '../../api.shared-schemas/shared-schemas';
import SimpleSchema from 'simpl-schema';

const WeightsSchema = new SimpleSchema({
  itemRefNo: String,
  weight: {
    type: Number,
    optional: true,
  },
});

const PackageTypesSchema = new SimpleSchema({
  itemRefNo: String,
  packageType: {
    type: String,
    optional: false,
  },
});

// Base schema
const ChemReceiptBaseSchema = new SimpleSchema({
  // Fields for Material Item.
  quantity: {
    type: Number,
    optional: false,
  },
  receiptType: {
    type: String,
    optional: false,
  },
  receiptNo: {
    type: String,
    optional: false,
  },
  description: {
    type: String,
    optional: false,
  },
  ncrs: {
    type: Array,
    optional: true,
    defaultValue: [],
  },
  'ncrs.$': String,
  receiptCategory: String,
  ccu: {
    type: String,
    optional: false,
  },
  offshoreClient: {
    type: String,
    optional: false,
  },
  offshoreLocation: {
    type: String,
    optional: false,
  },
  materialManifestNo: {
    type: String,
    optional: true,
  },
  unNo: {
    type: String,
    optional: true,
  },
  imoHazardClass: {
    type: String,
    optional: true,
  },
  imoSubClass: {
    type: String,
    optional: true,
  },
}).extend(_idSchema);

// This Schema is used when verifying the Materials Receipt Form information.
export const ChemReceiptFromClientSchema = new SimpleSchema({
  weights: {
    type: Array,
    optional: false,
    minCount: 1,
  },
  'weights.$': WeightsSchema,
  packageTypes: {
    type: Array,
    optional: false,
    minCount: 1,
  },
  'packageTypes.$': PackageTypesSchema,
}).extend(ChemReceiptBaseSchema); // Extend with fields added from material receipt form.

// Schema to use for Items collection prior to update of item when material is receipted.
export const ChemReceiptSchema = new SimpleSchema({
  materialReceiptDateTime: Date,
  packageType: {
    type: String,
    optional: false,
  },
})
  .extend(ChemPreReceiptSchema) // Extend with fields added as part of pre-receipt.
  .extend(ChemReceiptBaseSchema); // Extend with fields added from material receipt form.
