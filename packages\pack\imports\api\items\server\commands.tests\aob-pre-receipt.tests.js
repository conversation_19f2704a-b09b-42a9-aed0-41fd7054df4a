/* eslint-env mocha */
/* eslint-disable func-names, prefer-arrow-callback */

import { AddPo } from '../../../purchase-orders/commands/add-po';
import { AobPreReceipt } from '../../commands/receipt-item.strategies/aob-pre-receipt';
import { EntitiesExistInConfiguration } from
  '../../..//company-site-profiles/queries/entities-exist-in-configuration';
import { Errors } from '../../../api.helpers/errors';
import { EventFactory } from '../../../api.events/event-factory';
import { Factory } from 'meteor/dburles:factory';
import { Items } from '../../items';
import { TestUtils } from '../../../api.helpers/test-utils';
import { chai } from 'meteor/practicalmeteor:chai';
import sinon from 'sinon';

const sandbox = sinon.createSandbox();

describe('AobPreReceipt', function () {
  before(function () {
    TestUtils
      .resetWholeDb()
      .setupCommonStaticData();
  });

  beforeEach(function () {
    TestUtils
      .resetDbIgnoringStaticData()
      .stubApiMethod(sandbox, EntitiesExistInConfiguration, { vendor: true, client: true, receiptLocation: true })
      .stubApiMethod(sandbox, AddPo);
  });

  after(function () {
    TestUtils
      .resetUserStub();
  });

  afterEach(function () {
    TestUtils
      .resetSandbox(sandbox);
  });

  it('inserts number of receipts into the db equaling the noOfPoLinesReceived', function () {
    // Arrange
    const aobPreReceiptOneHundredLines = Factory.tree('aobPreReceipt', { noOfPoLinesReceived: 100 });

    // Act
    AobPreReceipt.run({ item: aobPreReceiptOneHundredLines });

    // Assert
    const items = Items.find().fetch();

    chai.assert.equal(items.length, 100, 'Expect 100 receipts created from noOfPoLines.');
  });

  it('will not insert receipt or create PO for negative noOfPoLinesReceived', function () {
    // Arrange
    TestUtils
      .restoreStubApiMethod(AddPo)
      .mock(sandbox, AddPo, (m) => m.expects('call').never());

    const aobPreReceiptOneHundredLines = Factory.tree('aobPreReceipt', { noOfPoLinesReceived: -10 });

    // Act
    AobPreReceipt.run({ item: aobPreReceiptOneHundredLines });

    // Assert
    const items = Items.find().fetch();

    chai.assert.equal(items.length, 0, 'Expect 0 receipts created from negative noOfPoLines.');

    sandbox.verify();
  });

  it('formats the receiptNo as {receiptNo}-i, where i = [1...noOfPoLinesReceived]', function () {
    // Arrange
    const aobPreReceiptOneHundredLines = Factory.tree('aobPreReceipt', { noOfPoLinesReceived: 100 });

    // Act
    AobPreReceipt.run({ item: aobPreReceiptOneHundredLines });

    // Assert
    const items = Items.find({}).fetch().sort((a, b) => a.receiptNo.split('-').pop() - b.receiptNo.split('-').pop());

    for (let i = 0; i < 100; i++) {
      chai.assert.equal(items[i].receiptNo, `${aobPreReceiptOneHundredLines.receiptNo}-${i + 1}`);
    }
  });

  it('creates received event for item', function () {
    // Arrange
    const aobPreReceiptOneHundredLines = Factory.tree('aobPreReceipt');

    // Act
    AobPreReceipt.run({ item: aobPreReceiptOneHundredLines });

    // Assert
    const item = Items.findOne();

    chai.assert.equal(item.events.length, 1, 'Expect 1 event to be added for receipt');
    chai.assert.equal(item.events[0].eventType, EventFactory.Events.Item.RECEIVED, 'Expect item event of type RECEIVED');
  });

  it('throws error if the vendor is not in the sites configuration', function () {
    // Arrange
    TestUtils
      .restoreStubApiMethod(EntitiesExistInConfiguration)
      .stubApiMethod(sandbox, EntitiesExistInConfiguration, { vendor: false, client: true, receiptLocation: true });

    const aobPreReceipt = Factory.tree('aobPreReceipt');

    // Act & Assert
    chai.assert.throws(
      () => AobPreReceipt.run({ item: aobPreReceipt }),
      Meteor.Error,
      Errors.types.notFound,
    );
  });

  it('throws error if the client is not in the sites configuration', function () {
    // Arrange
    TestUtils
      .restoreStubApiMethod(EntitiesExistInConfiguration)
      .stubApiMethod(sandbox, EntitiesExistInConfiguration, { vendor: true, client: false, receiptLocation: true });

    const aobPreReceipt = Factory.tree('aobPreReceipt');

    // Act & Assert
    chai.assert.throws(
      () => AobPreReceipt.run({ item: aobPreReceipt }),
      Meteor.Error,
      Errors.types.notFound,
    );
  });

  it('throws error if the receiptLocation is not in the sites configuration', function () {
    // Arrange
    TestUtils
      .restoreStubApiMethod(EntitiesExistInConfiguration)
      .stubApiMethod(sandbox, EntitiesExistInConfiguration, { vendor: true, client: true, receiptLocation: false });

    const aobPreReceipt = Factory.tree('aobPreReceipt');

    // Act & Assert
    chai.assert.throws(
      () => AobPreReceipt.run({ item: aobPreReceipt }),
      Meteor.Error,
      Errors.types.notFound,
    );
  });
});
