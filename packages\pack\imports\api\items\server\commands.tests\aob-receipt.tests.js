/* eslint-env mocha */
/* eslint-disable func-names, prefer-arrow-callback */

import { Errors } from '../../../api.helpers/errors';
import { Factory } from 'meteor/dburles:factory';
import { Items } from '../../items';
import { TestUtils } from '../../../api.helpers/test-utils';
import { chai } from 'meteor/practicalmeteor:chai';
import sinon from 'sinon';

const sandbox = sinon.createSandbox();

describe('AobReceipt', function () {
  before(function () {
    TestUtils
      .resetWholeDb()
      .setupCommonStaticData();
  });

  beforeEach(function () {
    TestUtils
      .resetDbIgnoringStaticData();
  });

  after(function () {
    TestUtils
      .resetUserStub();
  });

  afterEach(function () {
    TestUtils
      .resetSandbox(sandbox);
  });
});
