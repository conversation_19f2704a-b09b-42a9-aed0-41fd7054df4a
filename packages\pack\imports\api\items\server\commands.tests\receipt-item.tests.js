/* eslint-env mocha */
/* eslint-disable func-names, prefer-arrow-callback */

import { AobPreReceipt } from '../../commands/receipt-item.strategies/aob-pre-receipt';
import { Factory } from 'meteor/dburles:factory';
import { IncrementItemRefCounter } from '../../../company-site-profiles/commands/increment-item-ref-counter';
import { ReceiptItem } from '../../commands/receipt-item';
import { TestUtils } from '../../../api.helpers/test-utils';
import { chai } from 'meteor/practicalmeteor:chai';
import sinon from 'sinon';

const sandbox = sinon.createSandbox();

describe('ReceiptItem', function () {
  before(function () {
    TestUtils
      .resetWholeDb()
      .setupCommonStaticData();
  });

  afterEach(function () {
    TestUtils
      .resetSandbox(sandbox)
      .resetDbIgnoringStaticData();
  });

  it('uses aob pre-receipt strategy when ReceiptType is aobPreReceipt', function () {
    // Arrange
    TestUtils
      .mock(sandbox, AobPreReceipt, (m) => m.expects('call').once());

    const aobPreReceiptClientSubmission = Factory.tree('aobPreReceipt', { noOfPoLinesReceived: 1 });

    // Act
    ReceiptItem.run({ item: aobPreReceiptClientSubmission });

    // Assert
    sandbox.verify();
  });

  it('calls increment receiptNo counter when aobPreReceipt is receipted', function () {
    // Arrange
    TestUtils
      .mock(sandbox, IncrementItemRefCounter, m => m.expects('call').once())
      .stubApiMethod(sandbox, AobPreReceipt, null, false);

    const aobPreReceipt = Factory.tree('aobPreReceipt');

    // Act
    ReceiptItem.run({ item: aobPreReceipt });

    // Assert
    sandbox.verify();
  });

  it('uses aob receipt strategy when ReceiptType is aobReceipt', function () {

  });

  it('throws error when unrecognised ReceiptType is used', function () {
    // Arrange
    const invalidReceipt = Factory.tree('aobPreReceipt', { receiptType: 'invalid-receipt-type' });

    // Act & Assert
    chai.assert
      .throws(() => ReceiptItem.run({ item: invalidReceipt }), Meteor.Error, 'Receipt Type not recognised.');
  });
});
