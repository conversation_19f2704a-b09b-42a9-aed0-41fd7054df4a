/* eslint-env mocha */
/* eslint-disable func-names, prefer-arrow-callback */
import { CompanySiteProfiles } from '../../../company-site-profiles/company-site-profiles';
import { DB_DATE_STR_FORMAT } from '../../../../shared/lib/constants';
import { Factory } from 'meteor/dburles:factory';
import { GetSiteFromIdentifier } from '../../../company-site-profiles/queries/get-site-from-identifier';
import { Items } from '../../../items/items';
import { StoreItems } from '../../commands/store-items';
import { TestUtils } from '../../../api.helpers/test-utils';
import { chai } from 'meteor/practicalmeteor:chai';
import moment from 'moment';
import sinon from 'sinon';

const sandbox = sinon.createSandbox();

const createTestReceipt = () => Factory.create('aobFullReceipt');

const storeItemWithDefaultLocations = (itemId) =>
  StoreItems.run({ itemIds: [itemId], location: 'TEST-LOCATION', subLocation: 'TEST-SUB-LOCATION' });

describe('StoreItems', function () {
  before(function () {
    TestUtils
      .resetWholeDb()
      .setupCommonStaticData();
  });

  beforeEach(function () {
    const siteProfile = CompanySiteProfiles.findOne();

    TestUtils
      .resetDbIgnoringStaticData()
      .stubApiMethod(sandbox, GetSiteFromIdentifier, siteProfile)
      .resetMockedDate();
  });

  after(function () {
    TestUtils
      .resetUserStub();
  });

  afterEach(function () {
    TestUtils
      .resetSandbox(sandbox);
  });

  it('stores item in the specified location and sublocation', function () {
    // Arrange
    const item = createTestReceipt();
    const location = 'TEST-STORAGE-LOCATION';
    const subLocation = 'TEST-SUB-LOCATION';

    // Act
    StoreItems.run({ itemIds: [item._id], location, subLocation });

    // Assert
    const storedItem = Items.findOne({ _id: item._id });

    chai.assert.equal(storedItem.location, location, 'Expect item`s location to be set.');
    chai.assert.equal(storedItem.subLocation, subLocation, 'Expect item`s subLocation to be set.');
  });

  it('sets the item`s isStored flag to true', function () {
    // Arrange
    const item = createTestReceipt();

    // Act
    storeItemWithDefaultLocations(item._id);

    // Assert
    const storedItem = Items.findOne({ _id: item._id });

    chai.assert.equal(storedItem.isStored, true, 'Expect isStored to be set to true.');
  });

  it('sets the item`s storedDate to the current time', function () {
    //  Arrange
    const item = createTestReceipt();
    const now = moment().utc();

    TestUtils
      .stubMomentAndJSDateNow(now);

    // Act
    storeItemWithDefaultLocations(item._id);

    // Assert
    const storedItem = Items.findOne({ _id: item._id });

    chai.assert.isTrue(moment(storedItem.storedDate).isSame(now));
  });

  it('the storedDateStr is a string representation of the storedDate', function () {
    // Arrange
    const item = createTestReceipt();

    // Act
    storeItemWithDefaultLocations(item._id);

    // Assert
    const storedItem = Items.findOne({ _id: item._id });
    const storedDate = storedItem.storedDate;
    const storedDateStr = storedItem.storedDateStr;

    chai.assert.equal(storedDateStr, moment(storedDate).format(DB_DATE_STR_FORMAT));
  });

  it('adds a stored event to the item`s event array with the location and subLocation', function () {
    // Arrange
    const item = createTestReceipt();
    const location = 'TEST-STORAGE-LOCATION';
    const subLocation = 'TEST-SUB-LOCATION';

    // Act
    StoreItems.run({ itemIds: [item._id], location, subLocation });

    // Assert
    const storedItem = Items.findOne({ _id: item._id });

    chai.assert.equal(storedItem.events.length, 1, 'Expect item to have one event.');
    chai.assert.equal(storedItem.events[0].eventData.location, location, 'Expect event to contain location.');
    chai.assert.equal(storedItem.events[0].eventData.subLocation, subLocation, 'Expect event to contain subLocation');
  });

  it('throws error if the itemId is not recognised', function () {
    // Arrange
    createTestReceipt();

    // Act & Assert
    chai.assert.throws(() =>
      storeItemWithDefaultLocations(new Mongo.ObjectID()._str), Meteor.Error);
  });

  it('throws error if the item is a partial receipt', function () {
    // Arrange
    const partialReceipt = Factory.create('aobPreReceipt');

    // Act & Assert
    chai.assert.throws(() =>
      storeItemWithDefaultLocations(partialReceipt._id), Meteor.Error);
  });
});
