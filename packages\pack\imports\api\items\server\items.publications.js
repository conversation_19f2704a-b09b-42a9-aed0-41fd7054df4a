import { ForClientFiltered } from './publications/for-client-filtered';
import { Items } from '../items';
import { ItemsFromListOfIds } from './publications/items-from-list-of-ids';
import { PackedOrCanPack } from './publications/packed-or-can-pack';
import { PreReceiptForPoRequiringReceipting } from './publications/pre-receipt-for-po-requiring-receipting';
import { ReactiveTable } from 'meteor/fcallem:reactive-table-semantic';
import { RecentItems } from './publications/recent-items';
import { Register } from '../../api.helpers/register';
import { SelectedItem } from './publications/selected-item';
import { SelectedItemForEdit } from './publications/selected-item-for-edit';
import { StoredOrCanStore } from './publications/stored-or-can-store';
import { ItemsForCargoId } from './publications/items-for-cargo-id';
import { ItemsFromListOfReceiptNos } from './publications/items-from-list-of-receiptNos';
import { ItemsHistory } from './publications/items-history';

// TODO: Add site filter to this
ReactiveTable.publish('items.receiptHistory', function publish() {
  return this.userId ? Items : [];
});

Register
  .publication(ForClientFiltered)
  .publication(RecentItems)
  .publication(SelectedItem)
  .publication(SelectedItemForEdit)
  .publication(StoredOrCanStore)
  .publication(PreReceiptForPoRequiringReceipting)
  .publication(ItemsFromListOfIds)
  .publication(PackedOrCanPack)
  .publication(ItemsForCargoId)
  .publication(ItemsFromListOfReceiptNos)
  .publication(ItemsHistory);
