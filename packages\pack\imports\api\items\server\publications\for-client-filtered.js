import { Items } from '../../items';
import { ItemsSelector } from '../../items.selector';
import { Publications } from '../../../api.publications/publications';
import SimpleSchema from 'simpl-schema';
import { User } from '../../../api.helpers/user';

const pubQuery = {
  clientId: String,
  query: {
    type: String,
    optional: true,
  },
  limit: SimpleSchema.Integer,
  ignoreDispatched: {
    type: Boolean,
    optional: true,
  },
};

export const ForClientFiltered = {
  name: Publications.items.forClientFiltered,

  validate(args) {
    new SimpleSchema(pubQuery)
      .validate(args);
  },

  run({ clientId, query, limit, ignoreDispatched = false }) {
    const siteIdentifier = User.activeSite();
    const storedItemsOnly = false;
    const ignorePacked = false;

    const selector = ItemsSelector.get({
      clientId,
      siteIdentifier,
      storedItemsOnly,
      query,
      ignorePacked,
      ignoreDispatched,
    });

    return Items.find(selector, { sort: { receivedDate: -1 }, limit });
  },
};
