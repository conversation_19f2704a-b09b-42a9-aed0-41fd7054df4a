import { Items } from '../../items';
import { Publications } from '../../../api.publications/publications';
import SimpleSchema from 'simpl-schema';
import { User } from '../../../api.helpers/user';
import { Log } from '../../../api.helpers/log';

const pubQuery = {
  cargoItemId: String,
  clientId: String,
};

function isEmpty(str) {
  return (!str || 0 === str.length);
}

export const ItemsForCargoId = {
  name: Publications.items.itemsForCargoId,

  validate(args) {
    Log.info(`ItemsForCargoId publication args: ${JSON.stringify(args)}.`)

    new SimpleSchema(pubQuery)
      .validate(args);
  },

  run({ cargoItemId, clientId }) {
    Log.info(`ItemsForCargoId publication args: ${JSON.stringify({ cargoItemId, clientId })}.`);
    const userSite = User.activeSite();

    let cargoItemIdToSearchFor = '*** NOT SPECIFIED ***';
    if (!isEmpty(cargoItemId)) {
      cargoItemIdToSearchFor = cargoItemId;
    }

    return Items.find({
      cargoItemId: cargoItemIdToSearchFor,
      siteId: userSite,
    });
  },
};
