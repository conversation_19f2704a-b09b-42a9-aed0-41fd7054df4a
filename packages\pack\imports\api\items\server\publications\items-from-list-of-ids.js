import { Items } from '../../items';
import { Publications } from '../../../api.publications/publications';
import SimpleSchema from 'simpl-schema';

const pubQuery = {
  itemIds: Array,
  'itemIds.$': String,
};

export const ItemsFromListOfIds = {
  name: Publications.items.itemsFromListOfIds,

  validate(args) {
    new SimpleSchema(pubQuery)
      .validate(args);
  },

  run({ itemIds }) {
    return Items.find({
      _id: { $in: itemIds },
    }, { sort: { receivedDate: -1 } });
  },
};
