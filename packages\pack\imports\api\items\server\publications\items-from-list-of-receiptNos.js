import { Items } from '../../items';
import { Publications } from '../../../api.publications/publications';
import SimpleSchema from 'simpl-schema';

const pubQuery = {
  receiptNos: Array,
  'receiptNos.$': String,
};

export const ItemsFromListOfReceiptNos = {
  name: Publications.items.itemsFromListOfReceiptNos,

  validate(args) {
    new SimpleSchema(pubQuery)
      .validate(args);
  },

  run({ receiptNos }) {
    console.log(`${receiptNos} receipt nos`);
    return Items.find({
      receiptNo: { $in: receiptNos },
    }, { sort: { receivedDate: -1 } });
  },
};
