import { Items } from '../../items';
import { Publications } from '../../../api.publications/publications';
import SimpleSchema from 'simpl-schema';
import { ItemsSelector } from '../../items.selector';
import { User } from '../../../api.helpers/user';
import { ReceiptTypes } from '../../receipt.types';

const pubQuery = {
  pageSize: SimpleSchema.Integer,
  page: SimpleSchema.Integer,
};

export const ItemsHistory = {
  name: Publications.items.itemsHistory,

  validate(args) {
    new SimpleSchema(pubQuery)
      .validate({ pageSize: args.pageSize, page: args.page });
  },

  run({ pageSize, page, filter }) {
    const userSite = User.activeSite();

    // Build query
    const query = ItemsSelector
      .get({
        clientId: filter.clientId,
        siteIdentifier: userSite,
        query: filter.query,
        ignorePacked: false,
        ignoreDispatched: false
      });

    // Build Date Filter
    const dateFilter = {};

    if (filter.fromDate) {
      dateFilter.$gte = filter.fromDate;
    }

    if (filter.toDate) {
      dateFilter.$lte = filter.toDate;
    }

    if (Object.keys(dateFilter).length) {
      query[filter.dateToFilter] = dateFilter;
    }

    if (filter.offshoreClient) {
      query.offshoreClient = filter.offshoreClient;
    }

    // Restrict to material items that have actually been receipted (i.e. removed from Cargo item).
    query.receiptType = ReceiptTypes.chemReceipt;

    const sortDirection = filter.sortDirection === 'desc' ? -1 : 1;

    const limit = pageSize;
    const skip = pageSize * (page - 1);

    return Items.find(query, { limit, skip, sort: { [filter.sortBy]: sortDirection } });
  },
};
