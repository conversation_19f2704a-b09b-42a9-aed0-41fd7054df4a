import { Items } from '../../items';
import { ItemsSelector } from '../../items.selector';
import { Publications } from '../../../api.publications/publications';
import { ReceiptTypes } from '../../receipt.types';
import SimpleSchema from 'simpl-schema';
import { User } from '../../../api.helpers/user';
import { queryBuilder } from '../../shared/packed-or-can-pack';

const paginationSchema = new SimpleSchema({
  pagination: {
    type: Object,
    optional: true,
  },
  'pagination.page': {
    type: Number,
    optional: false,
  },
  'pagination.perPage': {
    type: Number,
    optional: false,
  },
  sorting: {
    type: Object,
    optional: true,
    blackbox: true,
  },
});

const pubQuery = {
  clientId: String,
  limit: {
    type: SimpleSchema.Integer,
    optional: true,
  },
  packedItemsOnly: Boolean,
  query: {
    type: String,
    optional: true,
  },
  requestId: {
    type: String,
    optional: true,
  },
  packingUnitId: {
    type: String,
    optional: true,
  },
  selectedItemsOnly: {
    type: Array,
    optional: true,
  },
  'selectedItemsOnly.$': String,
  receiptNo: {
    type: Array,
    optional: true,
  },
  'receiptNo.$': String,
  offshoreClient: {
    type: String,
    optional: true,
  },
};

export const PackedOrCanPack = {
  name: Publications.items.packedOrCanPack,

  validate(args) {
    new SimpleSchema(pubQuery)
      .extend(paginationSchema)
      .validate(args);
  },

  run(args) {
    const limit = (args.pagination) ? args.pagination.perPage : 4;
    const skip = (args.pagination && args.pagination.page > 0)? ((args.pagination.page - 1) * limit) : 0;
    const sort = (args.sorting) ? args.sorting : {};

    const options = {
      limit,
      skip,
      sort,
    };

     console.log('options', options);
    const runQuery = (selector) =>
      Items.find(selector, options);

    const selector = queryBuilder(args);

    console.log(`PackedOrCanPack Selector ${JSON.stringify(selector)}`);
    return runQuery(selector);
  },
};

export const PackedOrCanPackCounts = {
  name: Publications.items.packedOrCanPack + '.counts',

  validate(args) {
    new SimpleSchema(pubQuery)
      .extend(paginationSchema)
      .validate(args);
  },

  run(args) {
    const runQuery = (selector) =>
      Items.find(selector, {}).count();

    const selector = queryBuilder(args);

    console.log(`PackedOrCanPack counts Selector ${JSON.stringify(selector)}`);
    return runQuery(selector);
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
