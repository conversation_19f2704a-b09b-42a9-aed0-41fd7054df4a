import { Items } from '../../items';
import { Publications } from '../../../api.publications/publications';
import { ReceiptTypes } from '../../receipt.types';
import SimpleSchema from 'simpl-schema';
import { User } from '../../../api.helpers/user';

const pubQuery = {
  poId: String,
  clientId: String,
};

export const PreReceiptForPoRequiringReceipting = {
  name: Publications.items.preReceiptForPoRequiringReceipting,

  validate(args) {
    new SimpleSchema(pubQuery)
      .validate(args);
  },

  run({ poId, clientId }) {
    const siteId = User.activeSite();

    const selector = {
      poId,
      receiptType: ReceiptTypes.chemPreReceipt, // Chemicals hardcode this to replace AOB receipt type
      'client._id': clientId,
      siteId,
    };

    return Items.find(selector, {
      fields: { receiptNo: 1 },
      sort: { batchInsertIndex: 1 },
      limit: 1,
    });
  },
};
