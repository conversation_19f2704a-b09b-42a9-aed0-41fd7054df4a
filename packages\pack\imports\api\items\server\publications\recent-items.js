import { Items } from '../../items';
import { Publications } from '../../../api.publications/publications';
import SimpleSchema from 'simpl-schema';
import { User } from '../../../api.helpers/user';

const pubQuery = {
  pageSize: SimpleSchema.Integer,
  skipCount: SimpleSchema.Integer,
  clientId: {
    type: String,
    optional: true,
  },
};

export const RecentItems = {
  name: Publications.items.recentItems,

  validate(args) {
    new SimpleSchema(pubQuery)
      .validate(args);
  },

  run({ pageSize, skipCount, clientId }) {
    const userSite = User.activeSite();

    const selector = {
      isPacked: false,
      siteId: userSite,
    };

    if (clientId) {
      selector['client._id'] = clientId;
    }

    return Items.find(selector, {
      sort: { receivedDate: -1 },
      limit: pageSize,
      skip: skipCount,
    });
  },
};
