import { Items } from '../../items';
import { Publications } from '../../../api.publications/publications';
import SimpleSchema from 'simpl-schema';
import { User } from '../../../api.helpers/user';

const pubQuery = {
  itemId: String,
};

// Used in Material Item Edit Screen.
export const SelectedItemForEdit = {
  name: Publications.items.selectedItemForEdit,

  validate(args) {
    new SimpleSchema(pubQuery)
      .validate(args);
  },

  run({ itemId }) {
    const userSite = User.activeSite();

    return Items.find({
      _id: itemId,
      siteId: userSite,
    });
  },
};
