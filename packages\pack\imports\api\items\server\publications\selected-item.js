import { Items } from '../../items';
import { Publications } from '../../../api.publications/publications';
import SimpleSchema from 'simpl-schema';
import { User } from '../../../api.helpers/user';

const pubQuery = {
  itemId: String,
};

export const SelectedItem = {
  name: Publications.items.selectedItem,

  validate(args) {
    new SimpleSchema(pubQuery)
      .validate(args);
  },

  run({ itemId }) {
    const userSite = User.activeSite();

    return Items.find({
      _id: itemId,
      siteId: userSite,
    });
  },
};
