import { Meteor } from 'meteor/meteor';
import { Items } from '../../items';
import { Publications } from '../../../api.publications/publications';
import SimpleSchema from 'simpl-schema';
import { queryBuilder } from '../../shared/stored-or-can-store';

const paginationSchema = new SimpleSchema({
  pagination: {
    type: Object,
    optional: true,
  },
  'pagination.page': {
    type: Number,
    optional: false,
  },
  'pagination.perPage': {
    type: Number,
    optional: false,
  },
  sorting: {
    type: Object,
    optional: true,
    blackbox: true,
  },
});

const querySchema = new SimpleSchema({
  clientId: String,
  query: {
    type: String,
    optional: true,
  },
  ignorePacked: {
    type: Boolean,
    optional: true,
  },
  storedItemsOnly: {
    type: Boolean,
    optional: true,
  },
  receiptCategory: {
    type: String,
    optional: true,
  },
  offshoreLocation: {
    type: String,
    optional: true,
  },
  offshoreClient: {
    type: String,
    optional: true,
  },
  selectedItemsOnly: {
    type: Array,
    optional: true,
  },
  location: {
    type: String,
    optional: true,
  },
  fromDate: {
    type: Date,
    optional: true,
  },
  toDate: {
    type: Date,
    optional: true,
  },
  'selectedItemsOnly.$': String,
  limit: SimpleSchema.Integer,
});

export const StoredOrCanStore = {
  name: Publications.items.storedOrCanStore,

  validate(args) {
    querySchema
      .extend(paginationSchema)
      .validate(args);
  },

  run(args) {
    const limit = (args.pagination) ? args.pagination.perPage : 4;
    const skip = (args.pagination && args.pagination.page > 0)? ((args.pagination.page - 1) * limit) : 0;
    const sort = (args.sorting) ? args.sorting : {};

    const options = {
      limit,
      skip,
      sort,
    };

    const runQuery = (selector) => Items.find(selector, options);
    const selector = queryBuilder(args);
    const result = runQuery(selector);
    return result;
  },
};

export const StoredOrCanStoreCounts = {
  name: Publications.items.storedOrCanStore + '.counts',

  validate(args) {
    querySchema
      .extend(paginationSchema)
      .validate(args);
  },

  run(args) {
    const runQuery = (selector) => Items.find(selector, {}).count();
    const selector = queryBuilder(args);
    const result = runQuery(selector);

    return result;
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
