import { Items } from '../items';
import { ItemsSelector } from '../items.selector';
import { ReceiptTypes } from '../receipt.types';
import { User } from '../../api.helpers/user';

export const queryBuilder = ({
  clientId,
  query,
  packedItemsOnly,
  requestId,
  packingUnitId,
  selectedItemsOnly,
  receiptNo,
  offshoreClient = null,
}) => {
  const siteId = User.activeSite();

  const selector = {
    $and: [
      {
        'client._id': clientId,
        siteId,
        isPacked: packedItemsOnly,
        receiptType: ReceiptTypes.chemReceipt, // Edited for Chemicals
        // NOTE: must allow dispatched items (isDispatched:true), so that packed list can be shown after packing unit is 'closed'.
      },
    ],
  };

  if (selectedItemsOnly && selectedItemsOnly.length) {
    selector.$and.push({
      _id: {
        $in: selectedItemsOnly,
      },
    });

    return selector;
  }

  if (receiptNo && receiptNo.length) {
    selector.$and.push({
      receiptNo: {
        $in: receiptNo,
      },
    });
  }

  if (requestId) {
    selector.$and.push({ requestId });
  }

  if (packingUnitId) {
    selector.$and.push({ packingUnit: packingUnitId });
  }

  if (query && query.length) {
    selector.$and.push(ItemsSelector.getStringQuerySelector({ query }));
  }

  if (offshoreClient) {
    // Edited to be case insensitive search - note this may have perf impact as it won't use indexes.
    selector.$and.push({ offshoreClient: { $regex: new RegExp(offshoreClient, 'i') } });
  }

  return selector;
};
