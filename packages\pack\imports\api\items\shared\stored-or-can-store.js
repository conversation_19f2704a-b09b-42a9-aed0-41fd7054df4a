import { ItemsSelector } from '../items.selector';
import { ReceiptTypes } from '../receipt.types';
import { User } from '../../api.helpers/user';

export const queryBuilder = ({
  clientId,
  query,
  ignorePacked = true,
  storedItemsOnly = false,
  receiptCategory = null,
  offshoreLocation = null,
  offshoreClient = null,
  selectedItemsOnly = null,
  location = null,
  fromDate = null,
  toDate = null,
}) => {
  const siteId = User.activeSite();
  const selector = {
    $and: [
      {
        'client._id': clientId,
        siteId,
        isStored: storedItemsOnly,
        isPacked: !ignorePacked,
        isDispatched: false,
        receiptType: ReceiptTypes.chemReceipt, // Chemicals receipt type when material is receipted.
      },
    ],
  };

  if (selectedItemsOnly && selectedItemsOnly.length) {
    selector.$and.push({
      _id: {
        $in: selectedItemsOnly,
      },
    });

    return selector;
  }

  if (receiptCategory) {
    selector.$and.push({ receiptCategory });
  }

  if (location) {
    selector.$and.push({ location });
  }

  if (offshoreLocation) {
    selector.$and.push({ offshoreLocation });
  }

  if (fromDate) {
    selector.$and.push({ materialReceiptDateTime: { $gte:fromDate } });
  }
  if (toDate) {
    selector.$and.push({ materialReceiptDateTime: { $lte:toDate } });
  }

  if (offshoreClient) {
    // Edited to be case insensitive search - note this may have perf impact as it won't use indexes.
    selector.$and.push({ offshoreClient: { $regex: new RegExp(offshoreClient, 'i') } });
  }

  if (query && query.length) {
    selector.$and.push(ItemsSelector.getStringQuerySelector({ query }));
  }

  return selector;
};
