import { ClientSchema } from '../../company-site-profiles/configuration/client.schema';
import { EventFactory } from '../../api.events/event-factory';
import { Meteor } from 'meteor/meteor';
import { PurchaseOrders } from '../purchase-orders';
import { PurchaseOrdersSchema } from '../purchase-orders.schema';
import SimpleSchema from 'simpl-schema';
import moment from 'moment';

const command = {
  id: {
    type: String,
    optional: true,
  },
  receivedDate: Date,
  identifier: String,
  noOfLines: {
    type: SimpleSchema.Integer,
    optional: true,
  },
  vendor: String,
  vendorDeliveryNo: {
    type: String,
    optional: true,
  },
  siteIdentifier: String,
  client: ClientSchema,
  receiptLocation: String,
  receiptNo: String,
  description: {
    type: String,
    optional: true,
  },
};

export const AddPo = {
  name: 'purchaseOrders.addPo',

  validate(args) {
    new SimpleSchema(command)
      .validate(args);
  },

  run({
    id,
    receivedDate,
    identifier,
    noOfLines,
    vendor,
    vendorDeliveryNo,
    siteIdentifier,
    client,
    receiptLocation,
    description,
    receiptNo,
  }) {
    const po = {
      receivedDate,
      identifier,
      noOfLines,
      vendor,
      vendorDeliveryNo,
      siteId: siteIdentifier,
      client,
      receiptLocation,
      description,
      receiptNo,
      events: [
        EventFactory.createItemEvent(
          EventFactory.Events.PurchaseOrders.RECEIVED,
          moment().utc().toDate(),
          Meteor.user().username,
        ),
      ],
    };

    if (id && id.length) {
      po._id = id;
    }

    console.log(PurchaseOrders.simpleSchema().validate(po));
    PurchaseOrders.insert(po, { filter: false });
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
