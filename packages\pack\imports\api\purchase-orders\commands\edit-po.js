import { Errors } from '../../api.helpers/errors';
import { EventFactory } from '../../api.events/event-factory';
import { GetPoById } from '../queries/get-po-by-id';
import { Meteor } from 'meteor/meteor';
import { PurchaseOrders } from '../purchase-orders';
import SimpleSchema from 'simpl-schema';
import moment from 'moment';

const command = {
  _id: {
    type: String,
    optional: true,
  },
  receivedDate: Date,
  identifier: String,
  vendor: String,
  vendorDeliveryNo: {
    type: String,
    optional: true,
  },
  receiptLocation: String,
  description: {
    type: String,
    optional: true,
  },
};

export const EditPo = {
  name: 'purchaseOrders.editPo',

  validate(args) {
    new SimpleSchema(command)
      .validate(args);
  },

  run(poToEdit) {
    const po = GetPoById.call({ id: poToEdit._id });

    if (!po) {
      throw new Meteor.Error(Errors.types.notFound, `Couldn't find PO with Id: ${poToEdit._id}`);
    }

    const propertiesToCheck = Object.keys(command);
    const audit = [];

    const update = propertiesToCheck.reduce((acc, prop) => {
      if (poToEdit[prop]
        && (poToEdit[prop] !== po[prop])
        && (!(poToEdit[prop] instanceof Date)
          || !moment(poToEdit[prop]).isSame(moment(po[prop])))) {
        acc.$set[prop] = poToEdit[prop];
        audit.push({
          propertyName: prop,
          originalVal: po[prop],
          newVal: poToEdit[prop],
        });
      }

      return acc;
    }, { $set: {} });

    if (audit.length) {
      update.$push = {
        events: EventFactory.createItemEvent(
          EventFactory.Events.PurchaseOrders.EDITED,
          moment().utc().toDate(),
          Meteor.user().username,
          { edits: audit },
        ),
      };

      PurchaseOrders.update({ _id: poToEdit._id }, update);
    }
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
