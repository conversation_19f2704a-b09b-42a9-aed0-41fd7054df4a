import { Meteor } from 'meteor/meteor';
import { PurchaseOrders } from '../purchase-orders';
import SimpleSchema from 'simpl-schema';

const command = {
  poId: String,
  noOfLinesReceived: SimpleSchema.Integer,
};

export const UpdateNoOfLinesReceived = {
  name: 'purchaseOrders.updateNoOfLinesReceived',

  validate(args) {
    new SimpleSchema(command)
      .validate(args);
  },

  run({ poId, noOfLinesReceived }) {
    PurchaseOrders.update({ _id: poId }, {
      $set: {
        noOfLines: noOfLinesReceived,
      },
    });
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
