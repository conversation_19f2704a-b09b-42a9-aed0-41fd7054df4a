import { Meteor } from 'meteor/meteor';
import { PurchaseOrders } from '../purchase-orders';
import SimpleSchema from 'simpl-schema';

const command = {
  identifier: String,
  receiptedItem: String,
};

export const UpdateReceiptedItems = {
  name: 'purchaseOrders.updateReceiptedItems',

  validate(args) {
    new SimpleSchema(command)
      .validate(args);
  },

  run({ identifier, receiptedItem }) {
    PurchaseOrders.update({ identifier }, {
      $push: {
        linesReceipted: receiptedItem,
      },
      $inc: {
        noOfLinesReceipted: 1,
      },
    });

    const po = PurchaseOrders.findOne({ identifier });

    if (po.noOfLines === po.noOfLinesReceipted) {
      PurchaseOrders.update({ identifier }, {
        $set: {
          allLinesReceipted: true,
        },
      });
    }
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
