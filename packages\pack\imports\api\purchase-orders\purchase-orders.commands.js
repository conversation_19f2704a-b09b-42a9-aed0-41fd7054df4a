import { AddPo } from './commands/add-po';
import { EditPo } from './commands/edit-po';
import { Register } from '../api.helpers/register';
import { UpdateNoOfLinesReceived } from './commands/update-no-of-lines-received';
import { UpdateReceiptedItems } from './commands/update-receipted-items';

Register
  .command(AddPo)
  .command(UpdateReceiptedItems)
  .command(UpdateNoOfLinesReceived)
  .command(EditPo);
