import { SiteIdSchema, TimestampsSchema, _idSchema } from
  '../api.shared-schemas/shared-schemas';
import { ClientSchema } from '../company-site-profiles/configuration/client.schema';
import { DB_DATE_STR_FORMAT } from '../../shared/lib/constants';
import SimpleSchema from 'simpl-schema';
import moment from 'moment';

export const PurchaseOrdersSchema = new SimpleSchema({
  receivedDate: Date,
  receivedDateStr: {
    type: String,
    optional: true,
    autoValue: function receivedDateToStr() {
      const receivedDate = this.field('receivedDate');

      if (receivedDate.isSet) {
        return moment(receivedDate.value).format(DB_DATE_STR_FORMAT);
      }

      return undefined;
    },
  },
  identifier: String,
  vendor: String,
  vendorDeliveryNo: {
    type: String,
    optional: true,
  },
  noOfLines: {
    type: SimpleSchema.Integer,
    min: 1,
    optional: true,
  },
  linesReceipted: {
    type: Array,
    optional: true,
    defaultValue: [],
  },
  'linesReceipted.$': String,
  noOfLinesReceipted: {
    type: SimpleSchema.Integer,
    min: 0,
    optional: true,
  },
  allLinesReceipted: {
    type: Boolean,
    optional: true,
    defaultValue: false,
  },
  client: ClientSchema,
  description: {
    type: String,
    max: 750,
    optional: true,
  },
  receiptLocation: String,
  receiptNo: String,
  events: {
    type: Array,
    optional: true,
    defaultValue: [],
  },
  'events.$': {
    type: Object,
    blackbox: true,
  },
})
  .extend(_idSchema)
  .extend(SiteIdSchema)
  .extend(TimestampsSchema);
