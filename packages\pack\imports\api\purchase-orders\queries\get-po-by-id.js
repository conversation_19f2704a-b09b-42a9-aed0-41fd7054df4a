import { Meteor } from 'meteor/meteor';
import { PurchaseOrders } from '../purchase-orders';
import SimpleSchema from 'simpl-schema';

const query = {
  id: String,
};

export const GetPoById = {
  name: 'purchaseOrders.getPoById',

  validate(args) {
    new SimpleSchema(query)
      .validate(args);
  },

  run({ id }) {
    return PurchaseOrders.findOne({ _id: id });
  },

  call(args, callback) {
    return Meteor.call(this.name, args, callback);
  },
};
