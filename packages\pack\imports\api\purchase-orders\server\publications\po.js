import { Publications } from '../../../api.publications/publications';
import { PurchaseOrders } from '../../purchase-orders';
import SimpleSchema from 'simpl-schema';
import { User } from '../../../api.helpers/user';

const pubQuery = {
  id: String,
};

export const Po = {
  name: Publications.purchaseOrders.po,

  validate(args) {
    new SimpleSchema(pubQuery)
      .validate(args);
  },

  run({ id }) {
    const selector = {
      siteId: User.activeSite(),
      _id: id,
    };

    return PurchaseOrders.find(selector);
  },
};
