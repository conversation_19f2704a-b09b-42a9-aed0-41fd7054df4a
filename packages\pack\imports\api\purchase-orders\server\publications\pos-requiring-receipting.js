import { Publications } from '../../../api.publications/publications';
import { PurchaseOrders } from '../../purchase-orders';
import SimpleSchema from 'simpl-schema';
import { User } from '../../../api.helpers/user';
import { utils } from '../../../../shared/utils';

const pubQuery = {
  query: {
    type: String,
    optional: true,
  },
  clientId: {
    type: String,
    optional: true,
  },
  limit: SimpleSchema.Integer,
  page: SimpleSchema.Integer,
};

export const PosRequiringReceipting = {
  name: Publications.purchaseOrders.posRequiringReceipting,

  validate(args) {
    new SimpleSchema(pubQuery)
      .validate(args);
  },

  run({ query, clientId, limit, page }) {
    const selector = {
      $and: [{
        siteId: User.activeSite(),
        allLinesReceipted: false,
      }],
    };

    if (query && query.length) {
      const querySelector = {
        $or: [
          { identifier: { $regex: utils.escapeRegExp(query), $options: 'i' } },
        ],
      };

      selector.$and.push(querySelector);
    }

    if (clientId) {
      selector.$and.push({ 'client._id': clientId });
    }

    const skip = limit * (page - 1);

    return PurchaseOrders.find(selector, { sort: { receivedDate: -1 }, limit, skip });
  },
};
