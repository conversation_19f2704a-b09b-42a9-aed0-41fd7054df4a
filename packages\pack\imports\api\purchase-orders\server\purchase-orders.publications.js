import { Po } from './publications/po';
import { PosRequiringReceipting } from './publications/pos-requiring-receipting';
import { PurchaseOrders } from '../purchase-orders';
import { ReactiveTable } from 'meteor/fcallem:reactive-table-semantic';
import { Register } from '../../api.helpers/register';

ReactiveTable.publish('purchaseOrders.receiptHistory', function publish() {
  return this.userId ? PurchaseOrders : [];
});

Register
  .publication(PosRequiringReceipting)
  .publication(Po);
