import { Items } from '../../../items/items';
import { Meteor } from 'meteor/meteor';
import { ReceiptTypes } from '../../../items/receipt.types';
import { ReportTypes } from '../../report.types';
import { Reports } from '../../reports';
import SimpleSchema from 'simpl-schema';
import { User } from '../../../api.helpers/user';
import moment from 'moment';

const command = {
  clientId: String,
};

export const ItemsNotPackedLocations = {
  name: 'reports.reportGeneration.itemsNotPackedLocations',

  validate(args) {
    new SimpleSchema(command)
      .validate(args);
  },

  run({ clientId }) {
    const siteIdentifier = User.activeSite();

    // Pull relevant items from DB
    const items = Items.find(
      {
        'client._id': clientId,
        siteId: siteIdentifier,
        receiptType: ReceiptTypes.fullReceipt,
        isPacked: false,
        isDispatched: false,
      },
      {
        fields: {
          isStored: 1,
          offshoreLocation: 1,
          receiptCategory: 1,
        },
      },
    ).fetch();

    // Generate report
    const report = items.reduce(
      (prev, cur) => {
        const currentReport = prev;
        const receiptCategory = cur.receiptCategory;
        const offshoreLocation = cur.offshoreLocation;
        const isStored = cur.isStored;
        const getCountToUpdate = (isStored) => (isStored ? 'storedCount' : 'notStoredCount');

        // Update count for receipt category
        if (!currentReport.receiptCategories[receiptCategory]) {
          currentReport.receiptCategories[receiptCategory] = {
            storedCount: 0,
            notStoredCount: 0,
            offshoreLocations: {

            },
          };
        }
        currentReport
          .receiptCategories[receiptCategory][getCountToUpdate(isStored)] += 1;

        // Update count for offshore location
        if (!currentReport.receiptCategories[receiptCategory].offshoreLocations[offshoreLocation]) {
          currentReport
            .receiptCategories[receiptCategory]
            .offshoreLocations[offshoreLocation] = {
              storedCount: 0,
              notStoredCount: 0,
            };
        }

        currentReport
          .receiptCategories[receiptCategory]
          .offshoreLocations[offshoreLocation][getCountToUpdate(isStored)] += 1;

        return currentReport;
      },
      {
        clientId,
        siteId: siteIdentifier,
        timestamp: moment().utc().toDate(),
        reportType: ReportTypes.UNDELIVERED_ITEMS,
        receiptCategories: {},
      },
    );

    Reports.insert(report);
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
