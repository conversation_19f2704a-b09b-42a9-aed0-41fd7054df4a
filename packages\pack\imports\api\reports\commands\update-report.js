import { Errors } from '../../api.helpers/errors';
import { ItemsNotPackedLocations } from './report-generation.strategies/items-not-packed-locations';
import { Meteor } from 'meteor/meteor';
import { ReportTypes } from '../report.types';
import { ReportsHelpers } from '../reports.helpers';
import SimpleSchema from 'simpl-schema';

const command = {
  reportType: {
    type: String,
    custom: function isValidReportType() {
      if (!ReportsHelpers.isValidReportType(this.value)) {
        return 'invalid_report_type';
      }

      return undefined;
    },
  },
  reportData: {
    type: Object,
    blackbox: true,
  },
};

export const UpdateReport = {
  name: 'reports.updateReport',

  validate(args) {
    new SimpleSchema(command)
      .validate(args);
  },

  run({ reportType, reportData }) {
    switch (reportType) {
      case ReportTypes.UNDELIVERED_ITEMS:
        ItemsNotPackedLocations.call(reportData);
        break;
      default:
        Errors.throw(Errors.types.notFound, `Report Type: ${reportType} not recognised.`);
        break;
    }
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
