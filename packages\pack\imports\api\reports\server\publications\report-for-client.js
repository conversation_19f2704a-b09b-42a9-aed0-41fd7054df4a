import { Errors } from '../../../api.helpers/errors';
import { GetClientFromId } from '../../../company-site-profiles/queries/get-client-from-id';
import { Publications } from '../../../api.publications/publications';
import { Reports } from '../../reports';
import { ReportsHelpers } from '../../reports.helpers';
import SimpleSchema from 'simpl-schema';
import { User } from '../../../api.helpers/user';

const pubQuery = {
  reportType: {
    type: String,
    custom: function isValidReportType() {
      if (!ReportsHelpers.isValidReportType(this.value)) {
        return 'invalid_report_type';
      }

      return undefined;
    },
  },
  clientId: String,
};

export const ReportForClient = {
  name: Publications.reports.reportForClient,

  validate(args) {
    new SimpleSchema(pubQuery)
      .validate(args);
  },

  run({ reportType, clientId }) {
    const siteIdentifier = User.activeSite();
    const client = GetClientFromId.call({ clientId, siteIdentifier });

    if (!client) {
      Errors.throw(Errors.types.notFound, `ClientId: <${clientId}> not recognised for site: ${siteIdentifier}`);
    }

    return Reports.find({
      clientId,
      reportType,
    }, { sort: { timestamp: -1 }, limit: 1 });
  },
};
