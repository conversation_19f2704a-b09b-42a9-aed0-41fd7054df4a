import { Errors } from '../../api.helpers/errors';
import { GetPackingUnitFromIdentifier } from '../../company-site-profiles/queries/get-packing-unit-from-identifier';
import { Meteor } from 'meteor/meteor';
import { Requests } from '../requests';
import SimpleSchema from 'simpl-schema';
import { User } from '../../api.helpers/user';
import { Log } from '../../api.helpers/log';

const command = {
  requestId: String,
  packingUnitType: String,
  packingUnitIdentifier: String,
};

export const AssignPackingUnitToRequest = {
  name: 'requests.assignPackingUnitToRequest',

  validate(args) {
    new SimpleSchema(command)
      .validate(args);
  },

  run({ requestId, packingUnitType, packingUnitIdentifier }) {
    Log.info('Assigning Packing Unit to Request.', { requestId, packingUnitType, packingUnitIdentifier });

    const createCustomPackingUnit = (unitType, identifier) => ({
      _id: new Mongo.ObjectID()._str,
      identifier,
      unitType,
    });

    const mapPackingUnitToRequestPackingUnit = (packingUnit) =>
      Object.assign({}, packingUnit, { items: [], isClosed: false });

    const siteId = User.activeSite();

    let packingUnit = {};

    try {
      packingUnit = GetPackingUnitFromIdentifier.call({ packingUnitIdentifier });
    } catch (err) {
      Log.info('Packing Unit not found in site config - created custom packing unit.', { requestId, packingUnitType, packingUnitIdentifier });
      packingUnit = createCustomPackingUnit(packingUnitType, packingUnitIdentifier);
    }

    const requestPackingUnit = mapPackingUnitToRequestPackingUnit(packingUnit);

    const updated = Requests.update({ _id: requestId, siteId }, {
      $push: {
        packingUnits: requestPackingUnit,
      },
    });

    if (updated === 0) {
      Log.info('Packing Request updated failed', requestPackingUnit);
      Errors.throw(Errors.types.commandFailed, `No Request with Id: ${requestId} was found to update.`);
    } else {
      Log.info('Packing Request updated', requestPackingUnit);
    }

    return requestPackingUnit._id;
  },

  call(args, callback) {
    return Meteor.call(this.name, args, callback);
  },
};
