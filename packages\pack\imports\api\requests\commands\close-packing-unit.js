import { Errors } from '../../api.helpers/errors';
import { Meteor } from 'meteor/meteor';
import { Requests } from '../requests';
import SimpleSchema from 'simpl-schema';
import { User } from '../../api.helpers/user';
import { SetItemsInPackingUnitDispatched } from '../../items/commands/dispatch-items';

const command = {
  requestId: String,
  packingUnitId: String,
};

export const ClosePackingUnit = {
  name: 'requests.closePackingUnit',

  validate(args) {
    new SimpleSchema(command)
      .validate(args);
  },

  run({ requestId, packingUnitId }) {
    const siteId = User.activeSite();

    const selector = {
      _id: requestId,
      'packingUnits._id': packingUnitId,
      siteId,
    };

    const update = {
      $set: {
        'packingUnits.$.isClosed': true,
      },
    };

    const updated = Requests.update(selector, update);

    if (updated === 0) {
      Errors.throw(Errors.types.commandFailed, `Update failed for request: ${requestId}, packingUnit: ${packingUnitId}`);
      return;
    }

    // Set Dispatched
    SetItemsInPackingUnitDispatched.run({ packingUnit: packingUnitId, setDispatched: true });
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
