import { GetClientFromId } from '../../company-site-profiles/queries/get-client-from-id';
import { GetDestinationFromId } from '../../company-site-profiles/queries/get-destination-from-id';
import { Meteor } from 'meteor/meteor';
import { Requests } from '../requests';
import SimpleSchema from 'simpl-schema';
import { User } from '../../api.helpers/user';

const command = {
  clientId: String,
  destinationIds: String, // Comma separated list of destinationVendorIds.
  scheduledDateTime: Date,
  packingRequestRefNo: String,
  transportCompany: {
    type: String,
    optional: true,
  },
  isRepeated: {
    type: Boolean,
    optional: true,
  },
  occurenceInDaysPerWeek: {
    type: SimpleSchema.Integer,
    optional: true,
  },
  repeatUntilDate: {
    type: Date,
    optional: true,
  },
};

export const CreateRequest = {
  name: 'requests.create',

  validate(args) {
    new SimpleSchema(command)
      .validate(args);
  },

  run(
    {
      clientId, destinationIds, scheduledDateTime, packingRequestRefNo, transportCompany,
    },
  ) {
    const siteIdentifier = User.activeSite();

    const client = GetClientFromId.call({ clientId, siteIdentifier });

    const destinationIdsArray = destinationIds.split(',');

    const destinationsArray = [];

    destinationIdsArray.forEach((destinationId) => {
      const destination = GetDestinationFromId.call({ destinationId });
      destinationsArray.push(destination);
    });

    const request = {
      siteId: siteIdentifier,
      transportCompany,
      client,
      identifier: `Pack ${packingRequestRefNo}`,
      packingRequestRefNo,
      scheduledDate: scheduledDateTime,
      destinations: destinationsArray,
      packingUnits: [],
    };

    console.log(`CreatingRequest ${JSON.stringify(request)}`);
    Requests.insert(request);
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
