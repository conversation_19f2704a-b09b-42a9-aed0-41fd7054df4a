import { GetClientFromId } from '../../company-site-profiles/queries/get-client-from-id';
import { GetDestinationFromId } from '../../company-site-profiles/queries/get-destination-from-id';
import { Meteor } from 'meteor/meteor';
import { Requests } from '../requests';
import SimpleSchema from 'simpl-schema';
import { User } from '../../api.helpers/user';

const command = {
  id: String,
};

const canDeleteRequest = (id) => {
  const request = Requests.findOne(id);

  if (request) {
    if (request.packingUnits && request.packingUnits.length > 0) {
      let count = request.packingUnits.reduce((acc, curr) => {
        if (curr.items) {
          return acc + curr.items.length;
        } else {
          return acc;
        }
      }, 0);

      return count === 0;
    } else {
      return true;
    }
  }

  return false;
};

export const DeleteRequest = {
  name: 'requests.delete',

  validate(args) {
    new SimpleSchema(command)
      .validate(args);
  },

  run({ id }) {
    const request = {
      $set: {
        softDeleted: true,
      },
    };

    if (canDeleteRequest(id)) {
      console.log('Deleting Request: id=<' + id + '>');
      Requests.update(id, request);
    } else {
      console.log('Delete Request: Error - Request has packed items. id=<' + id + '>');
    }
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
