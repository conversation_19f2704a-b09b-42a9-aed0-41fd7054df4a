import { Errors } from '../../api.helpers/errors';
import { Meteor } from 'meteor/meteor';
import { Requests } from '../requests';
import SimpleSchema from 'simpl-schema';
import { User } from '../../api.helpers/user';
import { SetItemsInPackingUnitDispatched } from '../../items/commands/dispatch-items';

const command = {
  requestId: String,
  packingUnitId: String,
};

export const OpenPackingUnit = {
  name: 'requests.openPackingUnit',

  validate(args) {
    new SimpleSchema(command)
      .validate(args);
  },

  run({ requestId, packingUnitId }) {
    const siteId = User.activeSite();

    const selector = {
      _id: requestId,
      'packingUnits._id': packingUnitId,
      siteId,
    };

    const update = {
      $set: {
        'packingUnits.$.isClosed': false,
      },
    };

    const updated = Requests.update(selector, update);

    if (updated === 0) {
      Errors.throw(Errors.types.commandFailed, `Update for requestId: ${requestId}, packingUnitId: ${packingUnitId} failed.`);
      return;
    }

    // Set Dispatched
    SetItemsInPackingUnitDispatched.run({ packingUnit: packingUnitId, setDispatched: false });
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
