import { Errors } from '../../api.helpers/errors';
import { Meteor } from 'meteor/meteor';
import { PackItems } from '../../items/commands/pack-items';
import { Requests } from '../requests';
import SimpleSchema from 'simpl-schema';
import { User } from '../../api.helpers/user';

const command = {
  requestId: String,
  packingUnitId: String,
  itemIds: Array,
  'itemIds.$': String,
};

export const PackItemsIntoUnit = {
  name: 'requests.packItemsIntoUnit',

  validate(args) {
    new SimpleSchema(command)
      .validate(args);
  },

  run({ requestId, packingUnitId, itemIds }) {
    const siteId = User.activeSite();

    const selector = {
      _id: requestId,
      siteId,
      'packingUnits._id': packingUnitId,
    };

    const update = {
      $push: {
        'packingUnits.$.items': { $each: itemIds },
      },
    };

    const updated = Requests.update(selector, update);

    if (updated === 0) {
      Errors.throw(Errors.types.notFound, `Request with Id: ${requestId} was not found, item(s) not packed into unit.`);
    }

    // Get PackingUnitIdentifier
    const packingRequest = Requests.findOne(requestId);
    const packingUnit = _.find(packingRequest.packingUnits, (pu) => pu._id === packingUnitId);
    const packingUnitIdentifier = packingUnit.identifier || null;
    PackItems.call({
      itemIds,
      requestId,
      packingUnitId,
      packingUnitIdentifier,
    });
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
