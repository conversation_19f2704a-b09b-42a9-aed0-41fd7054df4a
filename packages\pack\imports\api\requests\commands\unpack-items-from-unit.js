import { Errors } from '../../api.helpers/errors';
import { Meteor } from 'meteor/meteor';
import { Requests } from '../requests';
import SimpleSchema from 'simpl-schema';
import { UnpackItems } from '../../items/commands/unpack-items';
import { User } from '../../api.helpers/user';

const command = {
  requestId: String,
  packingUnitId: String,
  itemIds: Array,
  'itemIds.$': String,
};

export const UnpackItemsFromUnit = {
  name: 'requests.unpackItemsFromUnit',

  validate(args) {
    new SimpleSchema(command)
      .validate(args);
  },

  run({ requestId, packingUnitId, itemIds }) {
    const siteId = User.activeSite();

    const selector = {
      _id: requestId,
      siteId,
      'packingUnits._id': packingUnitId,
    };

    const update = {
      $pull: {
        'packingUnits.$.items': { $in: itemIds },
      },
    };

    const updated = Requests.update(selector, update);

    if (updated === 0) {
      Errors.throw(Errors.types.notFound, 'Unable to unpack items');
    }

    UnpackItems.call({ itemIds, requestId, packingUnitId });
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
