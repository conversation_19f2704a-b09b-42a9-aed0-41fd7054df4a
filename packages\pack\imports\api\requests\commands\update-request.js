import { GetClientFromId } from '../../company-site-profiles/queries/get-client-from-id';
import { GetDestinationFromId } from '../../company-site-profiles/queries/get-destination-from-id';
import { Meteor } from 'meteor/meteor';
import { Requests } from '../requests';
import SimpleSchema from 'simpl-schema';
import { User } from '../../api.helpers/user';

const command = {
  id: String,
  clientId: String,
  destinationIds: String, // Comma separated list of destinationVendorIds.
  scheduledDateTime: Date,
  packingRequestRefNo: String,
  transportCompany: {
    type: String,
    optional: true,
  },
  isRepeated: {
    type: Boolean,
    optional: true,
  },
  occurenceInDaysPerWeek: {
    type: SimpleSchema.Integer,
    optional: true,
  },
  repeatUntilDate: {
    type: Date,
    optional: true,
  },
};

export const UpdateRequest = {
  name: 'requests.update',

  validate(args) {
    new SimpleSchema(command)
      .validate(args);
  },

  run(
    {
      id, clientId, destinationIds, scheduledDateTime, packingRequestRefNo, transportCompany,
    },
  ) {
    const siteIdentifier = User.activeSite();

    const client = GetClientFromId.call({ clientId, siteIdentifier });

    const destinationIdsArray = destinationIds.split(',');

    const destinationsArray = [];

    destinationIdsArray.forEach((destinationId) => {
      const destination = GetDestinationFromId.call({ destinationId });
      destinationsArray.push(destination);
    });

    const request = {
      $set: {
        siteId: siteIdentifier,
        transportCompany,
        client,
        identifier: `Pack ${packingRequestRefNo}`,
        packingRequestRefNo,
        scheduledDate: scheduledDateTime,
        destinations: destinationsArray,
      },
    };

    console.log(`UpdatingRequest ${JSON.stringify(request)}`);
    Requests.update(id, request);
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
