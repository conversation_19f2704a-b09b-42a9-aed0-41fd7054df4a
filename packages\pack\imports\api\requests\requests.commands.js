import { AssignPackingUnitToRequest } from './commands/assign-packing-unit-to-request';
import { ClosePackingUnit } from './commands/close-packing-unit';
import { CreateRequest } from './commands/create-request';
import { UpdateRequest } from './commands/update-request';
import { DeleteRequest } from './commands/delete-request';
import { OpenPackingUnit } from './commands/open-packing-unit';
import { PackItemsIntoUnit } from './commands/pack-items-into-unit';
import { Register } from '../api.helpers/register';
import { UnpackItemsFromUnit } from './commands/unpack-items-from-unit';

Register
  .command(CreateRequest)
  .command(UpdateRequest)
  .command(DeleteRequest)
  .command(AssignPackingUnitToRequest)
  .command(PackItemsIntoUnit)
  .command(UnpackItemsFromUnit)
  .command(ClosePackingUnit)
  .command(OpenPackingUnit);
