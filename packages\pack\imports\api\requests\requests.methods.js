import { Match, check } from 'meteor/check';
import { CompanySiteProfiles } from '../company-site-profiles/company-site-profiles';
import { Meteor } from 'meteor/meteor';
import { Requests } from './requests';
import { Stock } from '../stock/stock';

Meteor.methods({
  'requests.packItem': function packItem(stockItemId, requestId, ccuId) {
    check(stockItemId, String);
    check(requestId, String);
    check(ccuId, String);

    const stockItem = Stock.findOne({ _id: stockItemId });
    const request = Requests.findOne({ _id: requestId });
    const utcTimestamp = moment().utc().toDate();

    stockItem.isPacked = true;
    stockItem.packedAt = utcTimestamp;
    stockItem.packedInRequest = {
      _id: request._id,
      client: request.client,
      destination: request.destination,
      dueForDispatch: request.dueForDispatch,
      createdAt: request.createdAt,
      repeatOfRequestId: request.repeatOfRequestId,
    };

    const ccuToPackItemInto = request.assignedCcus.find((ccu) => ccu._id === ccuId);
    stockItem.packedInCcu = {
      _id: ccuToPackItemInto._id,
      identifier: ccuToPackItemInto.identifier,
    };

    Stock.update(
      { _id: stockItemId },
      stockItem,
      (error) => {
        if (!error) {
          Requests.update(
            { _id: requestId, 'assignedCcus._id': ccuId },
            {
              $push: {
                'assignedCcus.$.contents': stockItem,
              },
            },
          );
        }
      },
    );
  },
  'requests.unpackItem': function unpackItem(stockItemId, requestId, ccuId) {
    check(stockItemId, String);
    check(requestId, String);
    check(ccuId, String);

    const stockItem = Stock.findOne({ _id: stockItemId });
    stockItem.isPacked = false;
    stockItem.packedAt = null;
    stockItem.packedInRequest = null;

    Stock.update(
      { _id: stockItemId },
      stockItem,
      (error) => {
        if (!error) {
          Requests.update(
            { _id: requestId, 'assignedCcus._id': ccuId },
            {
              $pull: {
                'assignedCcus.$.contents': {
                  _id: stockItemId,
                },
              },
            },
          );
        }
      },
    );
  },
  'requests.updateAssignedCcus': function updateAssignedCcus(requestId, ccuIdentifier) {
    check(requestId, String);
    check(ccuIdentifier, String);

    const assignedCcuId = new Meteor.Collection.ObjectID()._str;

    Requests.update(
      { _id: requestId },
      {
        $push:
        {
          assignedCcus: {
            _id: assignedCcuId,
            identifier: ccuIdentifier,
            isClosed: false,
            contents: [],
          },
        },
      },
    );
    return assignedCcuId;
  },
  'requests.closeCcu': function closeCcu(requestId, ccuId) {
    check(requestId, String);
    check(ccuId, String);

    Requests.update(
      { _id: requestId, 'assignedCcus._id': ccuId },
      {
        $set: {
          'assignedCcus.$.isClosed': true,
        },
      },
    );
  },
  'requests.openCcu': function openCcu(requestId, ccuId) {
    check(requestId, String);
    check(ccuId, String);

    Requests.update(
      { _id: requestId, 'assignedCcus._id': ccuId },
      {
        $set: {
          'assignedCcus.$.isClosed': false,
        },
      },
    );
  },
  'requests.exportCcuManifest': function exportCcuManifest(requestId, selectedCcuId) {
    check(requestId, String);
    check(selectedCcuId, String);

    const fields = [
      'Container',
      'Destination',
      'Quantity',
      'Packing Unit',
      'Description',
      'PO No',
      'Vendor',
      'Vendor Delivery No',
    ];
    // Array for formatted json
    let data = [];

    const selectedRequest = Requests.findOne({ _id: requestId });
    const selectedCcu = _.findWhere(selectedRequest.assignedCcus, { _id: selectedCcuId });
    const ccuName = selectedCcu.identifier;
    const destination = selectedRequest.destination.name;
    _.each(selectedCcu.contents, function (c) {
      data.push([
        ccuName,
        destination,
        c.quantity,
        c.packingUnit.name,
        c.description,
        c.poNo,
        c.vendor.name,
        c.vendorDeliveryNo,
      ]);
    });

    return { fields, data };
  },
});
