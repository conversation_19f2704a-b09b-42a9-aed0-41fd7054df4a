import { SiteIdSchema, TimestampsSchema, _idSchema } from '../api.shared-schemas/shared-schemas';
import { ClientSchema } from '../company-site-profiles/configuration/client.schema';
import { DB_DATE_STR_FORMAT } from '../../shared/lib/constants';
import { DestinationSchema } from '../company-site-profiles/configuration/destination.schema';
import { RequestsPackingUnitSchema } from './requests.packing-unit.schema';
import SimpleSchema from 'simpl-schema';
import moment from 'moment';

export const RequestsSchema = new SimpleSchema({
  client: ClientSchema,
  identifier: String,
  repeatOfRequestId: {
    type: String,
    optional: true,
  },
  transportCompany: {
    type: String,
    optional: true,
  },
  packingRequestRefNo: String,
  destinations: Array,
  'destinations.$': DestinationSchema,
  scheduledDate: Date,
  scheduledDateStr: {
    type: String,
    optional: true,
    autoValue: function scheduledDateToStr() {
      const receivedDate = this.field('scheduledDate');

      if (receivedDate.isSet) {
        return moment(receivedDate.value).format(DB_DATE_STR_FORMAT);
      }

      return undefined;
    },
  },
  packingUnits: Array,
  'packingUnits.$': RequestsPackingUnitSchema,
  softDeleted: {
    type: Boolean,
    optional: true,
  },
})
  .extend(_idSchema)
  .extend(SiteIdSchema)
  .extend(TimestampsSchema);
