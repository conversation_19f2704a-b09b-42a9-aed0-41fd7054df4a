import { Publications } from '../../../api.publications/publications';
import { Requests } from '../../requests';
import SimpleSchema from 'simpl-schema';
import { queryBuilder } from '../../shared/active-requests-for-client-builder';

const paginationSchema = new SimpleSchema({
  pagination: {
    type: Object,
    optional: true,
  },
  'pagination.page': {
    type: Number,
    optional: false,
  },
  'pagination.perPage': {
    type: Number,
    optional: false,
  },
  sorting: {
    type: Object,
    optional: true,
    blackbox: true,
  },
});

const pubQuery = {
  clientId: String,
  query: {
    type: String,
    optional: true,
  },
  fromDate: {
    type: Date,
    optional: true,
  },
  toDate: {
    type: Date,
    optional: true,
  },
};

export const ActiveRequestsForClient = {
  name: Publications.requests.activeRequestsForClient,

  validate(args) {
    new SimpleSchema(pubQuery)
      .extend(paginationSchema)
      .validate(args);
  },

  run(args) {
    const limit = (args.pagination) ? args.pagination.perPage : 4;
    const skip = (args.pagination && args.pagination.page > 0)? ((args.pagination.page - 1) * limit) : 0;
    const sort = (args.sorting) ? args.sorting : {};

    const options = {
      limit,
      skip,
      sort,
    };

    const selector = queryBuilder(args);
    return Requests.find(selector, options);
  },
};

export const ActiveRequestsForClientCounts = {
  name: Publications.requests.activeRequestsForClient + '.counts',

  validate(args) {
    new SimpleSchema(pubQuery)
      .validate(args);
  },

  run(args) {
    const selector = queryBuilder(args);
    return Requests.find(selector, {}).count();
  },

  call(args, callback) {
    Meteor.call(this.name, args, callback);
  },
};
