import { Publications } from '../../../api.publications/publications';
import { Requests } from '../../requests';
import SimpleSchema from 'simpl-schema';

const pubQuery = {
  requestId: String,
};

export const Request = {
  name: Publications.requests.request,

  validate(args) {
    new SimpleSchema(pubQuery)
      .validate(args);
  },

  run({ requestId }) {
    return Requests.find({
      _id: requestId,
      $or: [
        { softDeleted: false },
        { softDeleted: { $exists: false } },
      ],
    }, { limit: 1 });
  },
};
