import { Match, check } from 'meteor/check';

import { ActiveRequestsForClient } from './publications/active-requests-for-client';
import { Meteor } from 'meteor/meteor';
import { Register } from '../../api.helpers/register';
import { Request } from './publications/request';
import { Requests } from '../requests';

Register
  .publication(ActiveRequestsForClient)
  .publication(Request);

Meteor.publish('activeRequest', function getActiveRequest(requestId) {
  check(requestId, String);
  if (this.userId) return Requests.find({
    _id: requestId,
    $or: [
      { softDeleted: false },
      { softDeleted: { $exists: false } },
    ],
  });
  return [];
});

Meteor.publish('clientRequests', function getClientRequests(clientId) {
  check(clientId, String);
  if (this.userId) return Requests.find({ 
    'client._id': clientId,
    $or: [
      { softDeleted: false },
      { softDeleted: { $exists: false } },
    ],
  });
  return [];
});
