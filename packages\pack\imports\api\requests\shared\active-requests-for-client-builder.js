import { User } from '../../api.helpers/user';
import { utils } from '../../../shared/utils';

export const queryBuilder = ({
  clientId,
  query,
  fromDate,
  toDate,
}) => {
  const siteId = User.activeSite();

  const selector = {
    siteId,
    'client._id': clientId,
    $or: [
      { softDeleted: false },
      { softDeleted: { $exists: false } },
    ],
  };

  if (query && query.length) {
    const escapedQueryStr = new RegExp(utils.escapeRegExp(query), 'i');
    selector.$or = [
      { packingRequestRefNo: { $regex: escapedQueryStr } },
      { 'destinations.name': { $regex: escapedQueryStr } },
    ];
  }

  if (fromDate || toDate) {
    selector.scheduledDate = { };

    if (fromDate) selector.scheduledDate.$gte = fromDate;
    if (toDate) selector.scheduledDate.$lte = toDate;
  }

  return selector;
};
