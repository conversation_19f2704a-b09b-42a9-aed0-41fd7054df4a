import { Match, check } from 'meteor/check';
import { Meteor } from 'meteor/meteor';
import { ReactiveTable } from 'meteor/fcallem:reactive-table-semantic';
import { Requests } from '../../requests/requests';
import { Stock } from '../stock';
import { _ } from 'meteor/underscore';

// ReactiveTable publisher used by receipt history
ReactiveTable.publish('stock.receiptHistory', function getStockReceiptHistory() {
  if (this.userId) return Stock;
  return [];
});

// Method for escaping the regular expression
function escapeRegExp(query) {
  return query.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&');
}

Meteor.publish('stock.forClientFiltered', function getStockForClient(clientId, query, limit) {
  check(clientId, String);
  check(query, String);
  check(limit, Number);
  if (this.userId) {
  // Selector includes $and to ensure when query filter is used the other options aren't ignored
    const selector = {
      $and: [{
        'client._id': clientId,
        isPacked: false,
      }],
    };

    if (query && query.length > 2) {
      const querySelector = {
        $or: [
          { description: { $regex: escapeRegExp(query), $options: 'i' } },
          { 'vendor.name': { $regex: escapeRegExp(query), $options: 'i' } },
          { poNo: { $regex: escapeRegExp(query), $options: 'i' } },
        ],
      };
      // Push the $or operation onto the selector
      selector.$and.push(querySelector);
    }

    return Stock.find(selector, { sort: { receiptedAt: -1 }, limit });
  }
  return [];
});

Meteor.publish('stock.packedInOrder', function getStockPackedInOrder(clientId, requestId, query, limit) {
  check(clientId, String);
  check(requestId, String);
  check(query, String);
  check(limit, Number);
  if (this.userId) {
    // Selector includes $and to ensure when query filter is used the other options aren't ignored
    const selector = {
      $and: [{
        'client._id': clientId,
        'packedInRequest._id': requestId,
        isPacked: true,
      }],
    };

    if (query && query.length > 2) {
      const querySelector = {
        $or: [
          { description: { $regex: escapeRegExp(query), $options: 'i' } },
          { 'vendor.name': { $regex: escapeRegExp(query), $options: 'i' } },
          { poNo: { $regex: escapeRegExp(query), $options: 'i' } },
        ],
      };
      // Push the $or operation onto the selector
      selector.$and.push(querySelector);
    }
    return Stock.find(selector, { sort: { receiptedAt: -1 }, limit });
  }
  return [];
});
