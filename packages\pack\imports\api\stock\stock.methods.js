import { Match, check } from 'meteor/check';
import { CompanySiteProfiles } from '../company-site-profiles/company-site-profiles';
import { Meteor } from 'meteor/meteor';
import { Requests } from '../requests/requests';
import { Stock } from './stock';

// Method for escaping the regular expression
function escapeRegExp(query) {
  return query.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&');
}

Meteor.methods({
  'stock.add': function add(itemProperties) {
    const siteProfile = CompanySiteProfiles.findOne();
    if (!siteProfile) {
      throw new Meteor.Error(500, 'Error 500: Not found', 'the site does not exist');
    }

    const packingUnit = siteProfile
      .configuration
      .packingUnits
      .find((packingUnit) => packingUnit._id === itemProperties.packingUnitId);

    let contentUnit;
    if (itemProperties.contentUnitId) {
      contentUnit = siteProfile
        .configuration
        .packingUnits
        .find((packingUnit) => packingUnit._id === itemProperties.contentUnitId);
    }

    let vendor;
    if (itemProperties.vendorId && !itemProperties.isBackload) {
      vendor = siteProfile
        .configuration
        .vendors
        .find((vendor) => vendor._id === itemProperties.vendorId);
    } else {
      vendor = {
        _id: -1,
        name: 'Backload',
      };
    }

    const client = siteProfile
      .configuration
      .clients
      .find((client) => client._id === itemProperties.clientId);

    const stockItem = {
      client,
      vendor,
      vendorDeliveryNo: itemProperties.vendorDeliveryNo,
      isBackload: itemProperties.isBackload,
      poNo: itemProperties.poNo,
      quantity: itemProperties.quantity,
      packingUnit,
      contentUnit,
      contentQuantity: itemProperties.contentQuantity,
      description: itemProperties.description,
      isPacked: false,
      receiptedAt: moment().utc().toDate(),
      receiptNo: client.receiptNoSequence,
    };

    // TODO: This way isn't really safe as there could be issues due to no transactions.
    // Using find and modify is likely the correct way so increment and update is atomic.
    // Fix when required.
    client.receiptNoSequence++;
    CompanySiteProfiles.update({ _id: siteProfile._id }, siteProfile);
    Stock.insert(stockItem);
  },
  'stock.itemFilter': function itemFilter(searchText) {
    if (_.isEmpty(searchText)) {
      return Stock.find().fetch();
    }
    const result = Stock.find({
      description: { $regex: escapeRegExp(searchText) },
    });
    return result.fetch();
  },
  'stock.setItemAsPacked': function setItemAsPacked(itemId, ccu, requestId) {
    check(itemId, String);
    check(ccu, String);
    const utcTimestamp = moment().utc.toDate();
    Stock.update(
      { _id: itemId },
      {
        $set: {
          isPacked: true,
          packedAt: utcTimestamp,
          packedAtRequestId: requestId,
        },
      },
    );
  },
  'stock.storeItem': function storeItem(itemId, storageLocationId) {
    const siteProfile = CompanySiteProfiles.findOne();
    if (!siteProfile) {
      throw new Meteor.Error(500, 'Error 500: Not found', 'the site does not exist');
    }

    const storageLocation = siteProfile
      .configuration
      .storageLocations
      .find((storageLocation) => storageLocation._id === storageLocationId);

    storageLocation.contentCount++;

    CompanySiteProfiles.update({ _id: siteProfile._id }, siteProfile);

    const utcTimestamp = moment().utc().toDate();
    Stock.update(
      { _id: itemId },
      {
        $set: {
          isStored: true,
          storedAt: utcTimestamp,
          storedIn: storageLocation,
        },
      },
    );
  },
  'stock.removeItemFromStore': function removeItemFromStore(itemId, storageLocationId) {
    const siteProfile = CompanySiteProfiles.findOne();
    if (!siteProfile) {
      throw new Meteor.Error(500, 'Error 500: Not found', 'the site does not exist');
    }

    const storageLocation = siteProfile
      .configuration
      .storageLocations
      .find((storageLocation) => storageLocation._id === storageLocationId);

    storageLocation.contentCount--;

    CompanySiteProfiles.update({ _id: siteProfile._id }, siteProfile);

    Stock.update(
      { _id: itemId },
      {
        $set: {
          isStored: false,
          storedAt: null,
          storedIn: null,
        },
      },
    );
  },
});
