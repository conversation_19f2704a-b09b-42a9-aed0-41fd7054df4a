import { Match, check } from 'meteor/check';

import { Items } from '../../items/items';
import { Meteor } from 'meteor/meteor';
import { VehicleRuns } from '../../vehicle-runs/vehicle-runs';
import { moment } from 'meteor/momentjs:moment';
import { utils } from '../../../shared/utils';

// TODO: Need to change this find to also include the user's associated siteId
Meteor.publish('activeVehicleRun', function getActiveVehicleRun(vehicleRunId) {
  check(vehicleRunId, String);
  if (this.userId) return VehicleRuns.find({ _id: vehicleRunId });
  // If user is not logged in return empty set
  return [];
});

// TODO: Need to change this find to also include the user's associated siteId
Meteor.publishComposite('activeVehicleRunAndItems', function getActiveVehicleRunAndItems(vehicleRunId) {
  if (this.userId) {
    return {
      find: function getActiveVehicleRun() {
        return VehicleRuns.find({ _id: vehicleRunId });
      },
      children: [
        {
          find: function getItemsLoadedWithVehicleRun(vehicleRun) {
            return Items.find({ _id: { $in: vehicleRun.items } });
          },
        },
      ],
    };
  }
  return [];
});

const vehicleRunTimePeriods = {
  ALL: 'ALL',
  LAST_THREE_DAYS: 'LAST_THREE_DAYS',
  NEXT_SEVEN_DAYS: 'NEXT_SEVEN_DAYS',
  THIS_MONTH: 'THIS_MONTH',
};

// TODO: This should limit the publication to the sites associated to the user
// but for now hard code to client
Meteor.publish('siteVehicleRuns', function getSiteVehicleRuns(timePeriod, refNo) {
  if (this.userId) {
    const endOfToday = moment().utc().endOf('day').toDate();
    const threeDaysAgo = moment(endOfToday).subtract(3, 'd').toDate();
    const sevenDaysFromNow = moment(endOfToday).add(7, 'd').toDate();
    const endOfMonth = moment(endOfToday).add(1, 'M').toDate();
    const query = { siteId: 'peterson-lillyhall' };

    switch (timePeriod) {
      case vehicleRunTimePeriods.ALL: break;
      case vehicleRunTimePeriods.LAST_THREE_DAYS:
        query.scheduledDateTime = { $gte: threeDaysAgo, $lte: endOfToday };
        break;
      case vehicleRunTimePeriods.NEXT_SEVEN_DAYS:
        query.scheduledDateTime = { $gte: threeDaysAgo, $lte: sevenDaysFromNow };
        break;
      case vehicleRunTimePeriods.THIS_MONTH:
        query.scheduledDateTime = { $gte: threeDaysAgo, $lte: endOfMonth };
        break;
      // no default
    }
    if (refNo) {
      query.manifestRef = { $regex: utils.escapeRegExp(refNo), $options: 'i' };
    }

    return VehicleRuns.find(query);
  }
  // If user is not logged in return empty set
  return [];
});
