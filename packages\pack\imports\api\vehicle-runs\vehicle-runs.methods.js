import { Match, check } from 'meteor/check';
import { CompanySiteProfiles } from '../company-site-profiles/company-site-profiles';
import { EventFactory } from '../../shared/event-factory';
import { Items } from '../items/items';
import { Meteor } from 'meteor/meteor';
import { VehicleRuns } from '../vehicle-runs/vehicle-runs';
import { _ } from 'meteor/underscore';
import { moment } from 'meteor/momentjs:moment';
import { utils } from '../../shared/utils';

function checkAndGetUser() {
  const user = Meteor.user();
  if (!user) {
    throw new Meteor.Error('not-authorized');
  }
  return user;
}

const generateAndUpdateRunIdentifiers = (scheduledDateTime) => {
  const scheduledDate = moment(scheduledDateTime).format('LL');
  const vehicleRunsOnSameDay = VehicleRuns.find(
    { scheduledDate },
    { sort: { scheduledDateTime: 1 } }).fetch();

  if (vehicleRunsOnSameDay.length > 0) {
    return vehicleRunsOnSameDay.length + 1;
  }
  return 1;
};

const addDaysIgnoringWeekends = (date) => {
  date.add(1, 'days');
  while (date.isoWeekday() === 6 || date.isoWeekday() === 7) {
    date.add(1, 'days');
  }
};

const addOccurencesInDaysPerWeek = (date, daysPerWeek) => {
  switch (daysPerWeek) {
    case 1:
      date.add(7, 'days');
      break;
    case 5:
      addDaysIgnoringWeekends(date);
      break;
    case 7:
      date.add(1, 'days');
      break;
    // no default
  }
};

Meteor.methods({
  'vehicleRuns.create': function create(vehicleRunProperties) {
    check(vehicleRunProperties, {
      destinationIds: [String],
      clientId: String,
      scheduledDateTime: Date,
      isRepeated: Boolean,
      // Ignore linting errors on Match in object this is a meteor package
      occurenceInDaysPerWeek: Match.Maybe(Number),
      repeatUntil: Match.OneOf(Date, String),
    });

    const padToTwo = (no) => (no <= 99 ? ('0' + no).slice(-2) : no);

    const createManifestRef = (date, runNo) => {
      const dateObj = moment(date);
      const runNoStr = utils.isNumeric(runNo) ? padToTwo(runNo) : runNo.slice(1);
      return `VM${dateObj.format('YY')}/${dateObj.format('MM/DD')}-${runNoStr}`;
    };

    const user = checkAndGetUser();

    // TODO: Update this to select companySiteProfile based on the user's assigned siteid
    const siteProfile = CompanySiteProfiles.findOne({ identifier: 'peterson-lillyhall' });

    if (!siteProfile) {
      throw new Meteor.Error(500, 'Error 500: Not found', 'the site does not exist');
    }

    const destinations = _.filter(siteProfile.configuration.destinationOptions,
      (destination) => _.contains(vehicleRunProperties.destinationIds, destination._id));

    const client = siteProfile
      .configuration
      .clients
      .find((client) => client._id === vehicleRunProperties.clientId);

    const scheduledDateTime = moment(vehicleRunProperties.scheduledDateTime);

    const runIdentifier = generateAndUpdateRunIdentifiers(scheduledDateTime);
    const vehicleRunToCreate = {
      runIdentifierStr: runIdentifier,
      repeatOfVehicleRunId: null,
      vehicle: {
        vehicleRegistration: null,
        isClosed: false,
      },
      siteId: siteProfile.identifier,
      clientName: client.name,
      destinations,
      items: [],
      scheduledDateTime: scheduledDateTime.toDate(),
      scheduledDate: scheduledDateTime.format('LL'),
      createdAt: moment().utc().toDate(),
      createdBy: user.username,
      updatedAt: null,
      updatedBy: null,
      isDeleted: false,
      manifestRef: createManifestRef(scheduledDateTime, runIdentifier),
    };

    VehicleRuns.insert(vehicleRunToCreate, (error, createdVehicleRunId) => {
      if (!error) {
        if (vehicleRunProperties.isRepeated) {
          const daysBetweenOccurences = vehicleRunProperties.occurenceInDaysPerWeek;
          const repeatUntil = vehicleRunProperties.repeatUntil;
          addOccurencesInDaysPerWeek(scheduledDateTime, daysBetweenOccurences);
          while (!scheduledDateTime.isAfter(repeatUntil)) {
            vehicleRunToCreate.runIdentifier = generateAndUpdateRunIdentifiers(scheduledDateTime);
            vehicleRunToCreate.scheduledDateTime = scheduledDateTime.toDate();
            vehicleRunToCreate.scheduledDate = scheduledDateTime.format('LL');
            vehicleRunToCreate.repeatOfVehicleRunId = createdVehicleRunId;
            VehicleRuns.insert(vehicleRunToCreate);
            addOccurencesInDaysPerWeek(scheduledDateTime, daysBetweenOccurences);
          }
        }
      }
    });
  },
  'vehicleRuns.assignVehicle': function assignVehicle(vehicleRunId, vehicleReg) {
    check(vehicleRunId, String);
    check(vehicleReg, String);
    VehicleRuns.update(
      { _id: vehicleRunId },
      { $set: { 'vehicle.vehicleRegistration': vehicleReg } },
    );
  },
  'vehicleRuns.openVehicle': function openVehicle(vehicleRunId) {
    check(vehicleRunId, String);
    VehicleRuns.update({ _id: vehicleRunId }, { $set: { 'vehicle.isClosed': false } });
  },
  'vehicleRuns.closeVehicle': function closeVehicle(vehicleRunId) {
    check(vehicleRunId, String);

    const user = checkAndGetUser();

    VehicleRuns.update({ _id: vehicleRunId }, { $set: { 'vehicle.isClosed': true } });
    const vehicleRun = VehicleRuns.findOne({ _id: vehicleRunId });
    // Update item documents with vehicle/vehicle run association and record time
    Items.update(
      { _id: { $in: vehicleRun.items } },
      {
        $set:
        {
          vehicleRun: vehicleRun.runIdentifierStr,
          vehicle: vehicleRun.vehicle.vehicleRegistration,
          isReceivedAtContractorShelf: false,
          isReceivedAtClientStores: false, // NOTE: Assume if the item is loaded in vehicle then it is not in the contractor shelf or sl stores
        },
      },
      {
        multi: true,
      },
    );
    // Update partial delivery items
    Items.update(
      {
        _id: { $in: vehicleRun.items },
        quantityDelivered: { $exists: true },
      },
      { $set: { partialQtyInNewVehicleRunId: vehicleRunId } },
      { multi: true },
    );
    // Record loading event for items
    EventFactory.createGroupItemEvents(vehicleRun.items,
      EventFactory.ItemEvents.LOADED_ON_VEHICLE,
      moment().utc().toDate(),
      user.username,
      vehicleRun._id,
    );
  },
  'vehicleRuns.packItem': function packItem(vehicleRunId, itemId) {
    check(vehicleRunId, String);
    check(itemId, String);
    VehicleRuns.update(
      { _id: vehicleRunId },
      { $push: { items: itemId } },
    );
  },
  'vehicleRuns.unpackItem': function unpackItem(vehicleRunId, itemId) {
    check(vehicleRunId, String);
    check(itemId, String);

    const user = checkAndGetUser();

    // Remove item from vehicle run
    VehicleRuns.update(
      { _id: vehicleRunId },
      { $pull: { items: itemId } },
    );

    // Check whether the item has been removed after vehicle was closed
    const item = Items.findOne({ _id: itemId });
    if (item.vehicleRun && item.vehicle) {
      Items.update({ _id: itemId }, { $set: { vehicleRun: null, vehicle: null } });
      EventFactory.createSingularItemEvent(
        itemId,
        EventFactory.ItemEvents.UNLOADED_FROM_VEHICLE,
        moment.utc().toDate(),
        user.username,
        vehicleRunId,
      );
    }
  },
});
