import { Match, check } from 'meteor/check';
import { Meteor } from 'meteor/meteor';
import { Vehicles } from '../vehicles';

// TODO: This publication should return site vehicles corresponding to the site
// the user is assigned to. For now it is hard coded.
Meteor.publish('siteVehicles', function getSiteVehicles() {
  if (this.userId) return Vehicles.find({ siteId: 'peterson-lillyhall' });
  // If user is not logged in return empty set
  return [];
});
