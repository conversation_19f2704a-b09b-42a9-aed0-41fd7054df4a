import { Meteor } from 'meteor/meteor';

let inDevMode = true;

if (Meteor.settings
  && Meteor.settings.public
  && Meteor.settings.public.env
  && Meteor.settings.public.env !== 'dev') {
  inDevMode = false;
}

let inProductionMode = false;
if ((process.env.NODE_ENV && process.env.NODE_ENV === 'production') ||
    (Meteor.settings
     && Meteor.settings.public
     && Meteor.settings.public.env
     && Meteor.settings.public.env === 'production')) {
  inProductionMode = true;
}

export const App = {
  inDevMode,
  inProductionMode,
};
