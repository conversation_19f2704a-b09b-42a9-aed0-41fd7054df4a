import { moment } from 'meteor/momentjs:moment';
import { _ } from 'meteor/underscore';
import { ReceiptTypes } from '../api/items/receipt.types';

const materialsFields = {
  receiptNo: 'Receipt No.',
  quantity: 'Qty',
  packageType: 'Package Type',
  weightKg: 'Wt(Kg)',
  description: 'Description',
  unNo: 'UN No',
  imoHazardClass: 'Class',
  imoSubClass: 'Subclass',
  ccu: 'CCU',
  receiptType: 'Receipt Stage',
  receivedDate: 'CCU Received Date',
  materialReceiptDateTime: 'Material Receipt',
  offshoreClient: 'Operator',
  offshoreLocation: 'Offshore Location',
  isWaste: 'IsWaste',
  wasteDescription: 'Waste Description',
  marinePollutant: 'Marine Pollutant',
  createdBy: 'Receipted By',
  isStored: 'Item Stored',
  location: 'Stored Location',
  isPacked: 'Item Packed',
  materialManifestNo: 'Manifest No.',
  packingUnitIdentifier: 'Packed Into', // Don't output Guid, output packing unit name (e.g., CCU, reg plate etc).
  isDispatched: 'Item Dispatched',
};

const cargoFields = {
  identifier: 'Ccu',
  offshoreClient: 'Operator',
  offshoreLocation: 'Offshore Location',
  receivedDate: 'Received Date',
  description: 'Description',
  receiptNo: 'Base Receipt No.',
  noOfLines: 'No. Lines Receipted',
};

const mapItemObject = (item, wantedKeys, customMap) => {
  const itemMap = customMap || itemMapping;

  const filteredItemMap = wantedKeys ? _.pick(itemMap, wantedKeys) : itemMap;

  const fieldsInverse = _.invert(filteredItemMap);

  return _.mapObject(fieldsInverse, (val, key) => {
    const displayDateFormat = 'DD MMM YYYY';
    const displayDateTimeFormat = 'DD MMM YYYY HH:mm';

    let value = item[val];
    // Is this a date?
    if (value && typeof (value.getMonth) === 'function') {
      value = moment(value).format(displayDateTimeFormat);
    }

    // Is it a date string with IsoFormat. - note 'true' forces exact string format match.
    const isoDateStr = 'YYYY-MM-DD';
    if (value && (value.length === 10) && moment(value, isoDateStr, true).isValid()) {
      value = moment(value, isoDateStr).format(displayDateFormat);
    }

    // Is it a bool?
    // Replace any 'false' or 'true' with 'Yes' or 'No'
    if (typeof (value) === 'boolean') {
      if (value === false && value !== null) value = 'No';
      if (value === true && value !== null) value = 'Yes';
    }

    // Show receipt stage as something human readable.
    if (key === 'Receipt Stage') {
      if (value === ReceiptTypes.chemReceipt) value = 'Receipted';
      if (value === ReceiptTypes.chemPreReceipt) value = 'Not Removed';
    }

    // Is it a material description or Waste description field - if so unescape any html chars.
    if (key === 'Description' || key === 'Waste Description') {
      value = _.unescape(value)
        .replace(/&GT;/ig, '>')
        .replace(/&LT;/ig, '<')
        .replace(/&gt/ig, '>')
        .replace(/&lt/ig, '<');
    }

    return value;
  });
};

export const dtoMapper = {
  // Properties
  materialsFields,
  cargoFields,

  // Functions
  mapItemObject,
};
