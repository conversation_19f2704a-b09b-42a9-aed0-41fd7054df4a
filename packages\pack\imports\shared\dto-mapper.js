import { CompanySiteProfiles } from '../api/company-site-profiles/company-site-profiles';
import { moment } from 'meteor/momentjs:moment';

/**
 * @description maps table fields to human readable forms.
 */
const itemMapping = {
  itemRef: 'Peterson Ref',
  receivedDate: 'Date',
  destCategory: 'SL RECEIPT/Contractor Goods',
  destSublocation: 'Sublocation',
  quantity: 'Quantity.',
  weightKg: 'Weight (kg)',
  packageType: 'Package (Crate, Parcel, Pallet)',
  description: 'Description',
  deliveryCompany: 'Delivery Company',
  vendorRef: 'Vendor Ref (PO or DN)',
  receivedAtClientStoresDateStr: 'Received by SL Date',
  palletNumber: 'Pallet Number',
  contractorShelfDateStr: 'Contractor Shelf Date',
  deliveredDate: 'Actual Delivery Date',
  vehicle: 'Vehicle',
  vehicleRun: 'Run Number',
  isItemDamaged: 'Item Damaged',
  isDeliveryReturned: 'Item Returned',
  isDeliveredToDestination: 'Delivered to Destination',
  comments: 'Comments',
};

const getDestinationForItem = (item) => {
  const siteProfile = CompanySiteProfiles.findOne();

  // Get destination string to use on label from site config and item destCategory.
  const siteDestinations = siteProfile.configuration.destinationOptions;

  if (siteProfile && siteDestinations && item.destCategory) {
    const itemDeliveryDestination =
      siteDestinations.find((x) => x.name === item.destCategory).deliveryDestination;
    return itemDeliveryDestination;
  }

  return item.destCategory;
};

const mapItemObject = (item, wantedKeys, customMap, addDestination) => {
  // Item needs a full destination string applied.
  if (addDestination) {
    item.destination = getDestinationForItem(item);
  }

  const itemMap = customMap || itemMapping;

  const filteredItemMap = wantedKeys ? _.pick(itemMap, wantedKeys) : itemMap;

  const fieldsInverse = _.invert(filteredItemMap);

  return _.mapObject(fieldsInverse, (val, key) => {
    const displayDateFormat = 'DD MMM YYYY';
    let value = item[val];
    // Is this a date?
    if (value && typeof (value.getMonth) === 'function') {
      value = moment(value).format(displayDateFormat);
    }

    // Is it a date string with IsoFormat. - note 'true' forces exact string format match.
    const isoDateStr = 'YYYY-MM-DD';
    if (value && (value.length === 10) && moment(value, isoDateStr, true).isValid()) {
      value = moment(value, isoDateStr).format(displayDateFormat);
    }

    // Is it a bool?
    // Replace any 'false' or 'true' with 'Yes' or 'No'
    if (typeof (value) === 'boolean') {
      if (value === false && value !== null) value = 'No';
      if (value === true && value !== null) value = 'Yes';
    }
    return value;
  });
};

export const dtoMapper = {
  // Properties
  itemMapping,

  // Functions
  mapItemObject,
  getDestinationForItem,
};
