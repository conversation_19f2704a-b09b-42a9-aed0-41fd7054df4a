/* eslint-disable max-classes-per-file */
import { AbdnItems } from '../api/abdn-items/abdn-items';
import SimpleSchema from 'simpl-schema';
import { <PERSON> as log } from 'meteor/wylio:winston-papertrail';
import { moment } from 'meteor/momentjs:moment';

const ItemEvents = {
  RECEIVED: 'RECEIVED', // Item has been received and registered on the system.
  DELIVERED: 'DELIVERED', // Item has been delivered.
  PARTIAL_DELIVERY: 'PARTIAL_DELIVERY', // Subset of full quantity delivered.
  RETURNED: 'RETURNED',
  RECEIVED_BY_CLIENT_STORES: 'RECEIVED_BY_CLIENT_STORES', // Passed to SL Stores (not yet delivered to destn).
  LOCATION: 'LOCATION', // Item stored at a specified location (e.g. contractor shelves).
  REMOVED_FROM_LOCATION: 'REMOVED_FROM_LOCATION',
  STORE_RESET: 'STORE_RESET', // Item removed from stores during nightly reset
  SECURITY_SCREENED: 'SECURITY_SCREENED', // Item X-rayed/hand searched (successfully)
  LOADED_ON_VEHICLE: 'LOADED_ON_VEHICLE', // Item placed on vehicle for delivery
  UNLOADED_FROM_VEHICLE: 'UNLOADED_FROM_VEHICLE', // Item removed from vehicle after being loaded for delivery
  EDITED: 'EDITED', // Item record was edited.
};

const AltensItemEvents = {
  RECEIVED: 'RECEIVED',
  STORED: 'STORED',
  PACKED: 'PACKED',
  DISPATCHED: 'DISPATCHED',
  DELETED: 'DELETED',
  EDITED: 'EDITED', // Item record was edited.
};

class Event {
  constructor(evType, evDateTime, evCreatedBy, evCreatedAt) {
    this.eventType = evType; // Specific type of event.
    this.eventDateTime = evDateTime; // Datetime the event occurred (could be in the past).
    this.createdBy = evCreatedBy; // Who created the event.
    this.createdAt = evCreatedAt; // Datetime the event object was created.
  }
}

// Optional Event Data
// eventData = {
//   vehicleRunId:
//   location:
//   quantity:
//   edits: // array of change objects.
// }

class ItemEvent extends Event {
  constructor(evType, evDateTime, createdBy, createdAt, eventData = null) {
    super(evType, evDateTime, createdBy, createdAt);
    this.isDeleted = false;
    if (eventData) {
      if (eventData.requestId) this.requestId = eventData.requestId;
      if (eventData.packingUnitId) this.packingUnitId = eventData.packingUnitId;
      if (eventData.location) this.location = eventData.location;
      if (eventData.subLocation) this.subLocation = eventData.subLocation;
      if (eventData.quantity) this.quantity = eventData.quantity;
      if (eventData.edits) this.edits = eventData.edits;
    }
  }
}

class EventCreator {
  constructor(evType, evDateTime, createdBy) {
    this.evType = evType;
    this.createdBy = createdBy;
    this.evDateTime = evDateTime;
  }

  createEvent() {
    const createdAt = moment().utc().toDate();
    return new Event(
      this.evType,
      this.evDateTime,
      this.createdBy,
      createdAt,
    );
  }

  generateEvent() {
    return this.createEvent();
  }
}

class ItemEventCreator extends EventCreator {
  constructor(evType, evDateTime, createdBy, eventData = null) {
    super(evType, evDateTime, createdBy);
    this.eventData = eventData;
  }

  createEvent() {
    const createdAt = moment().utc().toDate();

    const newEvent = new ItemEvent(
      this.evType,
      this.evDateTime,
      this.createdBy,
      createdAt,
      this.eventData,
    );

    return newEvent;
  }
}

const createItemEvent = (evType, evDateTime, createdBy, eventData = null) => {
  const itemEventCreator = new ItemEventCreator(
    evType,
    evDateTime,
    createdBy,
    eventData,
  );
  // TODO: When aldeed:simple-schema V2 is stable, update
  // and remove assignment here, simple-schema can't validate classes
  // in V1.

  const genEvent = itemEventCreator.generateEvent();
  const returnEventObj = Object.assign({}, genEvent);
  return returnEventObj;
};

const createSingularItemEventWithData =
  (itemId, evType, evDateTime, createdBy, eventData = null) => {
    const event = createItemEvent(evType, evDateTime, createdBy, eventData);
    log.info(`Created Event for itemId <${itemId}>: <${JSON.stringify(event, null, 2)}> .`);
    return event;
  };

// Deprecated - use ...WithData() above.
const createSingularItemEvent =
  (itemId, evType, evDateTime, createdBy, vehicleRunId = null, location = null, quantity = null) => {
    const eventData = {
      vehicleRunId,
      location,
      quantity,
    };
    createSingularItemEventWithData(itemId, evType, evDateTime, createdBy, eventData);
  };

const createGroupItemEvents =
  (itemIds, evType, evDateTime, createdBy, vehicleRunId = null, location = null) => {
    const eventData = {
      vehicleRunId,
      location,
    };
    _.each(
      itemIds,
      (itemId) => createSingularItemEventWithData(
        itemId,
        evType,
        evDateTime,
        createdBy,
        eventData,
      ),
    );
  };

/*  EVENT FACTORY USED IN SDC
    REMOVE THIS WHEN SDC FUNCTIONALITY NOT NEEDED!
*/
export const EventFactory = {
  ItemEvents,
  createItemEvent,
  createSingularItemEvent,
  createSingularItemEventWithData,
  createGroupItemEvents,
};

/* ALTENS EVENT SCHEMA & FACTORY */
export const AltensEventFactory = {
  AltensItemEvents,
  createItemEvent,
  createSingularItemEventWithData,
  createGroupItemEvents,
};

export const ItemEventSchema = new SimpleSchema({
  eventType: {
    type: String,
    label: 'Event Type',
  },
  eventDateTime: {
    type: Date,
    label: 'Event Datetime',
  },
  createdBy: {
    type: String,
    label: 'Created By',
  },
  createdAt: {
    type: Date,
    label: 'Created At',
  },
  requestId: {
    type: String,
    label: 'Request Id',
    optional: true,
  },
  packingUnitId: {
    type: String,
    label: 'Packing Unit Id',
    optional: true,
  },
  location: {
    type: String,
    label: 'Location',
    optional: true,
  },
  subLocation: {
    type: String,
    label: 'Sub Location',
    optional: true,
  },
  quantity: {
    type: String,
    label: 'Quantity',
    optional: true,
  },
  isDeleted: {
    type: Boolean,
    label: 'IsDeleted',
    optional: true,
  },
  edits: {
    type: Array,
    label: 'Edits',
    optional: true,
  },
  'edits.$': {
    type: Object,
  },
  'edits.$.key': {
    type: String,
  },
  'edits.$.from': {
    type: String,
    optional: true,
  },
  'edits.$.to': {
    type: String,
    optional: true,
  },
});
