import { IncrementItemRefCounter } from
  '../api/company-site-profiles/commands/increment-item-ref-counter';
import moment from 'moment';

// Receipt Number Calculator
// Use to get current/new receipt number, and increment site counter.

const zeroPad = (n, width, z = '0') => {
  const nstr = `${n}`;
  return n.length >= width ? n : new Array(width - nstr.length + 1).join(z) + n;
};

const currentAutomaticReceiptNo = (receiptSeqNo, receiptNoFormat) => {
  const now = moment();
  const receiptSequence = receiptNoFormat
    .replace('DD', now.format('DD'))
    .replace('MM', now.format('MM'))
    .replace('YY', now.format('YY'));
  // Default to 2 characters for seq number but allow it to increase if required
  const seqLength = receiptSeqNo > 99
    ? receiptSeqNo.toString().length
    : 2;
  const paddedReceiptNo = zeroPad(receiptSeqNo, seqLength);
  return receiptSequence + paddedReceiptNo;
};

const incrementItemRefCounter = (siteIdentifier) =>
  IncrementItemRefCounter.call({ siteIdentifier });

function getReceiptNoAndIncrement(siteProfileConfiguration, siteIdentifier) {
  const config = siteProfileConfiguration;

  const receiptNo = currentAutomaticReceiptNo(
    config.receiptNoSequence,
    config.receiptNoFormatStr,
  );

  incrementItemRefCounter(siteIdentifier);
  return receiptNo;
}

export const ReceiptNumberCalculator = {
  getReceiptNoAndIncrement,
};
