// Shared Utilities.

/**
 * @description Generic timing function that works serverside
 * @example
 * const start = clock()
 * // block you wish to time //
 * const end = clock(start);
 * // log the time in ms
 * log.info(`took {time}ms`);
 * @param {any} start - the time you wish to compare to.
 * @returns time in ms
 */
function clock(start) {
  if (!start) return process.hrtime();
  const end = process.hrtime(start);
  return Math.round((end[0] * 1000) + (end[1] / 1000000));
}

function isNumeric(n) {
  return !isNaN(parseFloat(n)) && isFinite(n);
}

// Method for escaping the regular expression.
function escapeRegExp(query) {
  return query.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&');
}

// Use "dot.notation" strings as key for finding nested property
// values. See lodash _.get for similar.
const getFromDotNotation = (obj, key, hasMode = false) => {
  var keyExists = true;
  var running = obj;
  const parts = (key !== null) ? key.split('.') : [];

  parts.forEach((keyPart) => {
    if (keyExists === false) {
      return;
    }

    if (running && keyPart in running) {
      running = running[keyPart];
    } else {
      keyExists = false;
    }
  });

  if (hasMode === true) {
    return keyExists;
  }

  if (keyExists === false) {
    return undefined;
  } else {
    return running;
  }
};

// Use "dot.notation" strings as key for checking if a nested
// prperty exists in an object. See lodash _.has for  similar.
const hasFromDotNotation = (obj, key) => {
  return getFromDotNotation(obj, key, true);
};

export const utils = {
  clock, isNumeric, escapeRegExp, getFromDotNotation, hasFromDotNotation,
};
