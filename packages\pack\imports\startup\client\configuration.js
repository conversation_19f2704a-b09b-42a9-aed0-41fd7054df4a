import { Meteor } from 'meteor/meteor';
import { Session } from 'meteor/session';

Meteor.startup(function () {
  // Note: Changed from moment.locale to moment.updateLocale to remove console deprecate warning.
  moment.updateLocale('en', {
    relativeTime: {
      future: 'in %s',
      past: '%s ago',
      s: 'seconds',
      m: '1 minute',
      mm: '%d min',
      h: '1 hour',
      hh: '%d hours',
      d: '1 day',
      dd: '%d days',
      M: '1 month',
      MM: '%d months',
      y: '1 year',
      yy: '%d years',
    },
  });
});
