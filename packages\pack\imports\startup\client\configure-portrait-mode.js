// Get portrait mode for templating purposes
// NOTE: This is over-complicated, turns out there is a portrait class
// applied to the html element that could be sampled instead. Will change
// in later update.
export const portraitMode = new ReactiveVar(null);
var supportsOrientationChange = 'onorientationchange' in window,
    orientationEvent = supportsOrientationChange ? 'orientationchange' : 'resize';

window.addEventListener(orientationEvent, () => {
  Meteor.setTimeout(() => {
    portraitMode.set(window.matchMedia('(orientation: portrait)').matches);
  }, 300);
}, false);

Template.registerHelper('portraitMode', () => {
  if (portraitMode.get() === null) {
    portraitMode.set(window.matchMedia('(orientation: portrait)').matches);
  }

  return portraitMode.get();
});
