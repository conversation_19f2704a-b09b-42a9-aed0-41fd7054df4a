/* Standard Layouts */
import '../../ui/views/layout/layout';
/* Operations */
import '../../ui/views/operations/clients/clients';
import '../../ui/views/operations/receipt/receipt';
import '../../ui/views/operations/store/chem-store';
import '../../ui/views/operations/requests/requests-overview';
import '../../ui/views/operations/packlist/chem-packlist';
import '../../ui/views/operations/requests/create-request/add-request-items';
/* Picklists */
import '../../ui/views/operations/picklist/add-picklist-item';
import '../../ui/views/operations/picklist/add-ccu';
import '../../ui/views/operations/picklist/picklist';
/* Packlists */
import '../../ui/views/operations/packlist/vehicle-options/assign-vehicle';
import '../../ui/views/operations/packlist/vehicle-loading-list';
// Editing Items
import '../../ui/views/operations/item-edit/material-item-edit-page';
import '../../ui/views/operations/item-create/material-item-create-page';
/* REPORTING */
import '../../ui/views/reporting/intake-report/intake-report';
import '../../ui/views/reporting/history/client-receipt-history';
import '../../ui/views/reporting/history/receipt-history';
import '../../ui/views/reporting/kpi-reports/kpi1-orders-received/kpi1-orders-received';
import '../../ui/views/reporting/kpi-reports/kpi3-orders-delivered/kpi3-orders-delivered';
import '../../ui/views/reporting/test/report-test';
import '../../ui/views/operations/edit/edit-purchase-order';
import '../../ui/views/operations/scan/scan-screen';

import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Meteor } from 'meteor/meteor';
import { Router } from 'meteor/router';
import path from 'path';

const registerRoutes = (routePrefix = null) => {
  new Router(routePrefix)
    .route('pack', '/', 'clients')
    .route('clients', '/')
    .route('receiptAtStage', '/clients/:clientId/receipt/:receiptStage', 'receipt')
    .route('receiptAtStageWithPo', '/clients/:clientId/receipt/:receiptStage/:poId', 'receipt')
    .redirect(
      'unpackCargo',
      '/clients/5a71b0e16303af7e44ec7316/receipt/2',
      (params) => FlowRouter.go(
        'receiptAtStage',
        Object.assign(
          params,
          {
            receiptStage: '2',
            clientId: '5a71b0e16303af7e44ec7316', // Peterson client TODO: Remove hardcoding, ideally look up default client Id in config.
          },
        ),
      ),
    )
    .redirect(
      'receiptItem',
      '/clients/:clientId/receipt',
      (params) => FlowRouter.go('receiptAtStage', Object.assign(params, { receiptStage: '2' })),
    )
    .route('receiptAtStageWithCcu', '/clients/:clientId/receipt/:receiptStage/:ccuId', 'receipt')
    .route(
      'receiptAtStageWithCcuAndMaterial', // name
      '/clients/:clientId/receipt/:receiptStage/:ccuId/:itemId', // url
      'receipt',
    ) // template name
    .route('editPo', '/clients/:clientId/edit-po/:poId', 'editPurchaseOrder')
    .route('store', '/clients/:clientId/store', 'chemStore')
    .route('requests', '/clients/:clientId/requests', 'requestsOverview')
    .route('packlist', '/clients/:clientId/requests/:requestId/packlist', 'chemPacklist')
    .route('addRequestItems', '/requests/:requestId/add-items')
    .route('addPicklistItem', '/picklists/:requestId/add-item')
    .route('addCcu', '/clients/:clientId/orders/:requestId/add-ccu')
    .route('picklist', '/requests/:requestId/picklist')
    .route('assignVehicle', '/clients/:clientId/vehicle-runs/:vehicleRunId/assign-vehicle')
    .route('vehicleLoadingList', '/clients/:clientId/vehicle-runs/:vehicleRunId/:assignedVehicleId')
    .route('vehicleLoadingList', '/clients/:clientId/vehicle-runs/:vehicleRunId', 'vehicleLoadingListWithVehicle')
    .route('itemEditPage', '/clients/:clientId/item-edit/:itemId') // not needed
    .route('abdnItemEditPage', '/clients/:clientId/abdn-item-edit/:itemId') // not needed
    .route('materialItemEditPage', '/clients/:clientId/material-item-edit/:itemId')
    .route('materialItemCreatePage', '/clients/:clientId/material-item-create/:itemId')
    .route('clientReceiptHistory', '/clients/:clientId/reporting/client-receipt-history/')
    .route('receiptHistory', '/clients/:clientId/reporting/receipt-history')
    .route('kpi1OrdersReceived', '/clients/:clientId/reporting/kpi1-orders-received/')
    .route('kpi3OrdersDelivered', '/clients/:clientId/reporting/kpi3-orders-delivered/')
    .route('report-test', '/clients/:clientId/reporting/report-test/', 'reportTest')
    .route('scan', '/clients/:clientId/scan/', 'scanScreen')
    .route('intakeReport', '/clients/:clientId/reporting/intake-report/');
};

export const Routes = {
  register: (routePrefix = null) => registerRoutes(routePrefix),
};
