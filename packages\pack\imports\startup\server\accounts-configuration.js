import { Accounts } from 'meteor/accounts-base';
import { App } from '../../shared/app';
import { Meteor } from 'meteor/meteor';
import { Roles } from 'meteor/alanning:roles';
import { _ } from 'meteor/underscore';
import { <PERSON> as log } from 'meteor/wylio:winston-papertrail';

const createDevUserAccounts = () => {
  const devUserAccounts = [];
  _.each(devUserAccounts, (devUser) => {
    const id = Accounts.createUser(devUser);
    if (devUser.roles &&
        devUser.roles.length > 0 &&
        devUser.profile.siteIdentifier) {
      Roles.addUsersToRoles(id, devUser.roles, devUser.profile.siteIdentifier);
    }
  });
};

const createProductionUserAccounts = () => {
  const productionUserAccounts = [];
  _.each(productionUserAccounts, (productionUser) => {
    const id = Accounts.createUser(productionUser);
    if (productionUser.roles &&
        productionUser.roles.length > 0 &&
        productionUser.profile.siteIdentifier) {
      Roles.addUsersToRoles(id, productionUser.roles, productionUser.profile.siteIdentifier);
    }
  });
};

Meteor.startup(() => {
  if (Meteor.users.find().count() === 0) {
    if (App.inDevMode) {
      createDevUserAccounts();
      createProductionUserAccounts();
    }
    if (App.inProductionMode) {
      createProductionUserAccounts();
    }
  }
});
