import { Items } from '../../api/items/items';
import { Cargo } from '../../api/cargo/cargo';
import SimpleSchema from 'simpl-schema';

// Ensure receiptNo is unique in both items and cargo collections.
Items.createIndex({ receiptNo: 1 }, { unique: 1 });
Cargo.createIndex({ receiptNo: 1 }, { unique: 1 });

// Don't allow same Flow CargoLine to be inserted more than once.
Cargo.createIndex({ externalCargoLineId: 1 }, { unique: 1 });

// For filters on tables
Items.createIndex({ ccu: 1 });
Items.createIndex({ description: 1 });

// For fast lookup based on date (LT).
Items.createIndex({ receivedDateStr: 1 }); // CCU received date
Items.createIndex({ storedDateStr: 1 });
Items.createIndex({ packedDateStr: 1 });
Items.createIndex({ dispatchedDateStr: 1 });

// Add custom validation messages
SimpleSchema.setDefaultMessages({
  messages: {
    en: {
      publication_not_registered: 'The publication isn`t registered with api.publications',
      invalid_receipt_type: 'The receipt type is not recognised.',
      invalid_receipt_stage_type: 'The receipt stage type is not recognised',
      invalid_report_type: 'The report type is not recognised',
      invalid_event_type: 'The event type is not recognised',
      invalid_packing_unit_type: 'The packing unit type is not recognised',
    },
  },
});
