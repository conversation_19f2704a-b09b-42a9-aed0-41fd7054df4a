import { Meteor } from 'meteor/meteor';
import { ResetItemRefCounter } from
  '../../api/company-site-profiles/commands/reset-item-ref-counter';
import { SyncedCron } from 'meteor/percolate:synced-cron';
import { <PERSON> as log } from 'meteor/wylio:winston-papertrail';
import { moment } from 'meteor/momentjs:moment';

class CronTaskDetails {
  constructor(name, taskSchedule, cronFunction) {
    this.name = name;
    this.taskSchedule = taskSchedule;
    this.cronFunction = cronFunction;
  }
}

/*
  Method used for adding new background cron tasks.
  Pass in a details class structured as follows:
  details = {
    cronTaskName, // Used to identify the type of cron task
    cronTaskSchedule, // When should operation occur (uses later.js see https://github.com/percolatestudio/meteor-synced-cron Basics section)
    cronFunction, // The function that is to be called at each scheduled time
  }
*/
const addCronTask = (details) => {
  log.info(`Adding Cron-task <${details.name}>.`);
  SyncedCron.add({
    name: details.name,
    schedule: (parser) => parser.recur().on(details.taskSchedule).time(),
    job: details.cronFunction,
  });
};

const beginCronTasks = () => {
  SyncedCron.start();
};

const nightlyPetersonChemicalsItemCounterReset = () => {
  const siteIdentifier = 'peterson-chemicals-dnhr';
  const resetValue = 1;

  log.info(`Peterson-Chemicals-DenHelder: Nightly Item Counter Reset - Called at ${moment().utc().toDate()}`);

  ResetItemRefCounter.call({ resetValue, siteIdentifier });
};

const addNightlyPetersonChemicalsItemCounterResetCronTask = () => {
  const details = new CronTaskDetails(
    'NightlyPetersonChemicalsItemCounterReset',
    '01:01', // Set to 1 minute past 1am local time (reduce chances of confusion over UTC vs LT).
    nightlyPetersonChemicalsItemCounterReset,
  );
  addCronTask(details);
};

export const CronOperations = {
  addCronTask,
  beginCronTasks,
  addNightlyPetersonChemicalsItemCounterResetCronTask,
};
