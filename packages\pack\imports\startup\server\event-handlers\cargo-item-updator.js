import { Log } from '../../../api/api.helpers/log';
import { GetCargoItemByExternalCargoLineId } from '../../../api/cargo/queries/get-cargo-item-by-externalcargolineid';
import { GetItemsByExternalCargoLineId } from '../../../api/items/queries/get-items-by-external-cargo-line-id';
import { AuditCargoUpdate } from '../../../api/cargo/commands/audit-cargo-update';
import { UpdateSetCompletedWasteRemoval } from '../../../api/cargo/commands/update-set-completed-waste-removal';
import { _ } from 'meteor/underscore';
import { RemoveItemsFromCargoItem } from '../../../api/items/commands/external-item-updates/remove-items-from-cargo-item';
import { AddItemsToCargoItem } from '../../../api/items/commands/external-item-updates/add-items-to-cargo-item';
import { UpdateItemsInCargoItem } from '../../../api/items/commands/external-item-updates/update-items-in-cargo-item';
import { UpdateCargoLevelInformationOnItemsInCargoItem } from '../../../api/items/commands/external-item-updates/update-cargo-level-information-on-items-in-cargo-item';
import { UpdateCargoItem } from '../../../api/cargo/commands/external-cargo-updates/update-cargo-item';
import { ReinstateCargoItem } from '../../../api/cargo/commands/reinstate-cargo-item';
import { HideCargoItem } from '../../../api/cargo/commands/hide-received-cargo-item';

// Basic Deep diff (non-reversible) adapted from https://stackoverflow.com/questions/8572826/generic-deep-diff-between-two-objects
// Ignores arrays.
function diff(newObj, origObj) {
  const diffs = {}; // Results with list of key.values from a
  _.each(newObj, (val, key) => {
    if ((origObj[key] === val) || _.isArray(val)) return; // If a.[key] == b.[key] or value is array move on.
    diffs[key] = _.isObject(val) // Otherwise results[key] = a[key].
      ? diff(val, origObj[key]) // Recurse to diff the object.
      : { from: origObj[key], to: val };
  });
  return diffs;
}

// Retrieve currently stored cargo Item that is to be updated.
const getExistingCargoItemByExternalCargoLineId =
  (externalCargoLineId) => GetCargoItemByExternalCargoLineId.call({ externalCargoLineId });

// Update Cargo and MaterialLine information in Pack with new CargoLine version from Flow.
const updateExistingCargoItemAndMaterials = (eCargoCargoItem) => {
  Log.info(`CARGO UPDATE RECEIVED for <${eCargoCargoItem.identifier}>...`);

  // Load existing version from Pack collection.
  const currentCargoItem = getExistingCargoItemByExternalCargoLineId(eCargoCargoItem.cargoLineId);
  const currentEcargoData = currentCargoItem.ecargoCargoLine;

  const currentItems = GetItemsByExternalCargoLineId.call({ externalCargoLineId: eCargoCargoItem.cargoLineId });
  console.log(currentItems);
  // 1) Find changes in CargoLine fields.
  const cargoLineDiffs = diff(eCargoCargoItem, currentEcargoData); // new object first, then old object second.

  // 2) Find changes in Material Line fields.
  const updatedMaterialLines = eCargoCargoItem.materials;
  const currentMaterialLines = currentEcargoData.materials;

  Log.info(`${updatedMaterialLines.length} Material lines in update.`);
  Log.info(`${currentMaterialLines.length} Material lines in existing version of cargo item.`);

  const materialLineDiffs = [];
  const materialLineIdsDeleted = [];

  _.each(currentMaterialLines, (originalMl) => {
    const newMl = updatedMaterialLines.find((x) => x.lineId === originalMl.lineId);
    if (newMl) {
      Log.info('Found matching material line');
      if (newMl.isCancelled) {
        materialLineIdsDeleted.push(originalMl.lineId);
      } else {
        const mlDiff = diff(newMl, originalMl);
        materialLineDiffs.push({ mlId: originalMl.lineId, diffs: mlDiff });
      }
    } else {
      // Existing material line not found in this update - must have been removed.
      materialLineIdsDeleted.push(originalMl.lineId);
    }
  });

  // 3) Any additional material lines added in this update?
  const existingMlIds = _.pluck(currentMaterialLines, 'lineId');
  const updateMlIds = _.pluck(updatedMaterialLines, 'lineId');
  const materialLineIdsAdded = _.difference(updateMlIds, existingMlIds);

  // 4) Process updates
  Log.info('Diffs found in cargo line:', cargoLineDiffs);
  Log.info(`${materialLineIdsAdded.length} Material lines added:`, materialLineIdsAdded);
  Log.info(`${materialLineIdsDeleted.length} Material lines removed:`, materialLineIdsDeleted);
  Log.info(`${materialLineDiffs.filter((x) => Object.keys(x.diffs).length > 0).length} Material lines have differences:`);
  _.each(materialLineDiffs, (mldiff) => {
    Log.info(`Diffs found in material line: ${mldiff.mlId}`, mldiff.diffs);
  });

  // 4.1 Remove deleted material lines if not already receipted.
  const anyMaterialsHaveBeenRemoved = materialLineIdsDeleted.length > 0;
  if (anyMaterialsHaveBeenRemoved) {
    const idsOfItemsToRemove = currentItems
      .filter((y) => materialLineIdsDeleted.includes(y.externalMaterialLineId))
      .map((x) => x._id);

    if (idsOfItemsToRemove.length > 0) {
      RemoveItemsFromCargoItem.call({ cargoItemId: currentCargoItem._id, idsOfItemsToRemove: idsOfItemsToRemove });
    }
  }

  // 4.2 Add new material lines.
  const anyNewMaterialsHaveBeenAdded = materialLineIdsAdded.length > 0;
  if (anyNewMaterialsHaveBeenAdded) {
    const newEcargoMaterials = 
    eCargoCargoItem.materials.filter((x) => materialLineIdsAdded.includes(x.lineId));
    AddItemsToCargoItem.call({ cargoItemId: currentCargoItem._id, newItemDtos: newEcargoMaterials });
  }

  // 4.3 Update existing material lines if not already receipted.
  const materialLinesWithChanges =
    materialLineDiffs.filter((x) =>
      Object.keys(x.diffs).length > 0 && !materialLineIdsDeleted.includes(x.mlId));

  const anyMaterialsHaveBeenUpdated = materialLinesWithChanges.length > 0;
  if (anyMaterialsHaveBeenUpdated) {
    const itemUpdates = materialLinesWithChanges.map((diff) => ({
      itemId: currentItems.find((y) => diff.mlId === y.externalMaterialLineId)._id,
      itemUpdateDto: updatedMaterialLines.find((y) => diff.mlId === y.lineId),
    }));
    UpdateItemsInCargoItem.call({ cargoItemId: currentCargoItem._id, itemUpdateDtos: itemUpdates });
  }

  // 4.3.1) Set show variables
  ReinstateCargoItem.call({ id: currentCargoItem._id });

  // 4.4) Update cargo line and cargo line header info on material lines if not already receipted
  const anyCargoDetailsHaveChanged = Object.keys(cargoLineDiffs).length > 0;
  if (anyCargoDetailsHaveChanged) {
    UpdateCargoLevelInformationOnItemsInCargoItem.call({ cargoItemId: currentCargoItem._id, cargoUpdateDto: eCargoCargoItem });
  }

  UpdateCargoItem.call({ id: currentCargoItem._id, updateDto: eCargoCargoItem});

  // 4.5) Record updates on cargoItem for auditing
  const allDiffs = {
    cargoLineDiffs,
    materialLineDiffs,
    materialLineIdsAdded,
    materialLineIdsDeleted,
  };
  AuditCargoUpdate.call({ externalCargoLineId: eCargoCargoItem.cargoLineId, allDiffs });

  Log.info(`...FINISHED CARGO UPDATE for <${eCargoCargoItem.identifier}>.`);
};

// Update Cargo and MaterialLine information in Pack with new CargoLine version from Flow.
const updateCargoItemToWasteRemovalCompleted = (eCargoCargoItem) => {
  Log.info(`Set cargo item to Waste Removal Completed <${eCargoCargoItem.identifier}>...`);
  const cargoItem = getExistingCargoItemByExternalCargoLineId(eCargoCargoItem.cargoLineId);
  const isWasteRemovalCompleted = true;
  UpdateSetCompletedWasteRemoval.call({ cargoItemId: cargoItem._id, isWasteRemovalCompleted });
};

// Update Cargo in Pack to hide it in receipt screen.
const updateCargoItemToReceived = (eCargoCargoItem) => {
  Log.info(`Set cargo item to Received <${eCargoCargoItem.identifier}>...`);
  const cargoItem = getExistingCargoItemByExternalCargoLineId(eCargoCargoItem.cargoLineId);
  HideCargoItem.call({ id: cargoItem._id });
};

export const CargoItemUpdator = {
  updateExistingCargoItemAndMaterials,
  updateCargoItemToWasteRemovalCompleted,
  updateCargoItemToReceived,
};
