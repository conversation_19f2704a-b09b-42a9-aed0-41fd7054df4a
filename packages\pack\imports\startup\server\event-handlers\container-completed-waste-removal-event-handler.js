import { CargoItemUpdator } from './cargo-item-updator';
import { CheckCargoItemExists } from '../../../api/cargo/queries/check-cargo-item-exists';
import { Log } from '../../../api/api.helpers/log';

const doesCargoItemAlreadyExistInCargoCollection = (eCargoCargoItem) => {
  const externalCargoLineId = eCargoCargoItem.cargoLineId;
  return CheckCargoItemExists.call({ externalCargoLineId });
};

const updateCargoItemToWasteRemovalCompleted = (eCargoCargoItem) => {
  CargoItemUpdator.updateCargoItemToWasteRemovalCompleted(eCargoCargoItem);
};

// Handle new or updated cargo item info from Receipt side.
const handleContainerCompletedWasteRemoval = (eCargoCargoItem) => {
  Log.info('Handling container awaiting collection notification from Receipt.');
  if (doesCargoItemAlreadyExistInCargoCollection(eCargoCargoItem)) {
    Log.info(`CargoItem <${eCargoCargoItem.identifier}>, eCargoLineId:<${eCargoCargoItem.cargoLineId}> already exists in cargo collection!`);
    updateCargoItemToWasteRemovalCompleted(eCargoCargoItem);
  } else {
    Log.error('CargoItem not found in cargo collection - ignoring awaiting collection notification');
  }
  return true;
};

export const ContainerCompletedWasteRemovalEventHandler = {
  handleContainerCompletedWasteRemoval,
};
