import { CargoItemUpdator } from './cargo-item-updator';
import { CheckCargoItemExists } from '../../../api/cargo/queries/check-cargo-item-exists';
import { Log } from '../../../api/api.helpers/log';

const doesCargoItemAlreadyExistInCargoCollection = (eCargoCargoItem) => {
  const externalCargoLineId = eCargoCargoItem.cargoLineId;
  return CheckCargoItemExists.call({ externalCargoLineId });
};

const hideCargo = (eCargoCargoItem) => {
  CargoItemUpdator.updateCargoItemToReceived(eCargoCargoItem);
};

// Handle updated cargo item info from Receipt side.
const handleContainerGoingToPrep = (eCargoCargoItem) => {
  Log.info('Handling container preparation notification from Receipt.');
  if (doesCargoItemAlreadyExistInCargoCollection(eCargoCargoItem)) {
    hideCargo(eCargoCargoItem);
  } else {
    Log.info('Skipping event as it is containers first time in preparation state');
  }
};

export const ContainerReturnedToPrepEventHandler = {
  handleContainerGoingToPrep,
};
