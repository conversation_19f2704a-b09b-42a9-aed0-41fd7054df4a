import { CargoItemUpdator } from './cargo-item-updator';
import { ChemPreReceipt } from '../../../api/items/commands/receipt-item.strategies/chem-pre-receipt';
import { ReceiptTypes } from '../../../api/items/receipt.types';
import { CompanySiteProfiles } from '../../../api/company-site-profiles/company-site-profiles';
import { CheckCargoItemExists } from '../../../api/cargo/queries/check-cargo-item-exists';
import { Log } from '../../../api/api.helpers/log';
import moment from 'moment';
import { ReceiptNumberCalculator } from '../../../shared/receiptnumber-calculator';

const doesCargoItemAlreadyExistInCargoCollection = (eCargoCargoItem) => {
  const externalCargoLineId = eCargoCargoItem.cargoLineId;
  return CheckCargoItemExists.call({ externalCargoLineId });
};

// Perform 'pre-receipt' of cargo item and materials.
const insertNewCargoItemAndMaterials = (unit) => {
  const siteIdentifier = 'peterson-chemicals-dnhr';
  const siteProfile = CompanySiteProfiles.findOne({ identifier: siteIdentifier });

  const config = siteProfile.configuration;
  const receivedDate = moment().utc().toDate();

  const receiptNo = ReceiptNumberCalculator.getReceiptNoAndIncrement(config, siteIdentifier);

  const receiptLocation = config.receiptLocations[0].name;
  const clientObject = config.clients.find(x => x.name === 'Peterson');

  const preReceiptCargoItem = {
    receiptNo,
    receiptLocation,
    receivedDate,
    receiptType: ReceiptTypes.chemPreReceipt,
    siteId: siteIdentifier,
    client: clientObject,
    description: unit.description,
    offshoreClient: unit.clientName,
    ccu: unit.identifier,
    offshoreLocation: unit.offshoreInstallationName,
    voyageNo: unit.voyageNo || 'Unknown', // Is required.
    manifestNo: unit.manifestNo || 'Not set', // Is required.
    ecargoCargoLine: unit,
    externalCargoLineId: unit.cargoLineId, // This is Flow CargoLineId.
  };

  // Handle new cargo item as a Pack 'pre-receipt' (1st stage receipt).
  ChemPreReceipt.call({ item: preReceiptCargoItem }, (err) => {
    if (!err) {
      Log.debug('Finished receipt of Cargo item.');
    } else {
      Log.error('error: ChemPreReceipt.call', err);
    }
  });
};

const updateExistingCargoItemAndMaterials = (eCargoCargoItem) => {
  Log.info('Attempting cargo item update...');
  CargoItemUpdator.updateExistingCargoItemAndMaterials(eCargoCargoItem);
};

// Handle new or updated cargo item info from Receipt side.
const handleContainerUpdate = (eCargoCargoItem) => {
  Log.info('Checking If this is a new cargo item - if it is inserting it...');
  if (doesCargoItemAlreadyExistInCargoCollection(eCargoCargoItem)) {
    Log.info(`CargoItem <${eCargoCargoItem.identifier}>, eCargoLineId:<${eCargoCargoItem.cargoLineId}> already exists in cargo collection`);
    updateExistingCargoItemAndMaterials(eCargoCargoItem);
  } else {
    Log.info('CargoItem does not already exist in cargo collection - create it now');
    insertNewCargoItemAndMaterials(eCargoCargoItem);
  }
  return true;
};

export const ContainerUpdateEventHandler = {
  handleContainerUpdate,
};

export const ContainerReturnedToPrepEventHandler = {
  handleContainerUpdate,
};
