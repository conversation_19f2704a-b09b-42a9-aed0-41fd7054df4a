import { Accounts } from 'meteor/accounts-base';
import { Picker } from 'meteor/meteorhacks:picker';
import { getPowerBiReceiptedNotDispatched } from '../../api/items/queries/powerbi/items-receipted-not-dispatched';
import bodyParser from 'body-parser';
import auth from 'basic-auth';

const isValidApiUser = (req) => {
  const authString = req.headers.authorization;
  const authParts = auth.parse(authString);

  if (!authParts) {
    return false;
  }

  const username = authParts.name;
  const password = authParts.pass;

  if (!username) { return false; }
  if (!password) { return false; }

  const user = Accounts.findUserByUsername(username);
  if (typeof user === 'undefined') {
    return false;
  }

  const passCheck = Accounts._checkPassword(user, password);
  if (passCheck.error) {
    return false;
  }

  console.log(`info: [Pack] Successful login attempt for Power BI endpoint. username=<${username}>`);
  return true;
};

const registerRoutes = (routePrefix) => {
  Picker.middleware(bodyParser.json({
    limit: '100mb',
  }));

  Picker.route(`/${routePrefix}/api/items`, (params, req, res) => {
    const response = res;
    response.statusCode = 200;
    response.setHeader('Content-Type', 'application/json');
    response.setHeader('Access-Control-Allow-Origin', '*');
    response.setHeader(
      'Access-Control-Allow-Headers',
      'Origin, X-Requested-With, Content-Type, Accept',
    );

    if (!isValidApiUser(req)) {
      console.log('info: [Pack] Failed login attempt for Power BI endpoint.');
      response.statusCode = 401;
      response.setHeader('WWW-Authenticate', 'Basic realm="PowerBi API"');
      response.end('Access denied');
      return;
    }

    // This is not wrapped using the standard Meteor methods / Meteor.call approach
    // because there is no (good) way to do a server side login and the methods won't
    // run without a logged in user. So we just check user / password above.
    const results = getPowerBiReceiptedNotDispatched();
    response.end(JSON.stringify(results));
  });
};

export const Routes = {
  register: (routePrefix = null) => registerRoutes(routePrefix),
};
