import { ContainerUpdateEventHandler } from './event-handlers/container-update-event-handler';
import { ContainerCompletedWasteRemovalEventHandler } from './event-handlers/container-completed-waste-removal-event-handler';
import { ContainerReturnedToPrepEventHandler } from './event-handlers/container-returned-to-prep-event-handler'
import { Mediator } from 'meteor/mediator';

// Subscribe to Mediator CONTAINER_UPDATE events from Receipt.
Mediator.subscribe(Mediator.events.CONTAINER_UPDATE, (eCargoDeckCargoItem) => {
  console.log('[PACK] Event received for CONTAINER_UPDATE', eCargoDeckCargoItem);
  ContainerUpdateEventHandler.handleContainerUpdate(eCargoDeckCargoItem);
});

// Subscribe to Mediator CONTAINER_COMPLETED_WASTE_REMOVAL events from Receipt.
Mediator.subscribe(Mediator.events.CONTAINER_COMPLETED_WASTE_REMOVAL, (eCargoDeckCargoItem) => {
  console.log('[PACK] Event received for CONTAINER_COMPLETED_WASTE_REMOVAL', eCargoDeckCargoItem);
  ContainerCompletedWasteRemovalEventHandler.handleContainerCompletedWasteRemoval(eCargoDeckCargoItem);
});

// Subscribe to Mediator CONTAINER_RETURNED_TO_PREP events from Receipt.
Mediator.subscribe(Mediator.events.CONTAINER_RETURNED_TO_PREP, (eCargoDeckCargoItem) => {
  console.log('[PACK] Event received for CONTAINER_RETURNED_TO_PREP', eCargoDeckCargoItem);
  ContainerReturnedToPrepEventHandler.handleContainerGoingToPrep(eCargoDeckCargoItem);
});
