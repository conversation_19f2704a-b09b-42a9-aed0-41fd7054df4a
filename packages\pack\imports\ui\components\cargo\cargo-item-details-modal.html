<template name="cargoItemDetailsModal">
  <div class="ui modal cargo-item-details-modal">
    <i class="close icon"></i>
    {{> cargoItemDetails selectedCargoItem=selectedCargoItem }}
    <div class="actions">
      <div class="ui labeled icon button ok">
        <i class="checkmark icon"></i>
        OK
      </div>
    </div>
  </div>
</template>

<template name="cargoItemDetails">
  <div class="ui clearing segment">
    <h1 class="ui left floated header">Cargo Details: {{selectedCargoItem.ccu}} </h1>
  </div>
  <div class="content">
    <div class="ui container">
      <div class="ui middle aligned very relaxed stackable grid">
        <div class="twelve wide column">
          <div class="ui form">
            <div class="eight wide field">
              <label>Receipt No.</label>
              <input type="text" name="receiptNo" value="{{selectedCargoItem.receiptNo}}" readonly>
            </div>
            <div class="readonly fields">
              <div class="eight wide field">
                <label>Receipt Location</label>
                <input type="text" name="receiptLocation" value="{{selectedCargoItem.receiptLocation}}" readonly>
              </div>
              <div class="eight wide field">
                <label>Received Date</label>
                <input type="text" name="receivedDate" value="{{receivedDateFormatted}}" readonly>
              </div>
            </div>

            <div class="readonly fields">
              <div class="eight wide field">
                <label>Voyage No.</label>
                <input type="text" name="ccu" value="{{selectedCargoItem.voyageNo}}" readonly />
              </div>
              <div class="eight wide field">
                <label>No. of Material Lines</label>
                <input type="text" name="noOfMaterialLinesRcvd" value="{{selectedCargoItem.noOfLines}}" readonly/>
              </div>
            </div>

            <div class="readonly fields">
              <div class="eight wide field">
                <label>Operator</label>
                <input type="text" name="vendor" value="{{selectedCargoItem.offshoreClient}}" readonly>
              </div>
              <div class="eight wide field">
                <label>Offshore Location</label>
                <input type="text" name="vendorDeliveryNo" value="{{selectedCargoItem.offshoreLocation}}" readonly>
              </div>
            </div>
            <div class="readonly field">
              <label>Description</label>
              <input type="text" name="description" value="{{selectedCargoItem.description}}" readonly>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>