import './cargo-item-details-modal.html';
import { DISPLAY_DATETIME_FORMAT } from '../../../shared/lib/constants';
import { Template } from 'meteor/templating';
import moment from 'moment';

Template.cargoItemDetails.helpers({
  receivedDateFormatted() {
    const cargoItem = Template.instance().data.selectedCargoItem;
    if (!cargoItem || !cargoItem.receivedDate) {
      return '';
    }

    return moment(cargoItem.receivedDate).format(DISPLAY_DATETIME_FORMAT);
  },
});
