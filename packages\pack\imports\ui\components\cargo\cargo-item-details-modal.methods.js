import { BaseModalMixin } from '../../modal/base-modal-mixin';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';

export const CargoItemDetailsModalMethods = Object.assign({
  className: '.ui.modal.cargo-item-details-modal',

  init(cargoItemId) {
    let openItemEditScreen = false;

    $(this.className).modal({
      allowMultiple: false,
      onApprove: (element) => {
        if (element.hasClass('edit')) {
          openItemEditScreen = true;
        }
      },
      onHidden: () => {
        if (openItemEditScreen) {
          FlowRouter.go('editCargoItem',
            {
              clientId: FlowRouter.getParam('clientId'),
              cargoItemId,
            });
        }
      },
    }).modal('hide all');

    return this;
  },
}, BaseModalMixin);
