import './client-header.html';
import { SiteProfileService } from
  '../services/company-site-profiles/site-profile.service';
import { Template } from 'meteor/templating';
import { PackageSettings } from 'meteor/pack/imports/startup/package-settings';

Template.clientHeader.helpers({
  currentClient() {
    return SiteProfileService.currentClient();
  },
  moreThanOneClient() {
    return SiteProfileService.clients().length > 1;
  },
  headerText() {
    const header = Template.currentData().headerText;

    return header || 'Pack';
  },
  pkgName() {
    return PackageSettings.name;
  },
});
