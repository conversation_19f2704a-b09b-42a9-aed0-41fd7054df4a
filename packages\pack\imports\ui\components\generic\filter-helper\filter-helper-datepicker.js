import './filter-helper-datepicker.html';
import { Template } from 'meteor/templating';

const getDatepickerId = (template) => template.data.key + 'Datepicker';

const initialiseDatepickers = (templateInstance) => {
  const setupDate = (defaultSettings, template) => {
    const settings = defaultSettings;

    settings.initialDate = template.data.defaultDate;
    settings.onChange = (date) => {
      if (template.data.filterHelper) {
        template.data.filterHelper.filterChanged(template.data.key, date);
      }
    };

    template.$('#' + getDatepickerId(template)).calendar(settings);
    template.$('#'+ getDatepickerId(template)).calendar('set date', template.data.defaultDate);
  };

  const defaultSettings = {
    type: 'date',
    formatter: {
      date: (date, settings) => {
        if (!date) return '';
        const day = date.getDate();
        const month = settings.text.monthsShort[date.getMonth()];
        const year = date.getFullYear();
        return `${day} ${month} ${year}`;
      },
    },
  };

  setupDate(defaultSettings, templateInstance);
};

Template.filterHelperDatepicker.onCreated(function onCreated() {
  const template = this;
});

Template.filterHelperDatepicker.onDestroyed(function onDestroyed() {
  const template = this;
});

Template.filterHelperDatepicker.onRendered(function onRendered() {
  const template = this;
  const filterHelper = template.data.filterHelper;
  initialiseDatepickers(template);

  if (filterHelper) {
    filterHelper.registerClearMethod(template.data.key, function () {
      template.$('#'+getDatepickerId(template)).calendar('set date', template.data.defaultDate);
    });
  }
});

Template.filterHelperDatepicker.helpers({
  title() {
    var title = this.filterHelper.getFilterByKey(this.key).title;
    if (title) {
      return title;
    }
  },
  datepickerId() {
    return this.key + 'Datepicker';
  },
  placeholder() {
    const template = this;
    return template.placeholder ? template.placeholder : 'Date';
  },
});
