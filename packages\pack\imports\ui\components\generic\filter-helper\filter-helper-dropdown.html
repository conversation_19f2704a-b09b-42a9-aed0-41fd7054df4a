<template name="filterHelperDropdown">
    <div class="ui fluid dropdown basic blue labeled icon button {{#if disabled}}disabled{{/if}} {{classes}}">
        <i class="filter icon"></i>
        <span class="text"> {{ filterTitle }}</span>
        <div class="menu">
            <div class="ui icon search input">
                <i class="search icon"></i>
                <input type="text" placeholder="{{ placeholderText }}" readonly>
            </div>
            <div class="scrolling menu">
                {{#each item in items}}
                    {{> filterHelperDropdownOption item=item }}
                {{/each}}
            </div>
        </div>
    </div>
</template>

<template name="filterHelperDropdownOption">
    <div class="item" data-value="{{ item.name }}">
        <span class="text"> {{ item.name }} </span>
        <span class="description"></span>
    </div>
</template>