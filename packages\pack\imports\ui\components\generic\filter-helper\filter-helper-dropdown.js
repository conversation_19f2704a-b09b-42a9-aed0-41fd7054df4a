import './filter-helper-dropdown.html';
import { Template } from 'meteor/templating';

Template.filterHelperDropdown.onCreated(function onCreated() {
  const template = this;
  template.forceRefreshItems = new ReactiveVar(false);
});

Template.filterHelperDropdown.onDestroyed(function onDestroyed() {
  const template = this;
  template.$('.ui.dropdown').dropdown('destroy');
});

Template.filterHelperDropdown.onRendered(function onRendered() {
  const template = this;
  const filterHelper = template.data.filterHelper;
  const autoFocus = (template.data.autofocus) ? template.data.autofocus : false;
  const key = template.data.key;

  if (autoFocus) {
    template.$('.ui.dropdown').dropdown({
      fullTextSearch: true,
      onChange: function (value) {
        filterHelper.filterChanged(key, value);
      }
    });
  } else {
    template.$('.ui.dropdown').dropdown({
      fullTextSearch: true,
      onChange: function (value) {
        filterHelper.filterChanged(key, value);
      },
      onShow: function() {
        Meteor.setTimeout(() => {
          template.$("input[type='text']").prop('readonly', false);
        }, 500);
      },
      onHide: function() {
        template.$("input[type='text']").prop('readonly', true);
      },
    });
  }

  filterHelper.registerClearMethod(key, function (refreshItemsList = false) {
    if (template.view._domrange.attached) {
      template.$('.ui.dropdown').dropdown('restore defaults');
      if (refreshItemsList) {
        template.forceRefreshItems.set(!template.forceRefreshItems.get());
      }
    }
  });
});

Template.filterHelperDropdown.helpers({
  filterTitle() {
    return this.filterHelper.getFilterByKey(this.key).filterText;
  },

  placeholderText() {
    return this.filterHelper.getFilterByKey(this.key).filterSearchPlaceholder;
  },

  items() {
    let dummy = Template.instance().forceRefreshItems.get(); // Use this to force update of items.
    return this.filterHelper.getUniqueValues(this.key);
  },
});
