<template name="filterHelperPagination">
    {{#if showCount}}
        <div class="ui eight wide left aligned column">
            <span> <span> {{filterHelper.count.get}} </span> Results </span>
        </div>
    {{/if}}
    <div class="ui pagination menu">

        <a class="page-prev {{#unless hasPrev}}disabled{{/unless}} item">
            Prev
        </a>
        {{#if showPrevTruncateBox}}
            <a class="disabled item">
                ...
            </a>
        {{/if}}
        {{#each page in pages}}
            {{> filterHelperPaginationPage page=page current=currentPage}}
        {{/each}}
        {{#if showNextTruncateBox}}
            <a class="disabled item">
                ...
            </a>
        {{/if}}
        <a class="page-next {{#unless hasNext}}disabled{{/unless}} item">
            Next
        </a>

        {{#if showPerPageDropdown}}
        <div class="ui dropdown item per-page-dropdown">
            Per Page <i class="dropdown icon"></i>
            <div class="menu">
                <a class="item"> 10 </a>
                <a class="item"> 15 </a>
                <a class="item"> 20 </a>
                <a class="item"> 25 </a>
                <a class="item"> 50 </a>
            </div>
        </div>
        {{/if}}
    </div>
</template>

<template name="filterHelperPaginationPage">
    <a class="page-button {{#if eq current page}}active{{/if}} item" data-page="{{page}}">
        {{page}}
    </a>
</template>