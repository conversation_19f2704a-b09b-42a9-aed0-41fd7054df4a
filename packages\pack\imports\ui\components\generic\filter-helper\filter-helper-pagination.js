import './filter-helper-pagination.html';

import { Template } from 'meteor/templating';

Template.registerHelper('eq', (a, b) => a == b);

Template.filterHelperPagination.onCreated(function onCreated() {
  const template = this;
  const filterHelper = template.data.filterHelper;
});

Template.filterHelperPagination.onRendered(function onRendered() {
  const template = this;
  var perPage = template.data.filterHelper.pagination().perPage;

  // For useability, remember users pagination settings
  if (typeof (Storage) !== 'undefined') {
    if (typeof localStorage.storePagePaginationPerPage === 'undefined') {
      localStorage.setItem('storePagePaginationPerPage', perPage);
    } else {
      perPage = localStorage.storePagePaginationPerPage;
    }
  }

  template.$('.per-page-dropdown').dropdown({
    onChange(value) {
      template.data.filterHelper.pagination().setPerPage(value);
      localStorage.setItem('storePagePaginationPerPage', value);
    },
  }).dropdown('set selected', perPage);
});

Template.filterHelperPagination.helpers({
  pages() {
    const truncateAfter = Template.currentData().truncate;
    const pages = this.filterHelper.pagination().pages;
    const current = this.filterHelper.pagination().page;
    const pageArray = Array.from(new Array(pages), (val, index) => index + 1);

    const start = ((Math.ceil(current / truncateAfter) - 1) * truncateAfter);
    const truncated = pageArray.slice(start, start + truncateAfter);

    return truncated;
  },
  showPrevTruncateBox() {
    const truncateAfter = Template.currentData().truncate;
    const pages = this.filterHelper.pagination().pages;
    const current = this.filterHelper.pagination().page;

    if (current > truncateAfter) {
      return true;
    }

    return false;
  },
  showNextTruncateBox() {
    const truncateAfter = Template.currentData().truncate;
    const pages = this.filterHelper.pagination().pages;
    const current = this.filterHelper.pagination().page;

    const endOfSpan = ((Math.ceil(current / truncateAfter) - 1) * truncateAfter) + truncateAfter;
    if (endOfSpan < pages) {
      return true;
    }

    return false;
  },
  currentPage() {
    return this.filterHelper.pagination().page;
  },
  hasPrev() {
    return this.filterHelper.pagination().hasPrev;
  },
  hasNext() {
    return this.filterHelper.pagination().hasNext;
  },
});

Template.filterHelperPagination.events({
  'click .page-button': function onClick(event, templateInstance) {
    const elem = event.currentTarget;
    const page = elem.dataset.page;
    templateInstance.data.filterHelper.pagination().setPage(page);
  },
  'click .page-next': function onClick(event, templateInstance) {
    templateInstance.data.filterHelper.pagination().nextPage();
  },
  'click .page-prev': function onClick(event, templateInstance) {
    templateInstance.data.filterHelper.pagination().prevPage();
  },
  'change .per-page-dropdown': function onChange(event, templateInstance) {
    const perPage = event.currentTarget.value;
    templateInstance.data.filterHelper.pagination().setPerPage(perPage);
  },
});
