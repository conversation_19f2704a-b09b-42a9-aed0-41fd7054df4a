import './filter-helper-text-search.html';

Template.filterHelperTextSearch.onCreated(function onCreated() {
  this.keyTimer = null;
});

Template.filterHelperTextSearch.onCreated(function onCreated() {
  const template = this;
  const filterHelper = template.data.filterHelper;
  const key = template.data.key;

  filterHelper.registerClearMethod(key, function () {
    template.$('input[type="text"]').val('');
  });
});

Template.filterHelperTextSearch.events({
  'keyup input[type="text"]': function onKeyup(event) {
    const template = Template.instance();
    const filterHelper = template.data.filterHelper;

    // Wait until user has stopped typing so we aren't hammering
    // the server on every keystroke.
    clearTimeout(template.keyTimer);
    template.keyTimer = setTimeout(function () {
      filterHelper.filterChanged(template.data.key, event.target.value);
    }, 600);
  },
});

Template.filterHelperTextSearch.helpers({
  placeholderText() {
    return this.filterHelper.getFilterByKey(this.key).filterSearchPlaceholder;
  },
});
