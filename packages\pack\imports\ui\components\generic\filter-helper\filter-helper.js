import { isArray } from 'util';
import { Tracker } from 'meteor/tracker';
import { _ } from 'meteor/underscore';
import { ReactiveVar } from 'meteor/reactive-var';
import { Mongo } from 'meteor/mongo';

export class FilterHelper {
  // #region Initialization

  constructor(template, collection, subscription, filters) {
    this.result = null;
    this.template = template;
    this.q = null;
    this.update = new ReactiveVar(null);
    this.subscription = subscription;
    this.collection = collection;
    this.addFilters(filters);
    this.count = new ReactiveVar(0);

    // Pagination
    this.perPage = new ReactiveVar(25);
    this.page = new ReactiveVar(1);
    this.sort = new ReactiveVar({});

    // Highlighter
    this.highlighter = null;
    this.highlighterKey = null;
    this.highlighted = new ReactiveVar([]);
  }

  init(query, builder = null) {
    this.initialQuery = query;
    this.builder = builder;

    this.template.autorun(() => {
      const update = this.update.get();
      this.combined = this.buildFilterObject(this.initialQuery(), this.filters);
      const paginated = Object.assign({}, this.combined);
      paginated.pagination = {
        perPage: this.perPage.get(),
        page: this.page.get(),
      };
      paginated.sorting = this.sort.get();

      this.template.subscribe(this.subscription, paginated);

      // Counts / Pagination
      Meteor.call(this.subscription + '.counts', this.combined, (error, result) => {
        this.count.set(result);
      });
      const perPage = this.perPage.get();
      const page = this.page.get();

      if (this.highlighter !== null) {
        this.generateHighlighted(
          this.highlighter,
          this.highlighterKey,
        );
      }
    });
  }

  addFilters(filters) {
    if (!isArray(this.filters)) {
      this.filters = [];
    }

    // TODO: Add validation
    if (isArray(filters)) {
      this.filters = this.filters.concat(filters);
    }
  }

  addHighlighter(highlighter = {}, key = null) {
    this.highlighter = highlighter;
    this.highlighterKey = key;
  }

  // #endregion

  // #region Internal Methods

  // Take the original query and filters and merge them into a new query
  buildFilterObject(query, filters) {
    var obj = Object.assign({}, query);

    filters.forEach((filter) => {
      if (isArray(filter.values) && filter.values.length > 0) {
        if (filter.values[0] == '') {
          delete obj[filter.key];
        } else {
          obj[filter.key] = filter.values[0];
        }
      }
    });

    return obj;
  }

  getFilterByKey(key) {
    const filter = this.filters.find((filter) => filter.key == key);

    if (typeof filter !== 'undefined') {
      return filter;
    }
  }

  // #endregion

  // #region Form Element Methods

  registerClearMethod(key, method) {
    const filter = this.getFilterByKey(key);

    if (typeof filter !== 'undefined' && typeof method === 'function') {
      filter.resetMethod = method;
    }
  }

  filterChanged(key, value) {
    // Update values
    const vals = [value];

    const filter = this.getFilterByKey(key);

    filter.values = vals;
    this.update.set(!this.update.get());
  }

  getUniqueValues(key) {
    const filter = this.filters.find((filter) => filter.key == key);

    if (typeof filter !== 'undefined' && isArray(filter.distinct)) {
      return filter.distinct;
    }

    return [];
  }

  setUniqueValues(key, values) {
    const filter = this.filters.find((f) => f.key === key);

    if (typeof filter !== 'undefined' && isArray(values)) {
      filter.distinct = [];
      values.forEach((element) => filter.distinct.push(element));
    }

    const refreshFilterDropDownItemsList = true;
    filter.resetMethod(refreshFilterDropDownItemsList);
    this.update.set(!this.update.get());
    return;
  }

  highlight(text, key, color='#21ba45') {
    if (typeof text === 'undefined') {
      return '';
    }

    const filter = this.getFilterByKey(key);

    if (typeof filter === 'undefined') {
      return text;
    }

    if (!isArray(filter.values) || filter.values.length == 0) {
      return text;
    }

    let value = filter.values[0];
    let result = text;

    let terms = value.trim().split(' ').map(function (item) {
      return item.trim();
    });

    terms.forEach((term) => {
      const regex = new RegExp(term, 'gi');
      const matches = text.match(regex);

      if (matches !== null) {
        result = result.replace(matches[0], "<strong style='color: " + color + "'>" + matches[0] + '</strong>');
      }
    });

    return Spacebars.SafeString(result);
  }

  // #endregion

  // #region Actions

  // Resets all filters to the initial state.
  resetFilters() {
    this.filters.forEach((filter) => {
      if (typeof filter.resetMethod !== 'undefined') {
        // Temporary workaround
        filter.values = [''];
        filter.resetMethod();
      }
    });

    this.update.set(!this.update.get());
  }

  setValue(key, value, flush = false) {
    const filter = this.getFilterByKey(key);

    if (typeof filter !== 'undefined') {
      filter.values = [''];
    }

    if (flush) {
      this.update.set(!this.update.get());
    }
  }

  // Returns the filtered collection.
  filtered(highlight = false) {
    const query = (this.builder === null) ? {} : this.builder(this.combined);
    if (highlight === false) {
      return this.collection.find(query);
    } else {
      return this.highlighted.get();
    }
  }

  generateHighlighted(keys = {}, filterKey = null) {
    const results = this.filtered().fetch(false);
    var highlighted = [];

    results.forEach((result) => {
      const updated = {};

      for (key in result) {
        if (typeof result[key] === 'string') {
          if (key in keys) {
            updated[key] = this.highlight(result[key], filterKey);
          } else {
            updated[key] = result[key];
          }
        } else {
          updated[key] = result[key];
        }
      }

      highlighted.push(updated);
    });

    this.highlighted.set(highlighted);
  }

  pagination() {
    const count = this.count.get();
    const perPage = this.perPage.get();
    const pages = Math.ceil(count / perPage);
    if (this.page.get() < 1 || this.page.get() > pages) {
      this.page.set(1);
    }
    const page = this.page.get();
    const pageArray = Array.from(new Array(pages), (val,index) => index + 1);
    const hasNext = (page < Math.ceil(count / perPage));
    const hasPrev = (page > 1);

    return {
      count,
      page,
      pages,
      hasNext,
      hasPrev,
      pageArray,
      perPage,

      setPerPage: function setPerPage(limit) {
        this.perPage.set(parseInt(limit, 10));
      }.bind(this),

      setPage: function setPage(page) {
        this.page.set(parseInt(page, 10));
      }.bind(this),

      nextPage: function nextpage() {
        if (page < pages) { this.page.set(page + 1); }
      }.bind(this),

      prevPage: function prevPage() {
        if (page > 1) { this.page.set(page - 1); }
      }.bind(this),
    };
  }

  sorting() {
    return {
      addSort: function addSort(key, direction) {
        let obj = this.sort.get();
        obj[key] = direction;
        this.sort.set(obj);
      }.bind(this),

      removeSort: function removeSort(key) {
        let obj = this.sort.get();
        delete obj[key];
        this.sort.set(obj);
      }.bind(this),

      clear: function clear() {
        this.sort.set({});
      }.bind(this),
    };
  }

  // #endregion
}
