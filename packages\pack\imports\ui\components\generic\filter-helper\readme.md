# FilterHelper

A class (and templates) to make adding filters more simple.

## Why?

Keeps templates + code clean.

Integrates with existing reactive vars.

No tracker setup.

## Included form elements

* Dropdown
* TextSearch
* @todo Switch / Toggle
* From date -> To date

## Use

### Create an instance

Create an instance of the helper class in your parent view.
Add a helper so that it can be accessed from the template.

```javascript
const filterHelper(
    Items,                  // Your Collection
    "items.storeOrCanStore" // A server method
);

Template.yourView.helpers({
    filterHelper: filterHelper
})
```

#### Define your filters

**key:** This must match the property in your collection that you wish to filter by (all).

**distinct:** The disinct values you wish to appear in the list (dropwdowns)

**filterText:** Text that appears before a filter is selected (dropdowns).

**filterSearchPlaceholder:** Placeholder text for search fields (dropdown and text-search).

```javascript
filterHelper.addFilters([
    // This is a dropdown
    {
        key: "location",
        filterText: "Storage Location",
        filterSearchPlaceholder: "Search locations...",
        distinct: SiteProfileService.storageLocations()
    },
    // This is a text search
    {
        key: "query",
        filterSearchPlaceholder: "Search..."
    },
]);
```

#### Add templates

If your filters will be nested within a child template then you need
to pass the filterHelper in as a parameter.

```html
<div>
    {{> chemStoreItemRow item=item filterHelper=filterHelper}}
</div>
```

Place the templates for the form elements in your code (you will have
to import these in your js file as normal). You must pass in a key
which matches one of the filters you set up earlier.

```html
<div class="four wide column">
    {{> filterHelperDropdown filterHelper=filterHelper key="location"}}
</div>
<div class="three wide column">
    {{> filterHelperTextSearch filterHelper=filterHelper key="query"}}
</div>
```

#### Initialize

Overview:

1) An initial query is passed in.
2) The query is used to initialize the subscription.
3) The query is stored.
4) When a defined filter changes value, the initial query is combined with
the filter values and then sent to the server.

If your intial query has reactive variables in it, any changes will be detected
by the helper and used in the combined filter query. This means that FilterHelper
can be incorporated into existing pages and use filters other than the ones provided.
**This means you don't have to set up your own tracker**

##### With Query

You can pass in an initial query to add other search criteria. The intial query should
be a method that returns a query object. This is crucial to making reactivity work
without manually setting up your own tracker. Example:

```javascript
const initialQuery = () => { 
    return {
      clientId, // Static value
      receiptCategory: template.receiptCategory.get(), // Reactive value
      limit: template.limit.get() // Reactive value
    }
};

// Initialize
filterHelper.init(initialQuery); 
```

##### Without Query

@todo. Don't think passing in a null value currently works. Workaround would be
passing in initial query method that returns empty object.

### Getting the data

```javascript
// Returns the collection (in this case Items)
// filters have been applied at this point.
const collection = filterHelper.filtered();

// You can perform additional local querying
// as normal using .find();
const results = collection.find({});

// Use the data in a template by adding a helper
Template.yourView.helpers({
    filterHelper: filterHelper,
    items() {
        return filterHelper.filtered().find();
    }
})
```
