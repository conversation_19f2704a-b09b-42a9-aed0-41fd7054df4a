import './scan-items.html';
import './scan-item';

import { Session } from 'meteor/session';
import { Template } from 'meteor/templating';

Template.modalScanItems.onCreated(function onCreated() {
  let template = this;
  template.itemIdList = new ReactiveVar([]);

  template.autorun(() => {
    const settings = Template.currentData().settings;
    const subscription = settings.subscription;
    const selector = settings.selector;
    template.itemIdList.set(Template.currentData().itemIds);

    template.combined = selector;
    template.combined[settings.key] = template.itemIdList.get();
    template.subscribe(subscription, template.combined);
  });
});

Template.modalScanItems.onRendered(function onRendered() {

});

Template.modalScanItems.helpers({
  itemList() {
    const template = Template.instance();
    const settings = Template.currentData().settings;
    const collection = settings.collection;

    const query = settings.query;
    query[settings.key] = { $in: template.itemIdList.get() };
    const items = collection.find(query).fetch();

    return items;
  },
});

Template.modalScanItems.events({

});
