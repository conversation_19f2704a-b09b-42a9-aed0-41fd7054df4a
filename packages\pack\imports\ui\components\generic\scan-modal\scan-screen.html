<template name="modalScanScreen">
    <div class="header">
        <div class="row">
            <div class="sixteen wide column">
                <div class="ui raised segment">
                    <div class="content">
                        <div class="ui big fluid icon input focus">
                            <input type="text" placeholder="{{settings.placeholder}}" name="receiptNo" id="receiptNo" autofocus readonly>
                            <i class="barcode icon"></i>
                        </div>
                        {{#if noScannedItem}}
                        <h2 class="ui grey header">
                            <i class="barcode icon"></i>
                            <div class=" content">
                                Awaiting Input...
                            </div>
                        </h2>
                    {{else}}
                        {{#if scannedItemIsInvalid}}
                            <h2 class="ui red header">
                                <i class="ban icon"></i>
                                <div class=" content">
                                    Invalid
                                </div>
                            </h2>
                        {{else}}
                            <h2 class="ui green header">
                                <i class="check icon"></i>
                                <div class=" content">
                                    {{scannedItem}} 
                                    {{#if itemAlreadyExists}}
                                        (Already Added)
                                    {{/if}}
                                </div>
                            </h2>
                        {{/if}}
                    {{/if}}
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="content">
        <div class="ui fluid container">
            <div class="row">
                <div class="sixteen wide column">
                    <div class="ui raised segment">
                        {{> modalScanItems itemIds=itemList settings=settings}}
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="actions">
        <div class="ui grid">
            <div class="row">
                <div class="ui eight wide column">
                    <button class="fluid ui big button" id="cancel-btn">Cancel</button>
                </div>
                <div class="ui eight wide column">
                    <button class="fluid ui big green button" id="action-btn">{{settings.actionName}}</button>
                </div>
            </div>
        </div>
    </div>
    
</template>