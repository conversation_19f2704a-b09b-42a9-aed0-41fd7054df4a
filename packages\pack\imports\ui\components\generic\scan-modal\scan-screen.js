import './scan-screen.html';
import '../../client-header';
import './scan-items';

import { Session } from 'meteor/session';
import { Template } from 'meteor/templating';

Template.modalScanScreen.onCreated(function onCreated() {
  const template = this;

  template.scannedItem = new ReactiveVar();
  template.scannedItemState = new ReactiveVar('none');
  template.itemList = new ReactiveVar([]);
  template.alreadyExists = new ReactiveVar(false);
});

Template.modalScanScreen.onRendered(function onRendered() {
  const template = this;

  // Workaround to stop onscreen keyboard
  const modal = document.getElementsByClassName('scan-modal')[0];
  const config = {
    attributes: true,
  };

  const callback = () => {
    if ($('.scan-modal').hasClass('active')) {
      Meteor.setTimeout(() => {
        $('#receiptNo').removeAttr('readonly').select();
      }, 600);
    }
    else {
      $('#receiptNo').attr('readonly', 'readonly');
    }
  };

  template.observer = new MutationObserver(callback);
  template.observer.observe(modal, config);
});

Template.modalScanScreen.onDestroyed(function onDestroyed() {
  this.observer.disconnect();
});

Template.modalScanScreen.helpers({
  itemList() {
    return Template.instance().itemList.get();
  },
  scannedItem() {
    return Template.instance().scannedItem.get();
  },
  scannedItemIsInvalid() {
    var scannedItemState = Template.instance().scannedItemState.get();
    if (scannedItemState === 'invalid') {
      return true;
    }
    return false;
  },
  noScannedItem() {
    var scannedItemState = Template.instance().scannedItemState.get();
    if (scannedItemState === 'none') {
      return true;
    }
    return false;
  },
  itemAlreadyExists() {
    return Template.instance().alreadyExists.get();
  },
});

Template.modalScanScreen.events({
  'change #receiptNo': function onChange(event, templateInstance) {
    templateInstance.alreadyExists.set(false);
    event.preventDefault();
    const field = $(event.currentTarget);
    const val = field.val();
    const dto = {
      receiptNo: val,
    };

    const settings = Template.currentData().settings;
    const collection = settings.collection;
    const query = {
      isPacked: false,
      receiptNo: { $in: [val] },
    };

    const notPacked = (collection.find(query).count() > 0);
    if ((notPacked === true)) {
      templateInstance.scannedItem.set(val);
      templateInstance.scannedItemState.set('ok');
      field.val('');
      addToList(val, templateInstance);
    } else {
      field.select();
      templateInstance.scannedItemState.set('invalid');
    }
  },
  'click .del-button': function onClick(event, templateInstance) {
    const btn = $(event.currentTarget);
    const id = btn.data().id;
    const currentIds = templateInstance.itemList.get();
    const newIds = currentIds.filter((item) => item !== id);
    templateInstance.itemList.set(newIds);
  },
  'click #action-btn': function onClick() {
    const settings = Template.currentData().settings;
    const currentIds = Template.instance().itemList.get();

    // Reset Everything
    const template = Template.instance();
    template.scannedItem.set(null);
    template.scannedItemState.set('none');
    template.itemList.set([]);
    template.alreadyExists = new ReactiveVar(false);

    settings.approve(currentIds);
  },
  'click #cancel-btn': function onClick() {
    // Reset Everything
    const template = Template.instance();
    template.scannedItem.set(null);
    template.scannedItemState.set('none');
    template.itemList.set([]);
    template.alreadyExists = new ReactiveVar(false);

    // Call cancel callback
    const settings = Template.currentData().settings;
    settings.cancel();
  },
});

const addToList = (item, templateInstance) => {
  const currentList = templateInstance.itemList.get();
  if (currentList.some((i) => i === item)) {
    templateInstance.alreadyExists.set(true);
  }
  currentList.push(item);
  templateInstance.itemList.set(currentList);
};
