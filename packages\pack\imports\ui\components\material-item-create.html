<template name="materialItemCreate">

    <style>
        .ui.form .field > label.label-style {color:#2185d0}
        .ui.form .field > .checkbox > label.label-style {color:#2185d0}
    </style>

    <div class="content">
        <div class="ui container">
            <div class="ui middle aligned very relaxed stackable grid">
                <div class="twelve wide column">
                    <div data-class="sixteen wide computer eight wide large screen eight wide widescreen column" style="margin-bottom: 10px;">
                        <div class="ui form">
                            <div class="three fields">
                                <div class="six wide field required">
                                        <label class="label-style">Operator</label>
                                            <div class="ui fluid search selection dropdown" id="operatorDropdown">
                                                <input type="hidden" name="offshoreClientDropdown" value="{{selectedItem.offshoreClientDropdown}}">
                                                <i class="dropdown icon"></i>
                                                <div class="default text" style="padding-left:0.8em;">Operator</div>
                                                <div class="menu">
                                                    {{#each clients}}
                                                    <div class="item" data-value="{{name}}" data-text="{{name}}">
                                                        {{name}}
                                                    </div>
                                                    {{/each}}
                                                </div>
                                            </div>
                                            <input style="display: none;" type="text" name="offshoreClientText" value="{{selectedItem.offshoreClient}}" placeholder="Operator e.g. NAM">
                                    
                                <div class="field">
                                    <div class="ui checkbox" id="cannotFindClientCheckbox" style="padding-top: 5%;">
                                        <input type="checkbox" name="cannotFindClientCheckbox">
                                        <label class="label-style">Can't find the correct Operator?</label>
                                    </div>
                                </div>

                                </div>
                                <div class="six wide field required">
                                        <label class="label-style">Offshore Location</label>
                                        <input type="text" name="offshoreLocation" value="{{selectedItem.offshoreLocation}}" placeholder="Offshore Location e.g. L15A">
                                </div>
                                <div class="four wide field">
                                        <div class="ui checkbox" id="isWasteCheckbox">
                                            <input type="checkbox" name="isWasteCheckbox">
                                            <label class="label-style">Waste</label>
                                        </div>
                                </div>
                            </div>

                            <div class="three fields">
                                    <div class="six wide field required">
                                        <label class="label-style">CCU</label>
                                        <input type="text" name="ccu" value="{{selectedItem.ccu}}" placeholder="CCU Number">
                                    </div>
                                    <div class="six wide field required">
                                        <label class="label-style">Voyage No.</label>
                                        <input type="text" name="voyageNo" value="{{selectedItem.voyageNo}}" placeholder="Voyage Number">
                                    </div>
                                    <div class="four wide field">
                                        <div class="ui checkbox" id="isMarinePollutantCheckbox">
                                            <input type="checkbox" name="isMarinePollutantCheckbox">
                                            <label class="label-style">Marine Pollutant</label>
                                        </div>
                                    </div>
                            </div>

                            <div class="three fields">
                                    <div class="six wide field">
                                        <label class="label-style">IMO Hazard Class</label>
                                        <input type="text" name="imoHazardClass" value="{{selectedItem.imoHazardClass}}" placeholder="Hazard Class">
                                    </div>
                                    <div class="six wide field">
                                        <label class="label-style">IMO Hazard Subclass</label>
                                        <input type="text" name="imoSubClass" value="{{selectedItem.imoSubClass}}" placeholder="Subclass">
                                    </div>
                            </div>
        
                            <div class="three fields">
                                <div class="six wide field">
                                    <label class="label-style">Eural / EWC Code</label>
                                    <input type="text" name="euralCode" value="{{selectedItem.euralCode}}" placeholder="Eural EWC Code">
                                </div>
                                <div class="six wide field">
                                        <label class="label-style">UN No.</label>
                                        <input type="text" name="unNo" value="{{selectedItem.unNo}}" placeholder="UN No">
                                </div>
                            </div>


                            <div class="three fields">
                                <div class="six wide field required">
                                    <label class="label-style">Package Type</label>
                                    <div class="receiptForm ui fluid search selection dropdown" id="packageTypeDropdown">
                                        <input type="hidden" name="packageType" value="">
                                        <i class="dropdown icon"></i>
                                        <div class="default text" style="padding-left: 0.8em;">Package Type</div>
                                        <div class="menu">
                                            {{#each packageTypes}}
                                            <div class="item" data-value="{{name}}" data-text="{{name}}">
                                                {{name}}
                                            </div>
                                            {{/each}}
                                        </div>
                                    </div>
                                </div>
                                <div class="three wide field required">
                                    <label class="label-style">Qty</label>
                                    <input type="number" name="quantity" min="1" step="1" value="{{selectedItem.quantity}}" />
                                </div>
                                <div class="three wide field ">
                                    <label class="label-style">Weight (Kg)</label>
                                    <input type="number" name="weightKg" min="0.0" step="any" value="{{selectedItem.weightKg}}" />
                                </div>
                            </div>


                            <div class="one fields">
                                <div class="sixteen wide field required">
                                    <label class="label-style">Material Description</label>
                                    <input class="red input" type="text" name="materialDescription" value="{{selectedItem.description}}" placeholder="Material Description">
                                </div>
                            </div>
                            <div class="one fields">
                                <div class="sixteen wide field">
                                    <label class="label-style" >Waste Description</label>
                                    <input type="text" name="wasteDescription" value="{{selectedItem.wasteDescription}}" placeholder="Waste Description">
                                </div>
                            </div>

                            <div class="fields">
                                <div class="sixteen wide field">
                                  <label class="label-style">Material NCRs</label>
                                  <div class="ui fluid multiple selection dropdown" id="ncrsDropdown">
                                    <input name="ncrs" type="hidden" value="">
                                    <i class="dropdown icon"></i>
                                    <div class="default text" style="margin-left: 0.642857em;">Select NCR(s)</div>
                                    <div class="menu">
                                      {{#each ncrs}}
                                      <div class="item" data-value="{{name}}" data-text="{{name}}">
                                        {{name}}
                                      </div>
                                      {{/each}}
                                    </div>
                                  </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>

            </div>
        </div>


        <div class="ui divider"></div>

        <div class="ui grid">
            <div class="four wide column">

                <button class="ui primary left labeled icon button disabled js-item-save-edit">
            <i class="left save icon"></i>
            Save
            </button>
            </div>
            <div class="twelve wide column ">
                <div class="ui positive message hidden" id="updatesSavedMessageDiv">
                    <i class="close icon"></i>
                    <div class="header">
                        Item created and receipted
                    </div>
                    <p>Receipt No: {{createdItemReceiptNo}}</p>
                </div>

                <div class="ui negative message hidden" id="errorOnSaveMessageDiv">
                    <i class="close icon"></i>
                    <div class="header">
                        Failed to create and receipt material item.
                    </div>
                    <p>Created item has not been saved; please contact application support.</p>
                </div>
            </div>
        </div>
    </div>

    {{> materialItemDetailsModal selectedItem=getCreatedItem}}

</template>
