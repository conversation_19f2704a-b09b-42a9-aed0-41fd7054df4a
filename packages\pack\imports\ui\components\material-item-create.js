import './material-item-create.html';
import './material-item-details-modal';
import { Template } from 'meteor/templating';
import { CreateAndReceiptItem } from '../../api/items/commands/create-and-receipt-item';
import { SiteProfileService } from
  '../services/company-site-profiles/site-profile.service';
import { MaterialItemDetailsModalMethods } from '../components/material-item-details-modal.methods';

const strToArray = (str) => (str && str.length ? str.split(',') : []);

function isAllRequiredDataSet(templateInstance) {
  const packageType = templateInstance.$('[name=packageType]').val();
  const quantityVal = templateInstance.$('[name=quantity]').val();
  // For now allow submission if weightKg is not entered for consistency with main receipt screen.
  const materialDescription = templateInstance.$('[name=materialDescription]').val(); // material Description
  const ccu = templateInstance.$('[name=ccu]').val();
  const offshoreClient = templateInstance.$('[name=offshoreClientText]').val();
  const offshoreClientDropdown = templateInstance.$('[name=offshoreClientDropdown]').val();
  const offshoreLocation = templateInstance.$('[name=offshoreLocation]').val();
  const voyageNo = templateInstance.$('[name=voyageNo]').val();

  return packageType &&
         materialDescription &&
         ccu &&
         (offshoreClient || offshoreClientDropdown) &&
         offshoreLocation &&
         voyageNo &&
         quantityVal;
}

function saveItem(templateInstance, originalItem, onComplete) {
  const quantityVal = templateInstance.$('[name=quantity]').val();
  let quantity = null;
  if (quantityVal) {
    quantity = parseInt(quantityVal, 10);
  }

  const weightKgVal = templateInstance.$('[name=weightKg]').val();
  let weightKg = null;
  if (weightKgVal) {
    weightKg = parseFloat(weightKgVal);
  }

  const materialDescription = templateInstance.$('[name=materialDescription]').val(); // material Description
  const wasteDescription = templateInstance.$('[name=wasteDescription]').val();
  const packageType = templateInstance.$('[name=packageType]').val();
  const offshoreLocation = templateInstance.$('[name=offshoreLocation]').val();
  let offshoreClient;
  const ccu = templateInstance.$('[name=ccu]').val();
  const voyageNo = templateInstance.$('[name=voyageNo]').val();
  const imoHazardClass = templateInstance.$('[name=imoHazardClass]').val();
  const imoSubClass = templateInstance.$('[name=imoSubClass]').val();
  const euralCode = templateInstance.$('[name=euralCode]').val();
  const unNo = templateInstance.$('[name=unNo]').val();
  const ncrs = strToArray(templateInstance.$('[name=ncrs]').val());

  if (templateInstance.$('#cannotFindClientCheckbox').checkbox('is checked')) {
    offshoreClient = templateInstance.$('[name=offshoreClientText]').val();
  } else {
    offshoreClient = templateInstance.$('[name=offshoreClientDropdown]').val();
  }
  let isWaste = false;

  if (templateInstance.$('#isWasteCheckbox').checkbox('is checked')) {
    isWaste = true;
  }

  let isMarinePollutant = false;
  if (templateInstance.$('#isMarinePollutantCheckbox').checkbox('is checked')) {
    isMarinePollutant = true;
  }

  const newValues = {
    packageType,
    quantity,
    weightKg,
    description: materialDescription,
    wasteDescription,
    isWaste,
    isMarinePollutant,
    offshoreLocation,
    offshoreClient,
    ccu,
    voyageNo,
    imoHazardClass,
    imoSubClass,
    euralCode,
    unNo,
    ncrs,
  };
  CreateAndReceiptItem.call(newValues, onComplete);
}

Template.materialItemCreate.onCreated(function onCreate() {
  const template = this;
  template.createdItem = new ReactiveVar(null);
});

Template.materialItemCreate.onRendered(function onRendered() {
  const template = this;

  // Initialise PackageType and NCRs Dropdowns
  template.$('#packageTypeDropdown').dropdown();
  template.$('#ncrsDropdown').dropdown();
  template.$('#operatorDropdown').dropdown();

  template.$('#packageTypeDropdown')
    .dropdown('set selected', template.data.selectedItem.packageType);

  // Initialise checkboxes
  $('.ui.checkbox').checkbox();

  if (template.data.selectedItem.isWaste) {
    template.$('#isWasteCheckbox').checkbox('set checked');
  }

  if (template.data.selectedItem.marinePollutant) {
    template.$('#isMarinePollutantCheckbox').checkbox('set checked');
  }

  template.initialisationComplete = true;
});

Template.materialItemCreate.events({
  'click .js-item-save-edit': () => {
    const templateInstance = Template.instance();
    const item = Template.instance().data.selectedItem;
    saveItem(Template.instance(), item, (error, result) => {
      if (error) {
        // Show Message box to confirm updates have been saved.
        $('#errorOnSaveMessageDiv').transition('fade in');

        // Reset Save button to disabled.
        $('.js-item-save-edit').addClass('disabled');
      } else { // Success.
        templateInstance.createdItem.set(result);

        // Show Message box to confirm updates have been saved.
        $('#updatesSavedMessageDiv').transition('fade in');

        // Reset Save button to disabled.
        $('.js-item-save-edit').addClass('disabled');

        // Open details dialog to allow printing of label.
        MaterialItemDetailsModalMethods.init(templateInstance.createdItem.get()).show();
      }
    });
  },
  'change input, keyup input, click .ui.calendar, change textarea, keyup textarea': (event, templateInstance) => {
    if (templateInstance.initialisationComplete) {
      if (isAllRequiredDataSet(templateInstance)) {
        templateInstance.$('.js-item-save-edit').removeClass('disabled');
      } else {
        templateInstance.$('.js-item-save-edit').addClass('disabled');
      }

      if ($('#updatesSavedMessageDiv').hasClass('visible')) {
        $('#updatesSavedMessageDiv').closest('.message').transition('fade out');
      }
      if ($('#errorOnSaveMessageDiv').hasClass('visible')) {
        $('#errorOnSaveMessageDiv').closest('.message').transition('fade out');
      }
    }
  },
  'click .message': (event) => {
    $(event.target)
      .closest('.message')
      .transition('fade out');
  },
  'click .receiptForm.ui.dropdown .remove.icon': function clearDropdown(event, templateInstance) {
    templateInstance.$(event.target).parent('.dropdown').dropdown('clear');
    event.stopPropagation();
  },

  'click #cannotFindClientCheckbox': (event, templateInstance) => {
    var cantFindClient = templateInstance.$('#cannotFindClientCheckbox').checkbox('is checked');
    if (cantFindClient) {
      $('[name=offshoreClientText]').show();
      $('#operatorDropdown').hide();
    } else {
      $('[name=offshoreClientText]').hide();
      $('#operatorDropdown').show();
    }
  },
});

Template.materialItemCreate.helpers({
  packageTypes: () => SiteProfileService.packageTypes(),
  ncrs: () => SiteProfileService.ncrs(),
  labelStyle: () => 'style="color:#2185d0"',
  createdItemReceiptNo: () => (Template.instance().createdItem.get() && Template.instance().createdItem.get().receiptNo) || '',
  getCreatedItem: () => Template.instance().createdItem.get(),
  clients: () => SiteProfileService.offshoreClients(true),
});
