<template name="materialItemDetailsModal">
    <div class="ui modal item-details-modal" id="itemDetailsModal">
        <i class="close icon"></i>
        {{#if isDetailsMode}}
           {{> materialItemDetails selectedItem=selectedItem showAudit=showAudit}}
        {{else}}
           {{> materialItemEdit selectedItem=selectedItem}}
        {{/if}}
        <div class="actions">
            {{#if (allowEdit(selectedItem)) }} 
                {{#if allowEditItemInModal }}
                    {{#if isDetailsMode}}
                    <div class="ui right labeled icon button js-item-edit-button">
                        EDIT
                        <i class="edit icon"></i>
                    </div>
                    {{else}}
                    <div class="ui right labeled icon button js-item-details-button">
                        DETAILS
                        <i class="undo icon"></i>
                    </div>
                    {{/if}}
                {{else}}
                    <!-- Note 'ok' class - needed to make this work -->
                    <div class="ui ok right labeled icon button js-item-edit-button">
                        EDIT
                        <i class="edit icon"></i>
                    </div> 
                {{/if}}
            {{/if}}
            <div class="ui ok right labeled icon button ok-button">
               OK
               <i class="checkmark icon"></i>
            </div>
        </div>
    </div>
</template>
