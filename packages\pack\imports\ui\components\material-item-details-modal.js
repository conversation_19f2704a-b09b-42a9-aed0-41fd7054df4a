
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import './material-item-details-modal.html';
import { $ } from 'meteor/jquery';
import { ReceiptTypes } from '../../api/items/receipt.types';

// Components used inside the template.
import './material-item-details';
import './material-item-edit';

const Mode = {
  details: 'details',
  edit: 'edit',
};

Template.materialItemDetailsModal.onCreated(function onCreated() {
  const template = this;
  template.mode = new ReactiveVar(Mode.details);

  // Disabled for now - item details will need to be made reactive before enabling this.
  template.allowEditItemInModal = false;
});

Template.materialItemDetailsModal.onRendered(() => {
  const template = this;
});

Template.materialItemDetailsModal.events({
  'click .js-item-edit-button': function onClick(event, templateInstance) {
    if (templateInstance.allowEditItemInModal) {
      // console.log('Item-Details Modal - Item Edit clicked');
      if (templateInstance.mode.get() === Mode.details) {
        templateInstance.mode.set(Mode.edit);
      } else {
        templateInstance.mode.set(Mode.details);
      }
    }
  },
});

Template.materialItemDetailsModal.helpers({
  currentMode: function getCurrentMode() {
    return Template.instance().mode.get();
  },
  isEditMode: function getIsEditMode() {
    return Template.instance().mode.get() === Mode.edit;
  },
  isDetailsMode: function getIsDetailsMode() {
    return Template.instance().mode.get() === Mode.details;
  },
  allowEditItemInModal: function getAllowEditItemInModal() {
    return Template.instance().allowEditItemInModal;
  },
  allowEdit(selectedItem) {
    // Only allow material item to be edited if it has been receipted (i.e. removed from CCU).
    return selectedItem && selectedItem.receiptType === ReceiptTypes.chemReceipt;
  },
  showAudit() {
    return Template.instance().data.showAudit;
  },
});
