import { BaseModalMixin } from '../modal/base-modal-mixin';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';

export const MaterialItemDetailsModalMethods = Object.assign({
  className: '.ui.modal.item-details-modal',

  init(item) {
    const existingModals = document.getElementsByClassName('ui modal item-details-modal');
    
    console.log(`Initing Modal. Pre-existing modals: ${existingModals.length}`);

    let openItemEditScreen = false;

    $(this.className).modal({
      allowMultiple: false,
      onApprove: (element) => {
        if (element.hasClass('js-item-edit-button')) {
          openItemEditScreen = true;
        }
        if(element.hasClass('ok-button')){
          $('#audit-trail').transition('fade out');
        }
      },
      onHidden: () => {
        if (openItemEditScreen) {
          FlowRouter.go('materialItemEditPage',
            {
              clientId: FlowRouter.getParam('clientId'),
              itemId: item._id,
            }, {});
        }
        $('#audit-trail').transition('fade out');
      },
    }).modal('hide all');

    return this;
  },
}, BaseModalMixin);
