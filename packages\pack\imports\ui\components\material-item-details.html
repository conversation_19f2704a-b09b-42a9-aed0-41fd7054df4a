<template name="materialItemDetails">


  <div class="ui clearing segment">
  <h1 class="ui left floated header">Material Item Details: {{selectedItem.receiptNo}}  </h1>
  {{#if selectedItem.cargoItemId}} <!-- otherwise was manual entry of material item - with no ccu from Flow -->
    <h2 class="ui left floated header">CCU Received: {{receivedAtFormatted}} </h2>
  {{/if}}
  </div>
  {{#if selectedItem.materialReceiptDateTime}}
  <div class="ui clearing segment">
    <h2 class="ui left floated header">Material Receipted: {{receiptedAtFormatted}} </h2>
  </div>
  {{/if}}

    <div class="content">
         <div class="ui container">
            <div class="ui middle aligned very relaxed stackable grid">
                <div class="eleven wide column">
                    <div class="ui form">

                        <div class="three fields">
                            <div class="six wide field">
                                    <label>Operator</label>
                                    <input type="text" readonly="" name="offshoreClient" value="{{selectedItem.offshoreClient}}">
                            </div>
                            <div class="six wide field">
                                <label>Offshore Location</label>
                                <input type="text" readonly="" name="offshoreLocation" value="{{selectedItem.offshoreLocation}}">
                            </div>
                             <div class="four wide field">
                                <div class="ui checkbox">
                                    {{#if selectedItem.isWaste}}
                                        <input type="checkbox" readonly="" disabled="disabled" id="isWasteCheckbox" name="isWasteCheckbox" checked="checked">
                                    {{else}}
                                        <input type="checkbox" readonly="" disabled="disabled" id="isWasteCheckbox" name="isWasteCheckbox">
                                    {{/if}}
                                    <label>Waste</label>
                                </div>
                            </div>
                            
                            <div class="four wide field">
                                    <div class="ui checkbox">
                                        {{#if selectedItem.marinePollutant}}
                                        <input type="checkbox" readonly="" disabled="disabled" id="isMarinePollutantCheckbox" name="isMarinePollutantCheckbox" checked="checked">
                                        {{else}}
                                        <input type="checkbox" readonly="" disabled="disabled" id="isMarinePollutantCheckbox" name="isMarinePollutantCheckbox">
                                        {{/if}}
                                        <label>Marine Pollutant</label>
                                    </div>
                            </div>
                        </div>

                        <div class="three fields">
                            <div class="six wide field">
                                <label>CCU</label>
                                <input type="text" readonly="" name="ccu" value="{{selectedItem.ccu}}">
                            </div>
                            <div class="six wide field">
                                <label>Voyage No.</label>
                                <input type="text" readonly="" name="voyageNo" value="{{selectedItem.voyageNo}}">
                            </div>
                            <div class="six wide field">
                                <label>Mat. Manifest No.</label>
                                <input type="text" readonly="" name="materialManifestNo" value="{{selectedItem.materialManifestNo}}">
                            </div>
                        </div>

                        <div class="three fields">
                            <div class="six wide field">
                                <label>IMO Hazard Class</label>
                                <input type="text" readonly="" name="imoHazardClass" value="{{selectedItem.imoHazardClass}}">
                            </div>
                            <div class="six wide field">
                                <label>IMO Hazard Subclass</label>
                                <input type="text" readonly="" name="imoSubClass" value="{{selectedItem.imoSubClass}}">
                            </div>
                        </div>

                        <div class="three fields">
                            <div class="six wide field">
                              <label>Eural / EWC Code</label>
                              <input type="text" readonly="" name="euralCode" value="{{selectedItem.euralCode}}">
                            </div>
                            <div class="six wide field">
                                  <label>UN No.</label>
                                  <input type="text" readonly="" name="unNo" value="{{selectedItem.unNo}}">
                            </div>
                          </div>

                        <div class="three fields">
                            <div class="six wide field">
                                <label>Package Type</label>
                                <input type="text" readonly="" name="packingUnit" value="{{selectedItem.packageType}}">
                            </div>
                            <div class="three wide field">
                                <label>Qty</label>
                                <input type="text" readonly="" name="quantity" value="{{selectedItem.quantity}}">
                            </div>
                            <div class="three wide field">
                                <label>Weight (kg)</label>
                                <input type="text" readonly="" name="weightKg" value="{{selectedItem.weightKg}}">
                            </div>
                        </div>

                        <div class="field">
                            <label>Material Description</label>
                            <input type="text" readonly="" name="sublocation" value="{{materialDescriptionUnescaped}}">
                        </div>

                        <div class="field">
                            <label>Waste Description</label>
                            <input type="text" readonly="" name="sublocation" value="{{wasteDescriptionUnescaped}}">
                        </div>
                    </div>
                </div>
                <div class="five wide column">
                    <div class="ui clearing segment">
                    <div class="ui form">
                        <div class="field">
                                <label>Stored Date</label>
                                <input type="text" readonly="" name="storedDate" value="{{storedDateFormatted}}">
                        </div>
                        <div class="field">
                                <label>Stored Location</label>
                                <input type="text" readonly="" name="comments" value="{{storedLocationFormatted}}">
                        </div>
                        <div class="field">
                                <label>Packed Date</label>
                                <input type="text" readonly="" name="storedDate" value="{{packedDateFormatted}}">
                        </div>
                        <div class="field">
                                <label>Packed Into</label>
                                <input type="text" readonly="" name="comments" value="{{selectedItem.packingUnitIdentifier}}">
                        </div>
                         <div class="field">
                                <label>Manifest No</label>
                                <input type="text" readonly="" name="comments" value="{{selectedItem.manifestNo}}">
                        </div>
                        <div class="field">
                                <label>Dispatch Date</label>
                                <input type="text" readonly="" name="storedDate" value="{{dispatchDateFormatted}}">
                        </div>
                    </div>
                </div>
                </div>
            </div>
        </div>

        <div class="ui divider"></div>

        {{#if showPrintButton}}
        <button class="ui primary left labeled icon button js-item-print-label">
          <i class="left barcode icon"></i>
          Print Label
        </button>
        <button class="ui primary left labeled icon button js-item-download-label">
          <i class="left download icon"></i>
          Download Label
        </button>
        {{/if}}

        {{#if showAuditTrail}}
            <a class="audit-button" style="float:right" href="#" >View Audit Data</a>
            <div class="ui divider"></div>
            <div id="audit-trail" class="ui container" style="display:none">
                <div class="ui middle aligned very relaxed stackable grid">
                    <div class="sixteen wide column">
                        <div data-class="sixteen wide computer eight wide large screen eight wide widescreen column" style="margin-bottom: 10px;">
                            <div class="ui form">
                                {{#each event in selectedItem.events}}
                                    {{#if isEventEdit event}}
                                        <div class="six fields">
                                            <div class="four wide field">
                                                <label style="color:#2185d0">Username</label>
                                                <label> {{event.createdBy}} </label>
                                            </div>
                                            <div class="six wide field">
                                                <label style="color:#2185d0">Updated On</label>
                                                <label> {{createdAtFormatted}}</label>
                                            </div>
                                            <div class="sixteen wide field">
                                                <label style="color:#2185d0">Details Changed</label>
                                                <ul>
                                                    {{#each edit in event.edits}}
                                                        <li>{{getFormattedPropertyName edit.key}} was changed from {{convertToString edit.from}} to {{convertToString edit.to}}</li>
                                                    {{/each}}
                                                </ul>
                                            </div>
                                        </div>
                                    {{/if}}
                                {{/each}}
                            </div> 
                        </div>
                    </div>
                </div>
            </div>
        {{/if}}

    </div>
</template>
