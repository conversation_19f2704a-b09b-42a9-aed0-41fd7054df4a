import "./material-item-details.html";
import * as Constants from "../../shared/lib/constants";
import { ChangePrinterModalMethods } from "../views/printing/change-printer-modal.methods";
import { CompanySiteProfiles } from "../../api/company-site-profiles/company-site-profiles";
import { FlowRouter } from "meteor/ostrio:flow-router-extra";
import { PrintingService } from "../services/printing/printing.service";
import { Session } from "meteor/session";
import { Template } from "meteor/templating";
import { ZplLabelService } from "../zpl/label";
import { _ } from "meteor/underscore";
import { labelUtils } from "../pdf/label";
import { moment } from "meteor/momentjs:moment";
import { ReceiptTypes } from "../../api/items/receipt.types";

const printItem = (item) => {
  ChangePrinterModalMethods.showAndWaitForPrinterSelection(() => {
    const activePrinter = PrintingService.getUsersActivePrinter();
    ZplLabelService.printBarcodeLabels([item], activePrinter.printerIp);
  });
};

Template.materialItemDetails.events({
  "click .js-item-print-label": (event, templateInstance) => {
    event.preventDefault();
    printItem(templateInstance.data.selectedItem);
  },
  "click .js-item-download-label": (event, templateInstance) => {
    event.preventDefault();

    const item = templateInstance.data.selectedItem;
    if (!item) {
      console.error("No item found for download");
      return;
    }

    const logoUrl = "/images/client-logos/peterson-sel.png";
    const title = `Receipt - ${item.identifier || item.receiptNo || "Item"}`;
    const barcodeNumber = item.identifier || item.receiptNo || item._id;

    labelUtils
      .createLabel(item, title, barcodeNumber, logoUrl, false)
      .then((doc) => {
        const timestamp = moment().format("YYYY-MM-DD-HHmm");
        const filename = `Receipt_${barcodeNumber}_${timestamp}.pdf`;
        doc.save(filename);
      })
      .catch((error) => {
        console.error("Failed to generate receipt PDF:", error);
      });
  },
});

Template.materialItemDetails.events({
  "click .js-item-edit": (event, templateInstance) => {
    // TODO: Remove screen darken from modal
    Session.set("selectedItemForEdit", templateInstance.data.selectedItem);

    // pathdef, params, queryparams.
    FlowRouter.go(
      "materialItemEditPage",
      {
        clientId: FlowRouter.getParam("clientId"),
        itemId: templateInstance.data.selectedItem._id,
      },
      {}
    );
  },
  "click .audit-button": function onClick(event, templateInstance) {
    if ($("#audit-trail").hasClass("visible")) {
      $("#audit-trail").transition("fade out");
    } else {
      $("#audit-trail").transition("fade in");
    }
  },
});

Template.materialItemDetails.helpers({
  receivedAtFormatted() {
    let retval = null;
    const selItem = Template.instance().data.selectedItem;
    if (selItem && selItem.receivedDate) {
      retval = moment(selItem.receivedDate).format(
        Constants.DISPLAY_DATETIME_FORMAT
      );
    }
    return retval;
  },
  receiptedAtFormatted() {
    let retval = null;
    const selItem = Template.instance().data.selectedItem;
    if (selItem && selItem.materialReceiptDateTime) {
      retval = moment(selItem.materialReceiptDateTime).format(
        Constants.DISPLAY_DATETIME_FORMAT
      );
    }
    return retval;
  },
  storedDateFormatted() {
    let retval = null;
    const selItem = Template.instance().data.selectedItem;
    if (selItem && selItem.storedDate) {
      retval = moment(selItem.storedDate).format(Constants.DISPLAY_DATE_FORMAT);
    }
    return retval;
  },
  packedDateFormatted() {
    let retval = null;
    const selItem = Template.instance().data.selectedItem;
    if (selItem && selItem.packedDate) {
      retval = moment(selItem.packedDate).format(Constants.DISPLAY_DATE_FORMAT);
    }
    return retval;
  },
  dispatchDateFormatted() {
    let retval = null;
    const selItem = Template.instance().data.selectedItem;
    if (selItem && selItem.dispatchedDate) {
      retval = moment(selItem.dispatchedDate).format(
        Constants.DISPLAY_DATE_FORMAT
      );
    }
    return retval;
  },
  storedLocationFormatted() {
    const selItem = Template.instance().data.selectedItem;
    if (selItem && selItem.location && selItem.subLocation) {
      return `${selItem.location} - ${selItem.subLocation}`;
    } else if (selItem && selItem.location) {
      return selItem.location;
    }
    return null;
  },
  showPrintButton() {
    // Don't show print button if item has not been receipted (i.e is still in the CCU)
    const selItem = Template.instance().data.selectedItem;
    let showIt = false;
    if (selItem) {
      showIt = selItem.receiptType != ReceiptTypes.chemPreReceipt;
    }
    return showIt;
  },
  materialDescriptionUnescaped() {
    // Material descriptions can come html encoded from Flow e.g. "<<" as "&gt;&gt;"
    let retval = null;
    const selItem = Template.instance().data.selectedItem;
    if (selItem && selItem.description) {
      retval = _.unescape(selItem.description)
        .replace(/&GT;/gi, ">")
        .replace(/&LT;/gi, "<")
        .replace(/&gt/gi, ">")
        .replace(/&lt/gi, "<");
    }
    return retval;
  },
  wasteDescriptionUnescaped() {
    let retval = null;
    const selItem = Template.instance().data.selectedItem;
    if (selItem && selItem.wasteDescription) {
      retval = _.unescape(selItem.wasteDescription)
        .replace(/&GT;/gi, ">")
        .replace(/&LT;/gi, "<")
        .replace(/&gt/gi, ">")
        .replace(/&lt/gi, "<");
    }
    return retval;
  },
  isEventEdit(event) {
    return event.eventType == "EDITED";
  },
  createdAtFormatted(date) {
    return moment(date).format(Constants.DISPLAY_DATETIME_FORMAT_COMPACT);
  },
  getFormattedPropertyName(elementName) {
    var dictionary = {
      offshoreClient: "Operator",
      offshoreLocation: "Offshore Location",
      isWaste: "Waste",
      marinePollutant: "Marine Pollutant",
      ccu: "CCU",
      voyageNo: "VoyageNo",
      materialManifestNo: "Mat. Manifest No.",
      imoHazardClass: "IMO Hazard Class",
      imoSubClass: "IMO Hazard SubClass",
      euralCode: "Eural/EWC Code",
      unNo: "UN No.",
      packageType: "Package Type",
      quantity: "Qty",
      weightKg: "Weight (Kg)",
      description: "Material Description",
      wasteDescription: "Waste Description",
    };
    return dictionary[elementName];
  },
  convertToString(val) {
    return val + "";
  },
  showAuditTrail() {
    var templateInstance = Template.instance();
    return (
      templateInstance.data &&
      templateInstance.data.showAudit &&
      templateInstance.data.selectedItem &&
      templateInstance.data.selectedItem.events.some(
        (x) => x.eventType == "EDITED"
      )
    );
  },
});
