<template name="materialItemEdit">
    <div class="ui clearing segment">
        <div class="ui grid">
            <div class="seven wide column">
                <h1 class="ui left floated header">Receipt No: </h1>

                <div class="ui big input ">
                    <input type="text" readonly="" name="receiptNo" size="15" value="{{selectedItem.receiptNo}}">
                </div>

            </div>
            <div class="nine wide column">
                <h1 class="ui left floated header">CCU Received:</h1>
                <div class="ui calendar" id="receivedDatepicker">
                    <div class="ui disabled big input">
                        <input type="text" readonly="" size="18" placeholder="Date" name="receivedAt" />
                    </div>
                </div>
            </div>
            <div class="nine wide column">
                <h1 class="ui left floated header">Material Receipted:</h1>
                <div class="ui calendar" id="materialReceiptedDatepicker">
                    <div class="ui disabled big input">
                        <input type="text" readonly="" size="18" placeholder="Date" name="materialReceiptedAt" />
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="content">
        <div class="ui container">
            <div class="ui middle aligned very relaxed stackable grid">
                <div class="twelve wide column">
                    <div data-class="sixteen wide computer eight wide large screen eight wide widescreen column" style="margin-bottom: 10px;">
                        <div class="ui form">
                            <div class="four fields">

                                <div class="six wide field">
                                        <label style="color:#2185d0">Operator</label>
                                        <input type="text" name="offshoreClient" value="{{selectedItem.offshoreClient}}">
                                </div>
                                <div class="six wide field">
                                        <label style="color:#2185d0">Offshore Location</label>
                                        <input type="text" name="offshoreLocation" value="{{selectedItem.offshoreLocation}}">
                                </div>
                                <div class="four wide field">
                                        <div class="ui checkbox">
                                            <label style="color:#2185d0">Waste</label>
                                            {{#if selectedItem.isWaste}}
                                            <input type="checkbox" id="isWasteCheckbox" name="isWasteCheckbox" checked="checked">
                                            {{else}}
                                            <input type="checkbox" id="isWasteCheckbox" name="isWasteCheckbox">
                                            {{/if}}
                                        </div>
                                    </div>
                                    <div class="four wide field">
                                            <div class="ui checkbox">
                                                <label style="color:#2185d0">Marine Pollutant</label>
                                                {{#if selectedItem.marinePollutant}}
                                                <input type="checkbox" id="isMarinePollutantCheckbox" name="isMarinePollutantCheckbox" checked="checked">
                                                {{else}}
                                                <input type="checkbox" id="isMarinePollutantCheckbox" name="isMarinePollutantCheckbox">
                                                {{/if}}
                                            </div>
                                    </div>
                                

                            </div>

                            <div class="three fields">
                                    <div class="six wide field">
                                        <label style="color:#2185d0">CCU</label>
                                        <input type="text" name="ccu" value="{{selectedItem.ccu}}">
                                    </div>
                                    <div class="six wide field">
                                        <label style="color:#2185d0">Voyage No.</label>
                                        <input type="text" name="voyageNo" value="{{selectedItem.voyageNo}}">
                                    </div>
                                    <div class="six wide field">
                                        <label style="color:#2185d0">Mat. Manifest No.</label>
                                        <input type="text" name="materialManifestNo" value="{{selectedItem.materialManifestNo}}">
                                    </div>
                            </div>

                            <div class="three fields">
                                    <div class="six wide field">
                                        <label style="color:#2185d0">IMO Hazard Class</label>
                                        <input type="text" name="imoHazardClass" value="{{selectedItem.imoHazardClass}}">
                                    </div>
                                    <div class="six wide field">
                                        <label style="color:#2185d0">IMO Hazard Subclass</label>
                                        <input type="text" name="imoSubClass" value="{{selectedItem.imoSubClass}}">
                                    </div>
                                </div>
        
                                <div class="three fields">
                                    <div class="six wide field">
                                      <label style="color:#2185d0">Eural / EWC Code</label>
                                      <input type="text" name="euralCode" value="{{selectedItem.euralCode}}">
                                    </div>
                                    <div class="six wide field">
                                          <label style="color:#2185d0">UN No.</label>
                                          <input type="text" name="unNo" value="{{selectedItem.unNo}}">
                                    </div>
                                  </div>


                            <div class="three fields">
                                <div class="six wide field">
                                    <label style="color:#2185d0">Package Type</label>
                                    <div class="receiptForm ui fluid search selection dropdown" id="packageTypeDropdown">
                                        <input type="hidden" name="packageType" value="">
                                        <i class="dropdown icon"></i>
                                        <div class="default text">Package Type</div>
                                        <div class="menu">
                                            {{#each packageTypes}}
                                            <div class="item" data-value="{{name}}" data-text="{{name}}">
                                                {{name}}
                                            </div>
                                            {{/each}}
                                        </div>
                                    </div>
                                </div>
                                <div class="three wide field">
                                    <label style="color:#2185d0">Qty</label>
                                    <input type="number" name="quantity" min="1" step="1" value="{{selectedItem.quantity}}" />
                                </div>
                                <div class="three wide field">
                                    <label style="color:#2185d0">Weight (Kg)</label>
                                    <input type="number" name="weightKg" min="0.0" step="any" value="{{selectedItem.weightKg}}" />
                                </div>
              
                            </div>


                            <div class="one fields">
                                <div class="sixteen wide field ">
                                    <label style="color:#2185d0">Material Description</label>
                                    <input class="red input" type="text" name="materialDescription" value="{{materialDescriptionUnescaped}}">
                                </div>
                            </div>
                            <div class="one fields">
                                <div class="sixteen wide field">
                                    <label style="color:#2185d0">Waste Description</label>
                                    <input type="text" name="wasteDescription" value="{{wasteDescriptionUnescaped}}">
                                </div>
                            </div>

                        </div>
                    </div>
                </div>

                <div class="four wide column">
                     <div class="ui clearing segment">
                    <div class="ui form">
                        <div class="field">
                            <label>Stored Date</label>
                            <input type="text" readonly="" name="storedDate" value="{{storedDateFormatted}}">
                        </div>
                        <div class="field">
                            <label>Stored Location</label>
                            <input type="text" readonly="" name="comments" value="{{selectedItem.location}}">
                        </div>
                        <div class="field">
                            <label>Packed Date</label>
                            <input type="text" readonly="" name="storedDate" value="{{packedDateFormatted}}">
                        </div>
                        <div class="field">
                            <label>Packed Into</label>
                            <input type="text" readonly="" name="comments" value="{{selectedItem.packingUnitIdentifier}}">
                        </div>
                        <div class="field">
                            <label>Manifest No</label>
                            <input type="text" readonly="" name="comments" value="{{selectedItem.manifestNo}}">
                        </div>
                        <div class="field">
                            <label>Dispatch Date</label>
                            <input type="text" readonly="" name="storedDate" value="{{dispatchDateFormatted}}">
                        </div>
                    </div>
                </div>
</div>
            </div>
        </div>

        <div class="ui divider"></div>

        <div class="ui grid">
            <div class="four wide column">
                <button class="ui primary left labeled icon button disabled js-item-save-edit">
                <i class="left save icon"></i>
                Save
                </button>
            </div>
            <div class="twelve wide column ">
                <div class="ui positive message hidden" id="updatesSavedMessageDiv">
                    <i class="close icon"></i>
                    <div class="header">
                        Updates Saved
                    </div>
                    <p>Item updates have been saved.</p>
                </div>

                <div class="ui negative message hidden" id="errorOnSaveMessageDiv">
                    <i class="close icon"></i>
                    <div class="header">
                        Failed to save updates.
                    </div>
                    <p>Item updates have been not been saved; please contact application support.</p>
                </div>
            </div>
        </div>
    </div>
</template>
