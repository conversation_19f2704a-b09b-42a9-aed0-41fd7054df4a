import './material-item-edit.html';
import { CompanySiteProfiles } from '../../api/company-site-profiles/company-site-profiles';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Template } from 'meteor/templating';
import { _ } from 'meteor/underscore';
import { moment } from 'meteor/momentjs:moment';
import * as Constants from '../../shared/lib/constants';

function getSiteProfile() {
  // Assumes only one loaded clients-side.
  const siteProfile = CompanySiteProfiles.findOne();
  return siteProfile;
}

const onEditComplete = (error, result) => {
  if (error) {
    console.error(`Edit Item Failed ${JSON.stringify(error, null, 2)}`);

    // Show Message box to confirm updates have been saved.
    $('#errorOnSaveMessageDiv').transition('fade in');

    // Reset Save button to disabled.
    $('.js-item-save-edit').addClass('disabled');
  } else { // Success.
    // Show Message box to confirm updates have been saved.
    $('#updatesSavedMessageDiv').transition('fade in');

    // Reset Save button to disabled.
    $('.js-item-save-edit').addClass('disabled');
  }
};

function valueOrNullIfEmptyString(val) {
  let result = val;
  if (val === '') {
    result = null;
  }
  return result;
}

function updateItem(templateInstance, originalItem, onComplete) {
  const quantityVal = templateInstance.$('[name=quantity]').val();
  let quantity = null;
  if (quantityVal) {
    quantity = parseInt(quantityVal, 10);
  }

  const weightKgVal = templateInstance.$('[name=weightKg]').val();
  let weightKg = null;
  if (weightKgVal) {
    weightKg = parseFloat(weightKgVal);
  }

  const materialDescription = templateInstance.$('[name=materialDescription]').val(); // material Description
  const wasteDescription = templateInstance.$('[name=wasteDescription]').val();
  const packageType = templateInstance.$('[name=packageType]').val();
  const offshoreClient = templateInstance.$('[name=offshoreClient]').val();
  const offshoreLocation = templateInstance.$('[name=offshoreLocation]').val();
  const isWaste = templateInstance.$('[name=isWasteCheckbox]').is(':checked');
  const marinePollutant = templateInstance.$('[name=isMarinePollutantCheckbox]').is(':checked');
  const ccu = templateInstance.$('[name=ccu]').val();
  const voyageNo = templateInstance.$('[name=voyageNo]').val();
  const materialManifestNo = templateInstance.$('[name=materialManifestNo]').val();
  const imoHazardClass = templateInstance.$('[name=imoHazardClass]').val();
  const imoSubClass = templateInstance.$('[name=imoSubClass]').val();
  const euralCode = templateInstance.$('[name=euralCode]').val();
  const unNo = templateInstance.$('[name=unNo]').val();

  const newValues = {
    offshoreClient,
    offshoreLocation,
    isWaste,
    marinePollutant,
    ccu,
    voyageNo,
    materialManifestNo,
    imoHazardClass,
    imoSubClass,
    euralCode,
    unNo,
    packageType,
    quantity,
    weightKg,
    description: materialDescription,
    wasteDescription,
  };
  console.log(`Calling items.edit ${JSON.stringify(newValues, null, 2)}`);

  Meteor.call(
    'items.edit', // 'materialItems.edit',
    originalItem._id,
    newValues,
    onComplete,
  );
}

Template.materialItemEdit.onRendered(function onRendered() {
  const template = this;

  // Setup Datepicker.
  // https://github.com/mdehoog/Semantic-UI/blob/49178d6d0321357b959ff56f424ea1b3ed76a6ed/src/definitions/modules/calendar.js#L902-L1279
  const calendarSettings = {
    type: 'datetime',
    maxDate: moment().utc().toDate(), // today
    ampm: false,
    formatter: {
      date: (date, settings) => {
        if (_.isUndefined(date)) return '';
        const day = (`0${date.getDate()}`).slice(-2); // zero pad.
        const month = settings.text.monthsShort[date.getMonth()];
        const year = date.getFullYear();
        return day + '-' + month + '-' + year;
      },
      time: (date, settings) => {
        if (_.isUndefined(date)) return '';
        const hours = (`0${date.getHours()}`).slice(-2); // zero pad.
        const minutes = (`0${date.getMinutes()}`).slice(-2); // zero pad.
        return hours + ':' + minutes;
      },
    },
  };

  template.$('#receivedDatepicker').calendar(calendarSettings);
  template.$('#receivedDatepicker')
    .calendar('set date', moment(template.data.selectedItem.receivedDate).toDate());

  template.$('#materialReceiptedDatepicker').calendar(calendarSettings);
  template.$('#materialReceiptedDatepicker')
    .calendar('set date', moment(template.data.selectedItem.materialReceiptDateTime).toDate());

  // Initialise dropdowns
  $('.ui.dropdown').dropdown();

  template.$('#packageTypeDropdown')
    .dropdown('set selected', template.data.selectedItem.packageType);

  // Initialise checkboxes
  $('.ui.checkbox').checkbox();

  template.initialisationComplete = true;
});

Template.materialItemEdit.events({
  'click .js-item-save-edit': () => {
    const item = Template.instance().data.selectedItem;
    updateItem(Template.instance(), item, onEditComplete);
  },
  'change input, keyup input, click .ui.calendar, change textarea, keyup textarea': (event, templateInstance) => {
    if (templateInstance.initialisationComplete) {
      templateInstance.$('.js-item-save-edit').removeClass('disabled');

      if ($('#updatesSavedMessageDiv').hasClass('visible')) {
        $('#updatesSavedMessageDiv').closest('.message').transition('fade out');
      }
      if ($('#errorOnSaveMessageDiv').hasClass('visible')) {
        $('#errorOnSaveMessageDiv').closest('.message').transition('fade out');
      }
    }
  },
  'click .message': (event, templateInstance) => {
    const target = event.target;
    $(target)
      .closest('.message')
      .transition('fade out');
  },
  'click .receiptForm.ui.dropdown .remove.icon': function onClick(event, templateInstance) {
    templateInstance.$(event.target).parent('.dropdown').dropdown('clear');
    event.stopPropagation();
  },
  'click .audit-button': function onClick(event, templateInstance) {
    if ($('#audit-trail').hasClass('visible')) {
      $('#audit-trail').transition('fade out');
    } else {
      $('#audit-trail').transition('fade in');
    }
  },
});

Template.materialItemEdit.helpers({
  currentClient: function getCurrentClient() {
    const siteProfile = getSiteProfile();
    if (siteProfile) {
      const siteClients = siteProfile.configuration.clients;
      return _.find(siteClients, (client) => client._id === FlowRouter.getParam('clientId'));
    }
    return [];
  },
  receivedAtFormatted: function getReceivedAtFormatted() {
    return moment(Template.instance().receivedAt.get()).format(DISPLAY_DATETIME_FORMAT);
  },
  useAutoReceiptNo: function getUseAutoReceiptNo() {
    let retVal = { value: '', readonly: '' };
    if (Template.instance().useAutoReceiptNumber.get()) {
      retVal = { value: Template.instance().receiptNoAutomatic.get(), readonly: '' };
    }
    return retVal;
  },
  offshoreLocations: function getOffshoreLocations() {
    const siteProfile = getSiteProfile();
    if (siteProfile) {
      const siteOffshoreLocations = siteProfile.configuration.offshoreLocations;
      return _.sortBy(siteOffshoreLocations, (offshoreLocation) => offshoreLocation.name);
    }
    return [];
  },
  packageTypes: function getPackageTypes() {
    const siteProfile = getSiteProfile();
    if (siteProfile) {
      const sitePackageTypes = siteProfile.configuration.packageTypes;
      return _.sortBy(sitePackageTypes, (packageType) => packageType.name);
    }
    return [];
  },
  getSelectedItem: function getSelectedItem() {
    return Template.instance().selectedItem.get();
  },
  storedDateFormatted() {
    let retval = null;
    const selItem = Template.instance().data.selectedItem;
    if (selItem && selItem.storedDate) {
      retval = moment(selItem.storedDate)
        .format(Constants.DISPLAY_DATE_FORMAT);
    }
    return retval;
  },
  dispatchDateFormatted() {
    let retval = null;
    const selItem = Template.instance().data.selectedItem;
    if (selItem && selItem.dispatchedDate) {
      retval = moment(selItem.dispatchedDate)
        .format(Constants.DISPLAY_DATE_FORMAT);
    }
    return retval;
  },
  packedDateFormatted() {
    let retval = null;
    const selItem = Template.instance().data.selectedItem;
    if (selItem && selItem.packedDate) {
      retval = moment(selItem.packedDate)
        .format(Constants.DISPLAY_DATE_FORMAT);
    }
    return retval;
  },
  materialDescriptionUnescaped() {
    // Material descriptions can come html encoded from Flow e.g. "<<" as "&gt;&gt;"
    let retval = null;
    const selItem = Template.instance().data.selectedItem;
    if (selItem && selItem.description) {
      retval = _.unescape(selItem.description)
        .replace(/&GT;/ig, '>')
        .replace(/&LT;/ig, '<')
        .replace(/&gt/ig, '>')
        .replace(/&lt/ig, '<');
    }
    return retval;
  },
  wasteDescriptionUnescaped() {
    let retval = null;
    const selItem = Template.instance().data.selectedItem;
    if (selItem && selItem.wasteDescription) {
      retval = _.unescape(selItem.wasteDescription)
        .replace(/&GT;/ig, '>')
        .replace(/&LT;/ig, '<')
        .replace(/&gt/ig, '>')
        .replace(/&lt/ig, '<');
    }
    return retval;
  },
});
