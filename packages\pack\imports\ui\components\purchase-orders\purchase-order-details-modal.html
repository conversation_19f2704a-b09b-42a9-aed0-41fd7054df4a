<template name="purchaseOrderDetailsModal">
  <div class="ui modal po-details-modal">
    <i class="close icon"></i>
    {{> purchaseOrderDetails selectedPo=selectedPo }}
    <div class="actions">
      <div class="ui labeled icon button edit ok">
        <i class="edit icon"></i>
        Edit
      </div>
      <div class="ui labeled icon button ok">
        <i class="checkmark icon"></i>
        OK
      </div>
    </div>
  </div>
</template>

<template name="purchaseOrderDetails">
  <div class="ui clearing segment">
    <h1 class="ui left floated header">PO Details: {{selectedPo.identifier}} </h1>
  </div>
  <div class="content">
    <div class="ui container">
      <div class="ui middle aligned very relaxed stackable grid">
        <div class="twelve wide column">
          <div class="ui form">
            <div class="eight wide field">
              <label>Receipt No</label>
              <input type="text" name="receiptNo" value="{{selectedPo.receiptNo}}" readonly>
            </div>
            <div class="readonly fields">
              <div class="eight wide field">
                <label>Receipt Location</label>
                <input type="text" name="receiptLocation" value="{{selectedPo.receiptLocation}}" readonly>
              </div>
              <div class="eight wide field">
                <label>Received Date</label>
                <input type="text" name="receivedDate" value="{{receivedDateFormatted}}" readonly>
              </div>
            </div>

            <div class="readonly fields">
              <div class="eight wide field">
                <label>PO No</label>
                <input type="text" name="identifier" value="{{selectedPo.identifier}}" readonly />
              </div>
              <div class="eight wide field">
                <label>No. of PO Lines Received</label>
                <input type="text" name="noOfPoLinesRcvd" value="{{selectedPo.noOfLines}}" readonly/>
              </div>
            </div>

            <div class="readonly fields">
              <div class="eight wide field">
                <label>Vendor</label>
                <input type="text" name="vendor" value="{{selectedPo.vendor}}" readonly>
              </div>
              <div class="eight wide field">
                <label>Vendor Delivery No</label>
                <input type="text" name="vendorDeliveryNo" value="{{selectedPo.vendorDeliveryNo}}" readonly>
              </div>
            </div>
            <div class="readonly field">
              <label>Description</label>
              <input type="text" name="description" value="{{selectedPo.description}}" readonly>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>