import './purchase-order-details-modal.html';
import { DISPLAY_DATETIME_FORMAT } from '../../../shared/lib/constants';
import { Template } from 'meteor/templating';
import moment from 'moment';

Template.purchaseOrderDetails.helpers({
  receivedDateFormatted() {
    const po = Template.instance().data.selectedPo;
    if (!po || !po.receivedDate) {
      return '';
    }

    return moment(po.receivedDate).format(DISPLAY_DATETIME_FORMAT);
  },
});
