import { BaseModalMixin } from '../../modal/base-modal-mixin';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';

export const PurchaseOrderDetailsModalMethods = Object.assign({
  className: '.ui.modal.po-details-modal',

  init(poId) {
    let openItemEditScreen = false;

    $(this.className).modal({
      allowMultiple: false,
      onApprove: (element) => {
        if (element.hasClass('edit')) {
          openItemEditScreen = true;
        }
      },
      onHidden: () => {
        if (openItemEditScreen) {
          FlowRouter.go('editPo',
            {
              clientId: FlowRouter.getParam('clientId'),
              poId,
            });
        }
      },
    }).modal('hide all');

    return this;
  },
}, BaseModalMixin);
