/* eslint-disable max-classes-per-file */
import { jsPDF as JsPDF } from 'meteor/jspdf:core';
import { EventEmitter } from 'meteor/raix:eventemitter';

const jsBarcode = require('jsbarcode');

const mmToIn = (mm) => mm * 0.0393701;
const inToMm = (mm) => mm / 0.0393701;
const mmToPx = (mm, dpi) => mmToIn(mm) * dpi;
const pxToMm = (px, dpi) => inToMm(px / dpi);

const pageSizes = {
  a3: 'a3',
  a4: 'a4',
  a5: 'a5',
  a6: 'a6',
  a7: 'a7',
  letter: 'letter',
  legal: 'legal',
};

// useful for making objects human readable
const camelCaseToWords = (str) => {
  return str.match(/^[a-z]+|[A-Z][a-z]*/g).map((x) =>
    x[0].toUpperCase() + x.substr(1).toLowerCase()
  ).join(' ');
};

// This only runs client side, currently due to dependence on canvas.
// It may be possible to run on server with node-canvas.
const getDataUriFromImageUrl = (imageUrl) => {
  const image = new Image();
  image.src = imageUrl;

  return new Promise((resolve, reject) => {
    image.onload = function onLoad() {
      const canvas = document.createElement('canvas');
      canvas.width = this.naturalWidth; // or 'width' if you want a special/scaled size
      canvas.height = this.naturalHeight; // or 'height' if you want a special/scaled size

      canvas.getContext('2d').drawImage(this, 0, 0);

      // Get raw image as Data URI
      resolve({
        data: canvas.toDataURL('image/png'),
        width: canvas.width,
        height: canvas.height,
      });
    };

    image.onerror = () => reject();
  });
};

// This only runs client side, currently due to dependence on canvas.
// It may be possible to run on server with node-canvas.
const getBarcodeImageData = (barcodeNumber, barWidth, barHeight, format, width) => {
  // add barcode
  const canvas = document.createElement('canvas');

  jsBarcode(canvas, barcodeNumber, {
    format: format || 'CODE128',
    width: barWidth,
    height: barHeight,
  });

  return canvas.toDataURL('image/png');
};

class Barcode {
  constructor(barcodeNumber) {
    this.barcodeNumber = barcodeNumber;
    this.format = 'CODE128'; // hardcode for now
  }

  getImageData(width, height) {
    const pdfDpi = 72; // standard, hardocded for now.
    const barsPerCharacter = 6; // CODE128 standard, hardocded for now.
    const barWidth = 1;
    return getBarcodeImageData(this.barcodeNumber, barWidth, height, this.format, width);
  }
}

class PdfTableField {
  constructor(type, value, width, hOffset, vOffset) {
    this.type = type;
    this.value = value;
    this.width = width;
    this.hOffset = hOffset;
    this.vOffset = vOffset;
  }
}

class PdfDrawStyle {
  constructor(strokeColor, strokeWidth, fillColor) {
    this.strokeColor = strokeColor;
    this.strokeWidth = strokeWidth;
    this.fillColor = fillColor;
  }

  set(doc) {
    // TODO: REMOVED FOR TIME BEING - COLOUR SETTING METHOD
    // CAN BREAK ACROBAT. FIX THIS IN FUTURE.
    // if (this.strokeColor) doc.setDrawColor(this.strokeColor);
    if (this.strokeWidth) doc.setLineWidth(this.strokeWidth);
  }
}

class PdfDocument {
  constructor(pageSize, horizontalMargin, verticalMargin) {
    // Set up some initial values for pdf
    this.doc = new JsPDF('portrait', 'mm', (pageSize || 'a6'));

    // set margins proportions
    this.horizontalMargin = horizontalMargin || this.doc.internal.pageSize.height / 15;
    this.verticalMargin = verticalMargin || this.doc.internal.pageSize.height / 15;

    // page width without margins
    this.pageWidth = (this.doc.internal.pageSize.width - (2 * this.horizontalMargin));
    this.pageHeight = (this.doc.internal.pageSize.height - (2 * this.verticalMargin));

    this.titleFontSize = this.pageHeight / 8;
    this.regularFontSize = this.pageHeight / 14;

    this.lineHeight = this.pageHeight / 16;
    this.paragraphLineHeight = this.pageWidth / 18;
    this.paragraphIndent = this.pageWidth / 18;
    this.linePointer = this.verticalMargin;
    this.pageNo = 1;
    this.totalPagesExp = '{total_pages_count_string}';
    this.events = new EventEmitter();
    // useful boolean for avoiding unnecessary page moves
    this.pageIsEmpty = true;
  }

  addRegularLine(text) {
    this.doc.text(text, this.horizontalMargin, this.linePointer);
    this.linePointer += this.lineHeight;
  }

  /* shorthand method for adding a text only row */
  addTextOnlyRow(fields) {
    const textFields = _.map(fields, (f) => new PdfTableField('text', f));
    this.addTableRow(textFields);
  }

  addTableRow(fields, useBorders, borderStyle, x) {
    if (typeof (fields) !== 'object' || fields.length === 0) {
      console.error('not valid fields!');
      return;
    }

    const defaultColWidth = this.pageWidth / fields.length;
    let colPosition = x || this.horizontalMargin;
    const linePointerForBlocks = this.linePointer - this.lineHeight / 1.8;
    const defaultBorderStyle = new PdfDrawStyle('black', 0.2, null);
    const borderStyleToUse = borderStyle || defaultBorderStyle;

    const addField = (f) => {
      const width = f.width || defaultColWidth;
      const textPosition = (colPosition + width / 12) + 1;
      const hOffset = f.hOffset || 0;
      const vOffset = f.vOffset || 0;
      switch (f.type) {
        case 'text':
          if (f.value && this.doc.getTextDimensions(f.value).w / 2.4 > width) {
            const lines = this.doc.splitTextToSize(f.value, width - 10);
            const tempPointer = this.linePointer;
            this.linePointer -= (this.lineHeight / 4);
            for (let i = 0; i < lines.length; i++) { // inner loop, so using for.
              this.doc.text(lines[i] || ' ', textPosition + hOffset, this.linePointer + vOffset);
              if (i !== lines.length - 1) this.linePointer += (this.lineHeight / 2);
            }
            this.linePointer = tempPointer;
          } else {
            this.doc.text(f.value || ' ', textPosition + hOffset, this.linePointer + vOffset);
          }
          break;
        case 'img':
          this.doc.addImage(
            f.value, 'PNG', colPosition + 0.1, linePointerForBlocks + 0.5, width - 0.1, this.lineHeight - 1
          );
          break;
        case 'rect':
          f.value.set(this.doc);
          if (!useBorders) {
            this.doc.rect(colPosition, linePointerForBlocks, width, this.lineHeight);
          }
          break;
        default:
          this.doc.text(f.value, colPosition, this.linePointer);
          break;
      }
      if (useBorders) {
        borderStyleToUse.set(this.doc);
        this.doc.rect(colPosition, linePointerForBlocks, width, this.lineHeight);
      }
    };

    _.each(fields, (f) => {
      addField(f);
      colPosition += f.width || defaultColWidth;
    });

    this.linePointer += this.lineHeight;
    this.movePageIfNeeded();
  }

  movePageIfNeeded() {
    if (this.linePointer >= this.pageHeight) {
      this.movePage(true);
    }
  }

  addPageCount() {
    this.doc.text(
      `Page ${this.pageNo} of ${this.totalPagesExp}`, this.horizontalMargin, this.pageHeight + this.lineHeight,
    );
  }

  movePage(wasAutomatic, addPageCount) {
    const explicit = !wasAutomatic;
    if (addPageCount) {
      this.addPageCount();
      this.pageNo++;
    }

    this.doc.addPage();
    this.linePointer = this.verticalMargin;
    this.pageIsEmpty = true;
    this.events.emit('pageMoved', { data: { pageNo: this.pageNo, explicit } });
  }

  addFinalPageCounts() {
    this.addPageCount();
    this.doc.putTotalPages(this.totalPagesExp);
  }

  addLogo(logoUrl, x, y, width, height) {
    return new Promise((res, rej) => {
      // On Success
      const addLogoToDoc = (img) => {
        const logoW = width || this.pageWidth * 0.8;
        const scaleRatio = logoW / pxToMm(img.width, 72);
        const logoH = height || pxToMm(img.height, 72) * scaleRatio;
        const yOffset = y || 3;
        this.doc.addImage(
          img.data, 'PNG', x || this.horizontalMargin + this.pageWidth * 0.1, yOffset, logoW, logoH,
        );
        this.linePointer += (logoH * 1.1) + yOffset;
        this.movePageIfNeeded();
        res();
      };

      // On Failure
      const warnOfNoLogo = () => {
        console.log(`${logoUrl} didn't contain a valid image. Rendering Label with no logo.`);
        rej();
      };

      getDataUriFromImageUrl(logoUrl)
        .then(addLogoToDoc, warnOfNoLogo)
        .catch((e) => console.error(`error adding logo to doc! ${e}`));
    });
  }

  addImage(img, x, y, width, height) {
    // On Sucess
    const logoW = width || this.pageWidth * 0.8;
    const scaleRatio = logoW / pxToMm(img.width, 72);
    const logoH = height || pxToMm(img.height, 72) * scaleRatio;
    this.doc.addImage(
      img.data, 'PNG', x || this.horizontalMargin + this.pageWidth * 0.1, y || 0, logoW, logoH,
    );
    this.linePointer += logoH;
    this.movePageIfNeeded();
  }
}

export const pdfCore = {
  // methods
  mmToIn,
  inToMm,
  mmToPx,
  pxToMm,
  getDataUriFromImageUrl,
  camelCaseToWords,
  getBarcodeImageData,

  // classes
  PdfDocument,
  PdfTableField,
  PdfDrawStyle,
  Barcode,

  // page size 'enum'
  pageSizes,
};
