import { dtoMapper } from '../../shared/dto-mapper';
import { pdfCore } from './core';

class LabelField {
  constructor(key, value, isMultiLine, convertCamelCase) {
    const convert = convertCamelCase || false;
    this.key = (convert === true) ? this.camelCaseToWords(key.toString()) : key.toString();
    this.value = value && value.toString();
    this.isMultiLine = isMultiLine;
  }
}

const buildItemDestinationString = (dest, subDest) => {
  const destStr = dest && dest + '\r\n' || '';
  const subDestStr = subDest && subDest || '';

  return destStr + subDestStr;
};

// Lillyhall Item Format
const getDetailsFromItem = (item) => {
  const itemToPrint = _.clone(item);
  itemToPrint.packageType = `${item.packageType} x ${item.quantity}`;
  itemToPrint.destination = buildItemDestinationString(item.destination, item.destSublocation);
  const itemMapping = {
    deliveryCompany: 'Delivery Company',
    vendorRef: 'Vendor Ref (PO or DN)',
    packageType: 'Package',
    weightKg: 'Weight (kg)',
    description: 'Weight Category',
    destination: 'Destination',
  };

  const mappedItem = dtoMapper.mapItemObject(itemToPrint, null, itemMapping, true);

  const printFields =
    _.map(mappedItem, (val, key) => new LabelField(key, val, key === 'Destination', false));
  return _.reject(printFields, (field) => field.value === '');
};

// Peterson-Aberden Item Format
const getDetailsFromAbdnItem = (item) => {
  const itemToPrint = _.clone(item);
  itemToPrint.packageType = `${item.packageUnit} x ${item.quantity}`;

  // Strings / Fields to display on label.
  const itemMapping = {
    packageType: 'Package',
    receiptCategory: 'Category',
    // poNo: 'PO No', // Disable for now - as pushes barcode offscreen.
    offshoreLocation: 'Offshore Locn',
    description: 'Description',
  };

  const mappedItem = dtoMapper.mapItemObject(itemToPrint, null, itemMapping, true);

  const printFields =
    _.map(mappedItem,
      (val, key) => new LabelField(key, val, key === 'Description', false),
    );

  return _.reject(printFields, (field) => field.value === '');
};

export const labelUtils = {

  createLabel: (detailsToPrint, title, barcodeNumber, logoUrl, isAbdnItem) => {
    return new Promise((resolve, reject) => {

      if (detailsToPrint.length <= 0) {
        reject('invalid label details passed!');
      }

      let printFields = {};
      if (isAbdnItem) {
        printFields = getDetailsFromAbdnItem(detailsToPrint);
      } else { // Lillyhall item.
        printFields = getDetailsFromItem(detailsToPrint);
      }
      const label = new pdfCore.PdfDocument('a6');

      const leftColumnStart = label.horizontalMargin;
      const rightColumnStart = label.horizontalMargin + (label.pageWidth / 1.5);

      const shouldSplit =
        (key, value, explicit) => explicit || ((value.length > 28) || (key.length > 25));

      const getMiddle =
        (text) => label.pageWidth - (text.length * ((label.titleFontSize) / 4));

      const restOfDoc = (isLastPage) => {
        label.linePointer -= 3;
        label.doc.setFontSize(label.titleFontSize);
        label.doc.text(title, leftColumnStart, label.linePointer);

        label.linePointer += label.lineHeight * 0.3;

        label.doc.setDrawColor(0, 0, 0);
        label.doc.setLineWidth(1.5);
        label.doc.rect(leftColumnStart, label.linePointer, label.pageWidth, label.lineHeight * 1.2);

        label.linePointer += label.lineHeight * 0.9;

        label.doc.text(barcodeNumber, getMiddle(barcodeNumber), label.linePointer);

        label.linePointer += (label.lineHeight * 1.2);
        label.doc.setFontSize(label.regularFontSize);

        // add fields to PDF
        _.each(printFields, (field) => {
          if (field.value) {
            label.doc.setFontStyle('bold');
            label.doc.text(`${field.key} `, leftColumnStart, label.linePointer);
            label.doc.setFontStyle('normal');

            if (shouldSplit(field.value, field.key, field.isMultiLine)) {
              label.linePointer += label.paragraphLineHeight; // line break for paragraph heading;
              const lines = label.doc.splitTextToSize(field.value, label.pageWidth);
              for (let i = 0; i < lines.length; i++) { // inner loop, so using for.
                label.doc.text(lines[i], leftColumnStart + label.paragraphIndent, label.linePointer);
                if (i !== lines.length - 1) label.linePointer += label.paragraphLineHeight;
              }
            } else {
              label.doc.text(field.value, rightColumnStart, label.linePointer);
            }
            label.linePointer += label.lineHeight;
          }
        });

        // add barcode
        const pdfDpi = 72; // standard, hardocded for now.
        const barWidth = 1;
        const barHeight = pdfCore.mmToPx((label.pageHeight / 5), pdfDpi);

        const barcodeImageData = pdfCore.getBarcodeImageData(barcodeNumber, barWidth, barHeight, 'CODE128');

        const barcodeHeight = label.pageHeight / 5;
        const barcodeWidth = label.pageWidth / 2;

        label.doc.addImage(
          barcodeImageData,
          'PNG',
          leftColumnStart + (label.pageWidth / 4),
          label.linePointer - 2,
          barcodeWidth,
          barcodeHeight,
        );
        if (isLastPage) { resolve(label.doc); }
        else { label.movePage(false, false); }
      };

      // actually execute functions and create doc. this should return the resolved
      // promise with the PDF.
      const amountToPrint = parseInt(detailsToPrint.quantity, 10);
      let isLastPage = false;

      pdfCore.getDataUriFromImageUrl(logoUrl)
        .then((imgData) => {
          for (let i = 0; i < amountToPrint; i++) {
            if (i === detailsToPrint.quantity - 1) { isLastPage = true; }
            label.addImage(imgData, null, 2);
            restOfDoc(isLastPage);
          }
        });
    });
  },
};
