import { dtoMapper } from '../../shared/dto-mapper';
import { moment } from 'meteor/momentjs:moment';
import { pdfCore } from './core';

const createManifestList = (items) => {
  const itemsWithDestinations = _.map(items, (i) => {
    i.destination = dtoMapper.getDestinationForItem(i);
    return i;
  });

  return _.groupBy(itemsWithDestinations, 'destination');
};

export const manifestPdf = {

  create: (manifestDetails) => new Promise((resolve, reject) => {
    if (manifestDetails.length <= 0) reject('invalid label details passed!');

    // UOM for all document related numbers is mm, NOT px.
    const manifest = new pdfCore.PdfDocument('a4', 15, 15);
    const manifestDetailsToPrint = _.clone(manifestDetails);
    manifestDetailsToPrint.items = createManifestList(manifestDetails.items);
    const rectStyle = new pdfCore.PdfDrawStyle('black', 0.5, null);
    const colUnits = manifest.pageWidth / 12;
    const headerBorderStyle = new pdfCore.PdfDrawStyle('black', 0.5, [12, 12, 200]);
    const getLogoImageData = pdfCore.getDataUriFromImageUrl('/images/client-logos/peterson-sel.png');
    let logoImg = null;
    let currentDestination = null;

    const beginDoc = (logoImage) => {
      logoImg = logoImage;
      rectStyle.set(manifest.doc);
      return Promise.resolve();
    };

    const createManifestHeader = () => {
      const fieldWidth = colUnits * 2;
      const tablePos = colUnits * 5;
      const barcodeImg = new pdfCore.Barcode(manifestDetailsToPrint.manifestRef);
      const imgHeight = 20;
      const barcodeImgData = barcodeImg.getImageData(colUnits * 3, manifest.lineHeight * 2);
      manifest.addImage(logoImg, manifest.horizontalMargin, manifest.verticalMargin - 5, colUnits * 3, imgHeight);

      manifest.linePointer += imgHeight / 2.75;

      manifest.addTableRow([
        new pdfCore.PdfTableField('img', barcodeImgData, colUnits * 3),
      ], true, headerBorderStyle, manifest.horizontalMargin);

      manifest.doc.setFontSize(8);
      manifest.lineHeight = 8;
      manifest.linePointer = manifest.verticalMargin;

      const destinations = _.map(manifestDetailsToPrint.destinations, (d) => d.deliveryDestination);

      manifest.addTableRow([
        new pdfCore.PdfTableField('text', 'Destinations', fieldWidth),
        new pdfCore.PdfTableField('text', destinations && destinations.join() || '', fieldWidth * 3),
      ], true, headerBorderStyle, tablePos);

      manifest.addTableRow([
        new pdfCore.PdfTableField('text', 'Customer', fieldWidth),
        new pdfCore.PdfTableField('text', manifestDetailsToPrint.clientName, fieldWidth * 3),
      ], true, headerBorderStyle, tablePos);

      manifest.addTableRow([
        new pdfCore.PdfTableField('text', 'Unisto Lock No', fieldWidth),
        new pdfCore.PdfTableField('text', ' ', fieldWidth * 3),
      ], true, headerBorderStyle, tablePos);

      manifest.addTableRow([
        new pdfCore.PdfTableField('text', 'Prepared By', fieldWidth),
        new pdfCore.PdfTableField('text', manifestDetailsToPrint.createdBy.username, fieldWidth),
        new pdfCore.PdfTableField('text', 'Vehicle', fieldWidth),
        new pdfCore.PdfTableField('text', manifestDetailsToPrint.vehicle.vehicleRegistration, fieldWidth),
      ], true, headerBorderStyle, tablePos);

      manifest.addTableRow([
        new pdfCore.PdfTableField('text', 'Date', fieldWidth),
        new pdfCore.PdfTableField('text', moment(manifestDetailsToPrint.createdAt).format('DD-MMM-YYYY HH:MM'), fieldWidth),
        new pdfCore.PdfTableField('text', 'Run Number', fieldWidth),
        new pdfCore.PdfTableField('text', manifestDetailsToPrint.runIdentifierStr.toString(), fieldWidth),
      ], true, headerBorderStyle, tablePos);

      manifest.linePointer += manifest.lineHeight;

      manifest.lineHeight = manifest.pageHeight / 16;

      return Promise.resolve();
    };

    const addItemDestinationHeader = (dest) => {
      manifest.doc.setFontSize(14);
      manifest.addTableRow([
        new pdfCore.PdfTableField('text', `Items for ${dest}`, manifest.pageWidth),
      ], true, headerBorderStyle);
      manifest.doc.setFontSize(12);
    };

    const createManifest = () => {
      const refWidth = colUnits * 3;
      const quantWidth = colUnits * 1;
      const weightWidth = colUnits * 1.5;
      const barcodeWidth = colUnits * 2.5;
      const signatureWidth = colUnits * 3;
      const deliveredWidth = colUnits * 1;

      let i = 1;
      _.each(manifestDetailsToPrint.items, (value, key) => {
        const dest = key || 'non specified destination';
        currentDestination = dest;
        manifest.pageIsEmpty = false;
        if (dest !== 'null') { addItemDestinationHeader(dest); }

        manifest.doc.setFontStyle('bold');

        manifest.addTableRow([
          new pdfCore.PdfTableField('text', 'Vendor Ref.', refWidth),
          new pdfCore.PdfTableField('text', 'Qty.', quantWidth),
          new pdfCore.PdfTableField('text', 'Wt.', weightWidth),
          new pdfCore.PdfTableField('text', 'Peterson Ref.', barcodeWidth),
          new pdfCore.PdfTableField('text', 'Sign/Print', signatureWidth),
          new pdfCore.PdfTableField('text', 'Y/N', deliveredWidth),
        ], true, headerBorderStyle);

        manifest.doc.setFontSize(12);

        manifest.doc.setFontStyle('normal');

        _.each(value, (f) => {
          const barcodeImg = new pdfCore.Barcode(f.itemRef);
          const barcodeImgData = barcodeImg.getImageData(barcodeWidth, manifest.lineHeight * 2);
          const ref = new pdfCore.PdfTableField('text', f.vendorRef, refWidth);
          const quantity = new pdfCore.PdfTableField('text', f.quantity.toString(), quantWidth);
          const weight = new pdfCore.PdfTableField('text', `${f.weightKg || 0}kg`, weightWidth);
          const barcode = new pdfCore.PdfTableField('img', barcodeImgData, barcodeWidth);
          const signatureBox = new pdfCore.PdfTableField('text', 'Sign:\n\nPrint:', signatureWidth, -4, -4.5);
          const delivered = new pdfCore.PdfTableField('text', ' ', deliveredWidth);
          manifest.addTableRow([ref, quantity, weight, barcode, signatureBox, delivered], true);
        });

        if (i !== Object.keys(manifestDetailsToPrint.items).length) {
          manifest.movePage(false, true);
        }
        i++;
      });

      manifest.addFinalPageCounts();

      return Promise.resolve();
    };

    manifest.events.on('pageMoved', (e) => {
      createManifestHeader();
      if (!e.data.explicit) {
        addItemDestinationHeader(`${currentDestination} (continued)`);
      }
    });

    const finish = () => resolve(manifest.doc);

    getLogoImageData
      .then(beginDoc)
      .then(createManifestHeader)
      .then(createManifest)
      .then(finish)
      .catch((e) => { console.error(`error creating manifest! ${e}`); });
  }),

};
