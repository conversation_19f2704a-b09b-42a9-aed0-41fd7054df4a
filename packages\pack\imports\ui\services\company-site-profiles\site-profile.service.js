import { CompanySiteProfiles } from '../../../api/company-site-profiles/company-site-profiles';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { GetOffshoreLocationsForNonDispatchedReceiptedItems } from '../../../api/items/queries/get-offshore-locations-for-non-dispatched-receipted-items';

const sortByStrProp = (list, propName) =>
  list.sort((a, b) => {
    const nameA = a[propName].toUpperCase();
    const nameB = b[propName].toUpperCase();

    if (nameA < nameB) {
      return -1;
    }

    if (nameA > nameB) {
      return 1;
    }

    // names must be equal
    return 0;
  });

const getConfigValueSortedByName = (config, valueName) => {
  if (config && config[valueName]) {
    return sortByStrProp(config[valueName], 'name');
  }

  return [];
};

const getOffshoreLocationsForNonDispatchedReceiptedItems = (callback) => {
  const args = {};
  return GetOffshoreLocationsForNonDispatchedReceiptedItems.call(args, callback);
};

export const SiteProfileService = {
  currentSite() {
    return CompanySiteProfiles.findOne();
  },
  currentSiteId() {
    return this.currentSite()._id;
  },
  currentSiteIdentifier() {
    return this.currentSite().identifier;
  },
  currentClient() {
    const clients = this.clients();
    const clientId = FlowRouter.getParam('clientId');

    return clients.find((client) => client._id === clientId);
  },
  siteConfiguration() {
    const siteProfile = this.currentSite();

    if (siteProfile) {
      return siteProfile.configuration;
    }

    return {};
  },
  receiptNoSequence() {
    const configuration = this.siteConfiguration();

    if (configuration && configuration.receiptNoSequence) {
      return configuration.receiptNoSequence;
    }

    return 0;
  },
  receiptNoFormatStr() {
    const configuration = this.siteConfiguration();

    if (configuration && configuration.receiptNoFormatStr) {
      return configuration.receiptNoFormatStr;
    }

    return '';
  },
  getConfigValueSortedByNameForCurrentSite(valueName) {
    return getConfigValueSortedByName(this.siteConfiguration(), valueName);
  },
  clients() {
    const user = Meteor.user();
    const usersClients = user.profile.clients;

    const allClients = this.getConfigValueSortedByNameForCurrentSite('clients');

    if (!usersClients) return allClients;

    const normalizedUsersClients = usersClients.map((client) => client.toUpperCase());
    return allClients.filter((client) => normalizedUsersClients.includes(client.name.toUpperCase()));
  },
  vendors() {
    return this.getConfigValueSortedByNameForCurrentSite('vendors');
  },
  receiptCategories() {
    return this.getConfigValueSortedByNameForCurrentSite('receiptCategories');
  },
  offshoreLocations(callback) {
    return getOffshoreLocationsForNonDispatchedReceiptedItems(callback);
  },
  offshoreClients(showActive) {
    var offshoreClients = this.getConfigValueSortedByNameForCurrentSite('offshoreClients');
    if (showActive) {
      return offshoreClients.filter((c) => c.isActive);
    }
    return offshoreClients;
  },
  packageTypes() {
    return this.getConfigValueSortedByNameForCurrentSite('packageTypes');
  },
  customsStatuses() {
    return this.getConfigValueSortedByNameForCurrentSite('customsStatuses');
  },
  ncrs() {
    return this.getConfigValueSortedByNameForCurrentSite('ncrs');
  },
  receiptLocations() {
    return this.getConfigValueSortedByNameForCurrentSite('receiptLocations');
  },
  printers() {
    return this.getConfigValueSortedByNameForCurrentSite('printers');
  },
  storageLocations(isActive) {
    var storageLocations = this.getConfigValueSortedByNameForCurrentSite('storageLocations');
    if (isActive) {
      return storageLocations.filter((l) => l.isActive);
    }
    return this.getConfigValueSortedByNameForCurrentSite('storageLocations');
  },
  requestDestinations() {
    return this.getConfigValueSortedByNameForCurrentSite('offshoreLocations');
  },
  destinationVendors() {
    return this.getConfigValueSortedByNameForCurrentSite('destinationVendors');
  },
  materialTransportCompanies() {
    return this.getConfigValueSortedByNameForCurrentSite('materialTransportCompanies');
  },
};
