import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { RequestsService } from '../requests/requests.service';

export const PacklistService = {
  currentRequestId() {
    return FlowRouter.getParam('requestId');
  },
  currentRequest() {
    return RequestsService
      .getRequest(this.currentRequestId());
  },
  packingUnits() {
    return RequestsService
      .request(this.currentRequestId())
      .packingUnits();
  },
  packingUnit(unitId) {
    return RequestsService
      .request(this.currentRequestId())
      .packingUnit(unitId);
  },
};
