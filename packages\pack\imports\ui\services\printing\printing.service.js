import { SiteProfileService } from '../company-site-profiles/site-profile.service'

export const PrintingService = {
  setUsersActivePrinter(printerId) {
    // Use session var so user doesn't have to input again
    Session.set('activePrinter', printerId);
  },
  getUsersActivePrinter() {
    const printerId = Session.get('activePrinter');

    if (printerId) {
      const sitesPrinters = SiteProfileService.printers();
      return sitesPrinters.find(printer => printer._id === printerId);
    }
    // Return undefined if the user hasn't selected an active printer
    return undefined;
  },
};
