import { ReceiptActionTypes } from './receipt-action.types';
import { ReceiptItem } from '../../../api/items/commands/receipt-item';
import { ChemReceipt } from '../../../api/items/commands/receipt-item.strategies/chem-receipt';

export const ReceiptActionServices = {
  getServiceOpForAction(actionType, receipt, callback = () => {}) {
    const item = receipt;

    let operation = null;

    switch (actionType) {
      case ReceiptActionTypes.next:
      case ReceiptActionTypes.back:
        // No Server Operation for Action Types use callback only
        break;
      case ReceiptActionTypes.receipt:
      case ReceiptActionTypes.receiptAndNext:
        // Operation should include callback
        operation = () => ChemReceipt.call({ item }, callback);
        break;
      case ReceiptActionTypes.preReceipt:
        // Operation should include callback
        operation = () => ReceiptItem.call({ item }, callback);
        break;
      default:
        throw new Meteor.Error('Action Type not recognised.', actionType);
    }

    return operation || callback;
  },
};
