import { ReceiptActionTypes } from './receipt-action.types';
import { ReceiptStageTypes } from
  '../../../api/company-site-profiles/configuration/receipt-stage.types';
import { SiteProfileService } from '../company-site-profiles/site-profile.service';

export const ReceiptConfigurationService = {
  configForCurrentSite() {
    const currentSite = SiteProfileService.currentSite();

    if (currentSite) {
      return currentSite.configuration.receipting;
    }

    return undefined;
  },
  stageByPosition(pos) {
    const config = this.configForCurrentSite();

    if (config && config.receiptStages) {
      return config.receiptStages.find((stage) => stage.position === pos);
    }

    return undefined;
  },
  previousStagePosition(pos) {
    const config = this.configForCurrentSite();

    if (config && config.receiptStages) {
      return config.receiptStages.filter((stage) => stage.position < pos)
        .map((stage) => stage.position)
        .reduce((a, b) => Math.max(a, b));
    }

    return 0;
  },
  postReceiptStagePosition() {
    const config = this.configForCurrentSite();

    if (config && config.receiptStages) {
      return config.receiptStages.filter((stage) => stage.type === ReceiptStageTypes.partialReceipt)
        .map((stage) => stage.position)
        .reduce((a, b) => Math.min(a, b));
    }

    return 0;
  },
  availableActionsForStage(pos) {
    const actions = [];
    const stage = this.stageByPosition(pos);

    if (!stage) {
      return actions;
    }

    if (stage.canSubmit) {
      if (stage.type === ReceiptStageTypes.preReceipt) {
        actions.push(...[
          ReceiptActionTypes.preReceipt,
        ]);
      }
      if (stage.type === ReceiptStageTypes.metadata) {
        actions.push(...[
          ReceiptActionTypes.back,
          ReceiptActionTypes.submit,
        ]);
      }
      if (stage.type === ReceiptStageTypes.partialReceipt) {
        actions.push(...[
          ReceiptActionTypes.receipt,
        ]);
      }
      if (stage.type === ReceiptStageTypes.wholeReceipt) {
        actions.push(...[
          ReceiptActionTypes.receipt,
        ]);
      }
    } else {
      // Assume there's a next
      actions.push(...[
        ReceiptActionTypes.next,
      ]);
    }

    return actions;
  },
};
