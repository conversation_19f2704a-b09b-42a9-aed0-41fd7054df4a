import { SiteProfileService } from '../company-site-profiles/site-profile.service';
import moment from 'moment';

const zeroPad = (n, width, z = '0') => {
  const nstr = `${n}`;
  return n.length >= width ? n : new Array(width - nstr.length + 1).join(z) + n;
};

export const ReceiptNoService = {
  currentAutomaticReceiptNo() {
    const receiptNo = SiteProfileService.receiptNoSequence();
    const receiptNoFormat = SiteProfileService.receiptNoFormatStr();

    const now = moment();
    const receiptSequence = receiptNoFormat
      .replace('DD', now.format('DD'))
      .replace('MM', now.format('MM'))
      .replace('YY', now.format('YY'));

    // Default to 2 characters for seq number but allow it to increase if required
    const seqLength = receiptNo > 99
      ? receiptNo.toString().length
      : 2;

    const paddedReceiptNo = zeroPad(receiptNo, seqLength);

    return receiptSequence + paddedReceiptNo;
  },
};
