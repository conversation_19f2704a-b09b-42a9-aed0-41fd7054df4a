import { Template } from 'meteor/templating';

export const ReceiptStageService = {
  position(templateInstance = null) {
    if (templateInstance) {
      return templateInstance.data.stage.position;
    }
    return Template.currentData().stage.position;
  },
  receipt(templateInstance = null) {
    if (templateInstance) {
      return templateInstance.data.receipt.get(this.position(templateInstance));
    }

    return Template.currentData().receipt.get(this.position());
  },
  updateReceipt(receipt, templateInstance) {
    if (!templateInstance) {
      throw new Meteor.Error('Template Instance is required to update receipt');
    }
    templateInstance.data.receipt.set(this.position(templateInstance), receipt);
  },
};
