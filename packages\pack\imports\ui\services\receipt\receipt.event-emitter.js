import { EventEmitter } from 'events';

export const ReceiptEvents = Object.freeze({
  submit: 'submit',
  submitAndNext: 'submitAndNext',
  dimForm: 'dimForm',
  showForm: 'showForm',
});

export class ReceiptEventEmitter {
  constructor() {
    this.eventEmitter = new EventEmitter();
  }

  onSubmit(callback) {
    this.eventEmitter.on(ReceiptEvents.submit, callback);
    return this;
  }

  hasSubmitted() {
    this.eventEmitter.emit(ReceiptEvents.submit);
    return this;
  }

  onSubmitAndNext(callback) {
    this.eventEmitter.on(ReceiptEvents.submitAndNext, callback);
    return this;
  }

  hasSubmittedAndNext() {
    this.eventEmitter.emit(ReceiptEvents.submitAndNext);
  }

  onDimForm(callback) {
    this.eventEmitter.on(ReceiptEvents.dimForm, callback);
    return this;
  }

  dimForm() {
    this.eventEmitter.emit(ReceiptEvents.dimForm);
    return this;
  }

  onShowForm(callback) {
    this.eventEmitter.on(ReceiptEvents.showForm, callback);
    return this;
  }

  showForm() {
    this.eventEmitter.emit(ReceiptEvents.showForm);
    return this;
  }

  removeListener(eventName, callback) {
    this.eventEmitter.removeListener(eventName, callback);
    return this;
  }
}
