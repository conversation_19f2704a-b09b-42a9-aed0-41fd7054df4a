import { Requests } from '../../../api/requests/requests';

export const RequestsService = {
  getRequest(requestId) {
    return Requests.findOne(requestId);
  },
  request(requestId) {
    const request = this.getRequest(requestId);

    return {
      packingUnits() {
        if (!request) return [];

        return request.packingUnits;
      },
      packingUnit(unitId) {
        const unit = this.packingUnits().find(u => u._id === unitId);

        return {
          isClosed() {
            return unit ? unit.isClosed : false;
          },
          items() {
            return unit ? unit.items : [];
          },
          packedCount() {
            return this.items().length;
          },
        };
      },
    };
  },
};
