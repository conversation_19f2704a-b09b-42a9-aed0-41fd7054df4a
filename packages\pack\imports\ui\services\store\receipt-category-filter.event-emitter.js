// This will be used to emit changes to the filter
import { EventEmitter } from 'events';

const eventEmitter = new EventEmitter();

export const ReceiptCategoryFilterEvents = Object.freeze({
  changed: 'receiptCategoryFilterChanged',
});

export const ReceiptCategoryFilterEventEmitter = {
  onFilterChange(callback) {
    eventEmitter.on(ReceiptCategoryFilterEvents.changed, callback);
    return this;
  },

  filterChanged(receiptCategory, offshoreLocation) {
    eventEmitter
      .emit(ReceiptCategoryFilterEvents.changed, receiptCategory, offshoreLocation);
    return this;
  },

  removeListeners() {
    eventEmitter.removeAllListeners();
  },
};
