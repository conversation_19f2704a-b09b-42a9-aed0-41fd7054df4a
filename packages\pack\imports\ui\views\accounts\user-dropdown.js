import './user-dropdown.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Meteor } from 'meteor/meteor';
import { Roles } from 'meteor/alanning:roles';
import { Template } from 'meteor/templating';
import { ChangePrinterModalMethods } from '../printing/change-printer-modal.methods';

const logout = () => {
  Meteor.logout();
  FlowRouter.go('login');
};

const changePassword = () => {
  FlowRouter.go('changePassword');
};

const enrollUser = () => {
  FlowRouter.go('enrollUser');
};

Template.userDropdown.onRendered(() => {
  const templateInstance = this;
  templateInstance.$('#userDropdown').dropdown({
    onChange(value) {
      if (value === 'logout') logout();
      if (value === 'changePassword') changePassword();
      if (value === 'enrollUser') enrollUser();
      if (value === 'changePrinter') ChangePrinterModalMethods.show();
    },
    action: 'hide',
  });
});

Template.userDropdown.helpers({
  username() {
    const user = Meteor.user();
    return user ? user.username : 'User';
  },
  canAddUsers() {
    const user = Meteor.user();
    return (Roles.userIsInRole(user, 'admin', 'peterson-chemicals-dnhr'));
  },
});
