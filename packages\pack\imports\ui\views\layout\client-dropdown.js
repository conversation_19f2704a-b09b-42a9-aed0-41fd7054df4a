import './client-dropdown.html';

import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { SiteProfileService } from '../../services/company-site-profiles/site-profile.service';
import { Template } from 'meteor/templating';

Template.clientDropdown.onCreated(function onCreated() {
});

Template.clientDropdown.onRendered(function onRendered() {
  const template = this;
  template.$('#clientDropdown').dropdown({
    onChange(value, text, $selectedItem) {
      const currentRouteName = FlowRouter.getRouteName();

      if (currentRouteName === 'packlist' || currentRouteName === 'addCcu') { // TODO: Use route groups to make this slicker.
        FlowRouter.go('requests', { clientId: value });
      } else {
        FlowRouter.setParams({ clientId: value });
        FlowRouter.reload();
      }
    },
  });

  template.$('#clientDropdown').dropdown('set selected', FlowRouter.getParam('clientId'));
});

Template.clientDropdown.helpers({
  clients() {
    return SiteProfileService.clients();
  },
});
