<template name="layout">
    <div class="ui top fixed inverted menu">

        <a class="header item" href="{{pathFor 'App.home' query=''}}">
            <img class="ui middle aligned tiny image" src="{{publicUrlFor pkgName '/images/peterson-logo-white.png'}}"/>
        </a>
        <div class="left menu">
            <a class="item" href="{{pathFor 'workItemOverview'}}">
                Cargo
            </a>
            <a class="item" href="{{pathFor 'receiptAtStage' clientId=selectedClient receiptStage=" 2 " }}" style="border-bottom: 3px solid #e7e9ed">
                <span style="padding-top:3px;">Waste</span>
            </a>
        </div>

        {{#if Template.subscriptionsReady}}
        <div class="right menu">
            {{#if selectedClient}} {{#unless onlyOneClient}}
            <div class="item">
                {{> clientDropdown}}
            </div>
            {{/unless}}
            <a class="item" href="{{pathFor 'receiptAtStage' clientId=selectedClient receiptStage=" 2 " }}">
                <i class="check icon"></i> Receipt
            </a>
            <a class="item" href="{{pathFor 'store' clientId=selectedClient}}">
                <i class="building outline icon"></i> Store
            </a>
            <a class="item" href="{{pathFor 'requests' clientId=selectedClient}}">
                <i class="archive icon"></i>Pack
            </a>
            {{> reportsDropdown clientId=selectedClient}} {{/if}} {{#if loggedIn}}
            <div class="item right floated">
                {{> userDropdown}}
            </div>
            {{/if}}
        </div>
        {{/if}}
    </div>
    <div id="main" style="overflow: auto">
        {{#if Template.subscriptionsReady}} {{> Template.dynamic template=main}} {{else}}
        <div class="ui active loader"></div>
        {{/if}}
    </div>
    {{> changePrinterModal }}
</template>