import './layout.html';
import './client-dropdown';
import '../accounts/user-dropdown';
import './reports-dropdown';
import '../printing/change-printer-modal';
import { ChangePrinterModalMethods } from '../printing/change-printer-modal.methods';
import { CompanySiteProfiles } from '../../../api/company-site-profiles/company-site-profiles';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Meteor } from 'meteor/meteor';
import { Publications } from '../../../api/api.publications/publications';
import { ReactiveVar } from 'meteor/reactive-var';
import { Template } from 'meteor/templating';
import { PackageSettings } from 'meteor/pack/imports/startup/package-settings';

require('waypoints/lib/jquery.waypoints.min.js');

Template.layout.onCreated(function onCreated() {
  const template = this;
  template.selectedClientId = new ReactiveVar();

  template.autorun(() => template.subscribe(Publications.companySiteProfiles.companySiteProfileForUser));

  template.autorun(() => {
    FlowRouter.watchPathChange();
    const clientId = FlowRouter.getParam('clientId');
    template.selectedClientId.set(clientId);
  });
});

Template.layout.onRendered(function onRendered() {
  ChangePrinterModalMethods.init();
});

const getClients = () => {
  const siteProfile = CompanySiteProfiles.findOne();

  if (siteProfile) {
    const siteClients = siteProfile.configuration.clients;
    return _.sortBy(siteClients, (client) => client.name);
  }
  return [];
};

Template.layout.helpers({
  clients() {
    return getClients();
  },
  onlyOneClient() {
    return getClients().length > 0;
  },
  selectedClient() {
    return Template.instance().selectedClientId.get();
  },
  loggedIn() {
    const user = Meteor.user();
    return user != null;
  },
  pkgName() {
    return PackageSettings.name;
  },
});

Template.layout.events({
  'click .ui.dropdown .remove.icon': function onClick(event, templateInstance) {
    templateInstance.$(event.target).parent('.dropdown').dropdown('clear');
    event.stopPropagation();
  },
});
