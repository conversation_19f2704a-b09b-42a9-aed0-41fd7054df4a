<template name="clientShortcut">

    <div class="ui card">
        
        <div class="content">
            <a class="header" href="#">{{name}}</a>
        </div>

        <div class="content center aligned">
            <a class="ui centered image" href="#" style="background-color:white">
                <img  class="ui image" src="{{publicUrlFor pkgName (concat '/images/client-logos/' logo)}}">     
            </a>
        </div>

        <div class="extra content">
            <div class="ui three column grid">
                <div class="column" style="padding-left:4px;padding-right:2px;">
                    <button class="receipt-button fluid ui large primary basic icon button">
                        <i class="barcode icon"></i>
                        Receipt
                    </button>
                </div>
                <div class="column" style="padding-left:2px;padding-right:2px;">
                    <button class="store-button fluid ui large black basic icon button">
                        <i class="building outline icon"></i>
                        Store
                    </button>
                </div>
                <div class="column" style="padding-left:2px;padding-right:4px;">
                    <button class="pack-button fluid ui large secondary basic icon button">
                        <i class="shipping icon"></i>
                        Pack
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>