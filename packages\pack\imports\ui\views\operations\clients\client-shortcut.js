import { Meteor } from 'meteor/meteor';
import { Template } from 'meteor/templating';
import { $ } from 'meteor/jquery';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { PackageSettings } from 'meteor/pack/imports/startup/package-settings';
import './client-shortcut.html';

Template.clientShortcut.events({
  'click .receipt-button': function onClick(event) {
    event.preventDefault();
    FlowRouter.go('receiptItem', {
      clientId: this._id,
    });
  },
  'click .pack-button': function onClick(event) {
    event.preventDefault();
    FlowRouter.go('requests', {
      clientId: this._id,
    });
  },
  'click .store-button': function onClick(event) {
    event.preventDefault();
    FlowRouter.go('store', {
      clientId: this._id,
    });
  },
});

Template.clientShortcut.helpers({
  pkgName() {
    return PackageSettings.name;
  },
});
