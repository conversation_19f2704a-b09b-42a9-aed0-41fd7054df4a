<template name="editPurchaseOrder">
  <div class="content">
    <div class="ui container">
      <h1 class="header">Edit - PO {{selectedPo.identifier}}</h1>
      <div class="ui middle aligned very relaxed stackable grid">
        <div class="twelve wide column">
          <div class="ui form">
            <div class="eight wide disabled field">
              <label>Receipt No</label>
              <input type="text" name="receiptNo" value="{{selectedPo.receiptNo}}" readonly>
            </div>
            <div class="fields">
              <div class="eight wide field">
                <label>Receipt Location</label>
                <div class="receiptForm ui fluid selection search dropdown">
                  <input type="hidden" name="receiptLocation" value="{{selectedPo.receiptLocation}}">
                  <i class="dropdown icon"></i>
                  <i class="remove icon"></i>
                  <div class="default text">Receipt Location</div>
                  <div class="menu">
                    {{#each receiptLocations}}
                    <div class="item" data-value="{{name}}" data-text="{{name}}">
                      {{name}}
                    </div>
                    {{/each}}
                  </div>
                </div>
              </div>
              <div class="eight wide field">
                <label>Received Date</label>
                <div class="ui calendar" id="receivedDatepicker">
                  <input type="text" placeholder="Date" name="receivedDate" value="{{receivedDateFormatted}}" />
                </div>
              </div>
            </div>

            <div class="fields">
              <div class="eight wide field">
                <label>PO No</label>
                <input type="text" name="identifier" value="{{selectedPo.identifier}}" />
              </div>
              <div class="eight wide disabled field">
                <label>No. of PO Lines Received</label>
                <input type="text" name="noOfPoLinesRcvd" value="{{selectedPo.noOfLines}}" readonly/>
              </div>
            </div>

            <div class="fields">
              <div class="eight wide field">
                <label>Vendor</label>
                <div class="receiptForm ui fluid selection search dropdown">
                  <input type="hidden" name="vendor" value="{{selectedPo.vendor}}">
                  <i class="dropdown icon"></i>
                  <i class="remove icon"></i>
                  <div class="default text">Vendor</div>
                  <div class="menu">
                    {{#each vendors}}
                    <div class="item" data-value="{{name}}" data-text="{{name}}">
                      {{name}}
                    </div>
                    {{/each}}
                  </div>
                </div>
              </div>
              <div class="eight wide field">
                <label>Vendor Delivery No</label>
                <input type="text" name="vendorDeliveryNo" value="{{selectedPo.vendorDeliveryNo}}" />
              </div>
            </div>
            <div class="field">
              <label>Description</label>
              <input type="text" name="description" value="{{selectedPo.description}}" />
            </div>
          </div>
        </div>
      </div>


      <div class="ui divider"></div>

      <div class="ui grid">
        <div class="four wide column">

          <button class="ui primary labeled icon button disabled save-edit">
            <i class="left save icon"></i>
            Save
          </button>
        </div>
        <div class="twelve wide column ">
          <div class="ui positive message hidden" id="updatesSavedMessageDiv">
            <i class="close icon"></i>
            <div class="header">
              Updates Saved
            </div>
            <p>Updates have been saved.</p>
          </div>

          <div class="ui negative message hidden" id="errorOnSaveMessageDiv">
            <i class="close icon"></i>
            <div class="header">
              Failed to save updates.
            </div>
            <p>Updates have been not been saved; please contact application support.</p>
          </div>
        </div>
      </div>
    </div>
  </div>

</template>