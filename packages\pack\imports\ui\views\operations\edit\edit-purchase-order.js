import './edit-purchase-order.html';
import { DISPLAY_DATETIME_FORMAT } from '../../../../shared/lib/constants';
import { EditPo } from '../../../../api/purchase-orders/commands/edit-po';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Publications } from '../../../../api/api.publications/publications';
import { PurchaseOrders } from '../../../../api/purchase-orders/purchase-orders';
import { SiteProfileService } from '../../../services/company-site-profiles/site-profile.service';
import { Template } from 'meteor/templating';
import moment from 'moment';

const poId = () => FlowRouter.getParam('poId');

const po = () => PurchaseOrders.findOne({ _id: poId() });

const emptyStrToNull = (str) => (str instanceof String && !str.length ? null : str);

const removeMessages = (templateInstance) => {
  if (templateInstance.$('#updatesSavedMessageDiv').hasClass('visible')) {
    templateInstance
      .$('#updatesSavedMessageDiv')
      .closest('.message')
      .transition('fade out');
  }

  if (templateInstance.$('#errorOnSaveMessageDiv').hasClass('visible')) {
    templateInstance
      .$('#errorOnSaveMessageDiv')
      .closest('.message')
      .transition('fade out');
  }
};

const enableButton = (templateInstance) => {
  templateInstance.$('.save-edit').removeClass('disabled');
};

const submitEdit = (templateInstance) => {
  const updatedPo = {
    _id: poId(),
    receivedDate: moment(templateInstance.$('[name=receivedDate]').val()).toDate(),
    identifier: templateInstance.$('[name=identifier]').val(),
    vendor: emptyStrToNull(templateInstance.$('[name=vendor]').val()),
    vendorDeliveryNo: emptyStrToNull(templateInstance.$('[name=vendorDeliveryNo]').val()),
    receiptLocation: emptyStrToNull(templateInstance.$('[name=receiptLocation]').val()),
    description: emptyStrToNull(templateInstance.$('[name=description]').val()),
  };

  EditPo.call(updatedPo, (err) => {
    if (err) {
      templateInstance
        .$('#errorOnSaveMessageDiv')
        .transition('fade in');
    } else {
      templateInstance
        .$('#updatesSavedMessageDiv')
        .transition('fade in');

      templateInstance
        .$('.js-item-save-edit').addClass('disabled');
    }
  });
};

Template.editPurchaseOrder.onCreated(function onCreated() {
  const template = this;

  template.autorun(() => {
    template.subscribe(Publications.purchaseOrders.po, { id: poId() });
  });
});

Template.editPurchaseOrder.onRendered(function onRendered() {
  const template = this;

  const calendarSettings = {
    type: 'datetime',
    maxDate: moment().utc().toDate(), // today
    ampm: false,
    formatter: {
      date: (date, settings) => {
        if (_.isUndefined(date)) return '';
        const day = (`0${date.getDate()}`).slice(-2); // zero pad.
        const month = settings.text.monthsShort[date.getMonth()];
        const year = date.getFullYear();
        return `${day}-${month}-${year}`;
      },
      time: (date) => {
        if (_.isUndefined(date)) return '';
        const hours = (`0${date.getHours()}`).slice(-2); // zero pad.
        const minutes = (`0${date.getMinutes()}`).slice(-2); // zero pad.
        return `${hours}:${minutes}`;
      },
    },
  };

  template.autorun(() => {
    if (template.subscriptionsReady()) {
      template.$('.dropdown').dropdown();
      template.$('.calendar').calendar(calendarSettings);
    }
  });
});

Template.editPurchaseOrder.helpers({
  selectedPo() {
    return po();
  },
  vendors() {
    return SiteProfileService.vendors();
  },
  receiptLocations() {
    return SiteProfileService.receiptLocations();
  },
  receivedDateFormatted() {
    const purchaseOrder = po();

    if (purchaseOrder) {
      return moment(purchaseOrder.receivedDate).format(DISPLAY_DATETIME_FORMAT);
    }

    return '';
  },
});

Template.editPurchaseOrder.events({
  'click .save-edit': function onClick(event, templateInstance) {
    submitEdit(templateInstance);
  },
  'change input, keyup input, click .calendar': function onChange(event, templateInstance) {
    removeMessages(templateInstance);
    enableButton(templateInstance);
  },
  'click .message': function onClick(event, templateInstance) {
    templateInstance
      .$(event.currentTarget)
      .transition('fade out');
  },
});
