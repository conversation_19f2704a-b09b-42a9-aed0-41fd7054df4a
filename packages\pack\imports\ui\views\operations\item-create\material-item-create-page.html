<template name="materialItemCreatePage">
    <div class="ui container">

        <div class="ui grid"  style="margin-top:0.7em">
            <div class="twelve wide column">
                <h1> Create and Receipt Waste Item </h1> 
            </div>
    
            <div class="four wide column">
                <button class="ui positive labeled icon button js-return-to-receipt-button">
                <i class="clockwise rotated level down alternate icon"></i>
                Return to Receipt
                </button>
            </div>
        </div>

       <div class="ui divider"></div>
       {{#if Template.subscriptionsReady}}
             {{> materialItemCreate selectedItem=itemDefault}}
       {{else}}
           <div class="ui active text loader">Loading...</div>
       {{/if}}
    </div>
</template>