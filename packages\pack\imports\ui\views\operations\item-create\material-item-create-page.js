import './material-item-create-page.html';
// Components used inside the template.
import '../../../components/material-item-create';
import { Items } from '../../../../api/items/items';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Publications } from '../../../../api/api.publications/publications';
import { CompanySiteProfiles } from '../../../../api/company-site-profiles/company-site-profiles';

const dummyItem =
{
  _id: null, // Define on insert.
  receiptNo: null, // Set when saved - show current latest receipt no.
  itemLineIndex: 0, // Only relevant if from Flow material line.
  cargoItemId: null,
  externalCargoLineId: null,
  receiptType: null, // set when saved.
  receivedDate: null, // set when saved.
  siteId: null, // Set dynamically based on user's site.
  client: {}, // Set dynamically
  receiptLocation: null,
  offshoreClient: null, // Set in UI.
  ccu: null, // keep as null, this has not come from a (Flow) CCU.
  offshoreLocation: null, // Set in UI.
  voyageNo: null, // Leave as null.
  ccuManifestNo: null, // Leave as null.
  weightKg: null, // Set in UI.
  externalMaterialLineId: null,
  ecargoMaterialInfo: null,
  euralCode: null, // Set in UI.
  description: null, // Set in UI.
  imoHazardClass: null, // Set in UI.
  imoSubClass: null, // Set in UI.
  unNo: null, // Set in UI.
  imoCode: null, // Set in UI.
  isWaste: true, // Default to true - looks like materials processed by Peterson Chemicals are all waste.
  materialManifestNo: null,
  packingUnit: null,
  marinePollutant: null, // (bool) Set in UI.
  wasteDescription: null, // Set in UI.
  quantity: null,
  events: [], // Set first receipted event when saved.
  isStored: false,
  isPacked: false,
  isDispatched: false,
  receiptCategory: 'Standard Receipt',
  ncrs: [],
  materialReceiptDateTime: null, // ISODate('2018-11-21T09:50:34.435Z'),
  packageType: null, // Set in UI.
};

function getSiteProfile() {
  // Assumes only one loaded clients-side.
  return CompanySiteProfiles.findOne();
}

Template.materialItemCreatePage.onCreated(function onCreated() {
  const template = this;

  template.clientId = FlowRouter.getParam('clientId');
  template.itemId = FlowRouter.getParam('itemId');

  template.itemLoaded = new ReactiveVar(false);
  template.item = new ReactiveVar(null);

  const siteProfile = getSiteProfile();
  const client = siteProfile.configuration.clients[0]; // Assumes one and only client - ok for DH Chemicals. (Peterson)

  // If no item specified on load use dummyItem values as starting point.
  if (template.itemId === '0') {
    // Use dummyItem as starting point.
    const item = JSON.parse(JSON.stringify(dummyItem));

    // Overwrite params with dynamically assigned values.
    item.client = client;
    item.siteId = siteProfile.identifier; // 'peterson-chemicals-dnhr'

    template.item.set(item);
    template.itemLoaded.set(true);
  } else {
    // create object to pass to subscription.
    template.itemSelector = {
      itemId: template.itemId,
    };

    template.autorun(() => {
      template.subscription = template.subscribe(
        Publications.items.selectedItemForEdit,
        template.itemSelector,
      );

      if (template.subscription.ready()) {
        template.itemLoaded.set(true);
        template.item.set(Items.findOne(template.itemId));
      }
    });
  }
});

Template.materialItemCreatePage.onDestroyed(() => {
  // Make sure we destory the item details modal that got added to document after saving,
  // otherwise it lives on as a zombie modal and haunts other pages.
  const modalClassName = '.ui.modal.item-details-modal';
  $(modalClassName).modal('destroy');
  $(modalClassName).remove();
});

Template.materialItemCreatePage.onRendered(() => {
});

Template.materialItemCreatePage.events({
  'click .js-return-to-receipt-button': function returnToReceiptButtonClicked() {
    FlowRouter.go(
      'receiptAtStage',
      {
        clientId: FlowRouter.getParam('clientId'),
        receiptStage: 2,
      },
      {},
    );
  },
});

Template.materialItemCreatePage.helpers({
  itemDefault() {
    return Template.instance().item.get();
  },
});
