import './material-item-edit-page.html';
// Components used inside the template.
import '../../../components/material-item-edit';
import { $ } from 'meteor/jquery';
import { Items } from '../../../../api/items/items';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Publications } from '../../../../api/api.publications/publications';

Template.materialItemEditPage.onCreated(function onCreated() {
  const template = this;

  template.clientId = FlowRouter.getParam('clientId');
  template.itemId = FlowRouter.getParam('itemId');

  template.itemLoaded = new ReactiveVar(false);
  template.item = new ReactiveVar(null);

  // create object to pass to subscription.
  template.itemSelector = {
    itemId: template.itemId,
  };

  // Autorun.
  template.autorun(() => {
    // Subscribe to the publication.
    template.subscription = template.subscribe(
      Publications.items.selectedItemForEdit,
      template.itemSelector,
    );

    if (template.subscription.ready()) {
      template.itemLoaded.set(true);
      template.item.set(Items.findOne(template.itemId));
    }
  });
});

Template.materialItemEditPage.onRendered(function onRendered() {
});

Template.materialItemEditPage.events({
});

Template.materialItemEditPage.helpers({
  selectedItem: function getSelectedItem() {
    return Template.instance().item.get();
  },
});
