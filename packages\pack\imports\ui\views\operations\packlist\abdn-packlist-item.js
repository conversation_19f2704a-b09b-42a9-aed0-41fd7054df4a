import './abdn-packlist-item.html';
import './vehicle-loading-buttons/returned-delivered-buttons';
import { AbdnRequests } from '../../../../api/abdn-requests/abdn-requests';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Template } from 'meteor/templating';

const isItemPacked = (itemId, packingUnit) => {
  const request = AbdnRequests.findOne(FlowRouter.getParam('requestId'));
  if (request && request.packingUnits && itemId && packingUnit) {
    return _.contains(packingUnit.items, itemId);
  }
  return false;
};

Template.abdnPacklistItem.helpers({
  itemClass: function getItemClass() {
    return isItemPacked(this._id, this.selectedPackingUnit) ? 'negative' : '';
  },
  isItemPacked: function getIsItemPacked() {
    return isItemPacked(this._id, this.selectedPackingUnit);
  },
  unitLoadedIn: function unitLoadedIn() {
    const request = AbdnRequests.findOne(FlowRouter.getParam('requestId'));
    return request.packingUnits[0].identifier;
  },
  displayingClosedUnit: function displayingClosedUnit() {
    const unit = this.selectedPackingUnit;
    if (unit) {
      return unit.isClosed;
    }
    return false;
  },
  isDGFormatted: function isDGFormatted() {
    const isDG = this.isDangerousGoods;
    return isDG ?
      `<span class="dg-icon" data-tooltip="${Template.instance().data.dgClassification}"
      data-inverted=""><i class="large red warning circle icon" style="pointer-events:none;"></i></span>` :
      '&nbsp;&nbsp;&nbsp;&nbsp;-';
  },
});
