<template name="abdnPacklist">
  <div class="row checklist ui default-padding fluid container" style="height:75%;">
    <div class="ui grid">
      <div class="row" style="margin-bottom: 10px;">
        <div class="seven wide column">
          <div class="ui two column grid">
            <div class="seven wide column">
              {{> clientHeader headerText="Pack" }}
            </div>
            <div class="nine wide column">
              <h3>{{requestDateFormatted}} {{requestTimeFormatted}} - {{requestIdentifier}}</h3>
              {{#each currentRequest.destinations}}
              <h5>{{name}}</h5>
              {{/each}}
            </div>
          </div>
        </div>
        <div class="nine wide middle aligned padded-top column">
          <div class="ui big secondary compact menu right floated">
            {{#if isUnitAssigned}} {{#if isClosedUnit}}
            <div class="horizontally fitted item">
              <button class="ui labeled icon basic blue button {{isGeneratingManifest}}" id="exportManifestButton">
                <i class = "download icon"></i> Export Manifest
              </button>
            </div>
            {{/if}}
            <div class="horizontally fitted item">
              {{#if isClosedUnit}}
              <div class="ui blue basic large negative button {{isOpeningOrClosingUnit}}" id="openUnitButton">Open Unit</div>
              {{else}}
              <div class="ui blue basic large button {{isOpeningOrClosingUnit}}" id="closeUnitButton">Close Unit</div>
              {{/if}}
            </div>
            {{/if}}
            <div class="horizontally fitted item">
              <div class="ui primary floating labeled icon dropdown basic button">
                <input type="hidden" name="packingUnit" value="">
                <i class="{{selectedUnitIcon}} icon"></i>
                <span class="text">Packing Units</span>
                <div class="menu">
                  <div class="header">
                    Packing Units
                  </div>
                  <div class="divider"></div>
                  {{#each packingUnits}}
                  <div class="item" data-value="{{_id}}" data-type="{{unitType}}">
                    <span class="description">{{identifier}}</span>
                    <span class="text">{{packedCount _id}}</span>
                  </div>
                  {{/each}}
                </div>
              </div>
            </div>
            <div class="horizontally fitted item">
              <div class="ui primary basic labeled icon button {{isAssigningUnit}}" id="assignUnitButton">
                <i class="edit icon"></i> {{assignUnitButtonText}}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="ui horizontal divider"></div>
    <div class="ui grid">
      <div class="two wide column left aligned">
        <h3 class="header">{{tableText}}</h3>
      </div>
      <div class="five wide right aligned column" style="padding-top: 1.5rem;">
        {{#if displayUpdateSelection }}
        <div class="ui toggle checkbox" id="viewSelectedItemsOnly">
            <input type="checkbox" name="viewSelectedItemsOnly" />
            <label>Selected Items Only</label>
          </div>
        {{/if}}
      </div>
      <div class="six wide column">
        <div class="ui fluid icon input">
          <input class="prompt" type="text" placeholder="Filter..." name="filterPackList">
          <i class="search icon"></i>
        </div>
      </div>
      <div class="three wide right aligned column" style="padding-top: 0.7rem;">
        {{#unless isClosedUnit}}
        <div class="ui large shadowed buttons">
          <button class="ui bordered button not-packed-btn {{viewNotPackedActive}}">Not Packed</button>
          <div class="or"></div>
          <button class="ui bordered button packed-btn {{viewPackedActive}}">Packed</button>
        </div>
        {{/unless}}
      </div>
    </div>
    <div class="ui fluid container" style="margin-top:10px;">
      {{#if Template.subscriptionsReady }}
      {{#if isUnitAssigned }}
        {{> packlistTable items=items selectedItems=selectedItems selectedUnit=selectedUnit}}
        <div class="infinite-scroll-element"></div>
      {{else}}
      <div class="ui horizontal divider"></div>
      <div class="ui visible floating info massive message" style="text-align: center;">
        Please Select a Packing Unit
      </div>
      {{/if}}
      {{else}}
      <div class="ui active loader"></div>
      {{/if}}
    </div>
  </div>
  {{> assignUnitModal }}
  {{> abdnItemDetailsModal selectedItem=selectedItem}}
</template>