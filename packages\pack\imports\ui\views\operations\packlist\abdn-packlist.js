import './abdn-packlist.html';
import './abdn-packlist-item';
import './unit-options/assign-unit-modal';
import '../../../components/client-header';
import './packlist-table';
import '../../../components/abdn-item-details-modal';
import { AbdnRequests, AbdnRequestsPackingUnitTypes } from '../../../../api/abdn-requests/abdn-requests';
import { AbdnItemDetailsModalMethods } from '../../../components/abdn-item-details-modal.methods';
import { AssignPackingUnitToRequest } from '../../../../api/requests/commands/assign-packing-unit-to-request';
import { AssignUnitModalMethods } from './unit-options/assign-unit-modal.methods';
import { ClosePackingUnit } from '../../../../api/requests/commands/close-packing-unit';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Items } from '../../../../api/items/items';
import { Meteor } from 'meteor/meteor';
import { OpenPackingUnit } from '../../../../api/requests/commands/open-packing-unit';
import { PackItemsIntoUnit } from '../../../../api/requests/commands/pack-items-into-unit';
import { PacklistService } from '../../../services/packlist/packlist.service';
import { Publications } from '../../../../api/api.publications/publications';
import { ReactiveVar } from 'meteor/reactive-var';
import { Requests } from '../../../../api/requests/requests';
import { SiteProfileService } from '../../../services/company-site-profiles/site-profile.service';
import { Template } from 'meteor/templating';
import { UnpackItemsFromUnit } from '../../../../api/requests/commands/unpack-items-from-unit';
import { _ } from 'meteor/underscore';
import { manifestPdf } from '../../../pdf/abdn-manifest';
import moment from 'moment';

const getItemIdFromTableRowElement = ($el) =>
  $($el)
    .closest('.packlist-item-row')
    .data('item-id');

const packItem = (itemIds, requestId, packingUnitId) =>
  PackItemsIntoUnit.call({ itemIds, requestId, packingUnitId });

const unpackItem = (itemIds, requestId, packingUnitId) =>
  UnpackItemsFromUnit.call({ itemIds, requestId, packingUnitId });

const getFirstPackingUnit = () => {
  const packingUnits = PacklistService
    .packingUnits();

  if (packingUnits && packingUnits.length) {
    return packingUnits[0];
  }
  return false;
};

const getUnitFromPackingUnitsList = (unitId, packingUnits) => {
  return packingUnits.find((unit) => {
    return unit._id === unitId;
  });
};

const setSelectedUnit = (templateInstance) => {
  templateInstance.$('.dropdown')
    .dropdown('set selected', templateInstance.selectedUnitId.get());
};

const clearBulkSelectedItems = (templateInstance) => {
  templateInstance.bulkUpdateSelectedItems.set([]);
  $('.group-pack-checkbox').checkbox('uncheck');
  templateInstance.selectedItemsOnly.set(false);
};

const isClosedUnit = (templateInstance) => {
  const selectedUnitId = templateInstance.selectedUnitId.get();

  return PacklistService
    .packingUnit(selectedUnitId)
    .isClosed();
};

const setUpPackingUnitDropdown = (templateInstance) => {
  // Setup packing unit dropdown
  templateInstance.$('.dropdown').dropdown({
    onChange: (value, text, $choice) => {
      templateInstance.selectedUnitId.set(value);
    },
  });
  // observe changes to the items for dropdown counter
  templateInstance.abdnRequestsCursor.observeChanges({
    changed: (id, fields) => {
      if (fields && fields.packingUnits) {
        // update must of been for pack/unpack event
        // timeout used for rendering purposes
        Meteor.setTimeout(() => {
          setSelectedUnit(templateInstance);
        }, 100);
      }
    },
  });
  // if packing units present select first one
  const unit = getFirstPackingUnit();
  if (unit) {
    templateInstance.selectedUnitId.set(unit._id);
    setSelectedUnit(templateInstance);
  }
};

Template.abdnPacklist.onCreated(function onCreated() {
  const template = this;

  // Set to item id for bounce animation on most recently loaded
  template.checkForTransitionAnimation = new ReactiveVar(false);
  // Vars for storing filter query term and query results
  template.packingListFilter = new ReactiveVar('');
  // Used to define number of results returned
  template.moreItemsIncrement = 5;
  template.packingListLimit = new ReactiveVar(30);
  // Used for determining state of view loaded items checkbox (checkbox initialised to false)
  template.packingListPackedCheckbox = new ReactiveVar(false);
  template.packedItemsOnly = new ReactiveVar(false);
  // Used for selecting what unit to pack into
  template.selectedUnitId = new ReactiveVar(null);
  // Used for recording most recently packed items
  template.mostRecentlyPackedId = new ReactiveVar(null);

  template.bulkUpdateSelectedItems = new ReactiveVar([]);
  template.selectedItemsOnly = new ReactiveVar(false);
  template.selectedItem = new ReactiveVar();
  template.isAssigningUnit = new ReactiveVar(false);
  template.isGeneratingManifest = new ReactiveVar(false);
  template.isOpeningOrClosingUnit = new ReactiveVar(false);

  template.autorun(() => {
    const packedOrCanPackSelector = {
      clientId: FlowRouter.getParam('clientId'),
      query: template.packingListFilter.get(),
      limit: template.packingListLimit.get(),
      packedItemsOnly: template.packedItemsOnly.get(),
    };

    if (template.packedItemsOnly.get()) {
      packedOrCanPackSelector.requestId = FlowRouter.getParam('requestId');
      packedOrCanPackSelector.packingUnitId = template.selectedUnitId.get();
    }

    const selectedItems = template.bulkUpdateSelectedItems.get();

    if (template.selectedItemsOnly.get() && selectedItems.length) {
      packedOrCanPackSelector.selectedItemsOnly = selectedItems;
    }

    template.subscribe(Publications.items.packedOrCanPack, packedOrCanPackSelector);

    template.subscribe(Publications.requests.request, {
      requestId: FlowRouter.getParam('requestId'),
    });
  });
});

Template.abdnPacklist.onRendered(function onRendered() {
  const template = this;
  // Hide dimmer initially.
  template.$('packingListSegment').dimmer('hide');

  // Setup cursor that is used for observing changes and updating
  // selected packing unit counter in dropdown
  template.abdnRequestsCursor =
    Requests.find({ _id: FlowRouter.getParam('requestId') });

  setUpPackingUnitDropdown(template);

  template.autorun(() => {
    const closedUnit = isClosedUnit(template);

    if (closedUnit) {
      template.packedItemsOnly.set(true);
    } else {
      template.packedItemsOnly.set(false);
    }
  });

  const setupInfiniteScroll = () => {
    template.$('.infinite-scroll-element')
      .waypoint((direction) => {
        if (direction === 'down') {
          const limit = template.packingListLimit.get();
          template.packingListLimit.set(limit + template.moreItemsIncrement);
        }
      }, { context: '#main', offset: '100%' });
  };

  setupInfiniteScroll();
});

Template.abdnPacklist.helpers({
  currentRequest() {
    return PacklistService.currentRequest();
  },
  currentClient() {
    return SiteProfileService.currentClient();
  },
  isClosedUnit() {
    return isClosedUnit(Template.instance());
  },
  viewPackedOnly() {
    return Template.instance().packingListPackedCheckbox.get();
  },
  viewPackedActive() {
    return Template.instance().packedItemsOnly.get() ? 'positive active' : '';
  },
  viewNotPackedActive() {
    return Template.instance().packedItemsOnly.get() ? '' : 'positive active';
  },
  getMostRecentItemsInUnit() {
    const request = Requests.findOne(FlowRouter.getParam('requestId'));
    if (request) {
      const selectedUnitId = Template.instance().selectedUnitId.get();
      const unit = getUnitFromPackingUnitsList(selectedUnitId, request.packingUnits);

      if (!unit) return [];

      const mostRecent = _.last(unit.items, 3);
      const items = Items.find({ _id: { $in: mostRecent } }).fetch();
      return items;
    }
    return [];
  },
  anyItems: function getAnyItems() {
    const itemsCount = Items.find().count();
    return itemsCount > 0;
  },
  items() {
    return Items.find().fetch();
  },
  assignUnitButtonText: function assignUnitButtonText() {
    return 'Assign Unit';
  },
  unitWithContents: function getUnitWithContents() {
    return {};
  },
  isUnitAssigned() {
    return PacklistService
      .packingUnits()
      .length > 0;
  },
  packingUnits() {
    return PacklistService
      .packingUnits();
  },
  packedCount(packingUnitId) {
    return PacklistService
      .packingUnit(packingUnitId)
      .packedCount();
  },
  itemWithSelectedPackingUnit: function getItemWithSelectedPackingUnit() {
    const request = Requests.findOne(FlowRouter.getParam('requestId'));
    if (request) {
      const selectedUnitId = Template.instance().selectedUnitId.get();
      const unit = getUnitFromPackingUnitsList(selectedUnitId, request.packingUnits);
      if (unit) {
        return _.extend({ selectedPackingUnit: unit }, this);
      }
    }
    return this;
  },
  requestDateFormatted: function requestDateFormatted() {
    const request = Requests.findOne(FlowRouter.getParam('requestId'));
    if (request) {
      return moment(request.scheduledDateTime).format('DD-MMM-YYYY');
    }
    return '';
  },
  requestTimeFormatted: function requestTimeFormatted() {
    const request = Requests.findOne(FlowRouter.getParam('requestId'));
    if (request) {
      return moment(request.scheduledDateTime).format('HH:mm');
    }
    return '';
  },
  requestIdentifier: function requestIdentifier() {
    const request = Requests.findOne(FlowRouter.getParam('requestId'));
    if (request) {
      return `${request.packingRequestRefNo}`;
    }
    return '';
  },
  selectedUnitIcon: function selectedUnitIcon() {
    const request = Requests.findOne(FlowRouter.getParam('requestId'));
    if (request) {
      const selectedUnitId = Template.instance().selectedUnitId.get();
      const unit = getUnitFromPackingUnitsList(selectedUnitId, request.packingUnits);
      if (unit) {
        if (unit.isClosed) {
          return 'red lock';
        } else if (unit.unitType === AbdnRequestsPackingUnitTypes.CCU) {
          return 'archive';
        } else if (unit.unitType === AbdnRequestsPackingUnitTypes.VEHICLE) {
          return 'shipping';
        }
      }
    }
    return 'archive';
  },
  tableText() {
    const isPackedOnly = Template.instance().packedItemsOnly.get();

    return isPackedOnly ? 'Packed Items' : 'Items to Pack';
  },
  displayUpdateSelection() {
    const selectedItems = Template.instance().bulkUpdateSelectedItems.get();

    return selectedItems && selectedItems.length;
  },
  selectedItems() {
    return Template.instance().bulkUpdateSelectedItems.get();
  },
  updateSelectionText() {
    const isPackedOnly = Template.instance().packedItemsOnly.get();

    return isPackedOnly ? 'Unpack Items' : 'Pack Items';
  },
  selectedUnit() {
    return Template.instance().selectedUnitId.get();
  },
  selectedItem() {
    return Template.instance().selectedItem.get();
  },
  isAssigningUnit() {
    return Template.instance().isAssigningUnit.get() ? 'disabled loading' : '';
  },
  isGeneratingManifest() {
    return Template.instance().isGeneratingManifest.get() ? 'disabled loading' : '';
  },
  isOpeningOrClosingUnit() {
    return Template.instance().isOpeningOrClosingUnit.get() ? 'disabled loading' : '';
  },
});

Template.abdnPacklist.events({
  'click .packlist-item-row': function onClick(event, templateInstance) {
    const evTarget = $(event.target);

    if (!(evTarget.hasClass('button')
          || evTarget.hasClass('checkbox')
          || evTarget.hasClass('checkbox-cell')
          || evTarget.hasClass('btn-cell')
          || evTarget.is('label'))) {
      const item = this.item;
      templateInstance.selectedItem.set(item);
      AbdnItemDetailsModalMethods
        .init(item)
        .show();
    }
  },
  'change #viewSelectedItemsOnly': function onChange(event, templateInstance) {
    const isChecked = templateInstance.$(event.target).is(':checked');
    templateInstance.selectedItemsOnly.set(isChecked);
  },
  'click .not-packed-btn': function onClick(event, templateInstance) {
    templateInstance.packedItemsOnly.set(false);
    clearBulkSelectedItems(templateInstance);
  },
  'click .packed-btn': function onClick(event, templateInstance) {
    templateInstance.packedItemsOnly.set(true);
    clearBulkSelectedItems(templateInstance);
  },
  'click #assignUnitButton': function onClick(event, templateInstance) {
    event.preventDefault();
    AssignUnitModalMethods
      .init(() => {
        const requestId = FlowRouter.getParam('requestId');
        const packingUnitType = $('[name="unitType"]:checked').val();
        const packingUnitIdentifier = $('[name="unitIdentifier"]').val();
        templateInstance.isAssigningUnit.set(true);
        AssignPackingUnitToRequest.call({ requestId, packingUnitType, packingUnitIdentifier }, (error, result) => {
          if (!error) {
            templateInstance.selectedUnitId.set(result);
            setSelectedUnit(templateInstance);
          }

          templateInstance.isAssigningUnit.set(false);
        });
      }).show();
  },
  'click #openUnitButton': function onClick(event, templateInstance) {
    event.preventDefault();
    OpenPackingUnit.call({
      requestId: FlowRouter.getParam('requestId'),
      packingUnitId: templateInstance.selectedUnitId.get(),
    });
  },
  'click #closeUnitButton': function onClick(event, templateInstance) {
    event.preventDefault();
    ClosePackingUnit.call({
      requestId: FlowRouter.getParam('requestId'),
      packingUnitId: templateInstance.selectedUnitId.get(),
    });
  },
  'click #packButton': function onClick(event, templateInstance) {
    event.preventDefault();
    const itemId = getItemIdFromTableRowElement($(event.currentTarget));
    const requestId = FlowRouter.getParam('requestId');
    const packingUnitId = templateInstance.selectedUnitId.get();
    const selection = templateInstance.bulkUpdateSelectedItems.get();

    if (selection && selection.length) {
      packItem(selection, requestId, packingUnitId);
      clearBulkSelectedItems(templateInstance);
    } else {
      packItem([itemId], requestId, packingUnitId);
    }
  },
  'click #unpackButton': function onClick(event, templateInstance) {
    event.preventDefault();
    const itemId = getItemIdFromTableRowElement($(event.currentTarget));
    const requestId = FlowRouter.getParam('requestId');
    const packingUnitId = templateInstance.selectedUnitId.get();
    const selection = templateInstance.bulkUpdateSelectedItems.get();

    if (selection && selection.length) {
      unpackItem(selection, requestId, packingUnitId);
      clearBulkSelectedItems(templateInstance);
    } else {
      unpackItem([itemId], requestId, packingUnitId);
    }
  },
  'input [name=filterPackList]': function onInput(event, templateInstance) {
    event.preventDefault();
    templateInstance.packingListFilter.set(event.target.value);
  },
  'change [name=limitToPacked]': function onChange(event, templateInstance) {
    event.preventDefault();
    templateInstance.packingListPackedCheckbox.set(event.target.checked);
  },
  'click .recentPacking': function onClick(event, templateInstance) {
    event.preventDefault();
    const clickedItem = templateInstance.$(`#${event.target.id}`);
    templateInstance.$('[name=limitToPacked]').prop('checked', true);
    templateInstance.packingListPackedCheckbox.set(true);
    // Scroll to item in list and animate with flash
    Meteor.defer(() => {
      if (templateInstance.subscriptionsReady) {
        templateInstance.$('#scrollingSegment').scrollTo(clickedItem);
        templateInstance.$(clickedItem).transition('pulse');
      }
    });
  },
  'click #loadMoreItems': function onClick(event, templateInstance) {
    event.preventDefault();
    const currentLimit = templateInstance.packingListLimit.get();
    templateInstance.packingListLimit.set(currentLimit + 50);
  },
  'click #exportManifestButton': function onClick(event, templateInstance) {
    event.preventDefault();
    templateInstance.isGeneratingManifest.set(true);
    const request = Requests.findOne(FlowRouter.getParam('requestId'));
    const packingUnitId = templateInstance.selectedUnitId.get(); // CCU or vehicle.
    const manifestDetails = _.clone(request);

    const unit = manifestDetails
      .packingUnits
      .find((packingUnit) => packingUnit._id === packingUnitId);

    manifestDetails.ccuOrVehicle = unit.identifier;
    manifestDetails.currentUser = Meteor.user().username;

    const packingRequestRefNoForFilename = request.packingRequestRefNo.replace(/\//g, '');

    const itemsForManifest = {};
    let destinationsCombined = '';
    _.each(manifestDetails.destinations, (element, index) => {
      if (index > 0) {
        destinationsCombined += ', ';
      }
      destinationsCombined += element.name;
    });

    // Set dest as key and value as array of hydrated items.
    itemsForManifest[destinationsCombined] = [];

    _.each(unit.items, (element) => {
      const itemId = element;
      const item = Items.findOne(itemId);
      itemsForManifest[destinationsCombined].push(item);
    });
    manifestDetails.itemsForManifest = itemsForManifest;

    const createTimestamp = moment().format('YYYY-MMM-DD-HH:mm');
    const saveFileName = `MANIFEST_${packingRequestRefNoForFilename}_${createTimestamp}.pdf`;

    manifestPdf.create(manifestDetails)
      .then((doc) => {
        doc.save(saveFileName);
        templateInstance.isGeneratingManifest.set(false);
      })
      .catch((e) => console.error(`Failed to output manifest doc: ${e}`));
  },
  'click .group-pack-checkbox': function onClick(event, templateInstance) {
    const selectedItems = templateInstance.bulkUpdateSelectedItems.get();
    const checkbox = $(event.currentTarget);
    const itemId = getItemIdFromTableRowElement(checkbox);

    if (checkbox.checkbox('is checked')) {
      templateInstance.bulkUpdateSelectedItems
        .set(selectedItems.concat([itemId]));
    } else {
      templateInstance.bulkUpdateSelectedItems
        .set(selectedItems.filter((id) => id !== itemId));
    }
  },
});
