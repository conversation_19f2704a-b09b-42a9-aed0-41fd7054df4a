import './ccu-option.html';
import { $ } from 'meteor/jquery';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import { Requests } from '../../../../api/requests/requests';
import { Session } from 'meteor/session';
import { Template } from 'meteor/templating';
import { Vehicles } from '../../../../api/vehicles/vehicles';
import { _ } from 'meteor/underscore';

Template.ccuOption.helpers({
  isActiveCcu() {
    const selectedCcuId = Session.get('packlist.selectedCcuId');
    return selectedCcuId === this._id;
  },
});

Template.ccuOption.events({
  'click .ccu-link-item': function onClick(event, template) {
    event.preventDefault();
    Session.set('packlist.selectedCcuId', this._id);
  },
});
