<template name="chemPacklist">
  <div class="row checklist ui default-padding fluid container" style="height:75%;">
    <div class="ui grid">

      {{#unless portraitMode}}
      
      <div class="row">
        <div class="three wide column">
          {{> clientHeader headerText="Pack" }}
        </div>
        <div class="thirteen wide right aligned column">
            <div class="ui small horizontal list">
              <div class="item">
                  <h3 style="margin-bottom: 0;">{{requestDateFormatted}} {{requestTimeFormatted}} - {{requestIdentifier}}</h3>
              </div>
              {{#each currentRequest.destinations}}
                <div class="item"><span class="ui blue label">{{name}}</span></div>
              {{/each}}
            </div>
        </div>
      </div>

      <div class="five column row" style="padding: 0px">
          <div class="column">
               <h3 class="header">{{tableText}}</h3> 
          </div>

          <div class="column">
              {{#if isUnitAssigned}} 
                {{#if isClosedUnit}}
                <div class="fluid ui blue basic negative button {{isOpeningOrClosingUnit}}" id="openUnitButton">Open Unit</div>
                {{else}}
                <div class="fluid ui blue basic button {{isOpeningOrClosingUnit}}" id="closeUnitButton">Close Unit</div>
                {{/if}}
              {{/if}}
          </div>
          <div class="column">
            {{#if isUnitAssigned}} 
              {{#unless isClosedUnit}}
              <div class="fluid ui labeled icon basic blue basic button {{isOpeningOrClosingUnit}}" id="scanModalButton"> 
                <i class = "barcode icon"></i> Scan Items
              </div>
              {{else}}
              <button class="fluid ui labeled icon basic blue basic button {{isGeneratingOuttakeReport}}" id="exportOuttakeReportButton">
                <i class = "download icon"></i> Outtake Report
              </button>
              {{/unless}}
            {{/if}}
          </div>
          <div class="column">
            <div class="fluid ui primary floating labeled icon dropdown packlist-dropdown basic button">
              <input type="hidden" name="packingUnit" value="">
              <i class="{{selectedUnitIcon}} icon"></i>
              <span class="text">Packing Units</span>
              <div class="menu">
                <div class="header">
                  Packing Units
                </div>
                <div class="divider"></div>
                {{#each packingUnits}}
                <div class="item" data-value="{{_id}}" data-type="{{unitType}}">
                  <span class="description">{{identifier}}</span>
                  <span class="text">{{packedCount _id}}</span>
                </div>
                {{/each}}
              </div>
            </div>
          </div>
          <div class="column">
            <div class="fluid ui primary basic labeled icon button {{isAssigningUnit}}" id="assignUnitButton">
              <i class="edit icon"></i> {{assignUnitButtonText}}
            </div>
          </div>

        </div>

      {{else}}
        
        <div class="row">
          <div class="six wide column">
            {{> clientHeader headerText="Pack" }}
          </div>
          <div class="ten wide column right aligned">
            <h3 style="margin-bottom: 0;">{{requestDateFormatted}} {{requestTimeFormatted}} - {{requestIdentifier}}</h3>
            <div class="ui small horizontal list">
              {{#each currentRequest.destinations}}
                <div class="item"><span class="ui blue label">{{name}}</span></div>
              {{/each}}
            </div>
          </div>
        </div>

        <div class="row">
          <div class="three wide column">
              {{#if isUnitAssigned}} 
                {{#if isClosedUnit}}
                <div class="fluid ui blue basic negative button {{isOpeningOrClosingUnit}}" id="openUnitButton">Open Unit</div>
                {{else}}
                <div class="fluid ui blue basic button {{isOpeningOrClosingUnit}}" id="closeUnitButton">Close Unit</div>
                {{/if}}
              {{/if}}
          </div>
          <div class="five wide column">
            {{#if isUnitAssigned}} 
              {{#unless isClosedUnit}}
              <div class="fluid ui labeled icon basic blue basic button {{isOpeningOrClosingUnit}}" id="scanModalButton"> 
                <i class = "barcode icon"></i> Scan Items
              </div>
              {{else}}
              <button class="fluid ui labeled icon basic blue basic button {{isGeneratingOuttakeReport}}" id="exportOuttakeReportButton">
                <i class = "download icon"></i> Outtake Report
              </button>
              {{/unless}}
            {{/if}}
          </div>
          <div class="four wide column">
            <div class="fluid ui primary floating labeled icon dropdown packlist-dropdown basic button">
              <input type="hidden" name="packingUnit" value="">
              <i class="{{selectedUnitIcon}} icon"></i>
              <span class="text">Packing Units</span>
              <div class="menu">
                <div class="header">
                  Packing Units
                </div>
                <div class="divider"></div>
                {{#each packingUnits}}
                <div class="item" data-value="{{_id}}" data-type="{{unitType}}">
                  <span class="description">{{identifier}}</span>
                  <span class="text">{{packedCount _id}}</span>
                </div>
                {{/each}}
              </div>
            </div>
          </div>
          <div class="four wide column">
            <div class="fluid ui primary basic labeled icon button {{isAssigningUnit}}" id="assignUnitButton">
              <i class="edit icon"></i> {{assignUnitButtonText}}
            </div>
          </div>
        </div>

      {{/unless}}
    </div>
    <div class="ui horizontal divider"></div>

    {{#unless portraitMode}}

    <div class="ui grid">

        <div class="five column row">
          <div class="left aligned column">
            {{#if displayUpdateSelection }}
            <div class="ui toggle checkbox" id="viewSelectedItemsOnly">
                <input type="checkbox" name="viewSelectedItemsOnly" />
                <label>Show Selected Only</label>
              </div>
            {{/if}}
          </div>

          <div class="column">
            {{#if isUnitAssigned}} 
              {{> filterHelperTextSearch filterHelper=filterHelper key="query"}}
            {{/if}}
          </div>

          <div class="right aligned column">
            {{#if isUnitAssigned}} 
              {{> filterHelperDropdown filterHelper=filterHelper key="offshoreClient"}}
            {{/if}}
          </div>

          <div class="right aligned column">

          </div>

          <div class="right aligned column">
            {{#unless isClosedUnit}}
            <div class="fluid ui buttons">
              <button class="ui button not-packed-btn {{viewNotPackedActive}}">Not Packed</button>
              <div class="or"></div>
              <button class="ui button packed-btn {{viewPackedActive}}">Packed</button>
            </div>
            {{/unless}}
          </div>
        </div>

      </div>

    {{else}}

      <div class="ui grid">

        <div class="row">
          <div class="eight wide column left aligned">
            <h3 class="header">{{tableText}}</h3>
          </div>
          <div class="eight wide right aligned column">
              {{#if displayUpdateSelection }}
              <div class="ui toggle checkbox" id="viewSelectedItemsOnly">
                  <input type="checkbox" name="viewSelectedItemsOnly" />
                  <label>Show Selected Items Only</label>
                </div>
              {{/if}}
            </div>
        </div>

        <div class="three column row">
          <div class="column">
            {{#if isUnitAssigned}} 
              {{> filterHelperTextSearch filterHelper=filterHelper key="query"}}
            {{/if}}
          </div>
          <div class="right aligned column">
            {{#if isUnitAssigned}} 
              {{> filterHelperDropdown filterHelper=filterHelper key="offshoreClient"}}
            {{/if}}
          </div>
          <div class="right aligned column">
            {{#unless isClosedUnit}}
            <div class="fluid ui buttons">
              <button class="ui button not-packed-btn {{viewNotPackedActive}}">Not Packed</button>
              <div class="or"></div>
              <button class="ui button packed-btn {{viewPackedActive}}">Packed</button>
            </div>
            {{/unless}}
          </div>
        </div>

      </div>

    {{/unless}}

    <div class="ui fluid container scroll-x" style="margin-top:10px;">
      {{#if Template.subscriptionsReady }}
      {{#if isUnitAssigned }}
        {{> packlistTable items=items selectedItems=selectedItems selectedUnit=selectedUnit}}
        <div class="infinite-scroll-element"></div>
      {{else}}
      <div class="ui horizontal divider"></div>
      <div class="ui visible floating info massive message" style="text-align: center;">
        Please Select a Packing Unit
      </div>
      {{/if}}
      {{else}}
      <div class="ui active loader"></div>
      {{/if}}
    </div>
  </div>
  {{> assignUnitModal }}
  {{> scanModal settings=scannerSettings}}
  {{> materialItemDetailsModal selectedItem=selectedItem}}


    <div class="ui grid" style="position:fixed; width: 100%; bottom: 1rem; left: 1rem; padding-left:2rem; background: #fafafa;">

      <div class="row ui grid">
        <div class="eight wide column">
          {{#unless portraitMode}}
            {{> filterHelperPagination filterHelper=filterHelper showPerPageDropdown=true truncate=5}}
          {{else}}
            {{> filterHelperPagination filterHelper=filterHelper showPerPageDropdown=true truncate=2}}
          {{/unless}}
        </div>

        {{#unless itemSelected}}

          <div class="eight wide right column"> 
            <div class="ui right aligned grey header" style="font-weight:normal;">
                <h4 style="margin-bottom:0.8rem;"> Select items from the table above <i class="check square outline blue icon" style="margin-left:8px;"></i> </h4>
                <h4 class="desktop-hide" style="margin-top:0; margin-bottom:0;"> Press and hold an item to view details <i class="hand point up outline blue icon" style="margin-left:8px;"></i> </h4>
                <h4 class="tablet-hide" style="margin-top:0; margin-bottom:0;"> Click an item to view details <i class="mouse pointer icon outline blue icon" style="margin-left:8px;"></i> </h4>
            </div>
          </div>

        {{else}}

          <div class="four wide column">
            {{#unless multipleItemsSelected}}
              <div class="fluid ui basic big button details-button"> Details </div>
            {{/unless}}
          </div>

          {{#unless packedOnly}}
          <div class="four wide column">
              <div class="fluid ui green basic big button pack-button"> Pack </div>
          </div>
          {{else}}
          <div class="four wide column">
              <div class="fluid ui red basic big button unpack-button"> Unpack </div>
          </div>
          {{/unless}}

        {{/unless}}
      </div>
    </div>
</template>