import './chem-packlist.html';
import './unit-options/assign-unit-modal';
import '../../../components/generic/scan-modal/scan-modal';
import '../../../components/client-header';
import './packlist-table';
import '../../../components/material-item-details-modal';
import { AbdnRequestsPackingUnitTypes } from '../../../../api/abdn-requests/abdn-requests';
import { MaterialItemDetailsModalMethods } from '../../../components/material-item-details-modal.methods';
import { AssignPackingUnitToRequest } from '../../../../api/requests/commands/assign-packing-unit-to-request';
import { AssignUnitModalMethods } from './unit-options/assign-unit-modal.methods';
import { ScanModalMethods } from '../../../components/generic/scan-modal/scan-modal-methods';
import { ClosePackingUnit } from '../../../../api/requests/commands/close-packing-unit';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Items } from '../../../../api/items/items';
import { Meteor } from 'meteor/meteor';
import { OpenPackingUnit } from '../../../../api/requests/commands/open-packing-unit';
import { PackItemsIntoUnit } from '../../../../api/requests/commands/pack-items-into-unit';
import { PacklistService } from '../../../services/packlist/packlist.service';
import { Publications } from '../../../../api/api.publications/publications';
import { ReactiveVar } from 'meteor/reactive-var';
import { Requests } from '../../../../api/requests/requests';
import { SiteProfileService } from '../../../services/company-site-profiles/site-profile.service';
import { Template } from 'meteor/templating';
import { UnpackItemsFromUnit } from '../../../../api/requests/commands/unpack-items-from-unit';
import { _ } from 'meteor/underscore';
import { manifestPdf } from '../../../pdf/chem-manifest';
import moment from 'moment';
import { CSV } from 'meteor/clinical:csv';
import { queryBuilder } from '../../../../api/items/shared/packed-or-can-pack';
import { portraitMode } from '../../../../startup/client/configure-portrait-mode';
import '../../../components/generic/filter-helper/filter-helper-pagination';
import { FilterHelper } from '../../../components/generic/filter-helper/filter-helper';
import '../../../components/generic/filter-helper/filter-helper-text-search';

const getItemIdFromTableRowElement = ($el) =>
  $($el)
    .closest('.packlist-item-row')
    .data('item-id');

const packItem = (itemIds, requestId, packingUnitId) =>
  PackItemsIntoUnit.call({ itemIds, requestId, packingUnitId });

const unpackItem = (itemIds, requestId, packingUnitId) =>
  UnpackItemsFromUnit.call({ itemIds, requestId, packingUnitId });

const getFirstPackingUnit = () => {
  const packingUnits = PacklistService
    .packingUnits();

  if (packingUnits && packingUnits.length) {
    return packingUnits[0];
  }
  return false;
};

const getUnitFromPackingUnitsList = (unitId, packingUnits) => {
  return packingUnits.find((unit) => {
    return unit._id === unitId;
  });
};

const setSelectedUnit = (templateInstance) => {
  templateInstance.$('.packlist-dropdown')
    .dropdown('set selected', templateInstance.selectedUnitId.get());
};

const clearBulkSelectedItems = (templateInstance) => {
  templateInstance.bulkUpdateSelectedItems.set([]);
  $('.group-pack-checkbox').checkbox('uncheck');
  templateInstance.selectedItemsOnly.set(false);
};

const isClosedUnit = (templateInstance) => {
  const selectedUnitId = templateInstance.selectedUnitId.get();

  return PacklistService
    .packingUnit(selectedUnitId)
    .isClosed();
};

const setupDropdowns = (template) => {
  Meteor.setTimeout(() => {
    const clients = $("select[name='offshoreClients']");
    const client = template.offshoreClient.get();

    if (!clients.parent().hasClass('selection')) {
      $(clients).dropdown();
    }

    if (client !== null) {
      $(clients).dropdown('set selected', client);
    }

    $("[name='filterPackList']").val(template.packingListFilter.get());
  }, 100);
};

const setUpPackingUnitDropdown = (templateInstance) => {
  // Check if already initialized
  if (templateInstance.$('.packlist-dropdown input').val() !== '') {
    return;
  }

  // Setup packing unit dropdown
  templateInstance.$('.packlist-dropdown').dropdown({
    onChange: (value, text, $choice) => {
      templateInstance.selectedUnitId.set(value);
    },
  });

  // observe changes to the items for dropdown counter
  templateInstance.abdnRequestsCursor.observeChanges({
    changed: (id, fields) => {
      if (fields && fields.packingUnits) {
        // update must of been for pack/unpack event
        // timeout used for rendering purposes
        Meteor.setTimeout(() => {
          setSelectedUnit(templateInstance);
        }, 100);
      }
    },
  });

  // if packing units present select first one
  if (templateInstance.selectedUnitId.get() === null) {
    const unit = getFirstPackingUnit();
    if (unit) {
      templateInstance.selectedUnitId.set(unit._id);
    }
  }

  setSelectedUnit(templateInstance);
};

Template.chemPacklist.onCreated(function onCreated() {
  const template = this;
  template.isTouch = false;

  // Set to item id for bounce animation on most recently loaded
  template.checkForTransitionAnimation = new ReactiveVar(false);
  // Vars for storing filter query term and query results
  template.packingListFilter = new ReactiveVar('');
  // Used to define number of results returned
  template.moreItemsIncrement = 5;
  template.packingListLimit = new ReactiveVar(60);
  // Used for determining state of view loaded items checkbox (checkbox initialised to false)
  template.packingListPackedCheckbox = new ReactiveVar(false);
  template.packedItemsOnly = new ReactiveVar(false);
  // Used for selecting what unit to pack into
  template.selectedUnitId = new ReactiveVar(null);
  // Used for recording most recently packed items
  template.mostRecentlyPackedId = new ReactiveVar(null);

  template.bulkUpdateSelectedItems = new ReactiveVar([]);
  template.selectedItemsOnly = new ReactiveVar(false);
  template.selectedItem = new ReactiveVar();
  template.isAssigningUnit = new ReactiveVar(false);
  template.isGeneratingManifest = new ReactiveVar(false);
  template.isGeneratingOuttakeReport = new ReactiveVar(false);
  template.isOpeningOrClosingUnit = new ReactiveVar(false);
  template.offshoreClient = new ReactiveVar(null);

  const initialQuery = () => {
    const query = {
      clientId: FlowRouter.getParam('clientId'),
      packedItemsOnly: template.packedItemsOnly.get(),
      offshoreClient: template.offshoreClient.get(),
    };
    const selectedItems = template.bulkUpdateSelectedItems.get();
    if (template.selectedItemsOnly.get() && selectedItems.length) {
      query.selectedItemsOnly = selectedItems;
    }
    if (template.packedItemsOnly.get()) {
      query.requestId = FlowRouter.getParam('requestId');
      query.packingUnitId = template.selectedUnitId.get();
    }
    return query;
  };
  template.filterHelper = new FilterHelper(
    template,
    Items,
    Publications.items.packedOrCanPack,
  );
  template.filterHelper.addFilters([
    {
      key: 'query',
      filterSearchPlaceholder: 'Filter...',
    },
    {
      key: 'offshoreClient',
      filterText: 'Offshore Client',
      filterSearchPlaceholder: 'Search clients',
      distinct: SiteProfileService.offshoreClients(),
    },
  ]);
  template.filterHelper.sorting().addSort('receivedDate', -1);
  template.filterHelper.init(initialQuery, queryBuilder);

  template.autorun(() => {
    // Orientation change
    portraitMode.get();

    template.packedOrCanPackSelector = {
      clientId: FlowRouter.getParam('clientId'),
      query: template.packingListFilter.get(),
      packedItemsOnly: template.packedItemsOnly.get(),
      offshoreClient: template.offshoreClient.get(),
    };

    template.itemsInPackingUnitSelector = {
      clientId: FlowRouter.getParam('clientId'),
      query: template.packingListFilter.get(),
      packedItemsOnly: template.packedItemsOnly.get(),
      offshoreClient: template.offshoreClient.get(),
      packingUnitId: template.selectedUnitId.get(),
    };

    const unit = getFirstPackingUnit();
    if (unit && template.selectedUnitId.get() === null) {
      Meteor.setTimeout(() => {
        setUpPackingUnitDropdown(template);
      });
    }

    Meteor.setTimeout(() => {
      setUpPackingUnitDropdown(template, template.selectedUnitId.get());
    });

    template.canPackSelector = {
      clientId: FlowRouter.getParam('clientId'),
      limit: template.packingListLimit.get(),
      packedItemsOnly: false,
    };

    template.subscribe(Publications.requests.request, {
      requestId: FlowRouter.getParam('requestId'),
    });

    Meteor.setTimeout(() => {
      highlightSelected(template);
    }, 300);
  });
});

Template.chemPacklist.onRendered(function onRendered() {
  const template = this;
  // Hide dimmer initially.
  template.$('packingListSegment').dimmer('hide');

  // Setup cursor that is used for observing changes and updating
  // selected packing unit counter in dropdown
  template.abdnRequestsCursor =
    Requests.find({ _id: FlowRouter.getParam('requestId') });

  setUpPackingUnitDropdown(template);

  template.autorun(() => {
    // Orientation change
    portraitMode.get();

    setupDropdowns(template);
    const closedUnit = isClosedUnit(template);

    if (closedUnit) {
      template.packedItemsOnly.set(true);
    } else {
      template.packedItemsOnly.set(false);
    }
  });

  const setupInfiniteScroll = () => {
    template.$('.infinite-scroll-element')
      .waypoint((direction) => {
        if (direction === 'down') {
          const limit = template.packingListLimit.get();
          template.packingListLimit.set(limit + template.moreItemsIncrement);
        }
      }, { context: '#main', offset: '100%' });
  };

  setupInfiniteScroll();
});

Template.chemPacklist.helpers({
  filterHelper() {
    return Template.instance().filterHelper;
  },
  currentRequest() {
    return PacklistService.currentRequest();
  },
  currentClient() {
    return SiteProfileService.currentClient();
  },
  isClosedUnit() {
    return isClosedUnit(Template.instance());
  },
  viewPackedOnly() {
    return Template.instance().packingListPackedCheckbox.get();
  },
  viewPackedActive() {
    return Template.instance().packedItemsOnly.get() ? 'positive active' : '';
  },
  viewNotPackedActive() {
    return Template.instance().packedItemsOnly.get() ? '' : 'positive active';
  },
  getMostRecentItemsInUnit() {
    const request = Requests.findOne(FlowRouter.getParam('requestId'));
    if (request) {
      const selectedUnitId = Template.instance().selectedUnitId.get();
      const unit = getUnitFromPackingUnitsList(selectedUnitId, request.packingUnits);

      if (!unit) return [];

      const mostRecent = _.last(unit.items, 3);
      const items = Items.find({ _id: { $in: mostRecent } }).fetch();
      return items;
    }
    return [];
  },
  offshoreClients() {
    return SiteProfileService.offshoreClients();
  },
  anyItems: function anyItems() {
    const itemsCount = Items.find().count();
    return itemsCount > 0;
  },
  items() {
    const template = Template.instance();
    return template.filterHelper.filtered();
  },
  assignUnitButtonText: function assignUnitButtonText() {
    return 'Assign Unit';
  },
  unitWithContents: function unitWithContents() {
    return {};
  },
  isUnitAssigned() {
    return PacklistService
      .packingUnits()
      .length > 0;
  },
  packingUnits() {
    return PacklistService
      .packingUnits();
  },
  packedCount(packingUnitId) {
    return PacklistService
      .packingUnit(packingUnitId)
      .packedCount();
  },
  itemWithSelectedPackingUnit: function getItemWithSelectedPackingUnit() {
    const request = Requests.findOne(FlowRouter.getParam('requestId'));
    if (request) {
      const selectedUnitId = Template.instance().selectedUnitId.get();
      const unit = getUnitFromPackingUnitsList(selectedUnitId, request.packingUnits);
      if (unit) {
        return _.extend({ selectedPackingUnit: unit }, this);
      }
    }
    return this;
  },
  requestDateFormatted: function getRequestDateFormatted() {
    const request = Requests.findOne(FlowRouter.getParam('requestId'));
    if (request) {
      return moment(request.scheduledDateTime).format('DD-MMM-YYYY');
    }
    return '';
  },
  requestTimeFormatted: function getRequestTimeFormatted() {
    const request = Requests.findOne(FlowRouter.getParam('requestId'));
    if (request) {
      return moment(request.scheduledDateTime).format('HH:mm');
    }
    return '';
  },
  requestIdentifier: function getRequestIdentifier() {
    const request = Requests.findOne(FlowRouter.getParam('requestId'));
    if (request) {
      return `${request.packingRequestRefNo}`;
    }
    return '';
  },
  selectedUnitIcon: function getSelectedUnitIcon() {
    const request = Requests.findOne(FlowRouter.getParam('requestId'));
    if (request) {
      const selectedUnitId = Template.instance().selectedUnitId.get();
      const unit = getUnitFromPackingUnitsList(selectedUnitId, request.packingUnits);
      if (unit) {
        if (unit.isClosed) {
          return 'red lock';
        } else if (unit.unitType === AbdnRequestsPackingUnitTypes.CCU) {
          return 'archive';
        } else if (unit.unitType === AbdnRequestsPackingUnitTypes.VEHICLE) {
          return 'shipping';
        } else if (unit.unitType === AbdnRequestsPackingUnitTypes.CMR) {
          return 'teal file outline';
        } else if (unit.unitType === AbdnRequestsPackingUnitTypes.BEGELEIDINGSBRIEF) {
          return 'pink file outline';
        }
      }
    }
    return 'archive';
  },
  tableText() {
    const isPackedOnly = Template.instance().packedItemsOnly.get();

    return isPackedOnly ? 'Packed Items' : 'Items to Pack';
  },
  displayUpdateSelection() {
    const selectedItems = Template.instance().bulkUpdateSelectedItems.get();

    return selectedItems && selectedItems.length;
  },
  selectedItems() {
    return Template.instance().bulkUpdateSelectedItems.get();
  },
  packedOnly() {
    return Template.instance().packedItemsOnly.get();
  },
  itemSelected() {
    return (Template.instance().bulkUpdateSelectedItems.get().length > 0);
  },
  multipleItemsSelected() {
    return (Template.instance().bulkUpdateSelectedItems.get().length > 1);
  },
  updateSelectionText() {
    const isPackedOnly = Template.instance().packedItemsOnly.get();

    return isPackedOnly ? 'Unpack Items' : 'Pack Items';
  },
  selectedUnit() {
    return Template.instance().selectedUnitId.get();
  },
  selectedItem() {
    return Template.instance().selectedItem.get();
  },
  isAssigningUnit() {
    return Template.instance().isAssigningUnit.get() ? 'disabled loading' : '';
  },
  isGeneratingManifest() {
    return Template.instance().isGeneratingManifest.get() ? 'disabled loading' : '';
  },
  isOpeningOrClosingUnit() {
    return Template.instance().isOpeningOrClosingUnit.get() ? 'disabled loading' : '';
  },
  scannerSettings() {
    const template = Template.instance();
    const requestId = FlowRouter.getParam('requestId');
    const packingUnitId = template.selectedUnitId.get();

    return {
      collection: Items,
      subscription: Publications.items.packedOrCanPack,
      selector: Template.instance().canPackSelector,
      query: {
        isPacked: false,
      },
      key: 'receiptNo',
      placeholder: 'Scan or Enter Receipt No',
      actionName: 'Pack Items',
      approve: (nos) => {
        const items = Items.find({ receiptNo: { $in: nos } }).fetch().map((item) => item._id);

        if (items && items.length) {
          console.log('Packing', items, requestId, packingUnitId);
          packItem(items, requestId, packingUnitId);
        }

        ScanModalMethods.hide();
      },
      cancel: () => {
        console.log('Cancelled');
        ScanModalMethods.hide();
      },
    };
  },
});

const highlightSelected = (template) => {
  const selection = template.bulkUpdateSelectedItems.get();
  template.$('tr.positive').removeClass('positive');
  selection.forEach((id) => {
    template.$("[data-item-id='" + id + "']").addClass('positive');
  });
};

Template.chemPacklist.events({
  'touchstart .packlist-item-row': function handleTouchStart(event, templateInstance) {
    Meteor.clearTimeout(templateInstance.touchTimer);
    templateInstance.isTouch = true;
  },
  'touchend .packlist-item-row': function handleTouchEnd(event, templateInstance) {
    templateInstance.touchTimer = Meteor.setTimeout(() => {
      templateInstance.isTouch = false;
    }, 800);
  },
  'click .packlist-item-row': function handleOnClick(event, templateInstance) {
    event.preventDefault();

    const closedUnit = isClosedUnit(templateInstance);
    if (closedUnit) {
      templateInstance.bulkUpdateSelectedItems
        .set([]);

      highlightSelected(templateInstance);
      return;
    }

    const evTarget = $(event.currentTarget);
    const target = $(event.target);
    const selectedItems = templateInstance.bulkUpdateSelectedItems.get();
    const itemId = getItemIdFromTableRowElement(evTarget);

    if (!templateInstance.isTouch) {
      // Show modal on click (except on checkbox or button)
      if (target.hasClass('button')) {
        event.stopPropagation();
        return;
      }

      if (!(target.hasClass('button')
          || target.hasClass('checkbox')
          || target.hasClass('checkbox-cell')
          || target.hasClass('btn-cell')
          || target.is('label'))) {
        const item = this.item;
        templateInstance.selectedItem.set(item);
        MaterialItemDetailsModalMethods
          .init(item)
          .show();

        return;
      }
    }

    if (target.is('label')) {
      var checked = evTarget.find('.ui.checkbox').checkbox('is checked');
    } else {
      var checked = evTarget.find('.ui.checkbox').checkbox('toggle').checkbox('is checked');
    }

    if (checked) {
      templateInstance.bulkUpdateSelectedItems
        .set(selectedItems.concat([itemId]));
    } else {
      templateInstance.bulkUpdateSelectedItems
        .set(selectedItems.filter((id) => id !== itemId));
    }

    highlightSelected(templateInstance);
  },
  'contextmenu .packlist-item-row': function handleLongPress(event, templateInstance) {
    // This works like a long press
    event.preventDefault();

    const evTarget = $(event.currentTarget);

    if (!(evTarget.hasClass('button')
        || evTarget.hasClass('checkbox')
        || evTarget.hasClass('checkbox-cell')
        || evTarget.hasClass('btn-cell')
        || evTarget.is('label'))) {
      const item = this.item;
      templateInstance.selectedItem.set(item);
      MaterialItemDetailsModalMethods
        .init(item)
        .show();
    }
  },
  'change #viewSelectedItemsOnly': function handleChange(event, templateInstance) {
    const isChecked = templateInstance.$(event.target).is(':checked');
    templateInstance.selectedItemsOnly.set(isChecked);
  },
  'click .not-packed-btn': function handleClick(event, templateInstance) {
    templateInstance.packedItemsOnly.set(false);
    clearBulkSelectedItems(templateInstance);
  },
  'click .packed-btn': function handleClick(event, templateInstance) {
    templateInstance.packedItemsOnly.set(true);
    clearBulkSelectedItems(templateInstance);
  },
  'click #assignUnitButton': function handleClick(event, templateInstance) {
    event.preventDefault();
    AssignUnitModalMethods
      .init(() => {
        const requestId = FlowRouter.getParam('requestId');
        const packingUnitType = $('[name="unitType"]:checked').val();
        const packingUnitIdentifier = $('[name="unitIdentifier"]').val();
        templateInstance.isAssigningUnit.set(true);
        AssignPackingUnitToRequest.call(
          { requestId, packingUnitType, packingUnitIdentifier },
          (error, result) => {
            if (!error) {
              templateInstance.selectedUnitId.set(result);
              setSelectedUnit(templateInstance);
            }

            templateInstance.isAssigningUnit.set(false);
          },
        );
      }).show();
  },
  'click #scanModalButton': function handleClick(event, templateInstance) {
    event.preventDefault();
    ScanModalMethods
      .init(() => {}).show();
  },
  'click #openUnitButton': function handleClick(event, templateInstance) {
    event.preventDefault();
    OpenPackingUnit.call({
      requestId: FlowRouter.getParam('requestId'),
      packingUnitId: templateInstance.selectedUnitId.get(),
    });
  },
  'click #closeUnitButton': function handleClick(event, templateInstance) {
    event.preventDefault();
    ClosePackingUnit.call({
      requestId: FlowRouter.getParam('requestId'),
      packingUnitId: templateInstance.selectedUnitId.get(),
    });

    templateInstance.bulkUpdateSelectedItems.set([]);
    highlightSelected(templateInstance);
  },
  'click .pack-button': function handleClick(event, templateInstance) {
    event.preventDefault();
    const itemId = getItemIdFromTableRowElement($(event.currentTarget));
    const requestId = FlowRouter.getParam('requestId');
    const packingUnitId = templateInstance.selectedUnitId.get();
    const selection = templateInstance.bulkUpdateSelectedItems.get();

    if (selection && selection.length) {
      packItem(selection, requestId, packingUnitId);
      clearBulkSelectedItems(templateInstance);
    } else {
      packItem([itemId], requestId, packingUnitId);
    }
  },
  'click .unpack-button': function handleClick(event, templateInstance) {
    event.preventDefault();
    const itemId = getItemIdFromTableRowElement($(event.currentTarget));
    const requestId = FlowRouter.getParam('requestId');
    const packingUnitId = templateInstance.selectedUnitId.get();
    const selection = templateInstance.bulkUpdateSelectedItems.get();

    if (selection && selection.length) {
      unpackItem(selection, requestId, packingUnitId);
      clearBulkSelectedItems(templateInstance);
    } else {
      unpackItem([itemId], requestId, packingUnitId);
    }
  },
  'click .details-button': function handleClick(event, templateInstance) {
    if (templateInstance.bulkUpdateSelectedItems.get().length !== 1) {
      return;
    }

    const item = Items.findOne(templateInstance.bulkUpdateSelectedItems.get()[0]);
    templateInstance.selectedItem.set(item);
    MaterialItemDetailsModalMethods
      .init(item)
      .show();
  },
  'input [name=filterPackList]': function handleInput(event, templateInstance) {
    event.preventDefault();
    templateInstance.packingListFilter.set(event.target.value);
  },
  'change select[name="offshoreClients"]': function handleChange(event, templateInstance) {
    const selectedOffshoreClientId = templateInstance.$(event.currentTarget).val();
    const selectedOffshoreClientName = templateInstance.$('select[name="offshoreClients"] option:selected').text();

    templateInstance.offshoreClient.set((selectedOffshoreClientId != 'ALL') ? selectedOffshoreClientName : null);
  },
  'change [name=limitToPacked]': function handleChange(event, templateInstance) {
    event.preventDefault();
    templateInstance.packingListPackedCheckbox.set(event.target.checked);
  },
  'click .recentPacking': function handleClick(event, templateInstance) {
    event.preventDefault();
    const clickedItem = templateInstance.$(`#${event.target.id}`);
    templateInstance.$('[name=limitToPacked]').prop('checked', true);
    templateInstance.packingListPackedCheckbox.set(true);
    // Scroll to item in list and animate with flash
    Meteor.defer(() => {
      if (templateInstance.subscriptionsReady) {
        templateInstance.$('#scrollingSegment').scrollTo(clickedItem);
        templateInstance.$(clickedItem).transition('pulse');
      }
    });
  },
  'click #loadMoreItems': function handleClick(event, templateInstance) {
    event.preventDefault();
    const currentLimit = templateInstance.packingListLimit.get();
    templateInstance.packingListLimit.set(currentLimit + 50);
  },
  'click #exportOuttakeReportButton': function handleClick(event, templateInstance) {
    event.preventDefault();

    templateInstance.isGeneratingOuttakeReport.set(true);
    const request = Requests.findOne(FlowRouter.getParam('requestId'));
    const packingUnitId = templateInstance.selectedUnitId.get(); // CCU or vehicle.
    const packingUnit = request.packingUnits.find((unit) => unit._id === packingUnitId);

    const query = templateInstance.itemsInPackingUnitSelector;

    Meteor.call(
      'items.getItemsInPackingUnit',
      query,
      (err, result) => {
        if (err) {
          console.log('Could not generate CSV:', err);
          return;
        }

        const itemsForOuttakeExport = result;

        const itemsForOuttakeExportFormatted = itemsForOuttakeExport.map((x) => ({
          'Receipt No': x.receiptNo,
          'Received Date': moment(x.receivedDate).format('DD/MM/YYYY HH:mm'),
          Qty: x.quantity,
          'Package Type': x.packageType,
          Weight: x.weightKg,
          Description: x.description,
          Client: x.offshoreClient,
          'Offshore Location': x.offshoreLocation,
          'EURAL/EWC Code': x.euralCode,
          'Hazard Class': x.imoHazardClass,
          'Hazard Subclass': x.imoSubClass,
          UnNo: x.unNo,
        }));
        const packingRequestRefNo = request.packingRequestRefNo.replace(/\//g, '');
        const timestamp = moment().format('YYYY-MM-DD-HH:mm');
        const filename = `OUTTAKE_${packingRequestRefNo}_${timestamp}.csv`;

        const formatUnitType = (key) => {
          const vals = {
            ccu: 'CCU',
            vehicle: 'Vehicle',
            cmr: 'CMR',
            begeleidingsbrief: 'Begeleidingsbrief',
          };

          return vals[key];
        };

        // Setup headerinfo for CSV file.
        let clients = _.pluck(itemsForOuttakeExport, 'offshoreClient');
        let distinctClients = _.uniq(clients);
        let fileHeader = [];
        fileHeader.push(['Chemicals Outtake Report']);
        fileHeader.push(['Materials Collected By Company:', request.transportCompany]);
        fileHeader.push(['Ref No:', packingRequestRefNo]);
        fileHeader.push(['Clients:', distinctClients.join('; ')]);
        fileHeader.push(['Unit No:', packingUnit.identifier]);
        fileHeader.push(['Unit Type:', formatUnitType(packingUnit.unitType)]);

        const headerText = fileHeader.reduce((running, value) => {
          return running + value.join(',') + '\r\n';
        }, '') + '\r\n';

        const mainContent = CSV.unparse(itemsForOuttakeExportFormatted);
        let fileContent = headerText + mainContent;

        if (fileContent) {
          const a = document.createElement('a');
          document.body.appendChild(a);
          const blob = new Blob([fileContent], { type: 'text/csv;charset=utf-8' });
          const url = window.URL.createObjectURL(blob);
          a.href = url;
          a.download = filename;
          a.click();
          window.URL.revokeObjectURL(url);
        }

        templateInstance.isGeneratingOuttakeReport.set(false);
      },
    );
  },
  'click #exportManifestButton': function handleClick(event, templateInstance) {
    event.preventDefault();
    templateInstance.isGeneratingManifest.set(true);
    const request = Requests.findOne(FlowRouter.getParam('requestId'));
    const packingUnitId = templateInstance.selectedUnitId.get(); // CCU or vehicle.
    const manifestDetails = _.clone(request);

    const unit = manifestDetails
      .packingUnits
      .find((packingUnit) => packingUnit._id === packingUnitId);

    manifestDetails.ccuOrVehicle = unit.identifier;
    manifestDetails.currentUser = Meteor.user().username;

    const packingRequestRefNoForFilename = request.packingRequestRefNo.replace(/\//g, '');

    const itemsForManifest = {};
    let destinationsCombined = '';
    _.each(manifestDetails.destinations, (element, index) => {
      if (index > 0) {
        destinationsCombined += ', ';
      }
      destinationsCombined += element.name;
    });

    // Set dest as key and value as array of hydrated items.
    itemsForManifest[destinationsCombined] = [];

    _.each(unit.items, (element) => {
      const itemId = element;
      const item = Items.findOne(itemId);
      itemsForManifest[destinationsCombined].push(item);
    });
    manifestDetails.itemsForManifest = itemsForManifest;
    const createTimestamp = moment().format('YYYY-MMM-DD-HH:mm');
    const saveFileName = `MANIFEST_${packingRequestRefNoForFilename}_${createTimestamp}.pdf`;

    manifestPdf.create(manifestDetails)
      .then((doc) => {
        doc.save(saveFileName);
        templateInstance.isGeneratingManifest.set(false);
      })
      .catch((e) => console.error(`Failed to output manifest doc: ${e}`));
  },
  'click .group-pack-checkbox': function handleClick(event, templateInstance) {
    const selectedItems = templateInstance.bulkUpdateSelectedItems.get();
    const checkbox = $(event.currentTarget);
    const itemId = getItemIdFromTableRowElement(checkbox);

    if (checkbox.checkbox('is checked')) {
      templateInstance.bulkUpdateSelectedItems
        .set(selectedItems.concat([itemId]));
    } else {
      templateInstance.bulkUpdateSelectedItems
        .set(selectedItems.filter((id) => id !== itemId));
    }
  },
});
