import './packlist-item.html';
import { $ } from 'meteor/jquery';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import { Requests } from '../../../../api/requests/requests';
import { Session } from 'meteor/session';
import { Template } from 'meteor/templating';
import { _ } from 'meteor/underscore';

Template.packlistItem.helpers({
  isItemPacked() {
    const templateInstanceData = Template.instance().data;
    const isPacked = templateInstanceData.isPacked;
    return isPacked;
  },
  ccuContainedIn() {
    const templateInstanceData = Template.instance().data;
    const ccu = templateInstanceData.packedInCcu.identifier;
    return ccu;
  },
});

Template.packlistItem.events({
  'click #unpackButton': function onClick(event, templateInstance) {
    event.preventDefault();

    const selectedCcuId = Session.get('packlist.selectedCcuId');
    const stockItemId = templateInstance.data._id;
    const currentRequestId = FlowRouter.getParam('requestId');

    Meteor.call(
      'requests.unpackItem',
      stockItemId,
      currentRequestId,
      selectedCcuId,
      (error) => {
        if (error) {
          console.log(error);
        }
      },
    );
  },
  'click #packButton': function onClick(event, templateInstance) {
    event.preventDefault();

    const selectedCcuId = Session.get('packlist.selectedCcuId');
    const stockItemId = templateInstance.data._id;
    const currentRequestId = FlowRouter.getParam('requestId');

    Meteor.call('requests.packItem',
      stockItemId,
      currentRequestId,
      selectedCcuId,
      (error) => {
        if (error) {
          console.log(error);
        } else {
          // Record the id for applying animation
          Session.set('packlist.mostRecentlyPackedId', stockItemId);
        }
      },
    );
  },
});
