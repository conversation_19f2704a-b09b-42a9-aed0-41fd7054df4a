<template name="packlistTable">
  <table class="ui very compact selectable celled striped table portrait no-select tablet-hide-last-column break-word" style="margin-bottom: 150px">
    <thead>
      <tr>
        {{#unless isUnitClosed}}
          <th>
            <!-- Checkbox -->
          </th>
        {{/unless}}
        <th>Receipt No</th>
        <th>Received Date</th>
        <th>Qty</th>
        <th>Package Type</th>
        <th>Weight</th>
        <th>CCU</th>
        <th>Description</th>
        <th>Offshore Client</th>
        <th>Offshore Location</th>
        <th>EURAL / EWC</th>
        <th>UnNo</th>
        <th>Hazard Class</th>
        <th>Storage Location</th>
        {{#unless isUnitClosed}}
          {{#unless portraitMode}}
          <th>
            <!-- Pack/Unpack Button -->
          </th>
          {{/unless}}
        {{/unless}}
      </tr>
    </thead>
    <tbody>
      {{#unless itemsToDisplay}}
      <tr>
        <td class="no-results" colspan="11" style="text-align: center;">
          <i class="warning circle icon"></i>&ensp;No Items
        </td>
      </tr>
      {{/unless}}
      {{#each item in items}} 
        {{> packlistTableRow 
          item=item 
          notActive=selectionSelected 
          isSelectedItem=(isSelectedItem item._id) 
          isUnitClosed=isUnitClosed 
          multipleSelected=multipleSelected}}
      {{/each}}
    </tbody>
  </table>
</template>

<template name="packlistTableRow">
  <tr class="packlist-item-row" data-item-id="{{item._id}}">
    {{#unless isUnitClosed}}
    <td class="checkbox-cell">
      <div class="ui checkbox group-pack-checkbox" data-item-id="{{item._id}}">
        <input type="checkbox">
      </div>
    </td>
    {{/unless}}
    <td class="bold">{{item.receiptNo}}</td>
    <td>{{receivedDateFormatted}}</td>
    <td>{{item.quantity}}</td>
    <td>{{item.packageType}}</td>
    <td>{{item.weightKg}}</td>
    <td>{{item.ccu}}</td>
    <td class="break">{{item.description}}</td>
    <td>{{item.offshoreClient}}</td>
    <td>{{item.offshoreLocation}}</td>
    <td>{{item.euralCode}}</td>
    <td>{{item.unNo}}</td>
    <td>{{imoClassFormatted}}</td>
    <td>{{item.location}}</td>
    {{#if viewStoredItems}}
    <td>{{item.location}}</td>
    {{/if}}
    {{#unless isUnitClosed}}
      {{#unless portraitMode}}
      <td class="right aligned btn-cell">
        {{#if item.isPacked}}
        <div class="negative packed ui button {{btnClass}} {{#if multipleSelected}}disabled{{/if}} unpack-button" data-value="{{_id}}">
          Unpack
        </div>
        {{else}}
        <div class="positive packed ui button {{btnClass}} {{#if multipleSelected}}disabled{{/if}} pack-button" data-value="{{_id}}">
          Pack
        </div>
        {{/if}}
      </td>
      {{/unless}}
    {{/unless}}
  </tr>
</template>