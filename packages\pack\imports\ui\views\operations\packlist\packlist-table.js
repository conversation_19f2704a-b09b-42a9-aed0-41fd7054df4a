import './packlist-table.html';

import { DISPLAY_DATETIME_FORMAT } from '../../../../shared/lib/constants';
import { PacklistService } from '../../../services/packlist/packlist.service';
import { Template } from 'meteor/templating';
import moment from 'moment';

const getItems = (templateInstance) =>
  templateInstance.data.items;

Template.packlistTable.helpers({
  itemsToDisplay() {
    const items = getItems(Template.instance()).fetch();
    return items && items.length;
  },
  items() {
    return getItems(Template.instance());
  },
  isSelectedItem(itemId) {
    return Template.instance().data.selectedItems.includes(itemId);
  },
  isUnitClosed() {
    return PacklistService
      .packingUnit(Template.instance().data.selectedUnit)
      .isClosed();
  },
  multipleSelected() {
    return ($.unique(Template.instance().data.selectedItems).length > 1);
  },
});

Template.packlistTableRow.onRendered(function onRendered() {
  const template = this;

  template.$('.checkbox').checkbox();

  if (template.data.isSelectedItem) {
    template.$('.checkbox').checkbox('set checked');
  }

  Waypoint.refreshAll();
});

Template.packlistTableRow.helpers({
  receivedDateFormatted() {
    return moment(Template.instance().data.item.receivedDate)
      .format(DISPLAY_DATETIME_FORMAT);
  },
  imoClassFormatted() {
    const item = Template.instance().data.item;
    const hClass = item.imoHazardClass;
    const sClass = item.imoSubClass;

    if (!hClass) {
      return '';
    }

    var out = 'CL.';
    out += ((hClass) ? hClass : 'N/P') + ' ';
    out += ((sClass) ? '(' + sClass + ')' : '');
    return out;
  },
  btnClass() {
    return Template.instance().data.notActive ? 'disabled' : 'active';
  },
});
