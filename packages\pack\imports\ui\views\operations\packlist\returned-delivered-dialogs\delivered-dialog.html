<template name="deliveredDialog">
    <div class="ui tiny modal delivered">
        <i class="close icon"></i>
        <div class="header">
            <i class="ui green check icon"></i>Item Delivered
        </div>
        <div class="content">
            <form class="ui form" style="width:100%;">
                <div class="field">
                    <label>Delivered Date & Time</label>
                    <div class="ui calendar" id="deliveredDatepicker">
                        <div class="ui input left icon">
                            <i class="calendar icon"></i>
                            <input type="text" placeholder="Date/Time">
                        </div>
                    </div>
                </div>
                <div class="field">
                    <label>Quantity Delivered  ( Original Item Quantity : {{getTotaltemQuantity}} )</label>
                    <select class="ui fluid dropdown" id="qtyDropdown">
                        {{#each getTotaltemQuantityAsArray}}
                            <option value="{{value}}">
                                {{displayValue}}
                            </option>
                        {{/each}}
                    </select>
                </div>
                <div class="actions">
                    <div class="ui button ok right labeled icon right floated">Ok<i class="checkmark icon"></i></div>
                    <div class="ui button cancel right labeled icon right floated">Cancel<i class="remove icon"></i></div>
                </div>
                <div class="clearfix"></div>
            </form>
        </div>
    </div>
</template>
