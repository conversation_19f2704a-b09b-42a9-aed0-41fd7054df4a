import { Meteor } from 'meteor/meteor';
import { Session } from 'meteor/session';
import { Template } from 'meteor/templating';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { ReturnedDeliveredSession } from './returned-delivered-session';
import './delivered-dialog.html';

// TODO: Research the Meteor way of defining const event types.
//       These are just currently copied straight from the event factory.
//       Ideally, these should be shared between client and server operations
//       for sending event updates to server.

const updateTypes = {
  DELIVERED: 'DELIVERED',
  PARTIAL_DELIVERY: 'PARTIAL_DELIVERY',
  RETURNED: 'RETURNED',
};

const updateItemStatus = (updateType, id, vehicleRunId, deliveredDateTime, quantity) => {
  Meteor.call('items.updateLoadedItem', updateType, id, vehicleRunId, deliveredDateTime, quantity);
};

// Setup Datepicker.
// https://github.com/mdehoog/Semantic-UI/blob/49178d6d0321357b959ff56f424ea1b3ed76a6ed/src/definitions/modules/calendar.js#L902-L1279
const calendarSettingsFormatter = {
  date: (date, settings) => {
    if (_.isUndefined(date)) return '';
    const day = (`0${date.getDate()}`).slice(-2); // zero pad.
    const month = settings.text.monthsShort[date.getMonth()];
    const year = date.getFullYear();
    return day + '-' + month + '-' + year;
  },
  time: (date, settings) => {
    if (_.isUndefined(date)) return '';
    const hours = (`0${date.getHours()}`).slice(-2); // zero pad.
    const minutes = (`0${date.getMinutes()}`).slice(-2); // zero pad.
    return hours + ':' + minutes;
  },
};

Template.deliveredDialog.onCreated(function onCreated() {
  const template = this;
  template.itemId = new ReactiveVar('');
  template.totalQuantity = new ReactiveVar(0);
});

Template.deliveredDialog.onRendered(function onRendered() {
  const template = this;
  const qtyDropdown = template.$('#qtyDropdown').dropdown();
  const datePicker = template.$('#deliveredDatepicker');
  const modal = template.$('div.ui.tiny.modal.delivered').modal({
    observeChanges: true,
    detachable: false,
    autofocus: false,
    onHidden: () => {
      Session.set(ReturnedDeliveredSession.Types.SHOW_DELIVERED_DIALOG, false);
      return true;
    },
    onApprove: () => {
      const deliveredDateTime = datePicker.calendar('get date');
      let quantity = qtyDropdown.find(':selected').val();
      quantity = parseInt(quantity, 10);
      if (quantity === template.totalQuantity.get()) {
        updateItemStatus(
          updateTypes.DELIVERED,
          template.itemId.get(),
          FlowRouter.getParam('vehicleRunId'),
          deliveredDateTime,
          quantity,
        );
      } else {
        updateItemStatus(
          updateTypes.PARTIAL_DELIVERY,
          template.itemId.get(),
          FlowRouter.getParam('vehicleRunId'),
          deliveredDateTime,
          quantity,
        );
      }
      return true;
    },
  });

  template.autorun(() => {
    const shouldShowModal = Session.get(ReturnedDeliveredSession.Types.SHOW_DELIVERED_DIALOG);

    if (shouldShowModal === true) {
      // Show modal and initialise its contents
      datePicker.calendar({
        type: 'datetime',
        maxDate: moment().utc().toDate(),
        ampm: false,
        formatter: calendarSettingsFormatter,
      });
      modal.modal('show');
      Meteor.defer(() => {
        template.$('#qtyDropdown').dropdown('set selected', template.totalQuantity.get());
        template.$('#deliveredDatepicker').calendar('set date', moment().utc().toDate());
        template.$('.ui.tiny.modal').modal('refresh');
      });
    }
  });
});

Template.deliveredDialog.helpers({
  getTotaltemQuantityAsArray: function getTotaltemQuantityAsArray() {
    const sessionParams = Session.get(ReturnedDeliveredSession.Types.DELIVERED_DIALOG_PARAMS);
    if (sessionParams && sessionParams.isInputParams) {
      Template.instance().itemId.set(sessionParams.itemId);
      Template.instance().totalQuantity.set(sessionParams.totalItemQuantity);
      const result = [];
      for (let i = 1; i <= sessionParams.totalItemQuantity; i++) {
        result.push({ value: i, displayValue: i });
      }
      return result;
    }
    return [];
  },
  getTotaltemQuantity: function getTotaltemQuantity() {
    return Template.instance().totalQuantity.get();
  },
});
