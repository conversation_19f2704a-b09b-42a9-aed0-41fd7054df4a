/* eslint-disable max-classes-per-file */
const Types = {
  SHOW_DELIVERED_DIALOG: 'vehicleLoadingList.showDeliveredDialog',
  DELIVERED_DIALOG_PARAMS: 'vehicleLoadingList.deliveredDialogParams',
  SHOW_RETURNED_DIALOG: 'vehicleLoadingList.showReturnedDialog',
};

class SessionInput {
  constructor(itemId) {
    this.itemId = itemId;
    this.isInputParams = true;
  }
}

class DeliveredSessionInput extends SessionInput {
  constructor(itemId, totalItemQuantity) {
    super(itemId);
    this.totalItemQuantity = totalItemQuantity;
  }
}

class SessionInputCreator {
  constructor(itemId) {
    this.itemId = itemId;
  }

  createSessionInput() {
    return new SessionInput(this.itemId);
  }

  generateSessionInput() {
    return this.createSessionInput();
  }
}

class DeliveredSessionInputCreator extends SessionInputCreator {
  constructor(itemId, totalItemQuantity) {
    super(itemId);
    this.totalItemQuantity = totalItemQuantity;
  }

  createSessionInput() {
    return new DeliveredSessionInput(this.itemId, this.totalItemQuantity);
  }
}

class SessionOutput {
  constructor(itemId) {
    this.itemId = itemId;
    this.isInputParams = false;
  }
}

class DeliveredSessionOutput extends SessionOutput {
  constructor(itemId, deliveredDateTime, quantityDelivered) {
    super(itemId);
    this.deliveredDateTime = deliveredDateTime;
    this.quantityDelivered = quantityDelivered;
  }
}

class SessionOutputCreator {
  constructor(itemId) {
    this.itemId = itemId;
  }

  createSessionOutput() {
    return new SessionOutput(this.itemId);
  }

  generateSessionOutput() {
    return this.createSessionOutput();
  }
}

class DeliveredSessionOutputCreator extends SessionOutputCreator {
  constructor(itemId, deliveredDateTime, quantityDelivered) {
    super(itemId);
    this.deliveredDateTime = deliveredDateTime;
    this.quantityDelivered = quantityDelivered;
  }

  createSessionOutput() {
    return new DeliveredSessionOutput(this.itemId, this.deliveredDateTime, this.quantityDelivered);
  }
}

const createDeliveredSessionInput = (itemId, totalItemQuantity) => {
  const deliveredSessionInputCreator = new DeliveredSessionInputCreator(itemId, totalItemQuantity);
  return deliveredSessionInputCreator.generateSessionInput();
};

const createDeliveredSessionOutput = (itemId, deliveredDateTime, quantityDelivered) => {
  const deliveredSessionOutputCreator =
  new DeliveredSessionOutputCreator(itemId, deliveredDateTime, quantityDelivered);
  return deliveredSessionOutputCreator.generateSessionOutput();
};

export const ReturnedDeliveredSession = {
  Types,
  createDeliveredSessionInput,
  createDeliveredSessionOutput,
};
