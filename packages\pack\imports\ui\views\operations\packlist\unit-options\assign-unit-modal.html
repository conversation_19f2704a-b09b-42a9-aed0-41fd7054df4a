<template name="assignUnitModal">
  <div class="ui tiny modal assign-unit">
    {{> assignUnitModalContents}}
  </div>
</template>

<template name="assignUnitModalContents">
  <i class="close icon"></i>
  <h1 class="header">Assign Unit</h1>
  <div class="content">
    <div class="ui container">
      <div class="ui form">
        <div class="grouped fields">
          <label>Type</label>
          <div class="inline fields">
            <div class="field">
              <div class="ui radio checkbox">
                <input type="radio" name="unitType" checked="checked" value="{{unitTypes.CCU}}">
                <label>CCU</label>
              </div>
            </div>
            <div class="field">
              <div class="ui radio checkbox">
                <input type="radio" name="unitType" value="{{unitTypes.VEHICLE}}">
                <label>Vehicle</label>
              </div>
            </div>
            <div class="field">
              <div class="ui radio checkbox">
                <input type="radio" name="unitType" value="{{unitTypes.CMR}}">
                <label>CMR Number</label>
              </div>
            </div>
            <div class="field">
              <div class="ui radio checkbox">
                <input type="radio" name="unitType" value="{{unitTypes.BEGELEIDINGSBRIEF}}">
                <label>Begeleidingsbrief Nummer</label>
              </div>
            </div>
          </div>
        </div>
        <div class="field">
          <label>{{inputLabelText}}</label>
          <div class="ui fluid search selection">
            <div class="ui icon input">
              <input class="prompt" type="text" placeholder="{{inputPromptText}}" name="unitIdentifier" autofocus>
            </div>
            <div class="results"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="actions">
    <div class="ui ok right labeled icon button {{canAssign}}" id="assignUnit">
      OK
      <i class="checkmark icon"></i>
    </div>
  </div>
</template>