import './assign-unit-modal.html';
import { AbdnRequestsPackingUnitTypes } from '../../../../../api/abdn-requests/abdn-requests';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import { Template } from 'meteor/templating';

const unitTypeLabelMappings = {
  ccu: {
    label: 'Enter CCU',
    prompt: 'CCU...',
  },
  vehicle: {
    label: 'Enter Vehicle Reg',
    prompt: 'Vehicle Reg...',
  },
  cmr: {
    label: 'Enter CMR Number',
    prompt: 'CMR Number...',
  },
  begeleidingsbrief: {
    label: 'Enter Begeleidingsbrief Nummer',
    prompt: 'Begeleidingsbrief Nummer...',
  },
};

Template.assignUnitModalContents.onCreated(function onCreated() {
  const template = this;

  template.unitType = new ReactiveVar(AbdnRequestsPackingUnitTypes.CCU);
  template.isIdentifierEntered = new ReactiveVar(false);
});

Template.assignUnitModalContents.helpers({
  unitTypes: function getUnitTypes() {
    return AbdnRequestsPackingUnitTypes;
  },
  inputLabelText: function getInputLabelText() {
    const unitType = Template.instance().unitType.get();
    return unitTypeLabelMappings[unitType].label;
  },
  inputPromptText: function getInputPromptTest() {
    const unitType = Template.instance().unitType.get();
    return unitTypeLabelMappings[unitType].prompt;
  },
  canAssign: function getCanAssign() {
    const template = Template.instance();
    return template.isIdentifierEntered.get() ? '' : 'disabled';
  },
});

Template.assignUnitModalContents.events({
  'change [name="unitType"]': function onChange(event, templateInstance) {
    event.preventDefault();
    const value = $(event.target).val();
    templateInstance.unitType.set(value);
  },
  'input [name="unitIdentifier"]': function onInput(event, templateInstance) {
    event.preventDefault();
    const value = $(event.target).val();
    const isEntered = value !== undefined && value !== '';
    templateInstance.isIdentifierEntered.set(isEntered);
  },
  'keyup [name="unitIdentifier"]': function onKeyup(event, templateInstance) {
    const isIdentifierEntered = templateInstance.isIdentifierEntered.get();
    if (event.keyCode === 13 && isIdentifierEntered) {
      // if user hits Enter and an identifier has been entered, click
      // submit button so we still utilise modal callbacks
      templateInstance.$('#assignUnit').trigger('click');
    }
  },
});

Template.assignUnitModalContents.onDestroyed(function onDestroyed() {
  $('body .modals>.modal.assign-unit').remove();
});
