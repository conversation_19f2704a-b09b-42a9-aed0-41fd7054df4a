import { BaseModalMixin } from '../../../../modal/base-modal-mixin';

export const AssignUnitModalMethods = Object.assign({
  className: '.ui.modal.assign-unit',

  init(onApprove) {
    $(this.className).modal({
      allowMultiple: false,
      onApprove,
      onHidden: () => {
        // reset the inputs on the modal
        $('input:radio[name="unitType"]:first').click();
        $('[name="unitIdentifier"]').val('').trigger('input');
      },
    });

    return this;
  },
}, BaseModalMixin);
