<template name="returnedDeliveredButtons">
    {{#unless isItemDelivered}}
        {{#unless isItemReturned}}
            {{#unless isPartialDelivery}}
                <div class="negative packed ui right aligned button" id="returnedButton">Set Returned</div>
                <div class="positive packed ui right aligned button" id="deliveredButton">Set Delivered</div>
            {{/unless}}
        {{/unless}}
    {{/unless}}
    {{#if isItemDelivered}}
        {{#unless isPartialDelivery}}
        <div class="ui center aligned label" style="width:50%;">Item Delivered</div>
        {{/unless}}
    {{/if}}
    {{#if isPartialDelivery}}
        <div class="ui center aligned label" style="width:50%;">Partial Delivery</div>
    {{/if}}
    {{#if isItemReturned}}
        <div class="ui center aligned label" style="width:50%;">Item Returned</div>
    {{/if}}
</template>
