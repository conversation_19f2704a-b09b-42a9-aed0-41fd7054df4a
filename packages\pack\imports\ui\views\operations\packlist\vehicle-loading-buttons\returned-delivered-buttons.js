import './returned-delivered-buttons.html';

import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Meteor } from 'meteor/meteor';
import { ReturnedDeliveredSession } from '../returned-delivered-dialogs/returned-delivered-session';
import { Session } from 'meteor/session';
import { Template } from 'meteor/templating';

// TODO: Research the Meteor way of defining const event types.
//       These are just currently copied straight from the event factory.
//       Ideally, these should be shared between client and server operations
//       for sending event updates to server.

const updateTypes = {
  DELIVERED: 'DELIVERED',
  PARTIAL_DELIVERY: 'PARTIAL_DELIVERY',
  RETURNED: 'RETURNED',
};

const updateItemStatus = (updateType, id, vehicleRunId) => {
  Meteor.call('items.updateLoadedItem', updateType, id, vehicleRunId);
};

Template.returnedDeliveredButtons.onCreated(function onCreated() {
  const template = this;
  template.vehicleRunId = FlowRouter.getParam('vehicleRunId');
});

Template.returnedDeliveredButtons.helpers({
  isItemDelivered: function isItemDelivered() {
    return this.isDeliveredToDestination;
  },
  isItemReturned: function isItemReturned() {
    return this.isDeliveryReturned;
  },
  isPartialDelivery: function isPartialDelivery() {
    if (this.quantityDelivered &&
      this.quantityDelivered < this.quantity &&
      (!this.partialQtyInNewVehicleRunId ||
      this.partialQtyInNewVehicleRunId !== FlowRouter.getParam('vehicleRunId'))) {
      return true;
    }
    return false;
  },
});

Template.returnedDeliveredButtons.events({
  'click #returnedButton': function onClick(event, templateInstance) {
    updateItemStatus(
      updateTypes.RETURNED,
      templateInstance.data._id,
      templateInstance.vehicleRunId,
    );
  },
  'click #deliveredButton': function onClick(event, templateInstance) {
    Session.set(ReturnedDeliveredSession.Types.SHOW_DELIVERED_DIALOG, true);
    // Check whether the item is a partial delivery and if so pass remaining quantity
    let quantityToSendToModal = templateInstance.data.quantity;
    if (templateInstance.data.quantityDelivered) {
      quantityToSendToModal = quantityToSendToModal - templateInstance.data.quantityDelivered;
    }
    Session.set(ReturnedDeliveredSession.Types.DELIVERED_DIALOG_PARAMS,
      ReturnedDeliveredSession
        .createDeliveredSessionInput(templateInstance.data._id, quantityToSendToModal));
  },
});
