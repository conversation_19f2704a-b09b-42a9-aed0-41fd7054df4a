<template name="vehicleLoadingListItem">
  {{#if isItemLoaded}}
  <tr class = "negative" id = "mR{{_id}}">
    <td class = "ui one wide"><div class="ui red large horizontal label">{{itemRef}}</div></td>
    <td class = "ui five wide"><h3 class="ui header">{{getDestination}}</h3></td>
    <td class = "ui five wide centre aligned"><a class="ui large label">{{quantity}} x {{packageType}}</a><a class="ui large blue label">{{getWeight}}</a></td>
    <td class = "ui five wide right aligned">
    {{#if displayingClosedVehicle}}
        {{> returnedDeliveredButtons this}}
    {{else}}
        <div class="negative packed ui right aligned button" id ="unpackButton">Unload from {{vehicleLoadedIn}}</div>
    {{/if}}
          </td>
  </tr>
  {{else}}
    <tr>
    <td class = "ui one wide"><div class="ui red large horizontal label">{{itemRef}}</div></td>
    <td class = "ui six wide"><h3 class="ui header">{{getDestination}}</h3></td>
    <td class = "ui three wide centre aligned"><a class="ui large label">{{quantity}} x {{packageType}}</a><a class="ui large blue label">{{getWeight}}</a></td>
    <td class = "ui six wide right aligned">
    {{#if isItemLoaded}}
      <div class="negative packed ui button" id ="unpackButton">Unload from {{vehicleLoadedIn}}</div>
    {{else}}
      <div class="positive packed ui button" id="packButton">Load Item</div>
      {{/if}}
    </td>
  </tr>
  {{/if}}
</template>
