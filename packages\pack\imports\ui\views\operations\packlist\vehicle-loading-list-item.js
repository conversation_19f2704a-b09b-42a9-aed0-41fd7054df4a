import './vehicle-loading-list-item.html';
import './vehicle-loading-buttons/returned-delivered-buttons';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import { Session } from 'meteor/session';
import { Template } from 'meteor/templating';
import { VehicleRuns } from '../../../../api/vehicle-runs/vehicle-runs';

Template.vehicleLoadingListItem.onCreated(function onCreated() {
  const template = this;
  const currentVehicleRunId = FlowRouter.getParam('vehicleRunId');
  template.vehicleRunId = new ReactiveVar(currentVehicleRunId);
  template.subscribe('activeVehicleRun', template.vehicleRunId.get());
});

Template.vehicleLoadingListItem.helpers({
  isItemLoaded() {
    const templateInstanceData = Template.instance().data;
    const itemId = templateInstanceData._id;
    const vehicleRun = VehicleRuns.findOne();
    if (vehicleRun && vehicleRun.items && itemId) {
      return _.contains(vehicleRun.items, itemId);
    }
    return false;
  },
  vehicleLoadedIn() {
    const vehicleRun = VehicleRuns.findOne();
    return vehicleRun.vehicle.vehicleRegistration;
  },
  getWeight() {
    const templateInstanceData = Template.instance().data;
    const weight = templateInstanceData.weightKg;
    return weight === '' ? 'No Weight' : weight + 'kg';
  },
  getQuantity() {
    const quantity = this.quantity;
    const quantityDelivered = this.quantityDelivered;
    if (quantityDelivered) {
      // if the document has a quantityDelivered field then display remaining qty
      return quantity - quantityDelivered;
    }
    // else return the quantity of the item
    return quantity;
  },
  getDestination() {
    const templateInstanceData = Template.instance().data;
    const destCategory = templateInstanceData.destCategory;
    return destCategory;
  },
});

Template.vehicleLoadingListItem.events({
  'click #unpackButton': function onClick(event, templateInstance) {
    event.preventDefault();
    const vehicleRunId = templateInstance.vehicleRunId.get();
    const itemId = templateInstance.data._id;
    Meteor.call('vehicleRuns.unpackItem', vehicleRunId, itemId);
  },
  'click #packButton': function onClick(event, templateInstance) {
    event.preventDefault();
    const vehicleRunId = templateInstance.vehicleRunId.get();
    const itemId = templateInstance.data._id;
    Meteor.call('vehicleRuns.packItem', vehicleRunId, itemId, (error) => {
      if (error) {
        console.log(error);
      } else {
        Session.set('vehicleLoadingList.mostRecentlyLoadedId', itemId);
      }
    });
  },
});
