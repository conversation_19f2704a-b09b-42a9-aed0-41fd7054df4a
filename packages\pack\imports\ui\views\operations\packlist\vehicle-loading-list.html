<template name="vehicleLoadingList">
  <div class="row checklist ui container" style="height:75%;">
      <div class="ui grid">
          <div class="row" style="margin-bottom: 10px;">
              <div class="ten wide column">
                  <div class="ui two column grid">
                      <div class="eight wide column">
                          <div class="ui large header">
                              <img class="tiny ui image" src="/images/client-logos/{{currentClient.logo}}"/>
                              <div class="content">{{currentVehicleRun.clientName}}</div>
                          </div>
                      </div>
                      <div class="column">
                        <h3>{{runDateFormatted}} {{runTimeFormatted}} - {{runIdentifier}}</h3>
                        {{#each currentVehicleRun.destinations}}
                            <h5>{{deliveryDestination}}</h5>
                        {{/each}}
                      </div>
                  </div>
              </div>
              <div class="six wide middle aligned column">
                  <div class="ui big secondary menu right floated">
                    {{#if isVehicleAssigned}}
                      {{> vehicleOptions vehicleWithContents}}
                    {{/if}}
                    {{#unless isClosedVehicle}}
                      <div class="horizontally fitted item">
                          <div class="ui blue basic button" id="addVehicleButton">
                              <i class="edit icon"></i>{{assignVehicleButtonText}}</div>
                      </div>
                     {{/unless}}
                  </div>

              </div>
          </div>
      </div>
      <div class="ui blue blurring attached segment" id="vehicleLoadingListSegment" style="height:90%;overflow-y:hidden;">
          <div class="ui segment">
              <div class="ui middle aligned two column grid">
                  <div class="column">
                      <div class="ui form">
                          <div class="fields" style="margin-bottom:0px;">
                              <div class="field">
                                  <div class="ui fluid search selection">
                                      <div class="ui icon input">
                                          <input class="prompt" type="text" placeholder="Filter Load List..." name="filterLoadList">
                                          <i class="search icon"></i>
                                      </div>
                                  </div>
                              </div>
                              <div class="field"></div>

                                {{#unless isClosedVehicle}}
                                <div class="field" style="padding-top:1.5%;">
                                  <div class="ui toggle checkbox" id="loadedOnly">
                                      <input type="checkbox" name="limitToLoaded">
                                      <label>View Loaded Items Only</label>
                                  </div>
                                  </div>
                                {{else}}
                                <div class = "field">
                                  <button class = "ui labeled icon basic blue button" id = "exportButton">
                                    <i class = "file pdf outline icon"></i>
                                    Create Manifest
                                  </button>
                                </div>
                                {{/unless}}

                          </div>
                      </div>
                  </div>
                  <div class="column">
                      <h4 style="text-align:right;">
                      {{#if isClosedVehicle}}
                      <i class="lock icon"></i>Open Vehicle to Update Contents
                      {{else}}
                      <i class="barcode icon"></i>Scan barcode or Select item to Load
                      {{/if}}
                  </h4>
                  </div>
              </div>
          </div>
          <div class="ui inverted dimmer">
              <div class="content">
                  <div class="center">
                      <h2 class="ui icon header">
                          <i class="arrow up icon"></i> Assign Vehicle to Begin Loading
                      </h2>
                  </div>
              </div>
          </div>
          <div class="fluid segment" style="height:80%;overflow-y:scroll;" id="scrollingSegment">
              

                    {{#if anyItems}}
                        <table class="ui basic very compact table" style="border: 0px;" id='loadtable'>
                        <tbody>
                            {{#each currentVehicleLoadList}}
                                {{> vehicleLoadingListItem vehicleLoadingListItemForDisplay}}
                            {{/each}}
                        </tbody>
                        {{#unless isClosedVehicle}}
                        {{#unless viewLoadedOnly}}
                        <tfoot class="ui full width">
                        <th colspan="4">
                            <div class="fluid ui basic blue icon button" id="loadMoreItems"><i class="ellipsis vertical icon"></i>Load More</div>
                        </th>
                        </tfoot>
                        {{/unless}}
                        {{/unless}}
                        </table>
                    {{else}}
                        <div class="ui center aligned disabled header" style="margin-top:10%;">
                            {{#if isClosedVehicle}}
                                <i class="info circle icon"></i>NO ITEMS IN VEHICLE
                            {{else}}
                            {{#if viewLoadedOnly}}
                                <i class = "info circle icon"></i>NO LOADED ITEMS
                            {{else}}
                                <i class="info circle icon"></i>NO ITEMS TO BE LOADED
                            {{/if}}
                            {{/if}}
                        </div>
                    {{/if}}

                  {{#unless Template.subscriptionsReady}}
                    <div class="ui inverted active dimmer">
                        <div class="content">
                            <div class="ui text loader">Loading...</div>
                        </div>
                    </div>
                  {{/unless}}

          </div>
      </div>

      <div class="ui bottom attached basic segment">
          <div class="ui fluid two column grid">

              <div class="thirteen wide column">
                  <table class="ui fixed single line basic celled table" style="border: 0px;" id="recentTable">
                      <tr>
                          <td>
                              <div class="ui tiny header item">
                                  <i class="ui tiny checked calendar icon"></i>Most Recently Loaded
                              </div>
                          </td>
                          <!-- The id has to be set with text infront to ensure we can access it -->
                          {{#each getMostRecentItemsInVehicle}}
                          <td class = "recentLoading" id = "mR{{_id}}" style="text-align:center;">
                              <a class="ui label recentLoading" id="mR{{_id}}">{{itemRef}}</a>
                          </td>
                          {{/each}}
                      </tr>
                  </table>
              </div>
              <div class="right aligned three wide column">
                  {{#if isClosedVehicle}}
                  <div class="ui blue basic button" id="openVehicle"><i class="ui lock icon"></i>Open Vehicle</div>
                  {{else}}
                  <div class="ui blue basic button" id="closeVehicle"><i class="ui unlock icon"></i>Close Vehicle</div>
                  {{/if}}
              </div>
          </div>
      </div>
      {{> deliveredDialog}}
  </div>
</template>
