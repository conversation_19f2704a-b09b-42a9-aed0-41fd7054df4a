import './vehicle-loading-list.html';
import './vehicle-loading-list-item';
import './vehicle-options/vehicle-options';
import './ccu-option';
import './returned-delivered-dialogs/delivered-dialog';
import { CompanySiteProfiles } from '../../../../api/company-site-profiles/company-site-profiles';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Items } from '../../../../api/items/items';
import { jsPDF as JsPDF } from 'meteor/jspdf:core';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import { ReturnedDeliveredSession } from './returned-delivered-dialogs/returned-delivered-session';
import { Session } from 'meteor/session';
import { Template } from 'meteor/templating';
import { VehicleRuns } from '../../../../api/vehicle-runs/vehicle-runs';
import { _ } from 'meteor/underscore';
import { manifestPdf } from '../../../pdf/manifest';
import { moment } from 'meteor/momentjs:moment';

const isClosedVehicle = () => {
  const selectedVehicleId = Session.get('vehicleLoadingList.selectedVehicleId');
  const currentVehicleRun = VehicleRuns.findOne();
  if (currentVehicleRun) {
    const assignedVehicle = currentVehicleRun.vehicle;
    if (assignedVehicle) {
      return assignedVehicle.isClosed;
    }
  }
  return false;
};

// Filters client-side items collection to display all items when vehicle is open
// and 'View Loaded Items' is not selected. And only displays loaded items if vehicle
// closed or 'View Loaded Items' is selected.
const currentVehicleLoadList = () => {
  const selectedVehicleId = Session.get('vehicleLoadingList.selectedVehicleId');
  const currentVehicleRunId = FlowRouter.getParam('vehicleRunId');
  const vehicleRun = VehicleRuns.findOne(currentVehicleRunId);
  const viewLoadedOnly = Template.instance().vehicleLoadingListLoadedCheckbox.get();
  let itemsToDisplay = [];
  if (vehicleRun) {
    let selector = {
      $or: [
        { vehicle: null, vehicleRun: null },
        { _id: { $in: vehicleRun.items } },
        // Include items that have been partially delivered
        { quantityDelivered: { $exists: true } },
      ],
    };
    const assignedVehicle = vehicleRun.vehicle;
    if (assignedVehicle && selectedVehicleId) {
      if (assignedVehicle.isClosed || viewLoadedOnly) {
        selector = { _id: { $in: vehicleRun.items } };
      }
    }
    itemsToDisplay = Items.find(selector).fetch();
    itemsToDisplay = _.sortBy(
      itemsToDisplay,
      (item) => -item.createdAt,
    );
  }
  return itemsToDisplay;
};

Template.vehicleLoadingList.onCreated(function onCreated() {
  const template = this;
  const currentVehicleRunId = FlowRouter.getParam('vehicleRunId');
  template.vehicleRunId = new ReactiveVar(currentVehicleRunId);
  // Stores the selected vehicle or false
  Session.set('vehicleLoadingList.selectedVehicleId', false);
  const assignedVehicleId = FlowRouter.getParam('assignedVehicleId');
  if (assignedVehicleId) {
    Session.set('vehicleLoadingList.selectedVehicleId', assignedVehicleId.vehicleRegistration);
  }
  // Stores whether to display the delivered dialog modal
  Session.set(ReturnedDeliveredSession.Types.SHOW_DELIVERED_DIALOG, false);
  // Set to item id for bounce animation on most recently loaded
  template.checkForTransitionAnimation = new ReactiveVar(false);
  // Vars for storing filter query term and query results
  template.vehicleLoadingListFilter = new ReactiveVar('');
  // Used to define number of results returned
  template.vehicleLoadingListLimit = new ReactiveVar(100);
  // Used for determining state of view loaded items checkbox (checkbox initialised to false)
  template.vehicleLoadingListLoadedCheckbox = new ReactiveVar(false);

  template.subscribe('activeVehicleRunAndItems', currentVehicleRunId);

  template.autorun(() => {
    template.subscribe('items.forClientFiltered',
      FlowRouter.getParam('clientId'),
      template.vehicleLoadingListFilter.get(),
      template.vehicleLoadingListLimit.get(),
      true,
    );
    template.vehicleRun = VehicleRuns.findOne(FlowRouter.getParam('vehicleRunId'));
    if (template.vehicleRun !== undefined) {
      template.vehicleRun.items = currentVehicleLoadList(true);
    }
  });
});

Template.vehicleLoadingList.onRendered(function onRendered() {
  const template = this;
  // Hide dimmer initially.
  template.$('#vehicleLoadingListSegment').dimmer('hide');

  // Autorun block used for vehicle selector, filtering and subscriptions
  template.autorun(() => {
    // Recently loaded transition animation
    const showTransitionId = Session.get('vehicleLoadingList.mostRecentlyLoadedId');
    Meteor.defer(() => {
      if (showTransitionId) {
        const recentlyLoadedItem = template.$('#recentTable')
          .find('tr:first')
          .find('#mR' + showTransitionId);
        recentlyLoadedItem.transition('bounce');

        Session.set('vehicleLoadingList.mostRecentlyLoadedId', false);
      }
    });
    // Vehicle selector and dimmer
    const isVehicleSelected = Session.get('vehicleLoadingList.selectedVehicleId');
    const currentVehicleRun = VehicleRuns.findOne();
    // Only update length if assigned vehicles are present
    if (currentVehicleRun) {
      const assignedVehicle = currentVehicleRun.vehicle;
      if (assignedVehicle && assignedVehicle.vehicleRegistration) {
        Session.set('vehicleLoadingList.selectedVehicleId', assignedVehicle.vehicleRegistration);
        // Hide the dimmer
        template.$('#vehicleLoadingListSegment').dimmer('hide');
      } else {
        // No vehicle selected so show the dimmer
        template.$('#vehicleLoadingListSegment').dimmer('show');
      }
    }
  });
});

Template.vehicleLoadingList.helpers({
  currentVehicleRun() {
    const currentVehicleRun = VehicleRuns.findOne(FlowRouter.getParam('vehicleRunId'));
    return currentVehicleRun;
  },
  currentClient() {
    const siteProfile = CompanySiteProfiles.findOne();
    if (siteProfile) {
      const siteClients = siteProfile.configuration.clients;
      return _.find(siteClients, client => client._id === FlowRouter.getParam('clientId'));
    }
    return [];
  },
  isClosedVehicle() {
    return isClosedVehicle();
  },
  viewLoadedOnly() {
    return Template.instance().vehicleLoadingListLoadedCheckbox.get();
  },
  currentVehicleLoadList() {
    return currentVehicleLoadList();
  },
  getMostRecentItemsInVehicle() {
    // TODO: Think how to make this work
    const vehicleRun = VehicleRuns.findOne(FlowRouter.getParam('vehicleRunId'));
    if (vehicleRun && vehicleRun.items) {
      const mostRecent = _.last(vehicleRun.items, 3);
      const items = Items.find({ _id: { $in: mostRecent } }).fetch();
      return items;
    }
    return [];
  },
  anyItems() {
    const result = Items.find().fetch();
    if (result) return result.length > 0;
    return false;
  },
  assignVehicleButtonText() {
    const vehicleRun = VehicleRuns.findOne(FlowRouter.getParam('vehicleRunId'));
    if (vehicleRun) {
      return vehicleRun.vehicle.vehicleRegistration === null
        ? 'Assign Vehicle' : 'Change Vehicle';
    }
    return '';
  },
  vehicleLoadingListItemForDisplay() {
    return _.extend({ displayingClosedVehicle: isClosedVehicle() }, this);
  },
  vehicleWithContents() {
    const vehicleRun = VehicleRuns.findOne(FlowRouter.getParam('vehicleRunId'));
    return _.extend({ contents: vehicleRun.items }, vehicleRun.vehicle);
  },
  isVehicleAssigned() {
    const vehicleRun = VehicleRuns.findOne(FlowRouter.getParam('vehicleRunId'));
    if (vehicleRun) {
      return vehicleRun.vehicle.vehicleRegistration !== null;
    }
    return false;
  },
  runDateFormatted() {
    const currentVehicleRun = VehicleRuns.findOne(FlowRouter.getParam('vehicleRunId'));
    if (currentVehicleRun) {
      return moment(currentVehicleRun.scheduledDateTime).format('DD-MMM-YYYY');
    }
    return '';
  },
  runTimeFormatted() {
    const currentVehicleRun = VehicleRuns.findOne(FlowRouter.getParam('vehicleRunId'));
    if (currentVehicleRun) {
      return moment(currentVehicleRun.scheduledDateTime).format('HH:mm');
    }
    return '';
  },
  runIdentifier() {
    const currentVehicleRun = VehicleRuns.findOne(FlowRouter.getParam('vehicleRunId'));
    if (currentVehicleRun) {
      return `Run ${currentVehicleRun.runIdentifierStr}`;
    }
    return '';
  },
});

Template.vehicleLoadingList.events({
  'click #addVehicleButton': function onClick(event, templateInstance) {
    event.preventDefault();
    FlowRouter.go('assignVehicle', {
      clientId: FlowRouter.getParam('clientId'),
      vehicleRunId: templateInstance.vehicleRunId.get(),
    });
  },
  'click #addItemButton': function onClick(event, templateInstance) {
    event.preventDefault();
    FlowRouter.go('addPicklistItem', {
      requestId: templateInstance.vehicleRunId.get(),
    });
  },
  'click #openVehicle': function onClick(event, templateInstance) {
    event.preventDefault();
    Meteor.call(
      'vehicleRuns.openVehicle',
      templateInstance.vehicleRunId.get(),
    );
  },
  'click #closeVehicle': function onClick(event, templateInstance) {
    event.preventDefault();
    Meteor.call(
      'vehicleRuns.closeVehicle',
      templateInstance.vehicleRunId.get(),
    );
  },
  'click #loadMoreItems': function onClick(event, templateInstance) {
    event.preventDefault();
    const currentLimit = templateInstance.vehicleLoadingListLimit.get();
    templateInstance.vehicleLoadingListLimit.set(currentLimit + 50);
  },
  'keyup [name=filterLoadList]': function onKeyup(event, templateInstance) {
    event.preventDefault();
    templateInstance.vehicleLoadingListFilter.set(event.target.value);
  },
  'change [name=limitToLoaded]': function onChange(event, templateInstance) {
    event.preventDefault();
    templateInstance.vehicleLoadingListLoadedCheckbox.set(event.target.checked);
  },
  'click .recentLoading': function onClick(event, templateInstance) {
    event.preventDefault();
    const clickedItem = templateInstance.$('#' + event.target.id);
    templateInstance.$('[name=limitToLoaded]').prop('checked', true);
    templateInstance.vehicleLoadingListLoadedCheckbox.set(true);
    // Scroll to item in list and animate with flash
    Meteor.defer(() => {
      if (templateInstance.subscriptionsReady) {
        templateInstance.$('#scrollingSegment').scrollTo(clickedItem);
        templateInstance.$(clickedItem).transition('flash');
      }
    });
  },
  'click #exportButton': function onClick(event, templateInstance) {
    event.preventDefault();
    const vehicleRun = templateInstance.vehicleRun;
    const dateForFileName = moment(vehicleRun.createdAt).format('DD-MMM-YYYY HH:MM');

    manifestPdf.create(vehicleRun)
      .then((doc) =>
        doc.save(`manifest_for_run_${vehicleRun.runIdentifierStr}_on_${dateForFileName}.pdf`))
      .catch((e) => console.error('Failed to save manifest doc: ' + e));
  },
});

Template.vehicleLoadingList.onDestroyed(function onDestroyed() {
  Session.set('vehicleLoadingList.selectedVehicleId', false);
});
