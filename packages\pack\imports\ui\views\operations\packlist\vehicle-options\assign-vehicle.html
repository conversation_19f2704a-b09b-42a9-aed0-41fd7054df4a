<template name="assignVehicle">
    <div class="ui container">
        <div class="ui vertical aligned grid">
            <div class="column">
                <div class="ui large header left floated">
                    <div class="content">
                        <h2>{{currentVehicleRun.clientName}}</h2>
                        <h3>{{runDateTimeFormatted}} - {{runIdentifier}}</h3>
                        <h3>{{destination}}</h3>
                    </div>
                </div>
            </div>
        </div>
        <div class="ui container">
            <div class="ui two column middle aligned very relaxed stackable grid">
                <div class="column">
                    <form class="ui form">
                        <div class="field">
                            <label>Assign Vehicle</label>
                            <div class="ui fluid search selection dropdown">
                                <input type="hidden" name="vehicle" value="{{currentVehicleRun.vehicle.vehicleRegistration}}">
                                <i class="dropdown icon"></i>
                                <div class="default text">Vehicle</div>
                                <div class="menu">
                                    {{#each availableVehicles}}
                                    <div class="item" data-value="{{vehicleRegistration}}" data-text="{{vehicleRegistration}}">
                                        {{vehicleRegistration}}
                                    </div>
                                    {{/each}}
                                </div>
                            </div>
                        </div>
                      </form>
                  </div>

        <div class="row">
          <div class="column right aligned" style="margin-top:10px;">
            <button class="ui big labeled icon button" id="backButton"><i class="angle left icon"></i>Back</button>
            <button class="ui big primary button {{canSubmit}}" id="addButton">Assign</button>
          </div>
        </div>
      </div>
  </div>
    </div>
</template>
