import './assign-vehicle.html';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import { Template } from 'meteor/templating';
import { VehicleRuns } from '../../../../../api/vehicle-runs/vehicle-runs';
import { Vehicles } from '../../../../../api/vehicles/vehicles';
import { moment } from 'meteor/momentjs:moment';

const assignVehicleToVehicleRun = (templateInstance, onComplete) => {
  const vehicleRunId = templateInstance.vehicleRunId.get();
  const form = templateInstance.$('form:first');
  const vehicleReg = form.find('[name=vehicle]').val();
  Meteor.call('vehicleRuns.assignVehicle', vehicleRunId, vehicleReg, onComplete);
};

Template.assignVehicle.onCreated(function onCreated() {
  const template = this;
  const vehicleRunId = FlowRouter.getParam('vehicleRunId');
  template.vehicleRunId = new ReactiveVar(vehicleRunId);
  template.canSubmit = new ReactiveVar(false);
  template.subscribe('activeVehicleRun', vehicleRunId);
  template.subscribe('siteVehicles');
});

Template.assignVehicle.onRendered(function onRendered() {
  const template = this;
  template.$('.dropdown').dropdown();
});

Template.assignVehicle.helpers({
  currentVehicleRun() {
    return VehicleRuns.findOne({ _id: Template.instance().vehicleRunId.get() });
  },
  availableVehicles() {
    const availableVehicles = Vehicles.find({}).fetch();
    if (availableVehicles) {
      return _.sortBy(availableVehicles, (vehicle) => vehicle.vehicleRegistration);
    }
    return [];
  },
  canSubmit() {
    const canSubmit = Template.instance().canSubmit.get();
    return canSubmit ? '' : 'disabled';
  },
  destination() {
    let destString = '';
    const vehicleRun = VehicleRuns.findOne({ _id: Template.instance().vehicleRunId.get() });
    if (vehicleRun && vehicleRun.destinations) {
      destString = vehicleRun.destinations[0].deliveryDestination;
    }
    return destString;
  },
  runDateTimeFormatted() {
    let runDateTimeFormatted = '';
    const vehicleRun = VehicleRuns.findOne({ _id: Template.instance().vehicleRunId.get() });
    if (vehicleRun) {
      runDateTimeFormatted = moment(vehicleRun.scheduledDateTime).format('DD-MMM-YYYY HH:mm');
    }
    return runDateTimeFormatted;
  },
  runIdentifier() {
    let identifierString = '';
    const vehicleRun = VehicleRuns.findOne({ _id: Template.instance().vehicleRunId.get() });
    if (vehicleRun) {
      identifierString = `Run ${vehicleRun.runIdentifierStr}`;
    }
    return identifierString;
  },
});

Template.assignVehicle.events({
  'click #backButton': function onClick(event, templateInstance) {
    event.preventDefault();
    FlowRouter.go('vehicleLoadingList', {
      clientId: FlowRouter.getParam('clientId'),
      vehicleRunId: templateInstance.vehicleRunId.get(),
    });
  },
  'click #addButton': function onClick(event, templateInstance) {
    event.preventDefault();
    const onComplete = (error, result) => {
      if (!error) {
        FlowRouter.go(
          'vehicleLoadingListWithVehicle',
          {
            clientId: FlowRouter.getParam('clientId'),
            vehicleRunId: templateInstance.vehicleRunId.get(),
          },
          { assignedVehicleId: result },
        );
      }
    };
    assignVehicleToVehicleRun(templateInstance, onComplete);
  },
  'change [name=vehicle]': function onChange(event, templateInstance) {
    event.preventDefault();
    const vehicleInput = event.target;
    const isEntered = vehicleInput !== undefined;
    templateInstance.canSubmit.set(isEntered);
  },
});
