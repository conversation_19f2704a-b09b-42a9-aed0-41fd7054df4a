<template name="addCcu">
    <div class="ui container">

        <div class="ui vertical aligned two column grid">
            <div class="column">
                <div class="ui large header left floated">
                    <img class="tiny ui image" src="/images/client-logos/{{currentRequest.client.logo}}" style="height:68px;"/>
                    <div class="content">
                        {{currentRequest.client.name}} - {{currentRequest.destination.name}}
                    </div>
                </div>
            </div>

        </div>
        <div class="ui container" style="position:relative;">
            <div class="ui two column middle aligned very relaxed stackable grid">
                <div class="column">
                    <div class="ui form">
                        <div class="field">
                            <label>Enter CCU</label>
                            <div class="ui fluid search selection">
                                <div class="ui icon input">
                                    <input class="prompt" type="text" placeholder="CCU..." name="ccu">
                                    <i class="search icon"></i>
                                </div>
                                <div class="results"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="ui vertical divider">
                    Or
                </div>
                <div class="center aligned column">
                    <h2 class="ui center aligned icon header">
                        <i class="barcode icon"></i>
                        <div class="content">
                            Scan CCU
                        </div>
                    </h2>
                </div>
            </div>
        </div>

            <div class="row">
                <div class="sixteen wide column">
                    <button class="ui big labeled icon button" id="backButton"><i class="angle left icon"></i>Back</button>
                    <button class="ui big primary button" id="addButton">Add</button>
                    <button class="ui big primary button" id="addAndNextButton">Add & Next</button>
                </div>
</div>
    </div>
</template>
