import './add-ccu.html';
import { $ } from 'meteor/jquery';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import { Requests } from '../../../../api/requests/requests';
import { Template } from 'meteor/templating';

function addCcuToAssignedCcus(template, onComplete) {
  const request = Requests.findOne();
  const ccuName = template.$('[name=ccu]').val();
  Meteor.call(
    'requests.updateAssignedCcus',
    request._id,
    ccuName,
    onComplete,
  );
}

Template.addCcu.onCreated(function onCreated() {
  const template = this;
  const requestId = FlowRouter.getParam('requestId');
  template.requestId = new ReactiveVar(requestId);
  template.autorun(() => {
    template.subscribe('activeRequest', requestId);
  });
});

Template.addCcu.helpers({
  currentRequest: function currentRequest() {
    return Requests.findOne({ _id: Template.instance().requestId.get() });
  },
});

Template.addCcu.events({
  'click #backButton': function onClick(event, templateInstance) {
    event.preventDefault();
    FlowRouter.go('packlist', { clientId: FlowRouter.getParam('clientId'), requestId: templateInstance.requestId.get() });
  },
  'click #addButton': function onClick(event, templateInstance) {
    event.preventDefault();
    const onComplete = (error, result) => {
      if (!error) {
        FlowRouter.go('packlistWithCcu', { clientId: FlowRouter.getParam('clientId'), requestId: templateInstance.requestId.get() }, { ccuId: result });
      }
    };
    addCcuToAssignedCcus(templateInstance, onComplete);
  },
  'click #addAndNextButton': function onClick(event, templateInstance) {
    event.preventDefault();
    const onComplete = (error, result) => {
      if (!error) {
        templateInstance.$('[name=ccu]').val('');
      }
    };
    addCcuToAssignedCcus(templateInstance, onComplete);
  },
});
