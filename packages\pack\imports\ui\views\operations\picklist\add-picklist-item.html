<template name="addPicklistItem">
    <div class="ui container">

        <div class="ui vertical aligned two column grid">
            <div class="column">
                <div class="ui left aligned large header">
                    <img class="tiny ui image" src="/images/client-logos/{{currentRequest.client.logo}}" />
                    <div class="content">
                        {{currentRequest.client.name}} - {{currentRequest.destination.name}}
                    </div>
                </div>
            </div>

        </div>
        <div class="ui container" style="position:relative;">
            <div class="ui two column middle aligned very relaxed stackable grid">
                <div class="column">
                    <div class="ui form">
                        <div class="field">
                            <label>Item</label>
                            <div class="ui fluid search selection">
                                <div class="ui icon input">
                                    <input class="prompt" type="text" placeholder="Description..." name="description">
                                    <i class="search icon"></i>
                                </div>
                                <div class="results"></div>
                            </div>
                        </div>
                        <div class="field">
                            <label>Serial No</label>
                            <input type="text" placeholder="Serial No" name="serialNo">
                        </div>
                        <div class="field">
                            <label>Item No</label>
                            <input type="text" placeholder="Item No" name="itemNo">
                        </div>
                    </div>
                </div>
                <div class="ui vertical divider">
                    Or
                </div>
                <div class="center aligned column">
                    <h2 class="ui center aligned icon header">
                        <i class="barcode icon"></i>
                        <div class="content">
                            Scan Item
                        </div>
                    </h2>
                </div>
            </div>
        </div>

        <div class="ui two column middle aligned very relaxed stackable grid">


            <div class="column">
                <div class="ui form">
                    <div class="eight wide field">
                        <label>Quantity</label>
                        <input type="text" placeholder="Quantity" name="quantity">
                    </div>
                        <div class="eight wide field">
                    <label>Packing Unit:</label>
                    <div class="ui fluid search selection dropdown">
                        <input type="hidden" name="packingUnit" value="">
                        <i class="dropdown icon"></i>
                        <div class="default text">Packing Unit</div>
                        <div class="menu">
                        {{#each packingUnits}}
                            <div class="item" data-value="{{_id}}" data-text="{{name}}">
                            	{{name}}
                            </div>
                        {{/each}}
                        </div>
                    </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="sixteen wide column">
                    <button class="ui big labeled icon button" id="backButton"><i class="angle left icon"></i>Back</button>
                    <button class="ui big primary button" id="addButton">Add</button>
                    <button class="ui big primary button" id="addAndNextButton">Add & Next</button>
                </div>
            </div>
        </div>
    </div>
</template>