import './add-picklist-item.html';
import { $ } from 'meteor/jquery';
import { CompanySiteProfiles } from '../../../../api/company-site-profiles/company-site-profiles';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Meteor } from 'meteor/meteor';
import { Publications } from '../../../../api/api.publications/publications';
import { ReactiveVar } from 'meteor/reactive-var';
import { Requests } from '../../../../api/requests/requests';
import { Template } from 'meteor/templating';

function addItemToPicklist(templateInstance, onComplete) {
  const requestId = Requests.findOne();

  const description = templateInstance.$('[name=description]').val();
  const serialNo = templateInstance.$('[name=serialNo]').val();
  const itemNo = templateInstance.$('[name=itemNo]').val();
  const packingUnit = templateInstance.$('[name=packingUnit]').val();
  const quantity = templateInstance.$('[name=quantity]').val();

  Meteor.call(
    'requests.addItem',
    requestId._id,
    {
      description, serialNo, itemNo, packingUnit, quantity,
    },
    onComplete,
  );
}

Template.addPicklistItem.onCreated(function onCreated() {
  const template = this;
  const requestId = FlowRouter.getParam('requestId');
  template.requestId = new ReactiveVar(requestId);
  template.autorun(() => {
    template.subscribe('activeRequest', requestId);
    template
      .subscribe(Publications.companySiteProfiles.CompanySiteProfileForUser);
  });
});

Template.addPicklistItem.onRendered(function onRendered() {
  const template = this;
  template.$('.dropdown').dropdown();
  template.$('[name=description]').search();
  template.$('.ui.search')
    .search({
      minCharacters: 2,
      apiSettings: {
        responseAsync(settings, callback) {
          const response = {
            success: true,
            results: [],
          };
          const searchText = template.$('[name=description]').val();

          Meteor.call(
            'catalogue.search',
            searchText,
            (error, searchResult) => {
              if (error) {
                console.log(error);
                response.success = false;
              } else {
                response.results = searchResult;
              }
              callback(response);
            });
        },
      },
      onSelect(result, response) {
        template.$('[name=serialNo]').val('SN-' + result.description);
        template.$('[name=itemNo]').val(result.description);
      },
    });
});

Template.addPicklistItem.helpers({
  currentRequest: function currentRequest() {
    return Requests.findOne();
  },
  packingUnits: function packingUnits() {
    const siteProfile = CompanySiteProfiles.findOne();

    if (siteProfile) {
      const sitePackingUnits = siteProfile.configuration.packingUnits;
      return _.sortBy(sitePackingUnits, (packingUnit) => packingUnit.name);
    }
    return [];
  },
});

Template.addPicklistItem.events({
  'click #addButton': function onClick(event, templateInstance) {
    event.preventDefault();

    const onComplete = (error, result) => {
      if (!error) {
        FlowRouter.go('checklist', { requestId: templateInstance.requestId.get() });
      }
    };

    addItemToPicklist(templateInstance, onComplete);
  },
  'click #addAndNextButton': function onClick(event, templateInstance) {
    event.preventDefault();

    const onComplete = (error, result) => {
      if (!error) {
        templateInstance.$('[name=description]').val('');
        templateInstance.$('[name=serialNo]').val('');
        templateInstance.$('[name=itemNo]').val('');
        templateInstance.$('[name=packingUnit]').val('');
        templateInstance.$('[name=quantity]').val('');
      }
    };

    addItemToPicklist(templateInstance, onComplete);
  },
  'click #backButton': function onClick(event, templateInstance) {
    event.preventDefault();
    FlowRouter.go('checklist', { requestId: templateInstance.requestId.get() });
  },
});
