import './checklist-item-in-ccu.html';

Template.checklistItemInCcu.onCreated(function onCreated() {
  const template = this;
  // Reactive Var for item id
  template.itemId = new ReactiveVar();
  template.itemId.set('');
});

Template.checklistItemInCcu.onRendered(function onRendered() {
  const template = this;
  template.autorun(function () {
    const templateData = Template.currentData();
    template.itemId.set(templateData._id);
  });
});

Template.checklistItemInCcu.events({
  'click .unpack.button': function onClick(event, template) {
    event.preventDefault();
    Meteor.call('requests.updatePackedStatus',
      Template.instance().itemId.get(),
      false,
      null,
      (error) => {
        if (error) {
          console.log(error);
        }
      },
    );
  },
});
