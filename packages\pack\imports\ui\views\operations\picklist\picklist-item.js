import './picklist-item.html';

import { $ } from 'meteor/jquery';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import { Requests } from '../../../../api/requests/requests';
import { Template } from 'meteor/templating';

Template.picklistItem.events({
  'click .picked-button': function onClick(event, templateInstance) {
    event.preventDefault();

    const thisItem = templateInstance.data;

    if (!thisItem.isPicked) {
      Meteor.call('requests.setItemAsPicked',
        FlowRouter.getParam('requestId'),
        thisItem._id,
        (error) => {
          if (!error) {
            console.log('success');
          } else {
            console.log(error);
          }
        },
      );
    } else {
      Meteor.call(
        'requests.setItemAsNotPicked',
        FlowRouter.getParam('requestId'),
        thisItem._id,
        (error) => {
          if (!error) {
            console.log('success');
          } else {
            console.log(error);
          }
        },
      );
    }
  },
});
