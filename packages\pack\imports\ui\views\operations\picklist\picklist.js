import './picklist.html';
import './picklist-item';
import './checklist-item-in-ccu';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { ReactiveVar } from 'meteor/reactive-var';
import { Requests } from '../../../../api/requests/requests';
import { Template } from 'meteor/templating';
import { _ } from 'meteor/underscore';

Template.picklist.onCreated(function onCreated() {
  const template = this;
  const requestId = FlowRouter.getParam('requestId');
  template.requestId = new ReactiveVar(requestId);
  template.autorun(() => {
    template.subscribe('activeRequest', requestId);
  });
});

Template.picklist.helpers({
  currentRequest() {
    const requestId = Template.instance().requestId.get();
    const request = Requests.findOne({
      _id: requestId,
    });
    return request;
  },
  items() {
    const requestId = Template.instance().requestId.get();
    const request = Requests.findOne({
      _id: requestId,
    });
    if (request && request.items) {
      return _.sortBy(request.items, item => item.isPicked);
    }
    return [];
  },
  hasAnyItems() {
    const requestId = Template.instance().requestId.get();
    const request = Requests.findOne({
      _id: requestId,
    });
    if (request) {
      return request.items.length > 0;
    }
    return false;
  },
});

Template.picklist.events({
  'click #addPicklistItem': function handleClick(event, templateInstance) {
    FlowRouter.go('addPicklistItem', {
      requestId: templateInstance.requestId.get(),
    });
  },
  'click #addCcuButton': function addCcu(event, templateInstance) {
    event.preventDefault();
    FlowRouter.go('addCcu', {
      requestId: templateInstance.requestId.get(),
    });
  },
});
