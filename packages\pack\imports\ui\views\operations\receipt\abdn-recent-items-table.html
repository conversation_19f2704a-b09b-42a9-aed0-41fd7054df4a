<template name="abdnRecentItemsTable">
    <h1 class="ui header">Recent Items</h1>
    {{#if itemsSubscriptionReady}}
        <table id="recentItemsTable" class="ui very compact selectable cell striped fixed single line table">
            <thead>
                <tr>
                    <th class="receiptNo two wide" fieldid="0">
                        Receipt No
                    </th>
                    <th class="receivedDate three wide" fieldid="1">
                        Received
                    </th>
                    <th class="receiptCategory two wide" fieldid="2">
                        Category
                    </th>
                    <th class="vendor two wide" fieldid="3">
                        Vendor
                    </th>
                    <th class="vendor two wide" fieldid="4">
                        Deliv. No
                    </th>
                    <th class="vendor two wide" fieldid="5">
                        PO No
                    </th>
                    <th class="description two wide" fieldid="6">
                        Description
                    </th>
                </tr>
            </thead>
            <tbody>
                {{#each recentItems}}
                    {{> abdnRecentItemRow}}
                {{/each}}
                {{#if noItemsToDisplay}}
                    <tr>
                        <td colspan="5">
                            No Recent Items To Display
                        </td>
                    </tr>
                {{/if}}
            </tbody>
        </table>
    {{else}}
        <div class="container">
            <div>Loading...</div>
        </div>
    {{/if}}
</template>

<template name="abdnRecentItemRow">
    <tr class="js-recent-item" data-item-id="{{_id}}">
        <td class="bold">
            {{receiptNo}}
        </td>
        <td>
            {{receivedDateFormatted}}
        </td>
        <td>
            {{receiptCategory}}
        </td>
        <td>
            {{vendor}}
        </td>
         <td>
            {{deliveryNo}}
        </td>
         <td>
            {{poNo}}
        </td>
        <td>
            {{description}}
        </td>
    </tr>
</template>
