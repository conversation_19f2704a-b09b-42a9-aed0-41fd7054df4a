import './abdn-recent-items-table.html';
import { DISPLAY_DATETIME_FORMAT_COMPACT } from '../../../../shared/lib/constants';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Items } from '../../../../api/items/items';
import { Publications } from '../../../../api/api.publications/publications';
import { ReactiveVar } from 'meteor/reactive-var';
import { Template } from 'meteor/templating';
import moment from 'moment';

const currentPage = () => 1;

const getItemsPerPage = () => 15;

const getRecentItemsCursor = (templateInstance) => {
  const itemsPerPage = templateInstance.recentItemsPageSize.get();
  const skipCount = (currentPage() - 1) * itemsPerPage;

  return Items.find({}, {
    sort: { receivedDate: -1 },
    limit: templateInstance.recentItemsNumberLoaded.get(),
    skip: skipCount,
  });
};

Template.abdnRecentItemsTable.onCreated(function onCreated() {
  const template = this;

  template.recentItemsPageSize = new ReactiveVar(getItemsPerPage());
  template.recentItemsNumberLoaded = new ReactiveVar(0);

  template.autorun(() => {
    const itemsPerPage = template.recentItemsPageSize.get();
    const skipCount = (currentPage() - 1) * itemsPerPage;
    template.subscription = template.subscribe(
      Publications.items.recentItems,
      {
        pageSize: itemsPerPage,
        skipCount,
        clientId: FlowRouter.getParam('clientId'),
      },
    );

    if (template.subscription.ready()) {
      template.recentItemsNumberLoaded.set(itemsPerPage);
    }
  });
});

Template.abdnRecentItemsTable.helpers({
  recentItems() {
    const template = Template.instance();

    return getRecentItemsCursor(template);
  },
  itemsSubscriptionReady() {
    return Template.instance().subscription.ready();
  },
  noItemsToDisplay() {
    const template = Template.instance();

    return getRecentItemsCursor(template).count() === 0;
  },
});

Template.abdnRecentItemRow.helpers({
  receivedDateFormatted() {
    const receivedDate = moment(Template.instance().data.receivedDate);
    return receivedDate.format(DISPLAY_DATETIME_FORMAT_COMPACT);
  },
});
