<template name="itemsContainedInCargoTable"> 
  {{#if itemsSubscriptionReady}}

   <table id="itemsContainedInCargoTable" class="ui cell striped teal table no-select break-word">
     <thead>
          <tr>
              <th class="itemRef" fieldid="0">Receipt No</th>
              <th class="quantity" fieldid="3">Qty.</th>
              <th class="weightKg" fieldid="4">Wt.(kg)</th>
              <th class="packageType" fieldid="5">Pkg.</th>
              <th class="description" fieldid="6">Description</th>
              <th class="" fieldid="7">Waste</th>
              <th class="" fieldid="8"></th>
              <th class="" style="width:200px">Receipted</th>
          </tr>
     </thead>
     <tbody>
			{{#each itemsContainedInSelectedCargo}}
				{{> itemInCargoRow}}
			{{/each}}
		</tbody>
    </table>

    {{else}}
        <div class="ui active text loader">Loading...</div>
    {{/if}}
</template>

<template name="itemInCargoRow">
  <tr class="js-contained-item {{rowClass}}" data-item-id="{{_id}}">
					<td class="bold" style="white-space: nowrap">{{receiptNo}}</td>
          <td>{{quantity}}</td>
					<td>{{weightKgFormatted}}</td>
					<td>{{pkgFormatted}}</td>
          <td class="break">{{materialDescriptionUnescaped}}</td>
          <td>
            {{isWasteFormatted}}
          </td>
          <td>
            {{#if isDangerous}}
              <i class="yellow exclamation triangle icon"></i>
            {{/if}}
          </td>
          <td class="right aligned" style="white-space: nowrap">
            {{#if isWaste}}
              {{#if isReceipted}}  
                <a class="fluid ui teal label" style="text-align: center">{{materialReceiptDateFormatted}}</a>
              {{else}}
                <button class="fluid mini ui primary button receive-button">Receive</button>
              {{/if}}
            {{/if}}
          </td>
  </tr>
</template>