import './items-contained-in-cargo-table.html';
import { $ } from 'meteor/jquery';
import { CompanySiteProfiles } from '../../../../api/company-site-profiles/company-site-profiles';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Items } from '../../../../api/items/items';
import { ReceiptTypes } from '../../../../api/items/receipt.types';
import { Cargo } from '../../../../api/cargo/cargo';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import { Template } from 'meteor/templating';
import { moment } from 'meteor/momentjs:moment';
import { _ } from 'meteor/underscore';

// Components used inside the template.
// None.

function getSiteProfile() {
  // Assumes only one loaded clients-side.
  return CompanySiteProfiles.findOne();
}

const currentPage = () => 1;

const getItemsPerPage = () => 100;

Template.itemsContainedInCargoTable.onCreated(function handleOnCreated() {
  const template = this;

  // 1. Initialization (See https://www.discovermeteor.com/blog/template-level-subscriptions/)
  const siteProfile = getSiteProfile();

  template.recentItemsPageSize = new ReactiveVar(getItemsPerPage());
  template.recentItemsNumberLoaded = new ReactiveVar(0);

  // 2. Autorun.
  // Will re-run if/when the "recentItemsPageSize" reactive variables changes
  template.autorun(function handleAutorun() {
    // get the limit
    const itemsPerPage = template.recentItemsPageSize.get();

    // Subscribe to the publication
    const skipCount = (currentPage() - 1) * itemsPerPage;

    let clientId = FlowRouter.getParam('clientId');
    let cargoItemId = FlowRouter.getParam('poId') || FlowRouter.getParam('ccuId');
    let itemId = FlowRouter.getParam('itemId') || '';

    let subscrParams = {
      cargoItemId: cargoItemId || 'NoItemSpecified',
      clientId,
    };

    template.subscription = template.subscribe(
      'items.itemsForCargoId',
      subscrParams);

    // If subscription is ready, set count of loaded items to itemsPerPage.
    if (template.subscription.ready()) {
      template.recentItemsNumberLoaded.set(itemsPerPage);
    }
  });
});

Template.itemsContainedInCargoTable.helpers({
  itemsContainedInSelectedCargo: function getItems() {
    const template = Template.instance();
    const itemsPerPage = template.recentItemsPageSize.get();
    const skipCount = (currentPage() - 1) * itemsPerPage;
    const cargoItemIdToFind = FlowRouter.getParam('poId') || FlowRouter.getParam('ccuId');
    // Get items from client-side collection (relying on publication to provide correct items.)
    let cargoItem = Cargo.findOne({ _id: cargoItemIdToFind, startWasteRemoval: true });
    if (cargoItem) {
      return Items.find(
        { cargoItemId: cargoItemIdToFind },
        {
          sort: { isWaste: -1, receiptNo: 1 },
          limit: template.recentItemsNumberLoaded.get(),
          skip: skipCount,
        },
      );
    }
  },
  itemsSubscriptionReady() {
    return Template.instance().subscription.ready();
  },
  selectedCcu() {
    const cargoItemId = FlowRouter.getParam('poId') || FlowRouter.getParam('ccuId');
    let cargoItem = Cargo.findOne({ _id: cargoItemId });
    if (cargoItem) {
      return cargoItem.identifier;
    } else {
      return '-';
    }
  },
});

Template.itemInCargoRow.helpers({
  receivedDateFormatted: function formatDate() {
    const receivedDate = moment(Template.instance().data.receivedDate);
    return receivedDate.format('DD-MMM-YY HH:mm');
  },
  materialReceiptDateFormatted: function formatDate() {
    const materialReceiptDateTime = Template.instance().data.materialReceiptDateTime;
    if (materialReceiptDateTime) {
      const receiptDate = moment(materialReceiptDateTime);
      return receiptDate.format('DD-MMM-YY HH:mm');
    }
    return ''; // receipt datetime not set.
  },
  receiptStageFormatted: function formatReceiptStage() {
    if (this.receiptType === ReceiptTypes.chemReceipt) {
      return 'Receipted';
    }
    if (this.receiptType === ReceiptTypes.chemPreReceipt) {
      return 'Not Removed';
    }
    return '-';
  },
  isWasteFormatted: function getIsWasteFormatted() {
    if (this.isWaste) {
      return 'Yes';
    }
    return 'No';
  },
  isDangerous() {
    const isDangerous = (typeof this.unNo !== 'undefined' && this.unNo.length > 0);
    return isDangerous;
  },
  isReceipted() {
    return (this.receiptType === ReceiptTypes.chemReceipt);
  },
  rowClass() {
    let cls = '';
    let materialItemIdFromUrl = FlowRouter.getParam('itemId') || '';
    if (this._id == materialItemIdFromUrl) {
      cls = 'active';
    }
    return cls;
  },
  weightKgFormatted() {
    const isReceipted = (this.receiptType === ReceiptTypes.chemReceipt);

    if (isReceipted) {
      return (this.weightKg) ? this.weightKg : '-';
    } else {
      return (this.ecargoMaterialInfo.grossQuantity)? this.ecargoMaterialInfo.grossQuantity : '-';
    }
  },
  pkgFormatted() {
    const isReceipted = (this.receiptType === ReceiptTypes.chemReceipt);

    if (isReceipted) {
      return (this.packageType) ? this.packageType : '-';
    } else {
      return (this.ecargoMaterialInfo.packingUnit)? this.ecargoMaterialInfo.packingUnit : '-';
    }
  },
  materialDescriptionUnescaped() {
    // Material descriptions can come html encoded from Flow e.g. "<<" as "&gt;&gt;"
    let retval = null;
    if (this.description) {
      retval = _.unescape(this.description)
        .replace(/&GT;/ig, '>')
        .replace(/&LT;/ig, '<')
        .replace(/&gt/ig, '>')
        .replace(/&lt/ig, '<');
    }
    return retval;
  },
});
