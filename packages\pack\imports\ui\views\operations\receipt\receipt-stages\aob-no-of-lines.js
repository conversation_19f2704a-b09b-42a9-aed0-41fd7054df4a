import './aob-no-of-lines.html';
import { AobNoOfLinesSchema } from '../../../../../api/items/receipt.schemas/aob-no-of-lines.schema';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { ReceiptEvents } from '../../../../services/receipt/receipt.event-emitter';
import { ReceiptStageService } from '../../../../services/receipt/receipt-stage.service';
import { ReceiptTypes } from '../../../../../api/items/receipt.types';
import { Template } from 'meteor/templating';

const clearForm = (templateInstance) => {
  templateInstance.$('[name=noOfLines]').val(1);
};

const updateReceipt = (templateInstance) => {
  const strToInt = (str) => {
    if (!str || !str.length) {
      return null;
    }

    const int = parseInt(str, 10);

    return int <= 0 ? 1 : int;
  };

  const receipt = {
    _id: FlowRouter.getParam('poId'),
    receiptType: ReceiptTypes.aobNoOfLines,
    noOfLines: strToInt(templateInstance.$('[name=noOfLines]').val()),
  };

  ReceiptStageService.updateReceipt(receipt, templateInstance);
};

Template.aobNoOfLines.onRendered(function onRendered() {
  const template = this;
  template.data.eventEmitter.showForm();

  template.validationContext = AobNoOfLinesSchema.namedContext('aobNoOfLinesForm');

  template.autorun(() => {
    const isValid = template.validationContext
      .validate(ReceiptStageService.receipt());

    template.data.isReceiptValid.set(isValid);
  });

  template.clearFormCallback = function clearFormCallback() {
    clearForm(template);
  };

  template.data.eventEmitter.onSubmit(template.clearFormCallback);

  updateReceipt(template);
});

Template.aobNoOfLines.helpers({
  purchaseOrder() {
    const po = Template.currentData().purchaseOrder;

    if (po) {
      return po.identifier;
    }

    return '';
  },
});

Template.aobNoOfLines.events({
  'input [name=noOfLines]': function onInput(event, templateInstance) {
    updateReceipt(templateInstance);
  },
});

Template.aobNoOfLines.onDestroyed(function onDestroyed() {
  const template = this;

  template.data.eventEmitter
    .removeListener(ReceiptEvents.submit, template.clearFormCallback);
});
