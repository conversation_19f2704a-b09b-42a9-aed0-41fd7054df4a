<template name="aobPreReceipt">
  <h3 class="ui header" style="margin-bottom: 0;">Pre-Receipt</h3>
  <hr class="receiptFormHeaderRule" />
  <div class="three fields">
    <div class="six wide field">
      <label>Received</label>
      <input type="text" readonly="" placeholder="Received At" name="receivedAt" value="{{receivedAt}}" />
    </div>
    <div class="six wide required field">
      <label>Receipt No</label>
      <input type="text" placeholder="Receipt No" name="receiptNo" value="{{receiptNo}}" {{receiptNoIsReadonly}} />
    </div>
    <div class="four wide field">
      <label>Set Manual Receipt No</label>
      <div class="ui toggle checkbox" style="padding-top:10px;" id="isManualReceiptNo">
        <input type="checkbox" name="isManualReceiptNo" tabindex="0" class="hidden" />
      </div>
    </div>
  </div>
  <div class="fields">
    <div class="six wide required field">
      <label>PO No</label>
      <input type="text" placeholder="PO No." name="poNo" />
    </div>
    <div class="six wide field">
      <label>No. of PO Lines Received</label>
      <input type="number" name="noOfPoLinesReceived" placeholder="No. of PO Lines Received" min="1"/>
    </div>
  </div>
  <div class="fields">
    <div class="six wide required field">
      <label>Vendor</label>
      <div class="receiptForm ui fluid selection search dropdown">
        <input type="hidden" name="vendor" value="">
        <i class="dropdown icon"></i>
        <i class="remove icon"></i>
        <div class="default text">Vendor</div>
        <div class="menu">
          {{#each vendors}}
          <div class="item" data-value="{{name}}" data-text="{{name}}">
            {{name}}
          </div>
          {{/each}}
        </div>
      </div>
    </div>
    <div class="six wide field">
      <label>Vendor Delivery No</label>
      <input type="text" placeholder="Vendor Delivery No." name="vendorDeliveryNo" />
    </div>
  </div>
  <div class="fields">
    <div class="twelve wide field">
      <label>Description</label>
      <input type="text" placeholder="Description..." name="description" />
    </div>
  </div>
</template>