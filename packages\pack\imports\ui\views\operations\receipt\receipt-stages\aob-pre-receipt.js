import './aob-pre-receipt.html';
import { AobPreReceiptSchema } from '../../../../../api/items/receipt.schemas/aob-pre-receipt.schema';
import { DISPLAY_DATETIME_FORMAT } from '../../../../../shared/lib/constants';
import { ReactiveVar } from 'meteor/reactive-var';
import { ReceiptEvents } from '../../../../services/receipt/receipt.event-emitter';
import { ReceiptNoService } from '../../../../services/receipt/receipt-no.service';
import { ReceiptStageService } from '../../../../services/receipt/receipt-stage.service';
import { ReceiptTypes } from
  '../../../../../api/items/receipt.types';
import { SiteProfileService } from
  '../../../../services/company-site-profiles/site-profile.service';
import { Template } from 'meteor/templating';
import { User } from '../../../../../api/api.helpers/user';
import moment from 'moment';

const elements = Object.freeze({
  receiptNo: '[name=receiptNo]',
  isManualReceiptNo: '#isManualReceiptNo',
  poNo: '[name=poNo]',
  noOfPoLinesReceived: '[name=noOfPoLinesReceived]',
  vendor: '[name=vendor]',
  vendorDeliveryNo: '[name=vendorDeliveryNo]',
  description: '[name=description]',
});

const updateReceipt = (templateInstance) => {
  const emptyStringToNull = (str) => {
    if (!str || !str.length) {
      return null;
    }

    return str;
  };

  const strToInt = (str) => {
    if (!str || !str.length) {
      return null;
    }

    const int = parseInt(str, 10);

    return int <= 0 ? 1 : int;
  };

  const preReceipt = {
    receiptNo: emptyStringToNull(templateInstance.$(elements.receiptNo).val()),
    receiptLocation: Session.get('receiptLocation'),
    receivedDate: templateInstance.receivedAt.get().toDate(),
    poNo: emptyStringToNull(templateInstance.$(elements.poNo).val()),
    noOfPoLinesReceived: strToInt(templateInstance.$(elements.noOfPoLinesReceived).val()),
    vendor: emptyStringToNull(templateInstance.$(elements.vendor).val()),
    vendorDeliveryNo: emptyStringToNull(templateInstance.$(elements.vendorDeliveryNo).val()),
    receiptType: ReceiptTypes.aobPreReceipt,
    siteId: User.activeSite(),
    client: SiteProfileService.currentClient(),
    description: emptyStringToNull(templateInstance.$(elements.description).val()),
  };

  ReceiptStageService.updateReceipt(preReceipt, templateInstance);
};

const clearForm = (templateInstance) => {
  templateInstance.$(elements.poNo).val('');
  templateInstance.$(elements.noOfPoLinesReceived).val('');
  templateInstance.$(elements.vendorDeliveryNo).val('');
  templateInstance.$(elements.description).val('');
  templateInstance.$('.dropdown').dropdown('clear');

  updateReceipt(templateInstance);
};

Template.aobPreReceipt.onCreated(function onCreated() {
  const template = this;

  template.automaticReceiptNo = new ReactiveVar(0);
  template.setManualReceiptNo = new ReactiveVar(false);
  template.receivedAt = new ReactiveVar(moment());

  template.autorun(() => {
    template.automaticReceiptNo.set(ReceiptNoService.currentAutomaticReceiptNo());
  });

  Meteor.setInterval(() => template.receivedAt.set(moment()), 20 * 100);
});

Template.aobPreReceipt.onRendered(function onRendered() {
  const template = this;

  template.data.eventEmitter.showForm();
  template.$(elements.isManualReceiptNo).checkbox({
    onChange() {
      const setManualReceiptNo = template.$(elements.isManualReceiptNo).checkbox('is checked');
      template.setManualReceiptNo.set(setManualReceiptNo);

      if (setManualReceiptNo) {
        template.$(elements.receiptNo).val('');
        Meteor.setTimeout(() => template.$(elements.receiptNo).focus(), 100);
      } else {
        template.$(elements.receiptNo).val(template.automaticReceiptNo.get());
      }
      updateReceipt(template);
    },
  });

  template.$('.dropdown').dropdown({
    onChange() {
      updateReceipt(template);
    },
  });

  template.validationContext = AobPreReceiptSchema.namedContext('aobPreReceiptForm');

  template.autorun(() => {
    const isReceiptValid = template.validationContext
      .validate(ReceiptStageService.receipt());

    template.data.isReceiptValid.set(isReceiptValid);
  });

  template.clearFormCallback = function clearFormCallback() {
    clearForm(template);
  };

  template.data.eventEmitter.onSubmit(template.clearFormCallback);
});

Template.aobPreReceipt.helpers({
  receivedAt() {
    return Template.instance().receivedAt.get().format(DISPLAY_DATETIME_FORMAT);
  },
  receiptNo() {
    const setManualReceiptNo = Template.instance().setManualReceiptNo.get();

    return setManualReceiptNo ? '' : Template.instance().automaticReceiptNo.get();
  },
  receiptNoIsReadonly() {
    const setManualReceiptNo = Template.instance().setManualReceiptNo.get();

    return setManualReceiptNo ? '' : 'readonly';
  },
  vendors() {
    return SiteProfileService.vendors();
  },
});

Template.aobPreReceipt.events({
  'input input': function onInput(event, templateInstance) {
    updateReceipt(templateInstance);
  },
});

Template.aobPreReceipt.onDestroyed(function onDestroyed() {
  const template = this;

  template.data.eventEmitter
    .removeListener(ReceiptEvents.submit, template.clearFormCallback);
});
