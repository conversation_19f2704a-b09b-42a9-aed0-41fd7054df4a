<template name="aobReceipt">
  <div class="aobReceiptForm">
    <div class="ui fluid grid">
      <div class="ten wide column">
          <h3 class="ui header">Receipt - PO {{purchaseOrder}}</h3>
      </div>
      <div class="six wide column" style="padding-top: 0.7rem;">
        <div class="ui right ribbon label">{{currentReceiptNo}}</div>
      </div>
    </div>

    <hr class="receiptFormHeaderRule" />
    <div class="three fields">
      <div class="ten wide field required">
        <label>Receipt Category</label>
        <div class="receiptForm ui fluid selection search dropdown" id="receiptCategoryDropdown">
          <input type="hidden" name="receiptCategory" value="">
          <i class="dropdown icon"></i>
          <i class="remove icon"></i>
          <div class="default text">Receipt Category</div>
          <div class="menu">
            {{#each receiptCategories}}
            <div class="item" data-value="{{name}}" data-text="{{name}}">
              {{name}}
            </div>
            {{/each}}
          </div>
        </div>
      </div>
      <div class="two wide field">
        <label>QA</label>
        <div class="ui toggle checkbox" style="padding-top:10px;">
          <input type="checkbox" name="isQa" tabindex="0" class="hidden" />
        </div>
      </div>
      <div class="two wide field">
        <label>Yard</label>
        <div class="ui toggle checkbox" style="padding-top:10px;">
          <input type="checkbox" name="isYard" tabindex="0" class="hidden" />
        </div>
      </div>
      <div class="two wide field">
        <label>Backload</label>
        <div class="ui toggle checkbox" style="padding-top:10px;" id="isBackloadCheckbox">
          <input type="checkbox" name="isBackload" tabindex="0" class="hidden" />
        </div>
      </div>
    </div>
    <div class="fields">
      <div class="eight wide field">
        <label>Offshore Location</label>
        <div class="receiptForm ui fluid search selection dropdown" id="offshoreLocationDropdown">
          <input type="hidden" name="offshoreLocation" value="">
          <i class="dropdown icon"></i>
          <i class="remove icon"></i>
          <div class="default text">Offshore Location</div>
          <div class="menu">
            {{#each offshoreLocations}}
            <div class="item" data-value="{{name}}" data-text="{{name}}">
              {{name}}
            </div>
            {{/each}}
          </div>
        </div>
      </div>
      <div class="eight wide field">
        <label>Offshore Location Storage Bin</label>
        <input type="text" placeholder="Storage Bin" name="offshoreLocationStorageBin" />
      </div>
    </div>
    <div class="fields">
      <div class="eight wide disabled field">
        <label>PO No.</label>
        <input type="text" placeholder="PO No." name="poNo" value="{{purchaseOrder}}" readonly="" />
      </div>
      <div class="eight wide field required">
        <label>PO Line No.</label>
        <input type="number" placeholder="PO Line No." name="poLineNo" />
      </div>
    </div>
    <div class="fields">
      <div class="eight wide required field">
        <label>SAP Delivery No.</label>
        <input type="text" placeholder="Delivery No." name="deliveryNo" />
      </div>
      <div class="eight wide field">
        <label>Customs Status</label>
        <div class="receiptForm ui fluid selection search dropdown">
          <input type="hidden" name="customsStatus" value="">
          <i class="dropdown icon"></i>
          <i class="remove icon"></i>
          <div class="default text">Customs Status</div>
          <div class="menu">
            {{#each customsStatuses}}
            <div class="item" data-value="{{name}}" data-text="{{name}}">
              {{name}}
            </div>
            {{/each}}
          </div>
        </div>
      </div>
    </div>
    <div class="fields">
      <div class="eight wide field">
        <label>Work Order No.</label>
        <input type="text" placeholder="Work Order No." name="workOrderNo" />
      </div>
      <div class="eight wide field">
        <label>Material No.</label>
        <input type="text" placeholder="Material No." name="materialNo" />
      </div>
    </div>
    <div class="three fields">
      <div class="eight wide field required">
        <label>Package Type</label>
        <div class="receiptForm ui fluid search selection dropdown" id="packageTypeDropdown">
          <input type="hidden" name="packageType" value="">
          <i class="dropdown icon"></i>
          <i class="remove icon"></i>
          <div class="default text">Package Type</div>
          <div class="menu">
            {{#each packageTypes}}
            <div class="item" data-value="{{name}}" data-text="{{name}}">
              {{name}}
            </div>
            {{/each}}
          </div>
        </div>
      </div>
      <div class="four wide field required">
        <label>Quantity</label>
        <input type="number" min="1" placeholder="Quantity" name="quantity" />
      </div>
      <div class="four wide field">
        <label>Weight</label>
        <input type="number" min="0.00" step="any" value="0.00" name="weight" />
      </div>
    </div>
    <div class="fields">
      <div class="sixteen wide field">
        <label>Description</label>
        <input type="text" placeholder="Description..." name="description" />
      </div>
    </div>
    <div class="fields">
      <div class="sixteen wide field">
        <label>NCRs</label>
        <div class="ui fluid multiple selection dropdown" id="ncrsDropdown">
          <input name="ncrs" type="hidden" value="">
          <i class="dropdown icon"></i>
          <div class="default text" style="margin-left: 0.642857em;">NCRs</div>
          <div class="menu">
            {{#each ncrs}}
            <div class="item" data-value="{{name}}" data-text="{{name}}">
              {{name}}
            </div>
            {{/each}}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>