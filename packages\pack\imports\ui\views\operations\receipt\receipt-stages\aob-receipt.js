import './aob-receipt.html';
import { AobReceiptSchema } from '../../../../../api/items/receipt.schemas/aob-receipt.schema';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Items } from '../../../../../api/items/items';
import { Publications } from '../../../../../api/api.publications/publications';
import { ReceiptEvents } from '../../../../services/receipt/receipt.event-emitter';
import { ReceiptStageService } from '../../../../services/receipt/receipt-stage.service';
import { ReceiptTypes } from
  '../../../../../api/items/receipt.types';
import { SiteProfileService } from
  '../../../../services/company-site-profiles/site-profile.service';
import { Template } from 'meteor/templating';

const elements = Object.freeze({
  receiptCategory: '[name=receiptCategory]',
  isQa: '[name=isQa]',
  isYard: '[name=isYard]',
  offshoreLocation: '[name=offshoreLocation]',
  offshoreLocationStorageBin: '[name=offshoreLocationStorageBin]',
  isBackload: '[name=isBackload]',
  poNo: '[name=poNo]',
  poLineNo: '[name=poLineNo]',
  deliveryNo: '[name=deliveryNo]',
  customsStatus: '[name=customsStatus]',
  workOrderNo: '[name=workOrderNo]',
  materialNo: '[name=materialNo]',
  packageType: '[name=packageType]',
  quantity: '[name=quantity]',
  weight: '[name=weight]',
  description: '[name=description]',
  ncrs: '[name=ncrs]',
});

const clearFormPartial = (templateInstance) => {
  templateInstance.$(elements.isQa).checkbox('set unchecked');
  templateInstance.$(elements.isYard).checkbox('set unchecked');
  templateInstance.$(elements.poLineNo).val('');
  templateInstance.$(elements.deliveryNo).val('');
  templateInstance.$(elements.materialNo).val('');
  templateInstance.$('#packageTypeDropdown').dropdown('clear');
  templateInstance.$(elements.quantity).val('');
  templateInstance.$(elements.weight).val('0.00');
  templateInstance.$(elements.description).val('');
  templateInstance.$('#ncrsDropdown').dropdown('clear');
  templateInstance.$(elements.offshoreLocationStorageBin).val('');
};

const clearFormFull = (templateInstance) => {
  clearFormPartial(templateInstance);
  templateInstance.$('.dropdown').dropdown('clear');
  templateInstance.$(elements.isBackload).checkbox('set unchecked');
  templateInstance.$(elements.workOrderNo).val('');
};

const updateReceipt = (templateInstance) => {
  const emptyStringToNull = (str) => {
    if (!str || !str.length) {
      return null;
    }

    return str;
  };

  const strToInt = (str) => {
    if (!str || !str.length) {
      return null;
    }

    const int = parseInt(str, 10);

    return int <= 0 ? 1 : int;
  };

  const strToFloat = (str) => {
    if (!str || !str.length) {
      return null;
    }

    return parseFloat(str);
  };

  const strToArray = (str) =>
    str && str.length ? str.split(',') : [];

  const receipt = {
    receiptNo: templateInstance.currentReceiptNo.get(),
    receiptCategory: emptyStringToNull(templateInstance.$(elements.receiptCategory).val()),
    offshoreLocation: emptyStringToNull(templateInstance.$(elements.offshoreLocation).val()),
    offshoreLocationStorageBin: emptyStringToNull(templateInstance.$(elements.offshoreLocationStorageBin).val()),
    isBackload: templateInstance.$(elements.isBackload).checkbox('is checked'),
    isQa: templateInstance.$(elements.isQa).checkbox('is checked'),
    isYard: templateInstance.$(elements.isYard).checkbox('is checked'),
    poNo: emptyStringToNull(templateInstance.$(elements.poNo).val()),
    poLineNo: strToInt(templateInstance.$(elements.poLineNo).val()),
    deliveryNo: emptyStringToNull(templateInstance.$(elements.deliveryNo).val()),
    customsStatus: emptyStringToNull(templateInstance.$(elements.customsStatus).val()),
    workOrderNo: emptyStringToNull(templateInstance.$(elements.workOrderNo).val()),
    materialNo: emptyStringToNull(templateInstance.$(elements.materialNo).val()),
    packageType: emptyStringToNull(templateInstance.$(elements.packageType).val()),
    quantity: strToInt(templateInstance.$(elements.quantity).val()),
    weightKg: strToFloat(templateInstance.$(elements.weight).val()),
    description: emptyStringToNull(templateInstance.$(elements.description).val()),
    ncrs: strToArray(templateInstance.$(elements.ncrs).val()),
    receiptType: ReceiptTypes.aobReceipt,
  };

  ReceiptStageService.updateReceipt(receipt, templateInstance);
};

Template.aobReceipt.onCreated(function onCreated() {
  const template = this;

  template.autorun(() => {
    const clientId = FlowRouter.getParam('clientId') || '';
    const poId = FlowRouter.getParam('poId') || '';

    template.subscribe(Publications.items.preReceiptForPoRequiringReceipting,
      { clientId, poId });
  });

  template.currentReceiptNo = new ReactiveVar(null);
});

Template.aobReceipt.onRendered(function onRendered() {
  const template = this;

  template.autorun(() => {
    const currentPreReceipt = Items.findOne();
    const currentReceiptNo = currentPreReceipt ? currentPreReceipt.receiptNo : null;

    template.currentReceiptNo.set(currentReceiptNo);

    updateReceipt(template);
  });

  template.$('.dropdown').dropdown({
    onChange() {
      updateReceipt(template);
    },
  });

  template.$('.checkbox').checkbox({
    onChange() {
      updateReceipt(template);
    },
  });

  template.autorun(() => {
    const po = Template.currentData().purchaseOrder;

    const shouldDimForm = po ? po.allLinesReceipted : true;

    if (shouldDimForm) {
      template.data.eventEmitter.dimForm();
    } else {
      template.data.eventEmitter.showForm();
    }
  });

  template.validationContext = AobReceiptSchema.namedContext('aobReceiptForm');

  template.autorun(() => {
    const isReceiptValid = template.validationContext
      .validate(ReceiptStageService.receipt());
    template.data.isReceiptValid.set(isReceiptValid);
  });

  template.clearFormPartialCallback = function clearFormPartialCallback() {
    clearFormPartial(template);
  };

  template.clearFormFullCallback = function clearFormFullCallback() {
    clearFormFull(template);
  };

  template.data.eventEmitter
    .onSubmit(template.clearFormFullCallback)
    .onSubmitAndNext(template.clearFormPartialCallback);
});

Template.aobReceipt.helpers({
  receiptCategories() {
    return SiteProfileService.receiptCategories();
  },
  offshoreLocations() {
    return SiteProfileService.offshoreLocations();
  },
  packageTypes() {
    return SiteProfileService.packageTypes();
  },
  customsStatuses() {
    return SiteProfileService.customsStatuses();
  },
  ncrs() {
    return SiteProfileService.ncrs();
  },
  purchaseOrder() {
    const po = Template.currentData().purchaseOrder;

    if (po) {
      return po.identifier;
    }

    return '';
  },
  allPoLinesReceipted() {
    const po = Template.currentData().purchaseOrder;

    if (po) {
      return po.allLinesReceipted;
    }

    return true;
  },
  currentReceiptNo() {
    const item = Items.findOne();

    if (item) {
      return item.receiptNo;
    }

    return '';
  },
});

Template.aobReceipt.events({
  'input input': function onInput(event, templateInstance) {
    updateReceipt(templateInstance);
  },
});

Template.aobReceipt.onDestroyed(function onDestroyed() {
  const template = this;

  template.data.eventEmitter
    .removeListener(ReceiptEvents.submit, template.clearFormFullCallback)
    .removeListener(ReceiptEvents.submitAndNext, template.clearFormPartialCallback);
});
