<template name="vchemPreReceipt">
  <div class="header">
    <h3 class="ui header" style="margin-bottom: 0;">CHEM Pre-Receipt *** TESTING ONLY ***</h3>
  </div>

  <div class="content">
    <form class="ui form" style="z-index: 1;">
      <div class="three fields">
        <div class="six wide field">
          <label>Received</label>
          <input type="text" readonly="" placeholder="Received At" name="receivedAt" value="{{receivedAt}}" />
        </div>
        <div class="six wide required field">
          <label>Receipt No</label>
          <input type="text" placeholder="Receipt No" name="receiptNo" value="{{receiptNo}}" {{receiptNoIsReadonly}} />
        </div>
        <div class="four wide field">
          <label>Set Manual Receipt No</label>
          <div class="ui toggle checkbox" style="padding-top:10px;" id="isManualReceiptNo">
            <input type="checkbox" name="isManualReceiptNo" tabindex="0" class="hidden" />
          </div>
        </div>
      </div>
      <div class="fields">
        <div class="six wide required field">
          <label>CCU</label>
          <input type="text" placeholder="CCU No." name="ccuNo" value="{{testRandomCcuNumber}}"/>
        </div>
        <div class="six wide field">
          <label>No. of Material Lines Received</label>
          <input type="number" name="noOfMaterialLinesReceived" placeholder="No. of Material Lines Received" min="1" value="3"/>
        </div>
      </div>
      <div class="fields">
        <div class="twelve wide field">
          <label>Description</label>
          <input type="text" placeholder="Description..." name="description" value="MINI CONTAINER" />
        </div>
      </div>
      <div class="fields">
        <div class="six wide required field">
          <label>Client</label>
          <div class="receiptForm ui fluid selection search dropdown">
            <input type="hidden" name="offshoreClient" value="SHELL">
            <i class="dropdown icon"></i>
            <i class="remove icon"></i>
            <div class="default text">Client</div>
            <div class="menu">
              {{#each offshoreClients}}
              <div class="item" data-value="{{name}}" data-text="{{name}}">
                {{name}}
              </div>
              {{/each}}
            </div>
          </div>
        </div>
        <div class="six wide field">
          <label>Offshore Location</label>
          <input type="text" placeholder="Offshore Location." name="offshoreLocation" value="GALLEON PG"/>
        </div>
      </div>
      <div class="fields">
        <div class="six wide required field">
          <label>VoyageNo</label>
          <input type="text" placeholder="Voyage No." name="voyageNo" value="VygNo112233"/>
        </div>
        <div class="six wide field">
          <label>CCU Manifest No.</label>
          <input type="text" name="manifestNo" placeholder="Manifest No" value="ManNo112233"/>
        </div>
      </div>
    </form>
  </div>
</template>