import './chem-pre-receipt.html';
import { ChemPreReceiptSchema } from '../../../../../api/items/receipt.schemas/chem-pre-receipt.schema';
import { DISPLAY_DATETIME_FORMAT } from '../../../../../shared/lib/constants';
import { ReactiveVar } from 'meteor/reactive-var';
import { ReceiptEvents } from '../../../../services/receipt/receipt.event-emitter';
import { ReceiptNoService } from '../../../../services/receipt/receipt-no.service';
import { ReceiptStageService } from '../../../../services/receipt/receipt-stage.service';
import { ReceiptTypes } from
  '../../../../../api/items/receipt.types';
import { SiteProfileService } from
  '../../../../services/company-site-profiles/site-profile.service';
import { Template } from 'meteor/templating';
import { User } from '../../../../../api/api.helpers/user';
import moment from 'moment';

const elements = Object.freeze({
  receiptNo: '[name=receiptNo]',
  isManualReceiptNo: '#isManualReceiptNo',
  description: '[name=description]',
  noOfMaterialLinesReceived: '[name=noOfMaterialLinesReceived]',
  offshoreClient: '[name=offshoreClient]',
  ccuNo: '[name=ccuNo]',
  offshoreLocation: '[name=offshoreLocation]',
  voyageNo: '[name=voyageNo]',
  manifestNo: '[name=manifestNo]',
});

const updateReceipt = (templateInstance) => {
  const emptyStringToNull = (str) => {
    if (!str || !str.length) {
      return null;
    }

    return str;
  };

  const strToInt = (str) => {
    if (!str || !str.length) {
      return null;
    }

    const int = parseInt(str, 10);

    return int <= 0 ? 1 : int;
  };

  const receiptLocn = Session.get('receiptLocation');
  console.log(`chem-pre-receipt.js updateReceipt() currentReceiptLocation is ${receiptLocn}.`)

  const preReceipt = {
    receiptNo: emptyStringToNull(templateInstance.$(elements.receiptNo).val()),
    receiptLocation: receiptLocn,
    receivedDate: templateInstance.receivedAt.get().toDate(),
    receiptType: ReceiptTypes.chemPreReceipt,
    siteId: User.activeSite(),
    client: SiteProfileService.currentClient(),
    description: emptyStringToNull(templateInstance.$(elements.description).val()),
    offshoreClient: emptyStringToNull(templateInstance.$(elements.offshoreClient).val()),
    ccu: emptyStringToNull(templateInstance.$(elements.ccuNo).val()),
    offshoreLocation: emptyStringToNull(templateInstance.$(elements.offshoreLocation).val()),
    voyageNo: emptyStringToNull(templateInstance.$(elements.voyageNo).val()),
    manifestNo: emptyStringToNull(templateInstance.$(elements.manifestNo).val()),
    ecargoCargoLine: ecargoCargoData,
    externalCargoLineId: Date.now().toString(), // Generating unique string for this test data.
  };

  console.log(`preReceiptObject: ${JSON.stringify(preReceipt, null, 3)}`);

  // This records the item being receipted.
  ReceiptStageService.updateReceipt(preReceipt, templateInstance);
};

const clearForm = (templateInstance) => {
  templateInstance.$(elements.description).val('');
  templateInstance.$('.dropdown').dropdown('clear');

  templateInstance.$(elements.ccuNo).val('');
  templateInstance.$(elements.noOfMaterialLinesReceived).val('');
  templateInstance.$(elements.offshoreLocation).val('');
  templateInstance.$(elements.voyageNo).val('');
  templateInstance.$(elements.manifestNo).val('');

  updateReceipt(templateInstance);
};

// *** ON-CREATED ***
Template.vchemPreReceipt.onCreated(function onCreated() {
  const template = this;

  template.automaticReceiptNo = new ReactiveVar(0);
  template.setManualReceiptNo = new ReactiveVar(false);
  template.receivedAt = new ReactiveVar(moment());

  template.autorun(() => {
    template.automaticReceiptNo.set(ReceiptNoService.currentAutomaticReceiptNo());
  });

  Meteor.setInterval(() => template.receivedAt.set(moment()), 20 * 100);
});

// *** ON-RENDERED ***
Template.vchemPreReceipt.onRendered(function onRendered() {
  const template = this;

  template.data.eventEmitter.showForm();
  template.$(elements.isManualReceiptNo).checkbox({
    onChange() {
      const setManualReceiptNo = template.$(elements.isManualReceiptNo).checkbox('is checked');
      template.setManualReceiptNo.set(setManualReceiptNo);

      if (setManualReceiptNo) {
        template.$(elements.receiptNo).val('');
        Meteor.setTimeout(() => template.$(elements.receiptNo).focus(), 100);
      } else {
        template.$(elements.receiptNo).val(template.automaticReceiptNo.get());
      }
      updateReceipt(template);
    },
  });

  template.$('.dropdown').dropdown({
    onChange() {
      updateReceipt(template);
    },
  });

  template.validationContext = ChemPreReceiptSchema.namedContext('chemPreReceiptForm');

  template.autorun(() => {
    const isReceiptValid = template.validationContext
      .validate(ReceiptStageService.receipt());

    template.data.isReceiptValid.set(true); // TODO: Force always valid to allow submit.
  });

  template.clearFormCallback = function clearFormCallback() {
    clearForm(template);
  };

  template.data.eventEmitter.onSubmit(template.clearFormCallback);

  // On rendered - Set the receipt item based on initial settings.
  console.log('chem-pre-receipt.js Setting up receipt cargo item on initial render.');
  updateReceipt(template);
});

// *** HELPERS ***
Template.vchemPreReceipt.helpers({
  receivedAt() {
    return Template.instance().receivedAt.get().format(DISPLAY_DATETIME_FORMAT);
  },
  receiptNo() {
    const setManualReceiptNo = Template.instance().setManualReceiptNo.get();

    return setManualReceiptNo ? '' : Template.instance().automaticReceiptNo.get();
  },
  receiptNoIsReadonly() {
    const setManualReceiptNo = Template.instance().setManualReceiptNo.get();

    return setManualReceiptNo ? '' : 'readonly';
  },
  offshoreClients() {
    return [{ name: 'SHELL' }, { name: 'MAERSK' }];
  },
  testRandomCcuNumber() {
    return 'AMD'+ (1000 + Math.floor(Math.random() * 8999));
  },
});

// *** EVENTS ***
Template.vchemPreReceipt.events({
  'input input': function onInput(event, templateInstance) {
    updateReceipt(templateInstance);
  },
});

// *** ON-DESTROYED ***
Template.vchemPreReceipt.onDestroyed(function onDestroyed() {
  const template = this;

  template.data.eventEmitter
    .removeListener(ReceiptEvents.submit, template.clearFormCallback);
});

const ecargoCargoData = JSON.parse(`

{
  "id": "5a7c138a10aa2fbb5c1799eb",
  "cargoGroupId": "5a7c135f10aa2fbb5c1771c8",
  "cargoLineId": "5a7c138a10aa2fbb5c1799eb",
  "timestamp": "2018-02-08T09:10:01.9347904",
  "eventType": "Planned",
  "quantity": 1,
  "identifier": "AMA124",
  "description": "Container - 5ft",
  "fromLocationId": null,
  "fromLocationName": "MILIEUPLAAT",
  "fromDistrict": null,
  "toLocationId": null,
  "toLocationName": "K13A",
  "toDistrict": null,
  "clientId": null,
  "clientName": "WINTERSHALL",
  "expectedTime": null,
  "isPriority": false,
  "comments": null,
  "trailerNo": null,
  "vessel": "Bourbon Rainbow",
  "offshoreInstallationName": "K13A",
  "offshoreInstallationId": "161",
  "orderNo": null,
  "isARedirect": null,
  "plannedDateTime": "2018-02-09T00:00:00",
  "allocatedDateTime": null,
  "collectedDateTime": null,
  "deliveredDateTime": null,
  "vehicleId": null,
  "vehicleRegNo": null,
  "trailerId": null,
  "trailerRegNo": null,
  "driverId": null,
  "driverName": null,
  "isAllocatedToSubcontractor": false,
  "subcontractorName": null,
  "expectedDateTime": null,
  "normalisedId": null,
  "fromLocationAddress": null,
  "fromLocationAliases": null,
  "fromLocationDetectedType": null,
  "fromLocationNormalisedId": null,
  "toLocationAddress": null,
  "toLocationAliases": null,
  "toLocationDetectedType": null,
  "toLocationNormalisedId": "petersonchemicalsdnhr",
  "clientNormalisedId": null,
  "clientAddress": null,
  "clientAliases": null,
  "voyageNo": null,
  "dischargeTimestamp": null,
  "manifestNo": null,
  "weight": null,
  "vendorName": "MILIEUPLAAT",
  "masterVendorId": null,
  "direction": "inbound",
  "isCancelled": false,
  "isRob": false,
  "isNob": false,
  "isBoatSkip": false,
  "materials": [
    {
      "lineId": "5a7c13d910aa2fbb5c17dafb",
      "lineNumber": 0,
      "sourceSystemId": null,
      "destination": "K13A",
      "customer": "WINTERSHALL",
      "cargoLineId": "5a7c138a10aa2fbb5c1799eb",
      "bulkLineId": null,
      "description": "Drums",
      "masterVendorId": "MILIEUPLAAT",
      "serialNo": null,
      "poNo": null,
      "euralcode": "070103",
      "customsStatus": null,
      "customsDeclarationType": null,
      "customsApprovedToLoad": false,
      "documentNumber": null,
      "value": null,
      "manifestNo": null,
      "properShippingName": null,
      "packingGroup": "III",
      "imoCode": "IMOCODE",
      "unNo": "UN 0004",
      "imoHazardClass": "8",
      "imoSubclass": "2.1",
      "packingUnit": "3",
      "comments": "Test material 1",
      "quantity": 2.56,
      "grossQuantity": 1,
      "grossUnitOfMeasurement": null,
      "netQuantity": 0,
      "netUnitOfMeasurement": null,
      "limitedQuantity": false,
      "marinePollutant": false,
      "status": null,
      "category": null,
      "isWaste": true,
      "wasteDescription": "Drums",
      "warehouseStatus": null,
      "requester": null,
      "warehouseLocation": null,
      "deliveryLocation": null,
      "collectDate": null,
      "collectTime": null,
      "collectPhone": null,
      "collectTransportType": null,
      "poTransport": null,
      "submitted": true,
      "highPriority": false,
      "state": "Active",
      "customsTransportStatus": "Released",
      "customsTransportStatusIsOverride": false,
      "customsDocumentNumber": null,
      "customsDocumentsRequired": false,
      "commodityCode": null,
      "coo": null,
      "jobCardNumber": null,
      "workPackNumber": null,
      "mivMmt": null,
      "currency": null
    },
    {
      "lineId": "6b8c13d910aa2fbb5c17dbbd",
      "lineNumber": 0,
      "sourceSystemId": null,
      "destination": "K13A",
      "customer": "WINTERSHALL",
      "cargoLineId": "5a7c138a10aa2fbb5c1799eb",
      "bulkLineId": null,
      "description": "Pallets",
      "masterVendorId": "MILIEUPLAAT",
      "serialNo": null,
      "poNo": null,
      "euralcode": "070103",
      "customsStatus": null,
      "customsDeclarationType": null,
      "customsApprovedToLoad": false,
      "documentNumber": null,
      "value": null,
      "manifestNo": null,
      "properShippingName": null,
      "packingGroup": "III",
      "imoCode": "IMOCODE",
      "unNo": "UN 0004",
      "imoHazardClass": "8",
      "imoSubclass": "2.1",
      "packingUnit": "2",
      "comments": "Test material 2",
      "quantity": 4.12,
      "grossQuantity": 1,
      "grossUnitOfMeasurement": null,
      "netQuantity": 0,
      "netUnitOfMeasurement": null,
      "limitedQuantity": false,
      "marinePollutant": false,
      "status": null,
      "category": null,
      "isWaste": false,
      "wasteDescription": "Boxes",
      "warehouseStatus": null,
      "requester": null,
      "warehouseLocation": null,
      "deliveryLocation": null,
      "collectDate": null,
      "collectTime": null,
      "collectPhone": null,
      "collectTransportType": null,
      "poTransport": null,
      "submitted": true,
      "highPriority": false,
      "state": "Active",
      "customsTransportStatus": "Released",
      "customsTransportStatusIsOverride": false,
      "customsDocumentNumber": null,
      "customsDocumentsRequired": false,
      "commodityCode": null,
      "coo": null,
      "jobCardNumber": null,
      "workPackNumber": null,
      "mivMmt": null,
      "currency": null
    },
    {
      "lineId": "6b8c13d910aa2fbb5c17dcde",
      "lineNumber": 0,
      "sourceSystemId": null,
      "destination": "K13A",
      "customer": "WINTERSHALL",
      "cargoLineId": "5a7c138a10aa2fbb5c1799eb",
      "bulkLineId": null,
      "description": "Boxes",
      "masterVendorId": "MILIEUPLAAT",
      "serialNo": null,
      "poNo": null,
      "euralcode": "070103",
      "customsStatus": null,
      "customsDeclarationType": null,
      "customsApprovedToLoad": false,
      "documentNumber": null,
      "value": null,
      "manifestNo": null,
      "properShippingName": null,
      "packingGroup": "III",
      "imoCode": "IMOCODE",
      "unNo": "UN 0004",
      "imoHazardClass": "8",
      "imoSubclass": "2.1",
      "packingUnit": "2",
      "comments": "Test material 3",
      "quantity": 6,
      "grossQuantity": 1,
      "grossUnitOfMeasurement": null,
      "netQuantity": 0,
      "netUnitOfMeasurement": null,
      "limitedQuantity": false,
      "marinePollutant": false,
      "status": null,
      "category": null,
      "isWaste": true,
      "wasteDescription": "Boxes",
      "warehouseStatus": null,
      "requester": null,
      "warehouseLocation": null,
      "deliveryLocation": null,
      "collectDate": null,
      "collectTime": null,
      "collectPhone": null,
      "collectTransportType": null,
      "poTransport": null,
      "submitted": true,
      "highPriority": false,
      "state": "Active",
      "customsTransportStatus": "Released",
      "customsTransportStatusIsOverride": false,
      "customsDocumentNumber": null,
      "customsDocumentsRequired": false,
      "commodityCode": null,
      "coo": null,
      "jobCardNumber": null,
      "workPackNumber": null,
      "mivMmt": null,
      "currency": null
    },
    {
      "lineId": "6b8c13d910aa2fbb5c17df12",
      "lineNumber": 0,
      "sourceSystemId": null,
      "destination": "K13A",
      "customer": "WINTERSHALL",
      "cargoLineId": "5a7c138a10aa2fbb5c1799eb",
      "bulkLineId": null,
      "description": "Bags",
      "masterVendorId": "MILIEUPLAAT",
      "serialNo": null,
      "poNo": null,
      "euralcode": "070103",
      "customsStatus": null,
      "customsDeclarationType": null,
      "customsApprovedToLoad": false,
      "documentNumber": null,
      "value": null,
      "manifestNo": null,
      "properShippingName": null,
      "packingGroup": "III",
      "imoCode": "IMOCODE",
      "unNo": "UN 0004",
      "imoHazardClass": "8",
      "imoSubclass": "2.1",
      "packingUnit": "2",
      "comments": "Test material 4",
      "quantity": 8,
      "grossQuantity": 1,
      "grossUnitOfMeasurement": null,
      "netQuantity": 0,
      "netUnitOfMeasurement": null,
      "limitedQuantity": false,
      "marinePollutant": false,
      "status": null,
      "category": null,
      "isWaste": true,
      "wasteDescription": "Boxes",
      "warehouseStatus": null,
      "requester": null,
      "warehouseLocation": null,
      "deliveryLocation": null,
      "collectDate": null,
      "collectTime": null,
      "collectPhone": null,
      "collectTransportType": null,
      "poTransport": null,
      "submitted": true,
      "highPriority": false,
      "state": "Active",
      "customsTransportStatus": "Released",
      "customsTransportStatusIsOverride": false,
      "customsDocumentNumber": null,
      "customsDocumentsRequired": false,
      "commodityCode": null,
      "coo": null,
      "jobCardNumber": null,
      "workPackNumber": null,
      "mivMmt": null,
      "currency": null
    }
  ]
}
`);
