/*
Will need to be updated if corresponding
dropdown dynamic max-height css is changed
*/
@media screen and (max-width : 767px) {
  /*item line height = 2.57em
  Search filter height = 5em*/
    #packageTypeParentMenu {
        height: calc(2.57rem * 4 + 5rem);
    }
}
@media screen and (min-width: 768px) {
    #packageTypeParentMenu {
        height: calc(2.57rem * 6 + 5rem);
    }
}
@media screen and (min-width: 992px) {
    #packageTypeParentMenu {
        height: calc(2.57rem * 8 + 5rem);
    }
}
@media screen and (min-width: 1920px) {
    #packageTypeParentMenu {
        height: calc(2.57rem * 8 + 5rem);
  }
}