<template name="vchemReceipt">
  <div class="header">
      <h3 class="ui header"><a class="large ui blue label"> {{currentReceiptNo}} </a> {{currentMaterialDisplayText}}</h3>
  </div>

  <div class="content">
    <form class="ui form" style="z-index: 1;">
      <div class="chemReceiptForm">

        <div class="ui grid">
          <div class="{{#if portraitMode}}one{{else}}two{{/if}} column row">
            
            <div class="column">
              <div class="fields">
                <div class="eight wide field">
                  <label>Operator</label>
                  <input type="text" placeholder="Operator" name="operator" readonly=""/>
                </div>
                <div class="eight wide field">
                  <label>Offshore Location</label>
                  <input type="text" placeholder="OffshoreLocation" name="offshoreLocation" readonly=""/>
                </div>
              </div>

              <div class="fields">
                <div class="eight wide field">
                  <label>CCU</label>
                  <input type="text" placeholder="CCU" name="ccu" readonly=""/>
                </div>
                <div class="eight wide field">
                    <label>Voyage No.</label>
                    <input type="text" placeholder="VoyageNo." name="voyageNo"  readonly="" />
                </div>
              </div>

              <div class="fields">
                <div class="eight wide field">
                  <label>CCU Manifest No.</label>
                  <input type="text" placeholder="CCU Manifest No." name="ccuManifestNo" readonly="" />
                </div>
                <div class="eight wide field">
                  <label>Material Manifest No.</label>
                  <input type="text" placeholder="Material Manifest No." name="materialManifestNo" readonly="" />
                </div>
              </div>
            
              <div class="fields">
                <div class="sixteen wide field">
                  <label>Waste Description</label>
                  <input type="text" placeholder="Waste Description" name="wasteDescription" readonly=""/>
                </div>
              </div>  

              <div class="fields">
                <div class="sixteen wide field">
                  <div class="ui checkbox">
                      {{#if isMarinePollutant}}
                      <input type="checkbox" readonly="" disabled="disabled" id="isMarinePollutantCheckbox" name="isMarinePollutantCheckbox" checked="checked">
                      {{else}}
                      <input type="checkbox" readonly="" disabled="disabled" id="isMarinePollutantCheckbox" name="iisMarinePollutantCheckbox">
                      {{/if}}
                      <label>Marine Pollutant</label>
                  </div>
                </div>
              </div>
            
              <div class="ui grid">
                <div class="two column row">
                  <div class="column" style="margin-bottom:1em;">
                    <div class="field">
                      <label>EURAL/EWC Code</label>
                      <input type="text" placeholder="Operator" name="euralEwcCode" readonly=""/>
                    </div>
                  </div>
                  <div class="column" style="margin-bottom:1em;">
                    <div class="field">
                      <label>UN No.</label>
                      <input type="text" placeholder="UnNo" name="unNo" readonly=""/>
                    </div>
                  </div>
                  <div class="column" style="margin-bottom:1em;">
                    <div class="field">
                      <label>IMO Hazard Class</label>
                      <input type="text" placeholder="HazardClass" name="hazardClass" readonly=""/>
                    </div>
                  </div>
                  <div class="column" style="margin-bottom:1em;">
                    <div class="field">
                      <label>IMO Hazard Subclass</label>
                      <input type="text" placeholder="SubClass" name="hazardSubclass" readonly=""/>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- User input side -->
            <div class="column">
          
              <div class="fields">
                <div class="sixteen wide field">
                  <label>Material Description</label>
                  <input type="text" placeholder="Description..." name="description" />
                </div>
              </div>
          
          
              <div class="fields">
                <div class="sixteen wide field">
                  <label>Material NCRs</label>
                  <div class="ui fluid multiple selection dropdown" id="ncrsDropdown">
                    <input name="ncrs" type="hidden" value="">
                    <i class="dropdown icon"></i>
                    <div class="default text" style="margin-left: 0.642857em;">NCRs</div>
                    <div class="menu">
                      {{#each ncrs}}
                      <div class="item" data-value="{{name}}" data-text="{{name}}">
                        {{name}}
                      </div>
                      {{/each}}
                    </div>
                  </div>
                </div>
              </div>

              <div class="two fields">

                <div class="eight wide field required">
                  <label>Quantity</label>
                  <input type="number" min="1" placeholder="Quantity" name="quantity" />
                </div>
          
          
                <div class="eight wide field">
                  <label>Treat As Single Item</label>
                  <div class="ui toggle checkbox" style="padding-top:10px;" id="isMultiQtyItemCheckbox">
                    <input type="checkbox" name="isMultiQtyItem" tabindex="0" class="" />
                  </div>
                </div>
          
              </div>
              
              {{#each singleQuantityItem in singleQuantityItems}}            
                {{> singleQtyItemTemplate 
                     singleQuantityItem=singleQuantityItem 
                     packageTypes=packageTypes 
                     numItems=singleQuantityItems.length }}
              {{/each}}

            </div>

          </div>
        </div>
      </div>
    </form>
  </div>
  
</template>


<template name="singleQtyItemTemplate">

    <div class="ui divider" style="background-color:#00b5ad"></div>

    <div class="two fields">
        <div class="eight wide field">
          <label>Item Receipt No ({{singleQuantityItem.itemIndex}} of {{numItems}})</label>
          <input type="text" min="1" placeholder="RefNo" name="refNo{{singleQuantityItem.itemIndex}}" value="{{singleQuantityItem.itemReceiptNo}}" readonly=""/>
        </div>
        <div class="eight wide field">
          <label>Weight (kg)</label>
          <input class="material-item-weight" type="number" min="0.00" step="any" value="" name="weight{{singleQuantityItem.itemIndex}}" id="{{singleQuantityItem.itemReceiptNo}}"/>
        </div>
    </div>

    <div class="sixteen wide field required">
        <label>Package Type ({{singleQuantityItem.itemIndex}} of {{numItems}}) </label>
        <div class="ui fluid dropdown basic button no-focus receiptForm packagetype-dropdown " id="packageTypeDropdown{{singleQuantityItem.itemIndex}}">
          <input class="material-item-package-type" name="packageType{{singleQuantityItem.itemIndex}}" type="hidden" value="" data-item-receipt-no="{{singleQuantityItem.itemReceiptNo}}">
          <i class="dropdown icon"></i>
          <div class="default text" >Package Type</div>
          <div id="packageTypeParentMenu" class="menu">
            <div class="ui icon search input" style="width:auto">
                <i class="search icon"></i>
                <input type="text" placeholder="{{singleQuantityItem.placeholderText }}" autocomplete="off" readonly>
            </div>
            <div class="scrolling menu" >
              {{#each packageTypes}}
              <div class="item" data-value="{{name}}" data-text="{{name}}">
                {{name}}
              </div>
              {{/each}}
            </div>
          </div>
        </div>
      </div>
      
</template>