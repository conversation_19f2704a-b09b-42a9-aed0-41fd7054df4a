import './chem-receipt.html';
import './chem-receipt.css';
import { ChemReceiptFromClientSchema } from '../../../../../api/items/receipt.schemas/chem-receipt.schema';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Items } from '../../../../../api/items/items';
import { Publications } from '../../../../../api/api.publications/publications';
import { ReceiptEvents } from '../../../../services/receipt/receipt.event-emitter';
import { ReceiptStageService } from '../../../../services/receipt/receipt-stage.service';
import { ReceiptTypes } from
  '../../../../../api/items/receipt.types';
import { SiteProfileService } from
  '../../../../services/company-site-profiles/site-profile.service';
import { Template } from 'meteor/templating';
import { _ } from 'meteor/underscore';

const elements = Object.freeze({
  offshoreLocation: '[name=offshoreLocation]',
  quantity: '[name=quantity]',
  weight: '[name=weight]',
  weightInputs: '.material-item-weight',
  packageTypeInputs: '.material-item-package-type',
  packageTypeDropdowns: '.packagetype-dropdown',
  description: '[name=description]',
  ncrs: '[name=ncrs]',
  ccu: '[name=ccu]',
  voyageNo: '[name=voyageNo]',
  ccuManifestNo: '[name=ccuManifestNo]',
  offshoreClient: '[name=operator]',
  euralEwcCode: '[name=euralEwcCode]',
  unNo: '[name=unNo]',
  hazardClass: '[name=hazardClass]',
  hazardSubclass: '[name=hazardSubclass]',
  isMultiQtyItem: '[name=isMultiQtyItem]',
  materialManifestNo: '[name=materialManifestNo]',
  wasteDescription: '[name=wasteDescription]',
});

const clearFormPartial = (templateInstance) => {
  templateInstance.$(elements.isQa).checkbox('set unchecked');
  templateInstance.$(elements.isYard).checkbox('set unchecked');
  templateInstance.$(elements.poLineNo).val('');
  templateInstance.$(elements.deliveryNo).val('');
  templateInstance.$(elements.materialNo).val('');
  templateInstance.$(elements.packageTypeDropdowns).dropdown('clear');
  templateInstance.$(elements.quantity).val('');
  templateInstance.$(elements.weight).val('0.00');
  templateInstance.$(elements.weightInputs).val('');
  templateInstance.$(elements.description).val('');
  templateInstance.$('#ncrsDropdown').dropdown('clear');
  templateInstance.$(elements.offshoreLocationStorageBin).val('');
};

const clearFormFull = (templateInstance) => {
  clearFormPartial(templateInstance);
  templateInstance.$('.dropdown').dropdown('clear');
  templateInstance.$(elements.isBackload).checkbox('set unchecked');
  templateInstance.$(elements.workOrderNo).val('');
};

const strToFloat = (str) => {
  if (!str || !str.length) {
    return null;
  }
  return parseFloat(str);
};

const updateReceipt = (templateInstance, forceItemReceiptNosAndWeights = null) => {
  const emptyStringToNull = (str) => {
    if (!str || !str.length) {
      return null;
    }
    return str;
  };

  const strToInt = (str) => {
    if (!str || !str.length) {
      return null;
    }
    const int = parseInt(str, 10);
    return int <= 0 ? 1 : int;
  };

  const strToArray = (str) => (str && str.length ? str.split(',') : []);

  const selectedItemId = FlowRouter.getParam('itemId') || '';

  const defaultReceiptCategory = SiteProfileService.receiptCategories()[0].name; // Standard Receipt

  // Get weights and ReceiptNos for each individual material item in this material line.
  let itemReceiptNosAndWeights = [];
  let itemReceiptNosAndPkgTypes = [];

  if (forceItemReceiptNosAndWeights) { // Get initial values following change in qty.
    itemReceiptNosAndWeights = forceItemReceiptNosAndWeights;
  } else { // Read from dom elements.
    const weightInputs = templateInstance.$(elements.weightInputs);
    weightInputs.each(function (index, weightInput) {
      const wt = strToFloat(weightInput.value);
      const id = weightInput.id;
      const itemAndWeight = {itemRefNo: id, weight: wt };
      itemReceiptNosAndWeights.push(itemAndWeight);
    });

    // Get PackageTypes for each individual item
    const pkgTypeInputs = templateInstance.$(elements.packageTypeInputs);
    pkgTypeInputs.each(function (index, pkgTypeInput) {
      const pkgType = pkgTypeInput.value;
      const id = $(pkgTypeInput).data('item-receipt-no');
      const itemAndPkgType = {itemRefNo: id, packageType: pkgType };
      itemReceiptNosAndPkgTypes.push(itemAndPkgType);
    });
  }

  // Set all values for this item.
  const receipt = {
    receiptType: ReceiptTypes.chemReceipt, // UPDATE RECEIPT TYPE ON MATERIAL
    receiptCategory: defaultReceiptCategory,
    receiptNo: templateInstance.currentReceiptNo.get(),
    _id: selectedItemId,
    ccu: emptyStringToNull(templateInstance.$(elements.ccu).val()),
    packageTypes: itemReceiptNosAndPkgTypes,
    quantity: strToInt(templateInstance.$(elements.quantity).val()),
    description: emptyStringToNull(templateInstance.$(elements.description).val()),
    ncrs: strToArray(templateInstance.$(elements.ncrs).val()),
    weights: itemReceiptNosAndWeights,
    offshoreClient: emptyStringToNull(templateInstance.$(elements.offshoreClient).val()),
    offshoreLocation: emptyStringToNull(templateInstance.$(elements.offshoreLocation).val()),
    materialManifestNo: emptyStringToNull(templateInstance.$(elements.materialManifestNo).val()),
    unNo: emptyStringToNull(templateInstance.$(elements.unNo).val()),
    imoHazardClass: emptyStringToNull(templateInstance.$(elements.hazardClass).val()),
    imoSubClass: emptyStringToNull(templateInstance.$(elements.hazardSubclass).val()),
  };

  // Set updated material information - will auto-trigger validation.
  ReceiptStageService.updateReceipt(receipt, templateInstance);
};

// *** ON-CREATED ***
Template.vchemReceipt.onCreated(function onCreated() {
  const template = this;

  template.autorun(() => {
    const clientId = FlowRouter.getParam('clientId') || '';
    const ccuId = FlowRouter.getParam('ccuId') || FlowRouter.getParam('poId') || '';
    const materialItemId = FlowRouter.getParam('itemId') || '';

    template.subscribe(Publications.items.selectedItem, { itemId: materialItemId });
  });

  template.currentReceiptNo = new ReactiveVar(null);

  // React to changes in Material Line Quantity Value.
  this.currentQuantitySetting = new ReactiveVar(0);
  this.isMultiQtySingleItem = new ReactiveVar(false);
});

// *** ON-RENDERED ***
Template.vchemReceipt.onRendered(function onRendered() {
  const template = this;

  // React to change in quantity.
  template.autorun(() => {
    const jq = (myid) => {
      return '#' + myid.replace(/(:|\.|\[|\]|,|=|@)/g, '\\$1');
    }
    // Update/Reset list of items/weights if Qty changed..
    const qtyValue = template.currentQuantitySetting.get();
    const itemReceiptNosAndWeights = [];
    const itemId = FlowRouter.getParam('itemId') || '';
    const item = Items.findOne({ _id: itemId });
    const isMultiQtySingleItem = template.isMultiQtySingleItem.get();
    if (item) {
      let actualItemsCount = qtyValue;
      if (isMultiQtySingleItem) {
        actualItemsCount = 1;
      }
      const count = actualItemsCount;
      const baseReceiptNo = item.receiptNo;
      for (let i = 1; i <= count; i++) {
        let itemReceiptNo = `${baseReceiptNo}.${i}`;
        const wtStr = template.$(jq(itemReceiptNo)).val() || null; // get onscreen value if it exists.
        const wt = strToFloat(wtStr);
        const id = itemReceiptNo;
        const itemAndWeight = { itemRefNo: id, weight: wt };
        itemReceiptNosAndWeights.push(itemAndWeight);
      }
    }
    updateReceipt(template, itemReceiptNosAndWeights);
  });

  // React to change in selected Item (material)
  template.autorun(() => {
    const itemId = FlowRouter.getParam('itemId') || '';
    const selectedItem = Items.findOne({ _id: itemId });
    const currentReceiptNo = selectedItem ? selectedItem.receiptNo : null;

    template.currentReceiptNo.set(currentReceiptNo);

    // Reset form values if selected item changes.
    clearFormFull(template);

    // Start Chemicals - pre fill form now.
    if (selectedItem) {
      template.$(elements.description).val(_.unescape(selectedItem.description)
        .replace(/&GT;/ig, '>')
        .replace(/&LT;/ig, '<')
        .replace(/&gt/ig, '>')
        .replace(/&lt/ig, '<'));
      template.$(elements.offshoreLocation).val(selectedItem.offshoreLocation || null);

      template.$(elements.packageTypeInputs).val(null); // Set all as empty.

      template.$(elements.quantity).val(selectedItem.quantity || null);

      template.$(elements.ccu).val(selectedItem.ccu);
      template.$(elements.voyageNo).val(selectedItem.voyageNo);
      template.$(elements.ccuManifestNo).val(selectedItem.ccuManifestNo);
      template.$(elements.offshoreClient).val(selectedItem.offshoreClient);

      template.$(elements.euralEwcCode).val(selectedItem.euralCode);
      template.$(elements.unNo).val(selectedItem.unNo);
      template.$(elements.hazardClass).val(selectedItem.imoHazardClass);
      template.$(elements.hazardSubclass).val(selectedItem.imoSubClass);
      template.$(elements.materialManifestNo).val(selectedItem.materialManifestNo);

      template.$(elements.wasteDescription).val(_.unescape(selectedItem.wasteDescription)
        .replace(/&GT;/ig, '>')
        .replace(/&LT;/ig, '<')
        .replace(/&gt/ig, '>')
        .replace(/&lt/ig, '<'));

      template.$(elements.isMultiQtyItem).checkbox('set unchecked');

      // Initialise the reactive Quantity that drives the list of items and weights.
      template.currentQuantitySetting.set(selectedItem.quantity);
    }
    // END Chemicals pre-fill.

    updateReceipt(template);
  });

  template.$('.dropdown').dropdown({
    onChange() {
      updateReceipt(template);
    },
  });

  template.$('.dropdown.no-focus').dropdown({
    onChange() {
      updateReceipt(template);
    },
    onShow() {
      Meteor.setTimeout(() => {
        template.$("input[type='text']").prop('readonly', false);
      }, 500);
    },
    onHide() {
      template.$("input[type='text']").prop('readonly', true);
    },
  });

  template.$('.checkbox').checkbox({
    onChange() {
      updateReceipt(template);

      const isMultiQtySingleItem = template.$('#isMultiQtyItemCheckbox').checkbox('is checked');
      template.isMultiQtySingleItem.set(isMultiQtySingleItem);
    },
  });

  template.autorun(() => {
    const item = Template.currentData().item;
    const cargoItem = Template.currentData().cargoItem;

    const shouldDimForm = item ? (item.receiptType != ReceiptTypes.chemPreReceipt) : true;

    if (shouldDimForm) {
      template.data.eventEmitter.dimForm();
    } else {
      template.data.eventEmitter.showForm();
    }
  });

  template.validationContext = ChemReceiptFromClientSchema.namedContext('chemReceiptForm');

  template.autorun(() => {
    // Continuously Validate current form data to allow action buttons to be enabled once valid.

    let isReceiptValid = template.validationContext.validate(ReceiptStageService.receipt());

    // Add validation check to ensure all package types are set for individual items.
    const valuesArr = [];
    template.$(elements.packageTypeInputs).each(function () {
      valuesArr.push($(this).val());
    });
    const isAllPackageTypesSet = !valuesArr.some(x => !x);
    const isAtLeastOnePackageType = valuesArr.length > 0;

    isReceiptValid = isAllPackageTypesSet && isAtLeastOnePackageType && isReceiptValid;

    template.data.isReceiptValid.set(isReceiptValid);
  });

  template.clearFormPartialCallback = function clearFormPartialCallback() {
    clearFormPartial(template);
  };

  template.clearFormFullCallback = function clearFormFullCallback() {
    clearFormFull(template);
  };

  template.data.eventEmitter
    .onSubmit(template.clearFormFullCallback)
    .onSubmitAndNext(template.clearFormPartialCallback);
});

// *** HELPERS ***
Template.vchemReceipt.helpers({
  receiptCategories() {
    return SiteProfileService.receiptCategories();
  },
  offshoreLocations() {
    return SiteProfileService.offshoreLocations();
  },
  packageTypes() {
    return SiteProfileService.packageTypes();
  },
  customsStatuses() {
    return SiteProfileService.customsStatuses();
  },
  ncrs() {
    return SiteProfileService.ncrs();
  },
  isMarinePollutant() {
    const itemId = FlowRouter.getParam('itemId') || '';
    const item = Items.findOne({ _id: itemId });
    if (item) {
      return item.marinePollutant;
    }
    return false;
  },
  currentReceiptNo() {
    const itemId = FlowRouter.getParam('itemId') || '';
    const item = Items.findOne({ _id: itemId });
    if (item) {
      return item.receiptNo;
    }
    return '';
  },
  currentMaterialDisplayText() {
    const itemId = FlowRouter.getParam('itemId') || '';
    const item = Items.findOne({ _id: itemId });

    if (item) {
      return `${_.unescape(item.description)
        .replace(/&GT;/ig, '>')
        .replace(/&LT;/ig, '<')
        .replace(/&gt/ig, '>')
        .replace(/&lt/ig, '<')} ` + ` from ${item.ccu}`;
    }
    return '';
  },
  showSingleItemToggle() {
    const show = Template.instance().currentQuantitySetting.get() > 1;
    if (!show) {
      return 'style="display:none"';
    }
    return '';
  },
  singleQuantityItems() {
    const singleQtyItems = [];

    const itemId = FlowRouter.getParam('itemId') || '';
    const item = Items.findOne({ _id: itemId });

    // Read Material Line Quantity value from form (reactive).
    let actualItemsCount = Template.instance().currentQuantitySetting.get();
    const isMultiQtySingleItem = Template.instance().isMultiQtySingleItem.get();
    if (isMultiQtySingleItem) {
      actualItemsCount = 1;
    }

    if (item) {
      const count = actualItemsCount;
      const baseReceiptNo = item.receiptNo;
      for (let i = 1; i <= count; i++) {
        const singleQtyitem = {
          itemIndex: i,
          totalItems: count,
          itemReceiptNo: `${baseReceiptNo}.${i}`,
        };
        singleQtyItems.push(singleQtyitem);
      }
    }
    return singleQtyItems;
  },

});

// *** EVENTS ***
Template.vchemReceipt.events({
  'input input': function onInput(event, templateInstance) {
    // Update list of items if Qty changed
    const qtyValue = parseInt(templateInstance.$(elements.quantity).val(), 10);
    templateInstance.currentQuantitySetting.set(qtyValue);

    // Now update receipt item values
    updateReceipt(templateInstance);
  },
  'change .material-item-package-type': function onChange(event, templateInstance) {
    updateReceipt(templateInstance);
  },
});

// *** ON-DESTROYED ***
Template.vchemReceipt.onDestroyed(function onDestroyed() {
  const template = this;

  template.data.eventEmitter
    .removeListener(ReceiptEvents.submit, template.clearFormFullCallback)
    .removeListener(ReceiptEvents.submitAndNext, template.clearFormPartialCallback);
});

// *** ON-RENDERED - singleQtyItemTemplate ***
Template.singleQtyItemTemplate.onRendered(function onRendered() {
  const template = this;
  template.$('.ui.dropdown').dropdown();
});
