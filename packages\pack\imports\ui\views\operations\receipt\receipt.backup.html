<template name="receipt">
  <div class="ui fluid container">
    <div class="ui padded grid">
      <div class="row">
        <div class="four wide column">
          {{> clientHeader headerText="Receipt"}}
        </div>
        {{#if showReceiptLocationsDropdown}}
          <div class="four wide column">
            <div style="padding-top: 1.7rem;">
              <div class="ui menu" style="font-size: 1.3rem;">
                <div class="ui fluid selection dropdown receiptLocationDropdown">
                  <input type="hidden" name="receiptLocation" value="">
                  <i class="dropdown icon"></i>
                  <div class="text" style="margin-left: 0;">{{defaultReceiptLocation}}</div>
                  <div class="menu">
                    {{#each receiptLocations}}
                    <div class="item" data-value="{{name}}" data-text="{{name}}">
                      {{name}}
                    </div>
                    {{/each}}
                  </div>
                </div>
              </div>
            </div>
          </div>
        {{/if}}
      </div>
      <div class="row">
        <div data-class="sixteen wide computer eight wide large screen eight wide widescreen column" style="margin-bottom: 2rem;">
          <div class="ui blurring raised segment" id="formSegment">
            <div class="ui inverted dimmer" style="z-index: 10;">
              <div class="content">
                <div class="center">
                  <h2 class="ui icon header" style="z-index: 10;">
                    <i class="warning icon"></i> {{dimmerText}}
                  </h2>
                </div>
              </div>
            </div>
            <form class="ui form" style="z-index: 1;">
              {{> Template.dynamic template=receiptStage data=receiptStageContext}}
            </form>
          </div>
          <div class="right aligned row">
            <div class="column">
              {{#each availableActions}}
              <button class="ui big labeled icon button {{buttonPosition this}} receipt-action {{buttonDisabled this}}" value="{{this}}">
                  <i class="ui icon {{iconForAction this}}"></i> {{this}}
              </button> {{/each}}
            </div>
          </div>
        </div>
        <div data-class="sixteen wide computer eight wide large screen eight wide widescreen column">
          <div class="ui raised segment">
            {{> recentCargoItemsTable }}
          </div>
          <div class="ui raised segment">
            {{> itemsContainedInCargoTable}}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>