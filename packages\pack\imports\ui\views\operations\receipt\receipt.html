<template name="receipt">
  <div class="menu-container ui pushable">
    <div class="ui sidebar inverted vertical menu grid">

          <div class="row">
            <div class="column">
              {{> recentCargoItemsTable }}
            </div>
          </div>

    </div>
    <div class="pusher" style="background: #fff">
      <div class="ui default-padding fluid container top-container">
        <div class="ui grid">
          <div class="row">
            <div class="eight wide column">
              {{> clientHeader headerText="Receipt"}}
            </div>
            <div class="eight wide right aligned column">
              {{#if showReceiptLocationsDropdown}}
                <!-- Show Receipt Locations Dropdown here -->
              {{/if}}
              <button class="ui primary labeled icon button js-item-create-button" >
                  <i class="plus icon"></i>
                  Create Waste Item
              </button>
            </div>
          </div>
        </div>

        {{#unless portraitMode}}
        <div class="ui grid">
            <div class="row">
              <div class="three wide column">
                {{> recentCargoItemsTable }}
              </div>
              <div class="thirteen wide column">
                <div class="row">
                  {{> cargoItemHeader}}
                </div>
                <div class="row" style="padding-top: 10px;">
                  {{> itemsContainedInCargoTable}}
                </div>
              </div>
            </div>
        </div>
        {{else}}
        <div class="ui grid">
            <div class="one column row">
              <div class="column">
                  {{> cargoItemHeader}}
              </div>
            </div>
            <div class="one column row">
              <div class="column">
                  {{> itemsContainedInCargoTable}}
              </div>
            </div>          
        </div>
        {{/unless}}

      </div>

      <div class="ui special modal receive-form-modal">
        <i class="close icon"></i>
        {{> Template.dynamic template=receiptStage data=receiptStageContext}}
        <div class="actions">
            <div class="ui grid">
                <div class="row">
                    <div class="column">
                        {{#each availableActions}}
                        <button class="ui big labeled icon button {{buttonPosition this}} receipt-action {{buttonDisabled this}} ok" value="{{this}}">
                            <i class="ui icon {{iconForAction this}}"></i> {{this}}
                        </button> 
                        {{/each}}
                    </div>
                </div>
            </div>
        </div>
      </div>
    </div>
  </div>
</template>