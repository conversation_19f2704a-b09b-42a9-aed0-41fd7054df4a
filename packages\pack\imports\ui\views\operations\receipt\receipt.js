import '../../../components/client-header';
import './receipt-stages/receipt-stages.index';
import './receipt.html';
import './abdn-recent-items-table';
import './recent-purchase-orders-table';
import './recent-cargo-items-table';
import './items-contained-in-cargo-table';
import { ChangePrinterModalMethods } from '../../printing/change-printer-modal.methods';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { PrintingService } from '../../../services/printing/printing.service';
import { PurchaseOrders } from '../../../../api/purchase-orders/purchase-orders';
import { Cargo } from '../../../../api/cargo/cargo';
import { Items } from '../../../../api/items/items';
import { ReactiveDict } from 'meteor/reactive-dict';
import { ReactiveVar } from 'meteor/reactive-var';
import { ReceiptActionServices } from '../../../services/receipt/receipt-action.services';
import { ReceiptActionTypes } from '../../../services/receipt/receipt-action.types';
import { ReceiptConfigurationService } from
  '../../../services/receipt/receipt-configuration.service';
import { ReceiptEventEmitter } from '../../../services/receipt/receipt.event-emitter';
import { Session } from 'meteor/session';
import { SiteProfileService } from
  '../../../services/company-site-profiles/site-profile.service';
import { Template } from 'meteor/templating';
import { ZplLabelService } from '../../../zpl/label';
import { ReceiptTypes } from '../../../../api/items/receipt.types';
import { BaseModalMixin } from '../../../modal/base-modal-mixin';
import { ItemInCargoReceiptedEvent } from '../../../../api/cargo/commands/emit-receipted-event';

const dimForm = () => {
  $('#formSegment').dimmer('show');
};

const showForm = () => {
  $('#formSegment').dimmer('hide');
};

const ReceiveFormModalMethods = Object.assign({
  className: '.ui.modal.receive-form-modal',

  init(onApprove) {

    $(this.className).modal({
      centered: true,
      observeChanges: true,
      allowMultiple: false,
      onApprove: onApprove,
    });

    return this;
  },
}, BaseModalMixin);

const getReceiptStagePosition = (templateInstance) =>
  templateInstance.receiptStage.get();

const getReceiptForStage = (templateInstance) => {
  let recPosition = getReceiptStagePosition(templateInstance);
 return  templateInstance.receipt.get(recPosition); // Returns client-side representation of item being received.
};

const setReceiptStagePosition = (pos, currentReceipt, templateInstance, poId = null) => {
  const routeTo = (path, receiptStage, params = {}) => {
    const routeParams = Object.assign({
      clientId: FlowRouter.getParam('clientId'),
      receiptStage,
    }, params);

    FlowRouter.go(path, routeParams);
  };

  const nextStage = ReceiptConfigurationService.stageByPosition(pos);
  let route = 'receiptAtStage';
  const routeParams = {};

  if (poId && poId.length) {
    route = 'receiptAtStageWithPo';
    routeParams.poId = poId;
  }

  if ((nextStage.dependsOn && nextStage.dependsOn.length) ||
    (nextStage.dependsOnNull && nextStage.dependsOnNull.length)) {
    const dependsOn = nextStage.dependsOn || [];
    const dependsOnNull = nextStage.dependsOnNull || [];

    const passesDependsOn = dependsOn.reduce((acc, prop) => {
      let shouldSkip = acc;

      if (!currentReceipt[prop]) {
        shouldSkip = false;
      }

      return shouldSkip;
    }, true);

    const passesDependsOnNull = dependsOnNull.reduce((acc, prop) => {
      let shouldSkip = acc;

      if (currentReceipt[prop]) {
        shouldSkip = false;
      }

      return shouldSkip;
    }, true);

    if (!passesDependsOn || !passesDependsOnNull) {
      const currentPos = getReceiptStagePosition(templateInstance);
      let newPos = pos - 1;

      if (currentPos > pos) {
        newPos = pos - 1;
      }
      return routeTo(route, newPos, routeParams);
    }
  }
  return routeTo(route, pos, routeParams);
};

const incrementReceiptStagePosition = (templateInstance) =>
  setReceiptStagePosition(
    getReceiptStagePosition(templateInstance) + 1,
    getReceiptForStage(templateInstance),
    templateInstance,
  );

const getReceiptStage = (templateInstance) =>
  ReceiptConfigurationService
    .stageByPosition(getReceiptStagePosition(templateInstance));

const isSubmitting = (templateInstance) =>
  templateInstance.isSubmitting.set(true);

const submittingCompleted = (templateInstance) =>
  templateInstance.isSubmitting.set(false);

const initPanels = (templateInstance) => {
  const panels = templateInstance.findAll('[data-class]') || [];

  for (const panel of panels) {
    panel.setAttribute('class', panel.getAttribute('data-class'));
  }
};

const getLabelsToPrint = (item, receiptDateTime) => {
  // Setup item array for each label to be printed.
  const labelsToPrint = [];
  item.weights.forEach((wtAndItemRefNo, index, arr) => {
    const itemForLabel = JSON.parse(JSON.stringify(item)); // Clone material line item.
    itemForLabel.receiptNo = wtAndItemRefNo.itemRefNo; // Override with item receiptNo
    itemForLabel.weightKg = wtAndItemRefNo.weight; // Set weight for specific item.
    itemForLabel.packageType = item.packageTypes
      .find((x) => x.itemRefNo === wtAndItemRefNo.itemRefNo)
      .packageType; // Set pkgType for item.
    itemForLabel.quantity = arr.length > 1 ? 1 : item.quantity; // Handle case where full qty treated as single item
    itemForLabel.materialReceiptDateTime = receiptDateTime; // From server.
    labelsToPrint.push(itemForLabel);
  });
  return labelsToPrint;
};

const printItem = (item, receiptDateTime) => {
  ChangePrinterModalMethods.showAndWaitForPrinterSelection(() => {
    const activePrinter = PrintingService.getUsersActivePrinter();
    const labelsToPrint = getLabelsToPrint(item, receiptDateTime);

    ZplLabelService.printBarcodeLabels(labelsToPrint, activePrinter.printerIp);
  });
};

const actionForType = (actionType, templateInstance) => {
  let callback = () => { };

  isSubmitting(templateInstance);

  const item = getReceiptForStage(templateInstance);
  switch (actionType) {
    case ReceiptActionTypes.next:
      callback = () => {
        incrementReceiptStagePosition(templateInstance);
        submittingCompleted(templateInstance);
      };
      break;
    case ReceiptActionTypes.preReceipt:
      callback = (err) => {
        if (!err) {
          // Pre receipt only used for generating test data in Chemicals.
          // Furthermore, the page needs to be refreshed if you want to
          // generate more data, so we will do it automatically in the callback
          location.reload();
          templateInstance.eventEmitter.hasSubmitted();
          submittingCompleted(templateInstance);
        }
      };
      break;
    case ReceiptActionTypes.submit:
      callback = (err) => {
        // Not used in Chemicals.
      };
      break;
    case ReceiptActionTypes.receipt:
      callback = (err, result) => {
        if (!err) {
          printItem(item, result.receiptDateTime); // Get Receipt Date time from server.
          templateInstance.eventEmitter.hasSubmitted();
          submittingCompleted(templateInstance);

          const newItem = Items.findOne({receiptNo:item.weights[0].itemRefNo });
          ItemInCargoReceiptedEvent.call({
            externalCargoLineId: newItem.externalCargoLineId,
            materialLineId: newItem.externalMaterialLineId,
          });
        } else {
          console.log(err);
          alert('Error occurred on receipting item.');
          submittingCompleted(templateInstance);
        }
      };
      break;
    case ReceiptActionTypes.receiptAndNext:
      callback = () => {
        printItem(item);
        templateInstance.eventEmitter.hasSubmittedAndNext();
        submittingCompleted(templateInstance);
      };
      break;
    case ReceiptActionTypes.back:
      callback = () => {
        // For Chemicals use this to return to Receipt overview page.
        const path = 'workItemOverview';
        const routeParams = {};
        return FlowRouter.go(path, routeParams);
      };
      break;
    default:
      throw new Meteor.Error('Action Type not recognised.', actionType);
  }

  // Decouple UI actions from Service actions
  return ReceiptActionServices
    .getServiceOpForAction(actionType, item, callback);
};

const iconForActionType = (actionType) => {
  let icon = 'radio';
  switch (actionType) {
    case ReceiptActionTypes.next:
      icon = 'caret right';
      break;
    case ReceiptActionTypes.preReceipt:
    case ReceiptActionTypes.receipt:
    case ReceiptActionTypes.submit:
      icon = 'check square';
      break;
    case ReceiptActionTypes.receiptAndNext:
      icon = 'caret right';
      break;
    case ReceiptActionTypes.back:
      icon = 'caret left';
      break;
    default:
      throw new Meteor.Error('Action Type not recognised.', actionType);
  }
  return icon;
};

const disableButton = (actionType, templateInstance) => {
  let result = '';

  if (templateInstance.isSubmitting.get()) {
    return 'disabled loading';
  }
  switch (actionType) {
    case ReceiptActionTypes.next:
    case ReceiptActionTypes.preReceipt:
    case ReceiptActionTypes.receipt:
    case ReceiptActionTypes.receiptAndNext:
    case ReceiptActionTypes.submit:
      if (!templateInstance.currentReceiptStageIsValid.get()) {
        result = 'disabled';
      }
      break;
    case ReceiptActionTypes.back:
      break;
    default:
      throw new Meteor.Error('Action Type not recognised.', actionType);
  }

  return result;
};

const defaultReceiptLocation = () => {
  const currentReceiptLocation = Session.get('receiptLocation');

  if (currentReceiptLocation) {
    return currentReceiptLocation;
  }

  const receiptLocations = SiteProfileService.receiptLocations();

  if (receiptLocations && receiptLocations.length) {
    const receiptsWarehouse = receiptLocations.find((loc) => loc.name === 'Receipts Warehouse');
    return receiptsWarehouse ? receiptsWarehouse.name : receiptLocations[0].name;
  }

  return null;
};

// *** ON-CREATED ***
Template.receipt.onCreated(function onCreated() {
  const template = this;

  template.receiptStage = new ReactiveVar(0);
  template.currentReceiptStageIsValid = new ReactiveVar(false);
  template.receipt = new ReactiveDict(); // 'receipt' is reactive dictionary based on receipt stage.

  template.purchaseOrder = new ReactiveVar();
  template.cargoItem = new ReactiveVar(); // CCU/Cargo Guid - Added for Chemicals
  template.item = new ReactiveVar(); // Selected Item (Material) Guid - Added for Chemicals

  template.receipt.set(template.receiptStage.get(), {}); // sets receipt item to empty object for current receipt stage.

  template.isSubmitting = new ReactiveVar(false);

  template.eventEmitter = new ReceiptEventEmitter();

  template.eventEmitter
    .onShowForm(showForm)
    .onDimForm(dimForm);

  template.autorun(() => {
    const receiptStage = parseInt(FlowRouter.getParam('receiptStage'), 10);
    const cargoId = FlowRouter.getParam('ccuId');
    if (cargoId && cargoId.length) {
      const cargoItem = Cargo.findOne({ _id: cargoId });
      template.cargoItem.set(cargoItem);
    } else {
      template.cargoItem.set(null);
    }

    const itemId = FlowRouter.getParam('itemId');
    if (itemId && itemId.length) {
      const item = Items.findOne({ _id: itemId });
      template.item.set(item);
    } else {
      template.item.set(null);
    }

    if (!template.receipt.get(receiptStage)) { // if receipt stage is not set or is zero, clear the receipt item
      template.receipt.set(receiptStage, {});
    }

    template.receiptStage.set(receiptStage);

    // Setup receipt Location in onCreated, so it is available for other templates during render
    const currentReceiptLocation = Session.get('receiptLocation');
    if (!currentReceiptLocation) {
      let defaultReceiptLocn = defaultReceiptLocation();
      Session.set('receiptLocation', defaultReceiptLocn);
    }
  });
});

// *** ON-RENDERED ***
Template.receipt.onRendered(function onRendered() {
  const template = this;

  initPanels(template);

  template.$('.receiptLocationDropdown').dropdown({
    action: 'combo', // We update the text value of the dropdown in a helper
    onChange(value) {
      Session.set('receiptLocation', value);
    },
  });

  ReceiveFormModalMethods.init((element) => {
    const actionType = $(element).val();
    actionForType(actionType, template)();
  });

  const stage = getReceiptStage(template);
  const showTestPanel = (stage.templateName === 'vchemPreReceipt');

  if (showTestPanel) {
    Meteor.setTimeout(() => {
      ReceiveFormModalMethods.show();
    }, 500);
  }
});

Template.receipt.onDestroyed(function onDestroyed() {
  const template = this;
  ReceiveFormModalMethods.destroy();
});

// *** HELPERS ***
Template.receipt.helpers({
  receiptStage() {
    const stage = getReceiptStage(Template.instance());

    if (stage) {
      return stage.templateName;
    }

    return '';
  },
  receiptStageContext() {
    return {
      stage: getReceiptStage(Template.instance()),
      receipt: Template.instance().receipt,
      isReceiptValid: Template.instance().currentReceiptStageIsValid,
      cargoItem: Template.instance().cargoItem.get(), // Added for Chemicals.
      item: Template.instance().item.get(), // Added for Chemicals.
      eventEmitter: Template.instance().eventEmitter,
    };
  },
  availableActions() {
    return ReceiptConfigurationService
      .availableActionsForStage(getReceiptStagePosition(Template.instance()));
  },
  iconForAction(action) {
    return iconForActionType(action);
  },
  buttonDisabled(action) {
    return disableButton(action, Template.instance());
  },
  buttonPosition(action) {
    return action === ReceiptActionTypes.back
      ? 'left floated'
      : 'right floated';
  },
  receiptLocations() {
    return SiteProfileService.receiptLocations();
  },
  showReceiptLocationsDropdown() {
    return false;
  },
  defaultReceiptLocation() {
    return defaultReceiptLocation();
  },
  dimmerText() {
    return 'Please select a material that has not been receipted!';
  },
});

// *** EVENTS ***
Template.receipt.events({
  'click .receipt-action': function handleClick(event, templateInstance) {
    event.preventDefault();
    const actionType = templateInstance.$(event.currentTarget).val();
    actionForType(actionType, templateInstance)();
  },
  // Added for Chemicals
  'click .js-recent-cargo-item': function handleClick(event, templateInstance) {
    event.preventDefault();
    const cargoItemId = templateInstance.$(event.currentTarget).data('cargo-item-id');
    const cargoItem = Cargo.findOne({ _id: cargoItemId });
    setReceiptStagePosition(
      ReceiptConfigurationService.postReceiptStagePosition(),
      cargoItem,
      templateInstance,
      cargoItemId,
    );
  },
  'click .js-contained-item': function handleClick(event, templateInstance) {
    event.preventDefault();
    const itemId = templateInstance.$(event.currentTarget).data('item-id');
    const materialItem = Items.findOne({ _id: itemId });

    // Only allow material item selection if it has not yet been receipted
    // and is a waste item.
    if (materialItem.receiptType === ReceiptTypes.chemPreReceipt && materialItem.isWaste === true) {
      const routeTo = (path, receiptStage, params = {}) => {
        // Just combine params
        const routeParams = Object.assign({
          clientId: FlowRouter.getParam('clientId'),
          receiptStage,
        }, params);

        FlowRouter.go(path, routeParams);
      };

      const receiptStage = 2;
      const route = 'receiptAtStageWithCcuAndMaterial';
      const routeParams = {};
      routeParams.ccuId = FlowRouter.getParam('ccuId') || FlowRouter.getParam('poId');// NOTE: Assumption param available
      routeParams.itemId = itemId;

      return routeTo(route, receiptStage, routeParams);
    }
    return; // Do nothing.
  },
  'click .receive-button': function handleClick(event, template) {
    ReceiveFormModalMethods.show();
  },
  'click .show-menu-button': function handleClick(event, template) {
    $('.ui.sidebar').sidebar({
      context: $('div.menu-container'),
    }).sidebar('toggle');
  },
  'click .js-item-create-button': function createButtonClicked() {
    // pathdef, params, queryparams.
    FlowRouter.go(
      'materialItemCreatePage',
      {
        clientId: FlowRouter.getParam('clientId'),
        itemId: '0',
      }, // Not used, keeping incase we allow cloning of existing item.
      {},
    );
  },
});
