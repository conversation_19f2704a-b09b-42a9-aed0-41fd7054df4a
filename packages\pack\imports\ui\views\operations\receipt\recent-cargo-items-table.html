<template name="recentCargoItemsTable">
  <div class="fluid ui search">
    <div class="fluid ui icon input">
      <input class="{{#if portraitMode}}blue-border{{/if}}" name="query" type="text" placeholder="Search by CCU No...">
      <i class="search icon"></i>
    </div>
  </div>
  <table id="recentItemsTable" class="ui selectable cell striped blue table no-select">
    <thead>
      <tr>
        <th class="id" style="display:none" fieldid="0">_Id</th> 
        <th class="ccu" fieldid="0">CCU</th>
      </tr>
    </thead>
    <tbody>
      {{#each cargoItemsNotFullyReceipted}} 
        {{> cargoItemRow}} 
      {{/each}}
      {{#if noCargoItemsToDisplay}}
        <tr>
          <td colspan="6" style="text-align: center;">No Cargo Requiring Receipting</td>
        </tr>
      {{/if}}
    </tbody>
  </table>

</template>

<template name="cargoItemRow">
  <tr class="js-recent-cargo-item {{rowClass}}" data-cargo-item-id="{{_id}}">
    <td class="bold whitespace-nowrap">{{identifier}}</td>
  </tr>
</template>

<template name="cargoItemHeader">
    <div class="ui grid">
      <div class="two column row">
        <div class="column">
          {{#if item.identifier}}
            <h4>{{item.identifier}}</h4>
          {{else}}
            <h4>No CCU Selected.</h4>
          {{/if}}
        </div>
        <div class="right aligned column">
            {{#if portraitMode}}
              <button class="ui small blue button show-menu-button">Change CCU</button>
            {{/if}}
            {{#if item}}
              <button class="ui primary left labeled icon button js-item-download-label">
                <i class="left download icon"></i>
                Download Label
              </button>
            {{/if}}
        </div>
      </div>

    </div>
    <table id="" class="ui selectable cell striped blue table no-select break-word">
        <thead>
          <tr>
            <th class="id" style="display:none" fieldid="0">_Id</th> 
            <th class="ccu" fieldid="0">CCU</th>
            <th class="receivedDate" fieldid="1">Received</th>
            <th class="operator" fieldid="3">Operator</th>
            <th class="offshoreLocation" fieldid="4">Offshore Locn</th>
            <th class="manifestNo" fieldid="6">ManifestNo</th>
            <th class="noOfLinesReceipted" fieldid="7">No. of Lines</th>
            <th class="noOfWasteLinesReceipted" fieldid="7">Waste Lines Receipted</th>
          </tr>
        </thead>
        <tbody>
          <td class="">{{formatString item.identifier}}</td>
          <td class="">{{formatString receivedDateFormatted}}</td>
          <td class="">{{formatString item.offshoreClient}}</td>
          <td class="">{{formatString item.offshoreLocation}}</td>
          <td>{{formatString item.manifestNo}}</td>
          <td>{{formatString noOfLines}}</td>
          <td>{{formatString noOfWasteLinesReceipted}}</td>
        </tbody>
    </table>
</template>