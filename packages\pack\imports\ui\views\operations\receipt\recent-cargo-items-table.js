import "./recent-cargo-items-table.html";
import { DISPLAY_DATETIME_FORMAT_COMPACT } from "../../../../shared/lib/constants";
import { FlowRouter } from "meteor/ostrio:flow-router-extra";
import { GetCountForRecentItems } from "../../../../api/cargo/queries/get-count-for-recent-items";
import { Publications } from "../../../../api/api.publications/publications";
import { Cargo } from "../../../../api/cargo/cargo";
import { ReactiveVar } from "meteor/reactive-var";
import { Template } from "meteor/templating";
import moment from "moment";
import { utils } from "../../../../shared/utils";
import { labelUtils } from "../../../pdf/label";

const updatePagination = (query, clientId, templateInstance) => {
  GetCountForRecentItems.call({ query, clientId }, (err, result) => {
    if (!err) {
      const noOfItems = result;

      const itemsPerPage = templateInstance.itemsPerPage;

      const noOfPages = Math.ceil(noOfItems / itemsPerPage);

      templateInstance.noOfPages.set(noOfPages);
    }
  });
};

const calculatePaginationSteps = (templateInstance) => {
  const currentPage = templateInstance.page.get();
  const noOfPages = templateInstance.noOfPages.get();
  const delta = 2;
  const left = currentPage - delta;
  const right = currentPage + delta + 1;

  const buildPageObj = (val) => {
    const isActive = val === currentPage;
    let className = isActive ? "active" : "";

    if (val === "...") {
      className = "disabled";
    } else {
      className = `${className} paging-item`;
    }

    const obj = {
      classy: className,
      index: val,
    };

    return obj;
  };

  const pages = [];

  for (let i = 1; i <= noOfPages; i++) {
    if (i === 1 || i === noOfPages || (i >= left && i < right)) {
      pages.push(i);
    }
  }

  const pagesWithDots = [];
  let l;

  for (const i of pages) {
    if (l) {
      if (i - l === 2) {
        pagesWithDots.push(buildPageObj(l + 1));
      } else if (i - l !== 1) {
        pagesWithDots.push(buildPageObj("..."));
      }
    }
    pagesWithDots.push(buildPageObj(i));
    l = i;
  }

  return pagesWithDots;
};

Template.recentCargoItemsTable.onCreated(function onCreated() {
  const template = this;

  template.query = new ReactiveVar();
  template.page = new ReactiveVar(1);
  template.noOfPages = new ReactiveVar(1);

  // It was not obvious this CCU list was being limited (to 10) - setting to 500 to ensure all CCUs are displayed.
  template.itemsPerPage = 500;

  template.autorun(() => {
    const query = template.query.get();
    const clientId = FlowRouter.getParam("clientId");
    const page = template.page.get();
    const limit = template.itemsPerPage;

    template.subscribe(Publications.cargo.cargoItemsRequiringReceipting, {
      query,
      clientId,
      limit,
      page,
    });

    updatePagination(query, clientId, template);
  });
});

// *** HELPERS ***
Template.recentCargoItemsTable.helpers({
  cargoItemsNotFullyReceipted() {
    const selector = {
      $and: [
        {
          isWasteRemovalCompleted: false,
          "client._id": FlowRouter.getParam("clientId"),
        },
      ],
    };

    const query = Template.instance().query.get();

    if (query && query.length) {
      const querySelector = {
        $or: [
          { identifier: { $regex: utils.escapeRegExp(query), $options: "i" } },
        ],
      };

      selector.$and.push(querySelector);
    }

    return Cargo.find(selector, { sort: { identifier: 1 } }).fetch();
  },
  noCargoItemsToDisplay() {
    return Cargo.find().count() === 0;
  },
  pages() {
    return calculatePaginationSteps(Template.instance());
  },
});

Template.recentCargoItemsTable.events({
  "input [name=query]": function onInput(event, templateInstance) {
    const queryStr = templateInstance.$(event.currentTarget).val();

    templateInstance.query.set(queryStr);
  },
  "click .paging-item": function onClick(event, templateInstance) {
    const page = parseInt(templateInstance.$(event.currentTarget).text(), 10);

    templateInstance.page.set(page);
  },
  "click .paging-left": function onClick(event, templateInstance) {
    const currentPage = templateInstance.page.get();
    const newPage = currentPage - 1;

    if (newPage > 0) {
      templateInstance.page.set(newPage);
    }
  },
  "click .paging-right": function onClick(event, templateInstance) {
    const currentPage = templateInstance.page.get();
    const newPage = currentPage + 1;
    const limit = templateInstance.noOfPages.get();

    if (newPage <= limit) {
      templateInstance.page.set(newPage);
    }
  },
});

// *** HELPERS ***
Template.cargoItemRow.helpers({
  receivedDateFormatted() {
    const receivedDate = moment(Template.instance().data.receivedDate);
    return receivedDate.format(DISPLAY_DATETIME_FORMAT_COMPACT);
  },
  noOfLinesReceipted() {
    const receiptedLines = Template.currentData().linesReceipted || [];
    const noOfLines = Template.currentData().noOfLines || 0;

    return `${receiptedLines.length} / ${noOfLines}`;
  },
  noOfWasteLinesReceipted() {
    const receiptedWasteLines = Template.currentData().linesReceipted || [];
    const noOfWasteLines = Template.currentData().noOfWasteLines || 0;

    return `${receiptedWasteLines.length} / ${noOfWasteLines}`;
  },
  descriptionFormatted() {
    return this.description || "-";
  },
  operatorFormatted() {
    return this.offshoreClient || "-";
  },
  offshoreLocationFormatted() {
    return this.offshoreLocation || "-";
  },
  voyageNoFormatted() {
    return this.voyageNo || "-";
  },
  manifestNoFormatted() {
    return this.manifestNo || "-";
  },
  rowClass() {
    let cls = "";
    let cargoItemIdFromUrl =
      FlowRouter.getParam("poId") || FlowRouter.getParam("ccuId") || "";
    if (this._id == cargoItemIdFromUrl) {
      cls = "active";
    }
    return cls;
  },
});

Template.cargoItemHeader.onCreated(function onCreated() {
  const template = this;

  let cargoItemIdFromUrl =
    FlowRouter.getParam("poId") || FlowRouter.getParam("ccuId") || "";
});

Template.cargoItemHeader.events({
  "click .js-item-download-label": function onClick(event, templateInstance) {
    event.preventDefault();

    const cargoItemIdFromUrl =
      FlowRouter.getParam("poId") || FlowRouter.getParam("ccuId") || "";
    const item = Cargo.findOne(cargoItemIdFromUrl);

    if (!item) {
      console.error("No cargo item found for download");
      return;
    }

    // Create a receipt-like document using the cargo item data
    const receiptData = {
      identifier: item.identifier,
      offshoreClient: item.offshoreClient,
      offshoreLocation: item.offshoreLocation,
      manifestNo: item.manifestNo,
      receivedDate: item.receivedDate,
      quantity: 1, // Default quantity for receipt
      packageType: "Receipt",
      description: `Receipt for ${item.identifier}`,
      // Add length property to work around the labelUtils bug
      length: 1,
    };

    const logoUrl = "/images/client-logos/peterson-sel.png";
    const title = `Receipt - ${item.identifier}`;
    const barcodeNumber = item.identifier;

    labelUtils
      .createLabel(receiptData, title, barcodeNumber, logoUrl, false)
      .then((doc) => {
        const timestamp = moment().format("YYYY-MM-DD-HHmm");
        const filename = `Receipt_${item.identifier}_${timestamp}.pdf`;
        doc.save(filename);
      })
      .catch((error) => {
        console.error("Failed to generate receipt PDF:", error);
      });
  },
});

Template.cargoItemHeader.helpers({
  item() {
    let cargoItemIdFromUrl =
      FlowRouter.getParam("poId") || FlowRouter.getParam("ccuId") || "";
    return Cargo.findOne(cargoItemIdFromUrl);
  },

  formatString(a) {
    return a ? a : "-";
  },

  receivedDateFormatted() {
    let cargoItemIdFromUrl =
      FlowRouter.getParam("poId") || FlowRouter.getParam("ccuId") || "";
    const item = Cargo.findOne(cargoItemIdFromUrl);
    if (typeof item === "undefined") {
      return "";
    }

    const receivedDate = moment(item.receivedDate);
    return receivedDate.format(DISPLAY_DATETIME_FORMAT_COMPACT);
  },

  noOfLinesReceipted() {
    let cargoItemIdFromUrl =
      FlowRouter.getParam("poId") || FlowRouter.getParam("ccuId") || "";
    const item = Cargo.findOne(cargoItemIdFromUrl);
    if (typeof item === "undefined") {
      return "";
    }

    const receiptedLines = item.linesReceipted || [];
    const noOfLines = item.noOfLines || 0;

    return `${receiptedLines.length} / ${noOfLines}`;
  },

  noOfLines() {
    let cargoItemIdFromUrl =
      FlowRouter.getParam("poId") || FlowRouter.getParam("ccuId") || "";
    const item = Cargo.findOne(cargoItemIdFromUrl);
    if (typeof item === "undefined") {
      return "";
    }

    return item.noOfLines;
  },

  noOfWasteLinesReceipted() {
    let cargoItemIdFromUrl =
      FlowRouter.getParam("poId") || FlowRouter.getParam("ccuId") || "";
    const item = Cargo.findOne(cargoItemIdFromUrl);
    if (typeof item === "undefined") {
      return "";
    }

    const receiptedWasteLines = item.linesReceipted || [];
    const noOfWasteLines = item.noOfWasteLines || 0;

    return `${receiptedWasteLines.length} / ${noOfWasteLines}`;
  },
});
