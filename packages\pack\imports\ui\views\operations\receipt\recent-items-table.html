<template name="recentItemsTable"> 
  <h1 class="ui header">Recent Items</h1>     
  {{#if itemsSubscriptionReady}}

   <table id="recentItemsTable" class="ui very compact selectable cell striped single line table">
     <thead>
          <tr>
                    <th class="itemRef" fieldid="0"><PERSON> Ref</th>
                    <th class="receivedDate" fieldid="2">Received</th>
                    <th class="destCategory" fieldid="3">SL/Contractor</th>
                    <th class="quantity" fieldid="4">Qty.</th>
                    <th class="weightKg" fieldid="5">Wt.(kg)</th>
                    <th class="packageType" fieldid="6">Pkg.</th>
                    <th class="description" fieldid="7">Description</th>
          </tr>
     </thead>
     <tbody>
			{{#each recentItems}}
				{{> recentItemRow}}
			{{/each}}
		</tbody>
    </table>

    {{else}}
        <div class="ui active text loader">Loading...</div>
    {{/if}}
</template>

<template name="recentItemRow">
  <tr class="js-recent-item" data-item-id="{{_id}}">
					<td class="bold">{{itemRef}}</td>
					<td>{{receivedDateFormatted}}</td>
					<td>{{destCategory}}</td>
          <td>{{quantity}}</td>
					<td>{{weightKg}}</td>
					<td>{{packageType}}</td>
          <td>{{description}}</td>
  </tr>
</template>