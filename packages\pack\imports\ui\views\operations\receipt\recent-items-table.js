import './recent-items-table.html';
import { $ } from 'meteor/jquery';
import { CompanySiteProfiles } from '../../../../api/company-site-profiles/company-site-profiles';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Items } from '../../../../api/items/items';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import { Template } from 'meteor/templating';
import { moment } from 'meteor/momentjs:moment';

// Components used inside the template.
// None.

function getSiteProfile() {
  // Assumes only one loaded clients-side.
  return CompanySiteProfiles.findOne();
}

const currentPage = function currentPage() {
  return 1;
};

const getItemsPerPage = function getItemsPerPage() {
  return 15;
};

Template.recentItemsTable.onCreated(function onCreated() {
  console.log('RecentItemsTableTemplate created..');
  const template = this;

  // 1. Initialization (See https://www.discovermeteor.com/blog/template-level-subscriptions/)
  const siteProfile = getSiteProfile();

  template.recentItemsPageSize = new ReactiveVar(getItemsPerPage());
  template.recentItemsNumberLoaded = new ReactiveVar(0);

  // 2. Autorun.
  // Will re-run if/when the "recentItemsPageSize" reactive variables changes
  template.autorun(function () {
    // get the limit
    const itemsPerPage = template.recentItemsPageSize.get();

    // Subscribe to the publication

    console.log(`siteProfile.identifier <${siteProfile.identifier}>.`);

    const skipCount = (currentPage() - 1) * itemsPerPage;

    console.log(template);

    let subscrParams = {
      pageSize: itemsPerPage,
      skipCount: skipCount,
      clientId: FlowRouter.getParam('clientId'),
    };

    template.subscription = template.subscribe(
      'items.recentItems',
      subscrParams,
    );

    // If subscription is ready, set count of loaded items to itemsPerPage.
    if (template.subscription.ready()) {
      template.recentItemsNumberLoaded.set(itemsPerPage);
    }
  });
});

Template.recentItemsTable.helpers({
  recentItems() {
    const template = Template.instance();
    const itemsPerPage = template.recentItemsPageSize.get();
    const skipCount = (currentPage() - 1) * itemsPerPage;
    // Get items from client-side collection (relying on publication to provide correct items.)
    return Items.find(
      {},
      {
        sort: { receivedDate: -1 },
        limit: template.recentItemsNumberLoaded.get(),
        skip: skipCount,
      },
    );
  },
  itemsSubscriptionReady() {
    return Template.instance().subscription.ready();
  },
});

Template.recentItemRow.helpers({
  receivedDateFormatted() {
    const receivedDate = moment(Template.instance().data.receivedDate);
    return receivedDate.format('DD-MMM-YY HH:mm');
  },
});
