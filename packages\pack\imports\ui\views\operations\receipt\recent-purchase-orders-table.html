<template name="recentPurchaseOrdersTable">
  <div class="ui fluid grid">
    <div class="row">
      <div class="ten wide column">
        <h1 class="ui header">POs Requiring Receipting</h1>
      </div>
      <div class="six wide right aligned column">
        <div class="ui search">
          <div class="ui icon input">
            <input class="prompt" name="query" type="text" placeholder="Search by PO No...">
            <i class="search icon"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
  <table id="recentItemsTable" class="ui very compact selectable cell striped table">
    <thead>
      <tr>
        <th class="itemRef" fieldid="0">PO No.</th>
        <th class="receivedDate" fieldid="2">Received</th>
        <th class="vendor" fieldid="3">Vendor</th>
        <th class="vendorDeliveryNo" fieldid="4">Vendor Deliv No.</th>
        <th class="description" fieldid="5">Description</th>
        <th class="noOfLinesReceipted" fieldid="6">No. Lines Receipted</th>
      </tr>
    </thead>
    <tbody>
      {{#each pos}} {{> poRow}} {{/each}}
      {{#if noPosToDisplay}}
        <tr>
          <td colspan="6" style="text-align: center;">No Purchase Orders Requiring Receipting</td>
        </tr>
      {{/if}}
    </tbody>
  </table>
  <div class="row">
    <div class="ui right floated pagination menu">
      <a class="icon item paging-left">
        <i class="left chevron icon"></i>
      </a>
      {{#each pages}}
      <a class="item {{this.classy}}">{{this.index}}</a>
      {{/each}}
      <a class="icon item paging-right">
        <i class="right chevron icon"></i>
      </a>
    </div>
    <div class="clearfix"></div>
  </div>
</template>

<template name="poRow">
  <tr class="js-recent-po" data-po-id="{{_id}}">
    <td class="bold whitespace-nowrap">{{identifier}}</td>
    <td class="whitespace-nowrap">{{receivedDateFormatted}}</td>
    <td>{{vendor}}</td>
    <td>{{vendorDeliveryNo}}</td>
    <td class="truncate-with-ellipsis">{{descriptionFormatted}}</td>
    <td>{{noOfLinesReceipted}}</td>
  </tr>
</template>