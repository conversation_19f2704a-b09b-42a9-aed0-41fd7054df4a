import './recent-purchase-orders-table.html';
import { DISPLAY_DATETIME_FORMAT_COMPACT } from '../../../../shared/lib/constants';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { GetCountForRecentItems } from '../../../../api/purchase-orders/queries/get-count-for-recent-items';
import { Publications } from '../../../../api/api.publications/publications';
import { PurchaseOrders } from '../../../../api/purchase-orders/purchase-orders';
import { ReactiveVar } from 'meteor/reactive-var';
import { Template } from 'meteor/templating';
import moment from 'moment';
import { utils } from '../../../../shared/utils';

const updatePagination = (query, clientId, templateInstance) => {
  GetCountForRecentItems.call({ query, clientId }, (err, result) => {
    if (!err) {
      const noOfItems = result;

      const itemsPerPage = templateInstance.itemsPerPage;

      const noOfPages = Math.ceil(noOfItems / itemsPerPage);

      templateInstance.noOfPages.set(noOfPages);
    }
  });
};

const calculatePaginationSteps = (templateInstance) => {
  const currentPage = templateInstance.page.get();
  const noOfPages = templateInstance.noOfPages.get();
  const delta = 2;
  const left = currentPage - delta;
  const right = currentPage + delta + 1;

  const buildPageObj = (val) => {
    const isActive = val === currentPage;
    let className = isActive ? 'active' : '';

    if (val === '...') {
      className = 'disabled';
    } else {
      className = `${className} paging-item`;
    }

    const obj = {
      classy: className,
      index: val,
    };

    return obj;
  };

  const pages = [];

  for (let i = 1; i <= noOfPages; i++) {
    if (i === 1 || i === noOfPages || (i >= left && i < right)) {
      pages.push(i);
    }
  }

  const pagesWithDots = [];
  let l;

  for (const i of pages) {
    if (l) {
      if (i - l === 2) {
        pagesWithDots.push(buildPageObj(l + 1));
      } else if (i - l !== 1) {
        pagesWithDots.push(buildPageObj('...'));
      }
    }
    pagesWithDots.push(buildPageObj(i));
    l = i;
  }

  return pagesWithDots;
};

Template.recentPurchaseOrdersTable.onCreated(function onCreated() {
  const template = this;

  template.query = new ReactiveVar();
  template.page = new ReactiveVar(1);
  template.noOfPages = new ReactiveVar(1);
  template.itemsPerPage = 10;

  template.autorun(() => {
    const query = template.query.get();
    const clientId = FlowRouter.getParam('clientId');
    const page = template.page.get();
    const limit = template.itemsPerPage;

    template.subscribe(Publications.purchaseOrders.posRequiringReceipting,
      {
        query, clientId, limit, page,
      },
    );

    updatePagination(query, clientId, template);
  });
});

Template.recentPurchaseOrdersTable.helpers({
  pos() {
    const selector = {
      $and: [{
        allLinesReceipted: false,
        'client._id': FlowRouter.getParam('clientId'),
      }],
    };

    const query = Template.instance().query.get();

    if (query && query.length) {
      const querySelector = {
        $or: [
          { identifier: { $regex: utils.escapeRegExp(query), $options: 'i' } },
        ],
      };

      selector.$and.push(querySelector);
    }

    return PurchaseOrders.find(selector, { sort: { receivedDate: -1 } }).fetch();
  },
  noPosToDisplay() {
    return PurchaseOrders.find().count() === 0;
  },
  pages() {
    return calculatePaginationSteps(Template.instance());
  },
});

Template.recentPurchaseOrdersTable.events({
  'input [name=query]': function onInput(event, templateInstance) {
    const queryStr = templateInstance.$(event.currentTarget).val();

    templateInstance.query.set(queryStr);
  },
  'click .paging-item': function onClick(event, templateInstance) {
    const page = parseInt(templateInstance.$(event.currentTarget).text(), 10);

    templateInstance.page.set(page);
  },
  'click .paging-left': function onClick(event, templateInstance) {
    const currentPage = templateInstance.page.get();
    const newPage = currentPage - 1;

    if (newPage > 0) {
      templateInstance.page.set(newPage);
    }
  },
  'click .paging-right': function onClick(event, templateInstance) {
    const currentPage = templateInstance.page.get();
    const newPage = currentPage + 1;
    const limit = templateInstance.noOfPages.get();

    if (newPage <= limit) {
      templateInstance.page.set(newPage);
    }
  },
});

Template.poRow.helpers({
  receivedDateFormatted() {
    const receivedDate = moment(Template.instance().data.receivedDate);
    return receivedDate.format(DISPLAY_DATETIME_FORMAT_COMPACT);
  },
  noOfLinesReceipted() {
    const receiptedLines = Template.currentData().linesReceipted || [];
    const noOfLines = Template.currentData().noOfLines;

    if (noOfLines) {
      return `${receiptedLines.length} / ${noOfLines}`;
    }

    return 0;
  },
  descriptionFormatted() {
    return this.description || '-';
  },
});
