<template name="addRequestDetails">
    <div class="ui container">

        <div class="ui vertical aligned two column grid">
            <div class="column">
                <div class="ui left aligned large header">
                    <img class="tiny ui image" src="/images/client-logos/{{currentClient.logo}}" style="height:68px;"/>
                    <div class="content">
                        {{currentClient.name}} - Add Order
                    </div>
                </div>
            </div>

        </div>
        <div class="ui container">
            <div class="ui middle aligned very relaxed stackable grid">
                <div class="twelve wide column">
                    <form class="ui form">

                        <div class="eight wide field">
                            <label>Ref No</label>
                            <div class="ui fluid search selection dropdown">
                                <input type="hidden" name="destination" value="">
                                <i class="dropdown icon"></i>
                                <div class="default text">Destination</div>
                                <div class="menu">
                                    {{#each destinations}}
                                    <div class="item" data-value="{{_id}}" data-text="{{name}}">
                                        {{name}}
                                    </div>
                                    {{/each}}
                                </div>
                            </div>
                        </div>

                        <div class="eight wide field">
                            <label>Destination:</label>
                            <div class="ui fluid search selection dropdown">
                                <input type="hidden" name="destination" value="">
                                <i class="dropdown icon"></i>
                                <div class="default text">Destination</div>
                                <div class="menu">
                                    {{#each destinations}}
                                    <div class="item" data-value="{{_id}}" data-text="{{name}}">
                                        {{name}}
                                    </div>
                                    {{/each}}
                                </div>
                            </div>
                        </div>

                        <div class="eight wide field">

                            <label>Due for Dispatch:</label>
                            <div class="ui calendar" id="dispatchDatepicker">
                                <div class="ui input left icon">
                                    <i class="calendar icon"></i>
                                    <input type="text" placeholder="Date/Time">
                                </div>
                            </div>
                        </div>

                        <div class="grouped fields">
                            <label>Repeat Every?</label>
                            <div class="inline fields">

                                <div class="field">
                                    <div class="ui radio checkbox">
                                        <input type="radio" name="frequency" checked="checked" value="">
                                        <label>Never</label>
                                    </div>
                                </div>
                                <div class="field">
                                    <div class="ui radio checkbox">
                                        <input type="radio" name="frequency" value="1">
                                        <label>1 Week</label>
                                    </div>
                                </div>
                                <div class="field">
                                    <div class="ui radio checkbox">
                                        <input type="radio" name="frequency" value="2">
                                        <label>2 Weeks</label>
                                    </div>
                                </div>
                                <div class="field">
                                    <div class="ui radio checkbox">
                                        <input type="radio" name="frequency" value="3">
                                        <label>3 Weeks</label>
                                    </div>
                                </div>
                                <div class="field">
                                    <div class="ui radio checkbox">
                                        <input type="radio" name="frequency" value="4">
                                        <label>4 weeks</label>
                                    </div>
                                </div>

                            </div>
                        </div>
                        <div class="eight wide field {{repeatedFieldState}}" id="untilField">

                            <label>Until</label>
                            <div class="ui calendar" id="untilDatepicker">
                                <div class="ui input left icon">
                                    <i class="calendar icon"></i>
                                    <input type="text" placeholder="Date/Time">
                                </div>
                            </div>
                        </div>

                    </form>
                </div>

            </div>
        </div>

        <div class="ui two column middle aligned very relaxed stackable grid">

            <div class="row">
                <div class="sixteen wide column">
                    <button class="ui big labeled icon button" id="backButton"><i class="angle left icon"></i>Back</button>
                    <button type="submit" class="ui big primary button" id="forwardStepButton">Submit</button>
                </div>
            </div>
        </div>
    </div>
</template>