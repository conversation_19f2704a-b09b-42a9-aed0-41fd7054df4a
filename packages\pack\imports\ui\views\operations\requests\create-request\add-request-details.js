import './add-request-details.html';

import { $ } from 'meteor/jquery';
import { CompanySiteProfiles } from '../../../../../api/company-site-profiles/company-site-profiles';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Meteor } from 'meteor/meteor';
import { Template } from 'meteor/templating';

Template.addRequestDetails.onCreated(function onCreated() {
  const template = this;
  template.repeated = new ReactiveVar(false);
});

Template.addRequestDetails.onRendered(function onRendered() {
  const template = this;
  template.$('.dropdown').dropdown();

  template.$('#dispatchDatepicker').calendar({
    type: 'datetime',
    ampm: false,
    formatter: {
      date: function formatDate(date, settings) {
        if (!date) return '';
        var day = date.getDate();
        var month = settings.text.monthsShort[date.getMonth()];
        var year = date.getFullYear();
        return day + ' ' + month + ' ' + year;
      },
    },
  });

  template.$('#untilDatepicker').calendar({
    type: 'date',
    ampm: false,
    formatter: {
      date: function formatDate(date, settings) {
        if (!date) return '';
        var day = date.getDate();
        var month = settings.text.monthsShort[date.getMonth()];
        var year = date.getFullYear();
        return day + ' ' + month + ' ' + year;
      },
    },
  });
});

Template.addRequestDetails.helpers({
  currentClient() {
    const siteProfile = CompanySiteProfiles.findOne();

    if (siteProfile) {
      const siteClients = siteProfile.configuration.clients;
      return _.find(siteClients, (client) => client._id === FlowRouter.getParam('clientId'));
    }
    return [];
  },

  destinations() {
    const siteProfile = CompanySiteProfiles.findOne();

    if (siteProfile) {
      const siteDestinations = siteProfile.configuration.destinations;
      return _.sortBy(siteDestinations, (destination) => destination.name);
    }
    return [];
  },
  repeatedFieldState() {
    const isRepeated = Template.instance().repeated.get();
    return isRepeated ? '' : 'disabled';
  },
});

Template.addRequestDetails.events({
  'click #forwardStepButton': function onClick(event, templateInstance) {
    event.preventDefault();

    const form = templateInstance.$('form:first');
    const destinationIds = form.find('[name=destination]').val(); // Comma separated list of destinationVendor ids.
    const siteProfile = CompanySiteProfiles.findOne();
    const dueForDispatch = form.find('#dispatchDatepicker').calendar('get date');
    const isRepeated = templateInstance.repeated.get();

    let daysBetweenOccurences = null;
    let repeatUntil = null;
    const numberOfWeeksBetweenOccurences = parseInt(templateInstance.$('input[name=frequency]:checked').val(), 10);
    if (isRepeated && numberOfWeeksBetweenOccurences) {
      daysBetweenOccurences = numberOfWeeksBetweenOccurences * 7;
      repeatUntil = form.find('#untilDatepicker').calendar('get date');
    }
    const clientId = FlowRouter.getParam('clientId');

    const requestProperties = {
      clientId,
      destinationIds,
      dueForDispatch,
      isRepeated,
      repeatUntil,
      daysBetweenOccurences,
    };

    Meteor.call(
      'requests.create',
      requestProperties,
      (error, storedRequest) => {
        if (!error) {
          FlowRouter.go('orders', { clientId: FlowRouter.getParam('clientId') });
        } else {
          console.log(error);
        }
      },
    );
  },
  'click #backButton': function onClick(event) {
    event.preventDefault();
    FlowRouter.go('orders', { clientId: FlowRouter.getParam('clientId') });
  },
  'click [name="frequency"]': function onClick(event, templateInstance) {
    const frequency = templateInstance.$('input[name=frequency]:checked').val();
    if (frequency) {
      templateInstance.repeated.set(true);
    } else {
      templateInstance.repeated.set(false);
      templateInstance.$('#untilDatepicker').calendar('clear');
    }
  },
});
