<template name="addRequestItems">

    <div class="row checklist ui container">
        <div class="ui grid">
            <div class="sixteen wide column">
                <div class="ui large header left floated">
            <img class="tiny ui image" src="/images/client-logos/{{currentRequest.client.logo}}" />
            <div class="content">
              {{currentRequest.client.name}} - {{currentRequest.destination.name}}
            </div>
      </div>
           
            <div class="row">
                <div class="column">
                    <button class="ui right floated large primary button" id="addRequestItem"> Add Item </button>
                </div>
            </div>

        </div>
        
</div>
           {{#if requestHasAnyItems}}
        <table class="ui basic very compact table" style="border: 0px;"> 
            <!-- Cannot use very basic table due to <PERSON> not being compatible with duplicate class names e.g. vry basic very compact -->
        
  <tbody>
    {{#each currentRequest.items}}
    	{{> requestItem}}
    {{/each}}
    

  </tbody>
</table>
{{else}}
                    <div class="ui center aligned disabled header">
                    <i class="info circle icon"></i>NO ITEMS CURRENTLY IN ORDER
                </div>
{{/if}}
    </div>

</template>
