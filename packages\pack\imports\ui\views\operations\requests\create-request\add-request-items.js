import './request-item';
import './add-request-items.html';
import { $ } from 'meteor/jquery';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Meteor } from 'meteor/meteor';
import { Requests } from '../../../../../api/requests/requests';
import { Template } from 'meteor/templating';

Template.addRequestItems.onCreated(function onCreated() {
  const template = this;
  const requestId = FlowRouter.getParam('requestId');

  template.autorun(() => template.subscribe('activeRequest', requestId));
});

Template.addRequestItems.onRendered(function onRendered() {
  const template = this;
  template.$('.dropdown').dropdown();
});

Template.addRequestItems.helpers({
  requestItems() {
    const requestInProgress = Requests.findOne();

    if (requestInProgress) {
      const requestItems = requestInProgress.items;
      return requestItems;
    }
    return [];
  },
  requestHasAnyItems() {
    const requestInProgress = Requests.findOne();

    if (requestInProgress) {
      const requestItems = requestInProgress.items;
      return requestItems.length > 0;
    }
    return false;
  },
  currentRequest() {
    const requestInProgress = Requests.findOne();
    return requestInProgress;
  },
});

Template.addRequestItems.events({
  'click #addRequestItem': function onClick() {
    FlowRouter.go('addRequestItem', { requestId: FlowRouter.getParam('requestId') });
  },
});
