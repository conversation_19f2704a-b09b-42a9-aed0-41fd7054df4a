import './request-item.html';
import { $ } from 'meteor/jquery';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import { Requests } from '../../../../../api/requests/requests';
import { Template } from 'meteor/templating';

Template.requestItem.onCreated(function onCreated() {
  const template = this;
  // Reactive Var for the item id
  template.itemId = new ReactiveVar();
  template.itemId.set('');
  // Reactive Var for pack listId
  template.picklist = new ReactiveVar();
  template.updateCcu = new ReactiveVar(false);
});

Template.requestItem.onRendered(function onRendered() {
  const template = this;
  template.autorun(function () {
    // Item specific stuff
    const templateData = Template.currentData();
    template.itemId.set(templateData._id);
    // Find packlist id from itemId
    const packlist = Requests.findOne({
      'items._id': templateData._id,
    });
    template.picklist.set(packlist._id);
  });
});

Template.requestItem.helpers({
  getCcusForItem() {
    const result = Requests.findOne({
      'items._id': this._id,
    });
    return result.assignedCcus;
  },
});

Template.requestItem.events({
  'click .packed.ui.button': function onClick(event, templateInstance) {
    event.preventDefault();
    templateInstance.$('.popup').show();
  },
  'click #ccuDisplayName': function onClick(event, templateInstance) {
    event.preventDefault();
    if ($(event.target).text() !== '') {
      Meteor.call(
        'requests.updatePackedStatus',
        Template.instance().itemId.get(),
        true,
        $(event.target).text(),
        (error) => {
          if (!error) {
            templateInstance.$('.popup').hide();
          } else {
            console.log(error);
          }
        },
      );
    }
  },
  'click #addCcu': function onClick(event, templateInstance) {
    event.preventDefault();
    FlowRouter.go('addCcu', {
      pickListId: templateInstance.picklist.get(),
    });
  },
  'click .cancel.button': function onClick(event, templateInstance) {
    event.preventDefault();
    templateInstance.$('.popup').hide();
  },
});
