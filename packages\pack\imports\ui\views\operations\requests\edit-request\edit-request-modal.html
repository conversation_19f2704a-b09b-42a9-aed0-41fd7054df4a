<template name="editRequestModal">
  <div class="ui modal edit-request">
    {{> editRequestModalContents }}
  </div>
</template>

<template name="editRequestModalContents">
  <i class="close icon"></i>
  <h1 class="header">Edit Packing Request / Material Collection</h1>
  <div class="content">
    <div class="ui container">
      <div class="ui form">
        <div class="field">
            <label>Ref No</label>
            <div class="ui input">
              <input type="hidden" name="requestId" value="">              
              <input type="text" name="packingRequestRefNo" placeholder="Enter ref no." readonly>
            </div>   
        </div>

        <div class="field">
          <label>Transport Company</label>
          <div class="receiptForm ui fluid search selection dropdown request-transport-company" id="transportCompany">
            <input type="hidden" name="transportCompany" value="" data-type="">
            <i class="dropdown icon"></i>
            <i class="remove icon"></i>
            <div class="default text">Transport Company</div>
            <div class="menu">
              {{#each materialTransportCompanies}}
              <div class="item" data-value="{{name}}">
                <span class="text">{{name}}</span>
              </div>
              {{/each}}
            </div>
          </div>
        </div>
       
        <div class="field">
          <label>Destination(s)</label>
          <div class="receiptForm ui fluid multiple selection dropdown request-destination" id="destination"> <!-- NOTE: Adding 'search' messes up dropdown close -->
            <input type="hidden" name="destination">
            <i class="dropdown icon"></i>
            <div class="default text">Select Destination(s)</div>
            <div class="menu">
              {{#each destinationOptions}}
              <div class="item" data-value="{{_id}}">
                <span class="text">{{name}}</span>
              </div>
              {{/each}}
            </div>
          </div>
        </div>
        
        <div class="field">
          <label>Collection Date Time</label>
          <div class="ui calendar" id="dispatchDatepicker">
            <div class="ui input left icon">
              <i class="calendar icon"></i>
              <input type="text" name="dispatchDate" placeholder="Date/Time">
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="actions">
    <div class="ui right labeled icon button {{canSubmit}} {{isSubmitting}}" id="submitRequest">
      Save Changes
      <i class="checkmark icon"></i>
    </div>
  </div>
</template>