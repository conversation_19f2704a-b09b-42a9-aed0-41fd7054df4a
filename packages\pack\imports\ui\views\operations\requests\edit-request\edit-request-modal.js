import './edit-request-modal.html';
import { EditRequestModalMethods } from './edit-request-modal.methods';
import { CreateRequest } from '../../../../../api/requests/commands/create-request';
import { UpdateRequest } from '../../../../../api/requests/commands/update-request';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { ReactiveVar } from 'meteor/reactive-var';
import { SiteProfileService } from '../../../../services/company-site-profiles/site-profile.service';
import { Template } from 'meteor/templating';
import { _ } from 'meteor/underscore';

const occurences = {
  Never: 'Never',
  Weekly: 'Weekly',
  Weekday: 'Weekday',
  Day: 'Day',
};

const occurenceFrequencyFormatter = (occurence) => {
  switch (occurence) {
    case occurences.Never:
      return 0;
    case occurences.Weekly:
      return 1;
    case occurences.Weekday:
      return 5;
    case occurences.Day:
      return 7;
    default:
      return 0;
  }
};

const updateDispatchStatus = (scheduledDateTime, templateInstance) => {
  templateInstance.dispatchEntered.set((scheduledDateTime !== null
    && scheduledDateTime !== undefined
    && scheduledDateTime !== ''));
};

const updateUntilStatus = (untilDateTime, templateInstance) => {
  templateInstance.untilEntered.set((untilDateTime !== null
    && untilDateTime !== undefined
    && untilDateTime !== ''));
};

const updateDisabledUntilDatePicker = (templateInstance) => {
  const form = templateInstance.$('form:first');
  const scheduledDateTime = form.find('#dispatchDatepicker').calendar('get date');
  const occurence = templateInstance.$('input[name=frequency]:checked').val();
  const frequency = occurenceFrequencyFormatter(occurence);

  if (frequency !== 0 && scheduledDateTime !== null) {
    templateInstance.repeated.set(true);
  } else {
    templateInstance.repeated.set(false);
    templateInstance.$('#untilDatepicker').calendar('clear');
  }
  updateDispatchStatus(scheduledDateTime, templateInstance);
};

Template.editRequestModalContents.onCreated(function onCreated() {
  const template = this;

  template.repeated = new ReactiveVar(false);
  template.destinationEntered = new ReactiveVar(false);
  template.dispatchEntered = new ReactiveVar(false);
  template.untilEntered = new ReactiveVar(false);
  template.isSubmitting = new ReactiveVar(false);
});

Template.editRequestModalContents.onRendered(function onRendered() {
  const template = this;

  template.$('.dropdown.request-destination').dropdown({
    onChange(value, text, $choice) {},
    selectOnKeydown: false,
  });

  template.$('.dropdown.request-transport-company').dropdown();
});

Template.editRequestModalContents.helpers({
  destinationOptions() {
    return _.sortBy(SiteProfileService.destinationVendors(), (x) => x.sortIndex);
  },
  materialTransportCompanies() {
    return _.sortBy(SiteProfileService.materialTransportCompanies(), (x) => x.sortIndex);
  },
  repeatedFieldState() {
    const isRepeated = Template.instance().repeated.get();
    return isRepeated ? '' : 'disabled';
  },
  occurences() {
    return occurences;
  },
  canSubmit() {
    const canSubmit = true;
    return canSubmit ? '' : 'disabled';
  },
  isSubmitting() {
    return Template.instance().isSubmitting.get() ? 'disabled loading' : '';
  },
});

Template.editRequestModalContents.events({
  'click .ui.dropdown .remove.icon': function onClick(event, templateInstance) {
    templateInstance.$(event.target).parent('.dropdown').dropdown('clear');
    event.stopPropagation();
    console.log('remove.icon clicked');
  },
  'click [name="frequency"]': function onClick(event, templateInstance) {
    updateDisabledUntilDatePicker(templateInstance);
  },
  'change [name=destination]': function onChange(event, templateInstance) {
    event.preventDefault();
    const destinationInput = event.target;
    const isEntered = destinationInput.value !== undefined && destinationInput.value !== '';
    templateInstance.destinationEntered.set(isEntered);
  },
  'click #submitRequest': function onClick(event, templateInstance) {
    event.preventDefault();
    templateInstance.isSubmitting.set(true);
    const clientId = FlowRouter.getParam('clientId');
    const requestId = templateInstance.$('input[name="requestId"]').val();
    const packingRequestRefNo = templateInstance.$('input[name="packingRequestRefNo"]').val();
    const transportCompany = templateInstance.$('input[name="transportCompany"]').val();
    const destinationIds = templateInstance.$('[name=destination]').val(); // This is CSV separatedlist of vendorDestinationIds.
    const scheduledDateTime = templateInstance.$('#dispatchDatepicker').calendar('get date');
    const isRepeated = templateInstance.repeated.get();
    const occurenceFrequency = templateInstance.$('input[name="frequency"]:checked').val();
    const occurenceInDaysPerWeek = occurenceFrequencyFormatter(occurenceFrequency);
    const repeatUntilDate = isRepeated && occurenceInDaysPerWeek !== 0 ?
      templateInstance.$('#untilDatepicker').calendar('get date') : null;

    UpdateRequest.call({
      id: requestId,
      clientId,
      destinationIds,
      scheduledDateTime,
      isRepeated,
      occurenceInDaysPerWeek,
      repeatUntilDate,
      packingRequestRefNo,
      transportCompany,
    }, () => {
      templateInstance.isSubmitting.set(false);
      // Clear any values set.
      templateInstance.$('input[name="packingRequestRefNo"]').val(null);
      EditRequestModalMethods.hide();
    });
  },
  'keyup input': function onKeyup(event, templateInstance) {
    if (event.keyCode === 13) { // RETURN OR ENTER KEY.
      const destinationEntered = templateInstance.destinationEntered.get();
      const dispatchEntered = templateInstance.dispatchEntered.get();
      const untilEntered = templateInstance.untilEntered.get();
      const isRepeated = templateInstance.repeated.get();
      const canSubmit = destinationEntered && dispatchEntered &&
        ((isRepeated && untilEntered) || (!isRepeated && !untilEntered));

      if (canSubmit) {
        templateInstance.$('#submitRequest').trigger('click');
      }
    }
  },
  'change [name=dispatchDate]': function onChange(event, templateInstance) {
    updateDisabledUntilDatePicker(templateInstance);
  },
  'change [name=untilDate]': function onChange(event, templateInstance) {
    const val = templateInstance.$(event.currentTarget).val();
    updateUntilStatus(val, templateInstance);
  },
});
