import { BaseModalMixin } from '../../../../modal/base-modal-mixin';

const calendarSettingsFormatter = {
  date: (date, settings) => {
    if (_.isUndefined(date)) return '';
    const day = (`0${date.getDate()}`).slice(-2); // zero pad.
    const month = settings.text.monthsShort[date.getMonth()];
    const year = date.getFullYear();
    return `${day}-${month}-${year}`;
  },
  time: (date) => {
    if (_.isUndefined(date)) return '';
    const hours = (`0${date.getHours()}`).slice(-2); // zero pad.
    const minutes = (`0${date.getMinutes()}`).slice(-2); // zero pad.
    return `${hours}:${minutes}`;
  },
};

export const EditRequestModalMethods = Object.assign({
  className: '.ui.modal.edit-request',

  init(id, request) {
    const context = $(this.className);

    $(this.className).modal({
      allowMultiple: false,
      onShow: () => {
        $('input[name="requestId"]', context).val(id);
        $('input[name="packingRequestRefNo"]', context).val(request.packingRequestRefNo);
        $('.request-transport-company', context).dropdown('set selected', [request.transportCompany]);
        $('.request-destination', context).dropdown('set selected', request.destinations.map((dest) => dest._id));
        $('#dispatchDatepicker', context).calendar('set date', request.scheduledDate, true, true);

        Meteor.setTimeout(() => {
          $('input[name="packingRequestRefNo"]', context).prop('readonly', false);
        }, 600);
      },
      onHidden: () => {
        $('input[name="requestId"]', context).val('');
        $('input[name="packingRequestRefNo"]', context).prop('readonly', true);
        $('input[name="packingRequestRefNo"]', context).val('');
        $('.request-transport-company', context).dropdown('restore defaults');
        $('.request-destination', context).dropdown('restore defaults');
        $('#dispatchDatepicker', context).calendar('clear');
        $('#untilDatepicker', context).calendar('clear');
        $('[name="frequency"][value="Never"]', context).prop('checked', true);
      },
    });

    $('#dispatchDatepicker', context).calendar({
      type: 'datetime',
      ampm: false,
      today: true,
      formatter: calendarSettingsFormatter,
      onChange: () => $('#dispatchDatepicker', context).find('[name=dispatchDate]').change(),
    });

    $('#untilDatepicker', context).calendar({
      type: 'date',
      ampm: false,
      today: true,
      formatter: calendarSettingsFormatter,
      startCalendar: $('#dispatchDatepicker', context),
      onChange: () => $('#untilDatepicker', context).find('[name=untilDate]').change(),
    });

    return this;
  },

}, BaseModalMixin);
