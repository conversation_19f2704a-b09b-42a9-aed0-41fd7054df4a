<template name="requestsListItem">
    <div class="pickListListItem card fluid" id={{_id}}>
        <div class="ui large attached header">
            <div class="ui grid">
            <div class="two column row">
                <div class="column">
                    <div>
                            Ref No: {{this.packingRequestRefNo}} 
                    </div>
                    <div>
                            {{this.transportCompany}} 
                    </div> 
                </div>
                <div class="right aligned column">
                    <button class="ui basic red icon button delete-collection {{#unless canDelete}}disabled{{/unless}}">
                        <i class="trash icon" style="margin: 0;"></i>
                    </button>
                    <button class="ui basic blue button edit-collection"> Edit </button>
                </div>
            </div>
            </div>
        </div>
        <div class="middle aligned content">
            <div class="ui grid">
                <div class="row">
                    <div class="ui two wide middle aligned column">
                        <div class="ui statistic">
                            <div class="label">
                                {{day}}
                            </div>
                            <div class="value">
                                {{dayOfMonth}}
                            </div>
                            <div class="label">
                                {{monthName}}
                            </div>
                            <div class="label">
                                {{time}}
                            </div>
                        </div>
                    </div>
                    <div class="ui five wide middle aligned column">
                        {{#each destinations}}
                            <h3 class="header center aligned">{{name}}</h3>
                        {{/each}}
                    </div>
                    <div class="ui three wide middle aligned column">
                        {{#if hasAssignedPackingUnits}}
                        <div class="ui large labels">
                            {{#each packingUnits}}
                                {{> assignedUnitLabel this}}
                            {{/each}}
                        </div>
                        {{else}}
                        <div class="meta">
                            No Assigned Packing Units
                        </div>
                        {{/if}}
                    </div>
                    <div class="ui six wide right aligned column">
                            <div class="teal ui statistic" style="margin-right:10px;">
                                <div class="value">
                                    {{noOfLoadedItems}}
                                </div>
                                <div class="label">
                                    TOTAL ITEMS PACKED
                                </div>
                            </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<template name="assignedUnitLabel">
    {{#if isClosed}}
        <div class="ui red basic label" style="width: 100%;">
            <i class="lock icon"></i>
            {{identifier}}
            <span class="right floated">({{packingUnitCount identifier}})</span>
        </div>
    {{else}}
        <div class="ui blue basic label" style="width: 100%;">
            <i class="{{typeIcon}} icon"></i>
            {{identifier}}
            <span class="right floated">({{packingUnitCount identifier}})</span>
        </div>
    {{/if}}
</template>
