import './requests-list-item.html';
import { AbdnRequestsPackingUnitTypes } from '../../../../api/abdn-requests/abdn-requests';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Meteor } from 'meteor/meteor';
import { Template } from 'meteor/templating';
import { moment } from 'meteor/momentjs:moment';

Template.requestsListItem.onCreated(function onCreated() {
  const template = this;
  template.totalItems = new ReactiveVar();
  template.totalItems.set(0);
  template.totalPackedItems = new ReactiveVar();
  template.totalPackedItems.set(0);
});

Template.requestsListItem.helpers({
  targetDispatchFormatted() {
    return moment(this.scheduledDate).format('DD/MM/YYYY HH:mm');
  },
  day() {
    return moment(this.scheduledDate).format('dddd');
  },
  dayOfMonth() {
    return moment(this.scheduledDate).format('DD');
  },
  monthName() {
    return moment(this.scheduledDate).format('MMMM');
  },
  year() {
    return moment(this.scheduledDate).format('YYYY');
  },
  time() {
    return moment(this.scheduledDate).format('HH:mm');
  },
  noOfLoadedItems() {
    let count = 0;
    if (this.packingUnits && this.packingUnits.length > 0) {
      _.each(this.packingUnits, (unit) => {
        count += unit.items.length;
      });
    }
    return count.toString();
  },
  hasAssignedPackingUnits() {
    return this.packingUnits && this.packingUnits.length > 0;
  },
  packingUnits() {
    return this.packingUnits;
  },
  canDelete() {
    let count = 0;
    if (this.packingUnits && this.packingUnits.length > 0) {
      _.each(this.packingUnits, (unit) => {
        count += unit.items.length;
      });
    }

    return count === 0;
  },
});

Template.requestsListItem.events({
  'click .pickListListItem .content': function onClick(event, templateInstance) {
    event.preventDefault();
    const requestId = this._id;
    FlowRouter.go('packlist', { clientId: FlowRouter.getParam('clientId'), requestId });
  },
});

Template.assignedUnitLabel.helpers({
  isClosed() {
    return this.isClosed;
  },
  typeIcon() {
    switch (this.unitType) {
      case AbdnRequestsPackingUnitTypes.CCU:
        return 'archive';
      case AbdnRequestsPackingUnitTypes.VEHICLE:
        return 'shipping';
      case AbdnRequestsPackingUnitTypes.CMR:
        return 'teal file outline';
      case AbdnRequestsPackingUnitTypes.BEGELEIDINGSBRIEF:
        return 'pink file outline';
      default:
        return 'archive';
    }
  },
  identifier() {
    return this.identifier;
  },
  packingUnitCount() {
    return this.items.length;
  },
});
