<template name='requestsOverview'>
  <div class="ui default-padding fluid container" style="padding-bottom: 5rem;">
    <div class="ui vertical aligned grid">
      <div class="row">
        <div class="ui four wide column">
          {{> clientHeader headerText="Pack" }}
        </div>
        <div class="ui eight wide column" style="padding-top: 1rem;">
          <form class="ui form">
            <div class="ui fields">
              <div class="ui eight wide field">
                <label>Filter</label>
                <div class="ui fluid icon input">
                  {{> filterHelperTextSearch filterHelper=filterHelper key="query"}}
                  <i class="search icon"></i>
                </div>
              </div>
              {{> filterHelperDatepicker filterHelper=filterHelper key="fromDate" defaultDate=initialFromDate cssClass="ui four wide field" }}
              {{> filterHelperDatepicker filterHelper=filterHelper key="toDate" defaultDate=initialToDate cssClass="ui four wide field" }}
            </div>

          </form>
        </div>
        <div class="ui four wide padded-top column">
          <button class="ui large primary right floated labeled icon basic button" id="addRequestButton">
            <i class="plus square icon"></i> Add Collection
            </button>
        </div>
      </div>
    </div>
    <div class="ui horizontal clearing divider"></div>
    {{#if Template.subscriptionsReady}} 
      {{> requestsList listOfRequests}} 
    {{else}}
      <div class="ui active text loader">Loading Requests...</div>
    {{/if}}
  </div>
  {{> addRequestModal }}
  {{> editRequestModal }}

  <div class = "ui grid" style="z-index: 6; position: fixed; width: 100%; bottom:1rem; left: 1rem; padding-left:3rem; background: #fafafa; ">
    <div class="row ui grid" style="padding-bottom: 0.3rem; padding-top: 0.3rem;" >
      <div class="eight wide column" style="padding-bottom: 0.5rem; padding-top: 0.5rem;">
        {{#unless portraitMode}}
          {{> filterHelperPagination filterHelper=filterHelper showPerPageDropdown=true truncate=5}}
        {{else}}
          {{> filterHelperPagination filterHelper=filterHelper showPerPageDropdown=true truncate=2}}
        {{/unless}}

    </div>
    </div>
  </div>

</template>