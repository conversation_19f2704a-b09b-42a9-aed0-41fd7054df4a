import './requests-list';
import './requests-overview.html';
import './add-request/add-request-modal';
import './edit-request/edit-request-modal';
import '../../../components/client-header';
import '../../../components/generic/filter-helper/filter-helper-pagination';
import '../../../components/generic/filter-helper/filter-helper-text-search';
import '../../../components/generic/filter-helper/filter-helper-datepicker';
import { AddRequestModalMethods } from './add-request/add-request-modal.methods';
import { EditRequestModalMethods } from './edit-request/edit-request-modal.methods';
import { DeleteRequest } from '../../../../api/requests/commands/delete-request';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Publications } from '../../../../api/api.publications/publications';
import { Requests } from '../../../../api/requests/requests';
import { SiteProfileService } from '../../../services/company-site-profiles/site-profile.service';
import { Template } from 'meteor/templating';
import { FilterHelper } from '../../../components/generic/filter-helper/filter-helper';
import { queryBuilder } from '../../../../api/requests/shared/active-requests-for-client-builder';
import moment from 'moment';

const openAddRequestModal = () => {
  AddRequestModalMethods
    .init()
    .show();
};

const openEditRequestModal = (id, request) => {
  EditRequestModalMethods
    .init(id, request)
    .show();
};

Template.requestsOverview.onCreated(function onCreated() {
  const template = this;

  // Subscription Filters
  template.fromDate = new ReactiveVar(null);
  template.toDate = new ReactiveVar(null);

  // filter instantiation
  template.filterHelper = new FilterHelper(
    template,
    Requests,
    Publications.requests.activeRequestsForClient,
  );

  template.filterHelper.addFilters([
    {
      key: 'query',
      filterSearchPlaceholder: 'Request Ref No or Destination...',
    },
    {
      key: 'fromDate',
      title: 'From Date',
    },
    {
      key: 'toDate',
      title: 'To Date',
    },
  ]);

  template.filterHelper.sorting().addSort('scheduledDate', -1);

  const initialQuery = () => {
    return query = {
      clientId: FlowRouter.getParam('clientId'),
    };
  };

  template.filterHelper.init(initialQuery, queryBuilder);
});

Template.requestsOverview.helpers({
  currentClient() {
    return SiteProfileService.currentClient();
  },
  listOfRequests() {
    return Template.instance().filterHelper.filtered();
  },
  filterHelper() {
    return Template.instance().filterHelper;
  },
  initialFromDate() {
    const now = moment().startOf('day');
    const defaultFromDate = now.toDate();
    return defaultFromDate;
  },
  initialToDate() {
    const defaultToDate = moment().add(3, 'd').endOf('day').toDate();
    return defaultToDate;
  },
});

Template.requestsOverview.events({
  'click #addRequestButton': function onClick() {
    openAddRequestModal();
  },
  'click .edit-collection': function onClick(event, templateInstance) {
    event.preventDefault();
    event.stopPropagation();

    const id = templateInstance.$(event.currentTarget).parents('.pickListListItem').attr('id');
    const request = Requests.findOne(id);
    openEditRequestModal(id, request);
  },
  'click .delete-collection': function onClick(event, templateInstance) {
    event.preventDefault();
    event.stopPropagation();

    const id = templateInstance.$(event.currentTarget).parents('.pickListListItem').attr('id');
    const request = Requests.findOne(id);

    if (window.confirm("Delete collection: '" + request.packingRequestRefNo + "'?")) {
      DeleteRequest.call({ id }, (err, response) => {});
    }
  },
});
