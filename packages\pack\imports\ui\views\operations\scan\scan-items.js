import './scan-items.html';
import './scan-item';
import { Session } from 'meteor/session';
import { Template } from 'meteor/templating';
import { Items } from '../../../../api/items/items';

Template.scanItems.onCreated(function onCreated() {
  let template = this;
  template.itemIdList = new ReactiveVar([]);

  template.autorun(() => {
    template.itemIdList.set(Template.currentData().itemIds);
    template.subscribe('items.fromListOfReceiptNos', { receiptNos: template.itemIdList.get() });
  });
});

Template.scanItems.onRendered(function onRendered() {

});

Template.scanItems.helpers({
  itemList() {
    let items = Items.find().fetch();
    return items;
  },
});

Template.scanItems.events({

});
