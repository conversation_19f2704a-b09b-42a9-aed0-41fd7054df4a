<template name="scanScreen">
    <div class="ui fluid container">
        <div class="ui padded grid">
            <div class="row">
                <div class="sixteen wide column">
                    <div class="ui raised segment">
                        <div class="content">
                            <div class="ui big fluid icon input focus">
                                <input type="text" placeholder="Scan or Enter Receipt No" name="receiptNo" id="receiptNo" autofocus>
                                <i class="barcode icon"></i>
                            </div>
                            {{#if noScannedItem}}
                            <h2 class="ui grey header">
                                <i class="barcode icon"></i>
                                <div class=" content">
                                    Awaiting Input...
                                </div>
                            </h2>
                        {{else}}
                            {{#if scannedItemIsInvalid}}
                                <h2 class="ui red header">
                                    <i class="ban icon"></i>
                                    <div class=" content">
                                        No such item
                                    </div>
                                </h2>
                            {{else}}
                                <h2 class="ui green header">
                                    <i class="check icon"></i>
                                    <div class=" content">
                                        {{scannedItem}} 
                                        {{#if itemAlreadyExists}}
                                            (Already Added)
                                        {{/if}}
                                    </div>
                                </h2>
                            {{/if}}
                        {{/if}}
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="sixteen wide column">
                    <div class="ui raised segment">
                        {{> scanItems itemIds=itemList}}
                    </div>
                </div>
            </div>
        </div>
        <div class="row" id="scan-progress-bottom">
                <div class="ui raised segment">
                    <button class="fluid ui big button" id="dispatch-btn">Dispatch</button>
                </div>
        </div>
    </div>
    
</template>