import './scan-screen.html';
import '../../../components/client-header';
import './scan-items';

import { Session } from 'meteor/session';
import { Template } from 'meteor/templating';

Template.scanScreen.onCreated(function onCreated() {
  const template = this;

  template.scannedItem = new ReactiveVar();
  template.scannedItemState = new ReactiveVar('none');
  template.itemList = new ReactiveVar([]);
  template.alreadyExists = new ReactiveVar(false);
});

Template.scanScreen.onRendered(function onRendered() {
});

Template.scanScreen.helpers({
  itemList() {
    return Template.instance().itemList.get();
  },
  scannedItem() {
    return Template.instance().scannedItem.get();
  },
  scannedItemIsInvalid() {
    var scannedItemState = Template.instance().scannedItemState.get();
    if (scannedItemState === 'invalid') {
      return true;
    }
    return false;
  },
  noScannedItem() {
    var scannedItemState = Template.instance().scannedItemState.get();
    if (scannedItemState === 'none') {
      return true;
    }
    return false;
  },
  itemAlreadyExists() {
    return Template.instance().alreadyExists.get();
  },
});

Template.scanScreen.events({
  'change #receiptNo': function onChange(event, templateInstance) {
    templateInstance.alreadyExists.set(false);
    event.preventDefault();
    const field = $(event.currentTarget);
    const val = field.val();
    const dto = {
      receiptNo: val,
    };
    Meteor.call('items.checkItemExists', dto, (err, result) => {
      if (!err && (result === true)) {
        templateInstance.scannedItem.set(val);
        templateInstance.scannedItemState.set('ok');
        field.val('');
        addToList(val, templateInstance);
      } else {
        templateInstance.scannedItemState.set('invalid');
      }
    });
  },
  'click .del-button': function onClick(event, templateInstance) {
    const btn = $(event.currentTarget);
    const id = btn.data().id;
    const currentIds = templateInstance.itemList.get();
    const newIds = currentIds.filter((item) => item !== id);
    templateInstance.itemList.set(newIds);
  },
  'click #dispatch-btn': function onClick() {

  },
});

const addToList = (item, templateInstance) => {
  const currentList = templateInstance.itemList.get();
  if (currentList.some((i) => i === item)) {
    templateInstance.alreadyExists.set(true);
  }
  currentList.push(item);
  templateInstance.itemList.set(currentList);
};
