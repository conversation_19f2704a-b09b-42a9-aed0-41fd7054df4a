import { Template } from 'meteor/templating';
import { Session } from 'meteor/session';
import './abdn-storage-location.html';

Template.abdnStorageLocation.helpers({
  isActiveLocation() {
    return Session.equals('store.selectedStorageLocationId', this.location._id);
  },
  locationCount() {
    const locnReport = this.locationCountsReport;
    if (locnReport) {
      const locnName = this.location.name;
      const locationCounts = locnReport.locationReportData.locationCounts;
      const countForThisLocation = locationCounts.find(
        function (el, indx, arr) { return el.location === locnName; })
        .itemCount;
      return countForThisLocation;
    }
    return '-';
  },
});

Template.abdnStorageLocation.events({
  'click .location-link-item': function onClick(event) {
    event.preventDefault();
    Session.set('store.selectedStorageLocationId', this.location._id);
  },
});
