<template name="abdnStoreItemsTable">
  <div class="fluid container">
    <table id="recentItemsTable" class="ui very compact selectable celled striped single line table">
      <thead>
        <tr>
          <th></th>
          <th>Receipt No</th>
          <th>PO No</th>
          <th>Delivery No</th>
          <th>Received</th>
          <th>Category</th>
          <th>Vendor</th>
          <th>Qty</th>
          <th>Package Type</th>
          <th>DG</th>
          <th>Description</th>
          {{#if viewStoredItems}}
            <th>Stored Location</th>
          {{/if}}
          <th></th>
        </tr>
      </thead>
      <tbody>
        {{#if noItemsToDisplay}}
        <tr>
          <td colspan="{{noOfColumns}}" style="text-align: center;"><i class="warning circle icon"></i>&ensp;No Items</td>
        </tr>
        {{/if}} 
        
        {{#each item in itemsToDisplay}}
          {{> abdnStoreItemRow item=item isSelectedItem=(isSelectedItem item._id) buttonsActive=canUpdateIndividually viewStoredItems=viewStoredItems }}
        {{/each}}
      
      </tbody>
      {{#if showMoreItemsButton}}
      <tfoot class="ui full width">
        <th colspan="{{noOfColumns}}">
          <div class="fluid ui basic blue icon button js-load-more-items" id="loadMoreItems">
            <i class="ellipsis vertical icon"></i> Load More
          </div>
        </th>
      </tfoot>
      {{/if}}
    </table>
  </div>
</template>

<template name="abdnStoreItemRow">
  <tr class="js-item-in-store-for-store" data-item-id="{{item._id}}">
    <td class="checkbox-cell">
      <div class="ui checkbox group-store-checkbox" data-item-id="{{item._id}}">
        <input type="checkbox">
      </div>
    </td>
    <td class="bold">{{item.receiptNo}}</td>
    <td>{{item.poNo}}</td>
    <td>{{item.deliveryNo}}</td>
    <td>{{receivedDateFormatted}}</td>
    <td>{{item.receiptCategory}}</td>
    <td>{{item.vendor}}</td>
    <td>{{item.quantity}}</td>
    <td>{{item.packageType}}</td>
    <td>{{{isDGFormatted}}}</td>
    <td>{{item.description}}</td>
    {{#if viewStoredItems}}
      <td>{{item.location}}</td>
    {{/if}}
    <td class="right aligned">
      {{> abdnStoreButton active=buttonsActive isStored=viewStoredItems}}
    </td>
  </tr>
</template>