import './abdn-store-items-table.html';
// Components used inside the template.
import './abdn-store-button';

import { ReactiveVar } from 'meteor/reactive-var';
import { Template } from 'meteor/templating';
import { moment } from 'meteor/momentjs:moment';

Template.abdnStoreItemsTable.onCreated(function onCreated() {
  const template = this;
  template.itemsNumberLoaded = new ReactiveVar(0);
});

Template.abdnStoreItemsTable.helpers({
  itemsToDisplay() {
    return Template.instance().data.items;
  },
  showMoreItemsButton() {
    const numItemsInTable = Template.instance().itemsNumberLoaded.get();
    return numItemsInTable >= Template.currentData().initialItemsLimit;
  },
  noItemsToDisplay() {
    return Template.instance().data.items.count() === 0;
  },
  isSelectedItem(itemId) {
    return Template.instance().data.selectedItems.includes(itemId);
  },
  viewStoredItems() {
    return Template.instance().data.storedItemsOnly;
  },
  noOfColumns() {
    // Extra column when viewing stored items
    return Template.instance().data.storedItemsOnly ? 13 : 12;
  },
});

Template.abdnStoreItemRow.onRendered(function onRendered() {
  const template = this;

  template.$('.checkbox').checkbox();

  if (template.data.isSelectedItem) {
    template.$('.checkbox').checkbox('set checked');
  }

  Waypoint.refreshAll();
});

Template.abdnStoreItemRow.helpers({
  receivedDateFormatted() {
    const receivedDate = moment(Template.instance().data.item.receivedDate);
    return receivedDate.format('DD-MMM-YY HH:mm');
  },
  isDGFormatted() {
    const isDG = Template.instance().data.isDangerousGoods;
    return isDG ?
      `<span class="dg-icon" data-tooltip="${Template.instance().data.item.dgClassification}"
      data-inverted=""><i class="large red warning circle icon" style="pointer-events:none;"></i></span>` :
      '&nbsp;&nbsp;&nbsp;&nbsp;-';
  },
});
