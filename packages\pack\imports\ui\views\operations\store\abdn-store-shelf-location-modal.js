import './abdn-store-shelf-location-modal.html';

import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import { SiteProfileService } from '../../../services/company-site-profiles/site-profile.service';
import { Template } from 'meteor/templating';

Template.adbnStoreShelfLocationModalContents.onCreated(function onCreated() {
  const template = this;
  template.isShelfLocationEntered = new ReactiveVar(false);
});

Template.adbnStoreShelfLocationModalContents.onRendered(function onRendered() {
  const template = this;

  template.$('.shelfLocationDropdown').dropdown({
    onChange(value) {
      if (value && value.length) {
        template.isShelfLocationEntered.set(true);
      } else {
        template.isShelfLocationEntered.set(false);
      }
    },
  });
});

Template.adbnStoreShelfLocationModalContents.helpers({
  canAssign() {
    const shelfLocationEntered = Template.instance().isShelfLocationEntered.get();
    return shelfLocationEntered ? '' : 'disabled';
  },
  storageLocations() {
    return SiteProfileService.storageLocations();
  },
});

Template.adbnStoreShelfLocationModalContents.events({
  'click .ui.dropdown .remove.icon': function onClick(event, templateInstance) {
    templateInstance.$(event.target).parent('.dropdown').dropdown('clear');
    event.stopPropagation();
  },
});

Template.adbnStoreShelfLocationModalContents.onDestroyed(function onDestroyed() {
  $('body .modals>.shelf-location.modal').remove();
});
