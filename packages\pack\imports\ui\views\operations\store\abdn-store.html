<template name="abdnStore">
  <div class="ui default-padding fluid container top-container" style="height:95%;">
    <div class="ui grid">
      <div class="row">
        <div class="three wide column">
          {{> clientHeader headerText="Store" }}
        </div>
        <div class="thirteen wide column">
          <div class="row">
            {{> storeReceiptCategoryFilter locationCounts=locationCountsReport storedItemsOnly=storedItemsOnly}}
          </div>
        </div>
      </div>
      <div class="horizontal divider"></div>
    </div>
    <div class="ui grid">
      <div class="two wide column left aligned">
        <h3 class="header">{{tableHeaderText}}</h3>
      </div>
      <div class="five wide right aligned column" style="padding-top: 1.5rem;">
        {{#if displayUpdateSelection}}
        <div class="ui toggle checkbox" id="viewSelectedItemsOnly">
          <input type="checkbox" name="viewSelectedItemsOnly" />
          <label>Selected Items Only</label>
        </div>
        {{/if}}
      </div>
      <div class="six wide column">
        <div id="storeFilter" class="ui fluid icon input js-query-text">
          <input class="form-control" type="text" placeholder="Filter..." autofocus>
          <i class="search icon"></i>
        </div>

      </div>
      <div class="ui three wide right aligned column" style="padding-top:0.7rem;">
        <div class="ui large shadowed buttons">
          <button class="ui bordered button not-stored-btn {{viewingNotStored}}">Not Stored</button>
          <div class="or"></div>
          <button class="ui bordered button stored-btn {{viewingStored}}">Stored</button>
        </div>
      </div>
    </div>
    <div class="ui fluid container" style="margin-top:10px;">
      {{> abdnStoreItemsTable items=items selectedItems=selectedItems canUpdateIndividually=canUpdateIndividually storedItemsOnly=storedItemsOnly}}
      <div class="infinite-scroll-element"></div>
    </div>
  </div>
  {{> abdnItemDetailsModal selectedItem=getSelectedItem}}
  {{> adbnStoreShelfLocationModal }}
</template>