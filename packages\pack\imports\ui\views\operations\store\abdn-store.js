import './abdn-store.html';
import './abdn-storage-location';
import './abdn-store-items-table';
import './abdn-store-shelf-location-modal';
import './store-receipt-category-filter';
import '../../../components/client-header';

import { AbdnItemDetailsModalMethods } from '../../../components/abdn-item-details-modal.methods';
import { AbdnStoreShelfLocationsModalMethods } from './abdn-store-shelf-locations-modal.methods';
import { CompanySiteProfiles } from '../../../../api/company-site-profiles/company-site-profiles';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Items } from '../../../../api/items/items';
import { Publications } from '../../../../api/api.publications/publications';
import { ReactiveVar } from 'meteor/reactive-var';
import { ReceiptCategoryFilterEventEmitter } from '../../../services/store/receipt-category-filter.event-emitter';
import { ReceiptTypes } from '../../../../api/items/receipt.types';
import { ReportTypes } from '../../../../api/reports/report.types';
import { Reports } from '../../../../api/reports/reports';
import { SiteProfileService } from '../../../services/company-site-profiles/site-profile.service';
import { StoreItems } from '../../../../api/items/commands/store-items';
import { Template } from 'meteor/templating';
import { UnStoreItems } from '../../../../api/items/commands/un-store-items';

const resetItemsSubLimit = (templateInstance) =>
  templateInstance.limit.set(templateInstance.initialItemsLimit);

const isMultiItemsSelected = (templateInstance) =>
  templateInstance
    .bulkUpdateSelectedItems
    .get()
    .length > 0;

const getItemsQuery = (templateInstance) => {
  const selector = {
    'client._id': FlowRouter.getParam('clientId'),
    siteId: SiteProfileService.currentSiteIdentifier(),
    isStored: templateInstance.storedItemsOnly.get(),
    isPacked: false,
    isDispatched: false,
    receiptType: ReceiptTypes.fullReceipt,
    receiptCategory: templateInstance.receiptCategory.get(),
  };

  return selector;
};

const storeItem = (items, shelfLocation = null, callback = null) => {
  StoreItems.call({ itemIds: items, location: shelfLocation }, callback);

  if (callback) {
    callback();
  }
};

const unStoreItems = (items, callback = null) => {
  UnStoreItems.call({ itemIds: items }, callback);
};

const clearBulkSelectedItems = (templateInstance) => {
  templateInstance.bulkUpdateSelectedItems.set([]);
  $('.group-store-checkbox').checkbox('uncheck');
};

const clearSelectedItem = (templateInstance) =>
  templateInstance.selectedItem.set(null);

const clearSelections = (templateInstance) => {
  clearBulkSelectedItems(templateInstance);
  clearSelectedItem(templateInstance);
  resetItemsSubLimit(templateInstance);
};

const openStoreModal = () =>
  AbdnStoreShelfLocationsModalMethods.show();

const updateReceiptCategoryAndOffshoreLocationFilters =
  (receiptCategory, offshoreLocation, templateInstance) => {
    templateInstance.receiptCategory.set(receiptCategory);
    templateInstance.offshoreLocation.set(offshoreLocation);
  };

const clearSelectedItemsOnReceiptCategoryChange = (receiptCategory, templateInstance) => {
  if (receiptCategory !== templateInstance.receiptCategory.get()) {
    clearBulkSelectedItems(templateInstance);
    templateInstance.selectedItemsOnly.set(false);
    resetItemsSubLimit(templateInstance);
  }
};

Template.abdnStore.onCreated(function onCreated() {
  const template = this;

  let siteProfile = CompanySiteProfiles.findOne();

  // Used for item details modal
  template.selectedItem = new ReactiveVar(null);

  // Item Subscription Filters
  template.receiptCategory = new ReactiveVar(null);
  template.offshoreLocation = new ReactiveVar(null);
  template.queryString = new ReactiveVar('');
  template.storedItemsOnly = new ReactiveVar(false);
  template.selectedItemsOnly = new ReactiveVar(false);
  template.moreItemsIncrement = 5;
  template.initialItemsLimit = 30;
  template.itemsNumberLoaded = new ReactiveVar(0);
  template.limit = new ReactiveVar(template.initialItemsLimit);

  template.bulkUpdateSelectedItems = new ReactiveVar([]);

  // Add receipt category changed event listener
  ReceiptCategoryFilterEventEmitter
    .onFilterChange((receiptCategory) =>
      clearSelectedItemsOnReceiptCategoryChange(receiptCategory, template))
    .onFilterChange((receiptCategory, offshoreLocation) =>
      updateReceiptCategoryAndOffshoreLocationFilters(receiptCategory, offshoreLocation, template));

  template.storageLocationIdsWithShelfLocations =
    siteProfile
      .configuration
      .storageLocations
      .filter((location) => location.hasShelfLocations === true)
      .map((location) => location._id);

  template.subscribe(Publications.reports.reportForClient,
    {
      clientId: FlowRouter.getParam('clientId'),
      reportType: ReportTypes.UNDELIVERED_ITEMS,
    },
  );

  // Initialise template's subscriptions
  template.autorun(() => {
    siteProfile = CompanySiteProfiles.findOne();
    const clientId = FlowRouter.getParam('clientId');

    const query = {
      clientId,
      query: template.queryString.get(),
      storedItemsOnly: template.storedItemsOnly.get(),
      receiptCategory: template.receiptCategory.get(),
      offshoreLocation: template.offshoreLocation.get(),
      limit: template.limit.get(),
    };

    const selectedItems = template.bulkUpdateSelectedItems.get();

    if (template.selectedItemsOnly.get() && selectedItems.length) {
      query.selectedItemsOnly = selectedItems;
    }

    template.subscription = template.subscribe(Publications.items.storedOrCanStore, query);
  });

  // Set receiptCategory filter to initial value
  template.isReceiptCategoryInitialised = false;
  template.autorun(() => {
    const countsReport = Reports.findOne();

    if (countsReport && !template.isReceiptCategoryInitialised) {
      const receiptCategory = SiteProfileService.receiptCategories()[0];
      template.receiptCategory.set(receiptCategory.name);
      template.isReceiptCategoryInitialised = true;
    }
  });
});

Template.abdnStore.onRendered(function onRendered() {
  const template = this;

  AbdnStoreShelfLocationsModalMethods
    .init((shelfLocation) => {
      const items = [];
      if (isMultiItemsSelected(template)) {
        items.push(...template.bulkUpdateSelectedItems.get());
      } else {
        items.push(template.selectedItem.get());
      }
      storeItem(items, shelfLocation, () => clearSelections(template));
    });

  const setupInfiniteScroll = () => {
    template.$('.infinite-scroll-element')
      .waypoint((direction) => {
        if (direction === 'down') {
          const limit = template.limit.get();
          template.limit.set(limit + template.moreItemsIncrement);
        }
      }, { context: '#main', offset: '100%' });
  };

  setupInfiniteScroll();
});

Template.abdnStore.onDestroyed(function onDestroyed() {
  ReceiptCategoryFilterEventEmitter.removeListeners();
});

Template.abdnStore.helpers({
  receiptCategories() {
    return SiteProfileService.receiptCategories();
  },
  offshoreLocations() {
    return SiteProfileService.offshoreLocations();
  },
  items() {
    const query = getItemsQuery(Template.instance());

    return Items.find(query, { sort: { receivedDate: -1 } });
  },
  selectedItems() {
    return Template.instance().bulkUpdateSelectedItems.get();
  },
  canUpdateIndividually() {
    return !isMultiItemsSelected(Template.instance());
  },
  currentClient() {
    const siteProfile = CompanySiteProfiles.findOne();

    if (siteProfile) {
      const siteClients = siteProfile.configuration.clients;
      return _.find(siteClients, (client) => client._id === FlowRouter.getParam('clientId'));
    }
    return [];
  },
  storageLocations() {
    const siteProfile = CompanySiteProfiles.findOne();

    if (siteProfile) {
      return _.sortBy(siteProfile.configuration.storageLocations, 'name');
    }
    return [];
  },
  locationCountsReport() {
    return Reports.findOne();
  },
  storedItemsOnly() {
    return Template.instance().storedItemsOnly.get();
  },
  selectedLocation() {
    return Template.instance().selectedStorageLocationName.get();
  },
  getSelectedItem() {
    return Template.instance().selectedItem.get();
  },
  displayUpdateSelection() {
    return isMultiItemsSelected(Template.instance());
  },
  updateSelectionText() {
    const viewingStored = Template.instance().storedItemsOnly.get();

    return viewingStored ? 'Unstore Items' : 'Store Items';
  },
  tableHeaderText() {
    const viewingStored = Template.instance().storedItemsOnly.get();
    const viewingSelectedItemsOnly = Template.instance().selectedItemsOnly.get();

    const header = `${viewingStored ? 'Stored' : 'Not Stored'} Items`;

    return viewingSelectedItemsOnly ? `Selected ${header}` : header;
  },
  viewingStored() {
    return Template.instance().storedItemsOnly.get() ? 'positive active' : '';
  },
  viewingNotStored() {
    return Template.instance().storedItemsOnly.get() ? '' : 'positive active';
  },
});

Template.abdnStore.events({
  'change #viewSelectedItemsOnly': function onChange(event, templateInstance) {
    const isChecked = templateInstance.$(event.target).is(':checked');
    templateInstance.selectedItemsOnly.set(isChecked);
  },
  'click .location-link-item': function onClick(event, templateInstance) {
    templateInstance.selectedStorageLocationName.set(this.location.name);
    const locationButton = $(event.target);
    templateInstance.$('#storageLocationsScroller').scrollTo(locationButton, { duration: 1000 });
  },
  'input .js-query-text': function onInput(event, templateInstance) {
    const searchText = event.target.value;
    templateInstance.queryString.set(searchText);
  },
  'click .js-load-more-items': function onClick(event, templateInstance) {
    const newLimit = templateInstance.limit.get() + templateInstance.moreItemsIncrement;
    templateInstance.limit.set(newLimit);
  },
  'click tr.js-item-in-store-for-store': function onClick(event, templateInstance) {
    const evTarget = event.target;
    if (!$(evTarget).hasClass('store-button')
      && !$(evTarget).hasClass('un-store-button')
      && !$(evTarget).hasClass('dg-icon')
      && !$(evTarget).is('label')
      && !$(evTarget).hasClass('checkbox-cell')
      && !$(evTarget).hasClass('checkbox')) {
      const item = this.item;
      templateInstance.selectedItem.set(item);

      AbdnItemDetailsModalMethods
        .init(item)
        .show();
    }
  },
  'click .store-button': function onClick(event, templateInstance) {
    const itemRow = templateInstance
      .$(event.currentTarget)
      .closest('.js-item-in-store-for-store');

    const selectedItems = templateInstance.bulkUpdateSelectedItems.get();

    if (!(selectedItems && selectedItems.length)) {
      templateInstance.selectedItem.set(itemRow.data('item-id'));
    }

    openStoreModal();
  },
  'click .un-store-button': function onClick(event, templateInstance) {
    const itemRow = templateInstance
      .$(event.currentTarget)
      .closest('.js-item-in-store-for-store');
    const selectedItems = templateInstance.bulkUpdateSelectedItems.get();

    if (selectedItems && selectedItems.length) {
      unStoreItems(selectedItems);
      clearSelections(templateInstance);
    } else {
      const id = itemRow.data('item-id');
      unStoreItems([id]);
    }
  },
  'click .group-store-checkbox': function onClick(event, templateInstance) {
    const selectedItems = templateInstance.bulkUpdateSelectedItems.get();
    const checkbox = $(event.currentTarget);
    const itemId = checkbox.data('item-id');

    if (checkbox.checkbox('is checked')) {
      templateInstance.bulkUpdateSelectedItems
        .set(selectedItems.concat([itemId]));
    } else {
      templateInstance.bulkUpdateSelectedItems
        .set(selectedItems.filter((id) => id !== itemId));
    }
  },
  'click .not-stored-btn': function onClick(event, templateInstance) {
    templateInstance.storedItemsOnly.set(false);
    clearSelections(templateInstance);
  },
  'click .stored-btn': function onClick(event, templateInstance) {
    templateInstance.storedItemsOnly.set(true);
    clearSelections(templateInstance);
  },
});
