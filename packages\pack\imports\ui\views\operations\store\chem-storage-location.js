import { Template } from 'meteor/templating';
import { Session } from 'meteor/session';
import './chem-storage-location.html';

Template.chemStorageLocation.helpers({
  isActiveLocation: function isActiveLocation() {
    return Session.equals('store.selectedStorageLocationId', this.location._id);
  },
  locationCount: function getLocationCount() {
    const locnReport = this.locationCountsReport;
    if (locnReport) {
      const locnName = this.location.name;
      const locationCounts = locnReport.locationReportData.locationCounts;
      const countForThisLocation = locationCounts.find(
        function (el, indx, arr) { return el.location === locnName; })
        .itemCount;
      return countForThisLocation;
    }
    return '-';
  },
});

Template.chemStorageLocation.events({
  'click .location-link-item': function handleClick(event) {
    event.preventDefault();
    Session.set('store.selectedStorageLocationId', this.location._id);
  },
});
