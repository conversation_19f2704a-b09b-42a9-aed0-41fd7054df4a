<template name="chemStoreItemsTable">
  <div class="fluid container">
    <table id="recentItemsTable" class="ui very compact selectable celled striped table portrait no-select tablet-hide-last-column break-word">
      <thead>
        <tr>
          <th></th>
          <th>Receipt No</th>
          <th>Qty</th>
          <th>Package Type</th>
          <th>Description</th>
          <th>CCU</th>
          <th>UnNo</th>
          <th>Class</th>
          <th>SubClass</th>
          <th>Client</th>
          <th>Offshore Location</th>  
          <th>Received Date</th>
          <th>Weight (kg)</th>    
          {{#if viewStoredItems}}
            <th>Stored Location</th>
          {{/if}}
          <th></th>
        </tr>
      </thead>
      <tbody>
        {{#if noItemsToDisplay}}
        <tr>
          <td class="no-results" colspan="{{noOfColumns}}" style="text-align: center;"><i class="warning circle icon"></i>&ensp;No Items</td>
        </tr>
        {{/if}} 
        
        {{#each item in itemsToDisplay}}
          {{> chemStoreItemRow item=item isSelectedItem=(isSelectedItem item._id) buttonsActive=canUpdateIndividually viewStoredItems=viewStoredItems filterHelper=filterHelper}}
        {{/each}}
      
      </tbody>
      {{#if showMoreItemsButton}}
      <tfoot class="ui full width">
        <th colspan="{{noOfColumns}}">
          <div class="fluid ui basic blue icon button js-load-more-items" id="loadMoreItems">
            <i class="ellipsis vertical icon"></i> Load More
          </div>
        </th>
      </tfoot>
      {{/if}}
    </table>
  </div>
</template>

<template name="chemStoreItemRow">
  <tr class="js-item-in-store-for-store" data-item-id="{{item._id}}">
    <td class="checkbox-cell">
      <div class="ui checkbox group-store-checkbox" data-item-id="{{item._id}}">
        <input type="checkbox">
      </div>
    </td>
    <td class="bold">{{ item.receiptNo}}</td>
    <td>{{item.quantity}}</td>
    <td>{{ item.packageType}}</td>
    <td class="break">{{ item.description}}</td>
    <td>{{ item.ccu}}</td>
    <td>{{ item.unNo}}</td>
    <td>{{ item.imoHazardClass}}</td>
    <td>{{ item.imoSubClass}}</td>
    <td>{{ item.offshoreClient}}</td>
    <td>{{ item.offshoreLocation}}</td>
    <td>{{ receivedDateFormatted}}</td>
    <td>{{ item.weightKg }}</td>
    {{#if viewStoredItems}}
      <td>{{ item.location}}</td>
    {{/if}}
    <td class="right aligned">
      {{> chemStoreButton active=buttonsActive isStored=viewStoredItems}}
    </td>
  </tr>
</template>