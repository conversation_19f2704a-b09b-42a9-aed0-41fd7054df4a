import './chem-store-items-table.html';
// Components used inside the template.
import './chem-store-button';

import { ReactiveVar } from 'meteor/reactive-var';
import { Template } from 'meteor/templating';
import { moment } from 'meteor/momentjs:moment';

Template.chemStoreItemsTable.onCreated(function onCreated() {
  const template = this;
  template.itemsNumberLoaded = new ReactiveVar(0);
});

Template.chemStoreItemsTable.helpers({
  itemsToDisplay() {
    return Template.currentData().items;
  },
  showMoreItemsButton() {
    const numItemsInTable = Template.instance().itemsNumberLoaded.get();
    return numItemsInTable >= Template.currentData().initialItemsLimit;
  },
  noItemsToDisplay() {
    return Template.instance().data.items.length === 0;
  },
  isSelectedItem(itemId) {
    return Template.instance().data.selectedItems.includes(itemId);
  },
  viewStoredItems() {
    return Template.instance().data.storedItemsOnly;
  },
  noOfColumns() {
    // Extra column when viewing stored items
    return Template.instance().data.storedItemsOnly ? 13 : 12;
  },
});

Template.chemStoreItemRow.onRendered(function onRendered() {
  const template = this;

  template.$('.checkbox').checkbox();

  if (template.data.isSelectedItem) {
    template.$('.checkbox').checkbox('set checked');
  }

  Waypoint.refreshAll();
});

Template.chemStoreItemRow.helpers({
  receivedDateFormatted() {
    const receivedDate = moment(Template.instance().data.item.receivedDate);
    return receivedDate.format('DD-MMM-YY HH:mm');
  },
  receiptedDateFormatted() {
    const receivedDate = moment(Template.instance().data.item.materialReceiptDateTime);
    return receivedDate.format('DD-MMM-YY HH:mm');
  },
  isDGFormatted() {
    const isDG = Template.instance().data.isDangerousGoods;
    return isDG ?
      `<span class="dg-icon" data-tooltip="${Template.instance().data.item.dgClassification}"
      data-inverted=""><i class="large red warning circle icon" style="pointer-events:none;"></i></span>` :
      '&nbsp;&nbsp;&nbsp;&nbsp;-';
  },
});
