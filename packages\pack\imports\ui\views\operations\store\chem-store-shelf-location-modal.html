<template name="chemStoreShelfLocationModal">
    <div class="ui tiny shelf-location modal">
      {{> chemStoreShelfLocationModalContents}}
    </div>
</template>

<template name="chemStoreShelfLocationModalContents">
    <i class="close icon"></i>
    <h1 class="header"><i class="ui write square icon"></i>Store at Location</h1>
    <div class="content">
      <div class="ui container">
        <div class="ui form">
          <div class="field">
            <label>Location</label>
            <div class="ui fluid search selection dropdown shelfLocationDropdown">
              <input type="hidden" name="shelfLocation" value="">
              <i class="dropdown icon"></i>
              <i class="remove icon"></i>
              <input type="text" class="search" autocomplete="off" tabindex="0" readonly>
              <div class="default text">Location...</div>
              <div class="menu">
                {{#each storageLocations}}
                <div class="item" data-value="{{name}}" data-text="{{name}}">
                  {{name}}
                </div>
                {{/each}}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="actions">
      <div class="ui ok right labeled icon button {{canAssign}}" id="setShelfLocation">
        OK
        <i class="checkmark icon"></i>
      </div>
    </div>
</template>