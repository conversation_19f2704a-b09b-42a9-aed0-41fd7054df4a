import './chem-store-shelf-location-modal.html';

import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import { SiteProfileService } from '../../../services/company-site-profiles/site-profile.service';
import { Template } from 'meteor/templating';

Template.chemStoreShelfLocationModalContents.onCreated(function onCreated() {
  const template = this;
  template.isShelfLocationEntered = new ReactiveVar(false);
});

Template.chemStoreShelfLocationModalContents.onRendered(function onRendered() {
  const template = this;

  template.$('.shelfLocationDropdown').dropdown({
    onChange(value) {
      if (value && value.length) {
        template.isShelfLocationEntered.set(true);
      } else {
        template.isShelfLocationEntered.set(false);
      }
    },
    // These stop auto keyboard on tablet
    onShow() {
      Meteor.setTimeout(() => {
        template.$("input[type='text']").prop('readonly', false);
      }, 500);
    },
    onHide() {
      template.$("input[type='text']").prop('readonly', true);
    },
  });
});

Template.chemStoreShelfLocationModalContents.helpers({
  canAssign() {
    const shelfLocationEntered = Template.instance().isShelfLocationEntered.get();
    return shelfLocationEntered ? '' : 'disabled';
  },
  storageLocations() {
    var locations = SiteProfileService.storageLocations(true);
    return locations;
  },
});

Template.chemStoreShelfLocationModalContents.events({
  'click .ui.dropdown .remove.icon': function onClick(event, templateInstance) {
    templateInstance.$(event.target).parent('.dropdown').dropdown('clear');
    event.stopPropagation();
  },
});

Template.chemStoreShelfLocationModalContents.onDestroyed(function onDestroyed() {
  $('body .modals>.shelf-location.modal').remove();
});
