import { BaseModalMixin } from '../../../modal/base-modal-mixin';

export const ChemStoreShelfLocationsModalMethods = Object.assign({
  className: '.ui.shelf-location.modal',

  init(onApproveCallback) { // onApproveCallback = (shelfLocation) => {...}
    $(this.className).modal({
      allowMultiple: false,
      onApprove: () => {
        if (onApproveCallback) {
          const shelfLocation = $('[name=shelfLocation]').val();
          onApproveCallback(shelfLocation);
        }
      },
      onHidden: () => {
        $('.shelfLocationDropdown').dropdown('restore defaults');
      },
    });
  },
}, BaseModalMixin);
