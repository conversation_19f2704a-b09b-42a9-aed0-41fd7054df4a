<template name="chemStore">
  <div class="ui default-padding fluid container top-container" style="height:95%;">
    <div class="ui grid">

      <div class="row">
        <div class="eight wide column">
          {{> clientHeader headerText="Store" }}
          
        </div>
        <div class="eight wide column right aligned">
          <div class="ui labeled icon basic blue button" id="csvExportNotStored">
            <i class="file outline icon"></i> Export Not Stored
          </div>
          <div class="ui labeled icon basic green button" id="csvExportStored">
            <i class="file outline icon"></i> Export Stored
          </div>
        </div>
      </div>

      <div class="{{#unless portraitMode}}seven{{else}}four{{/unless}} column row" style="padding: 0;">

        <div class="column">
            <h3 class="header">{{tableHeaderText}}</h3>
        </div>

        {{#unless portraitMode}}
        <div class="column">

        </div>
        {{/unless}}

        <div class="column" style="padding-top: 0.8rem;">
          {{> filterHelperTextSearch filterHelper=filterHelper key="query"}}
        </div>

        <div class="column" style="padding-top: 0.8rem;" id="locationFilter" >
          {{> filterHelperDropdown filterHelper=filterHelper key="location" disabled=viewingNotStored}}
        </div>
        
        {{> filterHelperDatepicker filterHelper=filterHelper placeholder="" key="fromDate" cssClass="column" }}
        {{> filterHelperDatepicker filterHelper=filterHelper placeholder="" key="toDate" cssClass="column" }}
        
        <div class="column" style="padding-top: 0.8rem;">
          <button id="clearButton" class="fluid ui labeled icon basic button"><i class="basic icon close"></i> Reset Filters </button>
        </div>

      </div>

    </div>

    <div class="ui grid">

      <div class="{{#unless portraitMode}}five{{else}}four{{/unless}}  column row">

        <div class="column">
            {{#if displayUpdateSelection}}	
            <div class="ui toggle checkbox" id="viewSelectedItemsOnly" style="margin-right:32px;">	
              <input type="checkbox" name="viewSelectedItemsOnly" />	
              <label>Selected Items Only</label>	
            </div>	
            {{/if}}	
        </div>

        {{#unless portraitMode}}
        <div class="column">

        </div>
        {{/unless}}

        <div class="column" id="installationFilter">
          {{> filterHelperDropdown filterHelper=filterHelper key="offshoreLocation" autofocus=false}}
        </div>

        <div class="column" id="clientFilter">
          {{> filterHelperDropdown filterHelper=filterHelper key="offshoreClient"}}
        </div>

        <div class="column">
          <div class="fluid ui buttons">
            <button class="ui button not-stored-btn {{viewingNotStored}}"> Not Stored </button>
            <div class="or"></div>
            <button class="ui button stored-btn {{viewingStored}}"> Stored </button>
          </div>
        </div>
      </div>


    </div>

    <div class="ui fluid container" style="margin-top:10px; margin-bottom: 150px; padding-bottom: 8rem;">
      {{> chemStoreItemsTable 
        items=highlighted
        selectedItems=selectedItems 
        canUpdateIndividually=canUpdateIndividually 
        storedItemsOnly=storedItemsOnly 
        filterHelper=filterHelper}}
    </div>
    <div class="ui fluid container">
        
    </div>
  </div>
  {{> materialItemDetailsModal selectedItem=getSelectedItem}}
  {{> chemStoreShelfLocationModal }}

  <div class="ui grid" style="position:fixed; width: 100%; bottom: 1rem; left: 1rem; padding-left:2rem; background: #fafafa;">

    <div class="row ui grid">
      <div class="eight wide column">
          {{#unless portraitMode}}
            {{> filterHelperPagination filterHelper=filterHelper showPerPageDropdown=true truncate=5}}
          {{else}}
            {{> filterHelperPagination filterHelper=filterHelper showPerPageDropdown=true truncate=2}}
          {{/unless}}

      </div>

      {{#unless itemSelected}}

        <div class="eight wide right column"> 
          <div class="ui right aligned grey header" style="font-weight:normal;">
              <h4 style="margin-bottom:0.8rem;"> Select items from the table above <i class="check square outline blue icon" style="margin-left:8px;"></i> </h4>
              <h4 class="desktop-hide" style="margin-top:0; margin-bottom:0;"> Press and hold an item to view details <i class="hand point up outline blue icon" style="margin-left:8px;"></i> </h4>
              <h4 class="tablet-hide" style="margin-top:0; margin-bottom:0;"> Click an item to view details <i class="mouse pointer icon outline blue icon" style="margin-left:8px;"></i> </h4>
          </div>
        </div>
  
      {{else}}

        <div class="four wide column">
          {{#unless multipleItemsSelected}}
            <div class="fluid ui basic big button details-button"> Details </div>
          {{/unless}}
        </div>


        {{#unless storedItemsOnly}}
        <div class="four wide column">
            <div class="fluid ui green basic big button store-button"> Store </div>
        </div>
        {{else}}
        <div class="four wide column">
            <div class="fluid ui red basic big button unstore-button"> Unstore </div>
        </div>
        {{/unless}}

      {{/unless}}
    </div>
  </div> 

</template>