import './chem-store.html';
import './chem-storage-location';
import './chem-store-items-table';
import './chem-store-shelf-location-modal';
import './store-receipt-category-filter';
import '../../../components/client-header';
import '../../../components/generic/filter-helper/filter-helper-dropdown';
import '../../../components/generic/filter-helper/filter-helper-text-search';
import '../../../components/generic/filter-helper/filter-helper-pagination';
import '../../../components/generic/filter-helper/filter-helper-datepicker';
import { MaterialItemDetailsModalMethods } from '../../../components/material-item-details-modal.methods';
import { ChemStoreShelfLocationsModalMethods } from './chem-store-shelf-locations-modal.methods';
import { CompanySiteProfiles } from '../../../../api/company-site-profiles/company-site-profiles';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Items } from '../../../../api/items/items';
import { Publications } from '../../../../api/api.publications/publications';
import { ReactiveVar } from 'meteor/reactive-var';
import { ReceiptCategoryFilterEventEmitter } from '../../../services/store/receipt-category-filter.event-emitter';
import { ReceiptTypes } from '../../../../api/items/receipt.types';
import { ReportTypes } from '../../../../api/reports/report.types';
import { Reports } from '../../../../api/reports/reports';
import { SiteProfileService } from '../../../services/company-site-profiles/site-profile.service';
import { StoreItems } from '../../../../api/items/commands/store-items';
import { Template } from 'meteor/templating';
import { UnStoreItems } from '../../../../api/items/commands/un-store-items';
import { FilterHelper } from '../../../components/generic/filter-helper/filter-helper';
import { queryBuilder } from '../../../../api/items/shared/stored-or-can-store';
import moment from 'moment';

const resetItemsSubLimit = (templateInstance) =>
  templateInstance.limit.set(templateInstance.initialItemsLimit);

const isMultiItemsSelected = (templateInstance) =>
  templateInstance
    .bulkUpdateSelectedItems
    .get()
    .length > 0;

const storeItem = (items, shelfLocation = null, callback = null) => {
  StoreItems.call({ itemIds: items, location: shelfLocation }, callback);

  if (callback) {
    callback();
  }
};

const unStoreItems = (items, callback = null) => {
  UnStoreItems.call({ itemIds: items }, callback);
};

const clearBulkSelectedItems = (templateInstance) => {
  templateInstance.bulkUpdateSelectedItems.set([]);
  $('.group-store-checkbox').checkbox('uncheck');
};

const clearSelectedItem = (templateInstance) =>
  templateInstance.selectedItem.set(null);

const clearSelections = (templateInstance) => {
  clearBulkSelectedItems(templateInstance);
  clearSelectedItem(templateInstance);
  resetItemsSubLimit(templateInstance);
};

const openStoreModal = () =>
  ChemStoreShelfLocationsModalMethods.show();

const updateReceiptCategoryAndOffshoreLocationFilters =
  (receiptCategory, offshoreLocation, templateInstance) => {
    templateInstance.receiptCategory.set(receiptCategory);
    console.log('set', offshoreLocation);
    templateInstance.offshoreLocation.set(offshoreLocation);
  };

const clearSelectedItemsOnReceiptCategoryChange = (receiptCategory, templateInstance) => {
  if (receiptCategory !== templateInstance.receiptCategory.get()) {
    clearBulkSelectedItems(templateInstance);
    templateInstance.selectedItemsOnly.set(false);
    resetItemsSubLimit(templateInstance);
  }
};

const getCsvReport = (template, serverMethod, reportPrefix, isStored = true) => {
  const today = new Date();
  const fileName = `${reportPrefix}_${moment(today).format('YYYY-MM-DD-HHmm')}.csv`;
  var filterObject = template.filterHelper
    .buildFilterObject(makeInitialQuery(template)(), template.filterHelper.filters);
  filterObject.storedItemsOnly = isStored;
  Meteor.call(serverMethod, filterObject, (err, fileContent) => {
    if (fileContent) {
      const a = document.createElement('a');
      document.body.appendChild(a);
      const blob = new Blob([fileContent], { type: 'text/plain;charset=utf-8' });
      const url = window.URL.createObjectURL(blob);
      a.href = url;
      a.download = fileName;
      a.click();
      window.URL.revokeObjectURL(url);
    }
  });
};

const highlightSelected = (template) => {
  const selection = template.bulkUpdateSelectedItems.get();
  template.$('tr.positive').removeClass('positive');
  selection.forEach((id) => {
    template.$(`[data-item-id='${id}']`).addClass('positive');
  });
};

function makeInitialQuery(template) {
  const clientId = FlowRouter.getParam('clientId');

  return initialQuery = () => {
    const query = {
      clientId: clientId,
      query: template.queryString.get(),
      storedItemsOnly: template.storedItemsOnly.get(),
      receiptCategory: template.receiptCategory.get(),
      limit: template.limit.get(),
    };

    const selectedItems = template.bulkUpdateSelectedItems.get();
    if (template.selectedItemsOnly.get() && selectedItems.length) {
      query.selectedItemsOnly = selectedItems;
    }

    return query;
  };
}

Template.chemStore.onCreated(function onCreated() {
  const template = this;

  template.isTouch = false;

  template.filterHelper = new FilterHelper(
    template,
    Items,
    Publications.items.storedOrCanStore,
  );

  // Used for item details modal
  template.selectedItem = new ReactiveVar(null);

  // Item Subscription Filters
  template.receiptCategory = new ReactiveVar(null);
  template.offshoreLocation = new ReactiveVar(null);
  template.queryString = new ReactiveVar('');
  template.storedItemsOnly = new ReactiveVar(false);
  template.selectedItemsOnly = new ReactiveVar(false);
  template.moreItemsIncrement = 5;
  template.initialItemsLimit = 30;
  template.itemsNumberLoaded = new ReactiveVar(0);
  template.limit = new ReactiveVar(template.initialItemsLimit);
  template.bulkUpdateSelectedItems = new ReactiveVar([]);

  let siteProfile = CompanySiteProfiles.findOne();

  const offshoreLocationsCallback = (err, result) => {
    template.filterHelper.setUniqueValues('offshoreLocation', result);
  };

  // Setup filters
  template.filterHelper.addFilters([
    {
      key: 'location',
      filterText: 'Storage Location',
      filterSearchPlaceholder: 'Search locations...',
      distinct: SiteProfileService.storageLocations(),
    },
    {
      key: 'offshoreClient',
      filterText: 'Offshore Client',
      filterSearchPlaceholder: 'Search clients...',
      distinct: SiteProfileService.offshoreClients(),
    },
    {
      key: 'offshoreLocation',
      filterText: 'Offshore Location',
      filterSearchPlaceholder: 'Search Offshore Location...',
      distinct: [],
    },
    {
      key: 'query',
      filterSearchPlaceholder: 'Filter...',
    },
    {
      key: 'fromDate',
      title: 'From Received Date',
      filterSearchPlaceholder: 'Search dates',
      distinct: [],
    },
    {
      key: 'toDate',
      title: 'To Received Date',
      filterSearchPlaceholder: 'Search dates',
      distinct: [],
    },
  ]);
  SiteProfileService.offshoreLocations(offshoreLocationsCallback);

  template.filterHelper.addHighlighter({
    receiptNo: true,
    packageType: true,
    ccu: true,
    offshoreClient: true,
    offshoreLocation: true,
    location: true,
    description: true,
  }, 'query');

  template.filterHelper.sorting().addSort('ccu', 1);

  template.filterHelper.init(makeInitialQuery(template), queryBuilder);

  // Add receipt category changed event listener
  ReceiptCategoryFilterEventEmitter
    .onFilterChange((receiptCategory) =>
      clearSelectedItemsOnReceiptCategoryChange(receiptCategory, template))
    .onFilterChange((receiptCategory, offshoreLocation) =>
      updateReceiptCategoryAndOffshoreLocationFilters(receiptCategory, offshoreLocation, template));

  template.storageLocationIdsWithShelfLocations =
    siteProfile
      .configuration
      .storageLocations
      .filter((location) => location.hasShelfLocations === true)
      .map((location) => location._id);

  template.subscribe(Publications.reports.reportForClient,
    {
      clientId: FlowRouter.getParam('clientId'),
      reportType: ReportTypes.UNDELIVERED_ITEMS,
    },
  );

  template.autorun(() => {
    Meteor.setTimeout(() => {
      highlightSelected(template);
    }, 300);
  });
});

Template.chemStore.onRendered(function onRendered() {
  const template = this;

  ChemStoreShelfLocationsModalMethods
    .init((shelfLocation) => {
      const items = [];
      if (isMultiItemsSelected(template)) {
        items.push(...template.bulkUpdateSelectedItems.get());
      } else {
        items.push(template.selectedItem.get());
      }
      storeItem(items, shelfLocation, () => clearSelections(template));
    });

  const setupInfiniteScroll = () => {
    template.$('.infinite-scroll-element')
      .waypoint((direction) => {
        if (direction === 'down') {
          const limit = template.limit.get();
          template.limit.set(limit + template.moreItemsIncrement);
        }
      }, { context: '#main', offset: '100%' });
  };

  setupInfiniteScroll();
});

Template.chemStore.onDestroyed(function onDestroyed() {
  this.filterHelper = null;
  ReceiptCategoryFilterEventEmitter.removeListeners();
});

Template.chemStore.helpers({
  filterHelper() {
    return Template.instance().filterHelper;
  },

  receiptCategories() {
    return SiteProfileService.receiptCategories();
  },
  items() {
    const template = Template.instance();
    const items = template.filterHelper.filtered();
    return items;
  },
  highlighted() {
    const template = Template.instance();
    const items = template.filterHelper.filtered(true);
    return items;
  },
  selectedItems() {
    return Template.instance().bulkUpdateSelectedItems.get();
  },
  canUpdateIndividually() {
    return !isMultiItemsSelected(Template.instance());
  },
  currentClient() {
    const siteProfile = CompanySiteProfiles.findOne();

    if (siteProfile) {
      const siteClients = siteProfile.configuration.clients;
      return _.find(siteClients, (client) => client._id === FlowRouter.getParam('clientId'));
    }
    return [];
  },
  locationCountsReport() {
    return Reports.findOne();
  },
  storedItemsOnly() {
    return Template.instance().storedItemsOnly.get();
  },
  selectedLocation() {
    return Template.instance().selectedStorageLocationName.get();
  },
  getSelectedItem() {
    return Template.instance().selectedItem.get();
  },
  itemSelected() {
    return (Template.instance().bulkUpdateSelectedItems.get().length > 0);
  },
  multipleItemsSelected() {
    return (Template.instance().bulkUpdateSelectedItems.get().length > 1);
  },
  displayUpdateSelection() {
    return isMultiItemsSelected(Template.instance());
  },
  updateSelectionText() {
    const viewingStored = Template.instance().storedItemsOnly.get();

    return viewingStored ? 'Unstore Items' : 'Store Items';
  },
  tableHeaderText() {
    const viewingStored = Template.instance().storedItemsOnly.get();
    const viewingSelectedItemsOnly = Template.instance().selectedItemsOnly.get();
    const header = `${viewingStored ? 'Stored' : 'Not Stored'} Items`;

    return viewingSelectedItemsOnly ? `Selected ${header}` : header;
  },
  viewingStored() {
    return Template.instance().storedItemsOnly.get() ? 'positive active' : '';
  },
  viewingNotStored() {
    return Template.instance().storedItemsOnly.get() ? '' : 'positive active';
  },
});

Template.chemStore.events({
  'touchstart tr.js-item-in-store-for-store': function handleTouchStart(event, templateInstance) {
    Meteor.clearTimeout(templateInstance.touchTimer);
    templateInstance.isTouch = true;
  },
  'touchend tr.js-item-in-store-for-store': function handleTouchEnd(event, templateInstance) {
    templateInstance.touchTimer = Meteor.setTimeout(() => {
      templateInstance.isTouch = false;
    }, 800);
  },
  'change #viewSelectedItemsOnly': function handleChange(event, templateInstance) {
    const isChecked = templateInstance.$(event.target).is(':checked');
    templateInstance.selectedItemsOnly.set(isChecked);
  },
  'click .location-link-item': function handleClick(event, templateInstance) {
    templateInstance.selectedStorageLocationName.set(this.location.name);
    const locationButton = $(event.target);
    templateInstance.$('#storageLocationsScroller').scrollTo(locationButton, { duration: 1000 });
  },
  'input .js-query-text': function handleClick(event, templateInstance) {
    const searchText = event.target.value;
    templateInstance.queryString.set(searchText);
  },
  'click .js-load-more-items': function handleClick(event, templateInstance) {
    const newLimit = templateInstance.limit.get() + templateInstance.moreItemsIncrement;
    templateInstance.limit.set(newLimit);
  },
  'click tr.js-item-in-store-for-store': function handleClick(event, templateInstance) {
    event.preventDefault();
    const evTarget = $(event.currentTarget);
    const target = $(event.target);
    const selectedItems = templateInstance.bulkUpdateSelectedItems.get();

    if (!templateInstance.isTouch) {
      // Show modal on click (except on checkbox or button)
      if (target.hasClass('button')) {
        event.stopPropagation();
        return;
      }

      if (!(target.hasClass('button')
          || target.hasClass('checkbox')
          || target.hasClass('checkbox-cell')
          || target.hasClass('button')
          || target.is('label'))) {
        const item = this.item;

        // NOTE this 'item' may have field values that are adapted to contain 'highlighting' html.
        // We need the original item without the HTML formatting for displaying on the Details modal.
        let itemWithoutHtmlHighlighting = Items.findOne(item._id); // Get it direct from Items collection

        templateInstance.selectedItem.set(itemWithoutHtmlHighlighting);

        // Show Material Details dialog with item details.
        MaterialItemDetailsModalMethods
          .init(itemWithoutHtmlHighlighting)
          .show();

        return;
      }
    }

    if (target.is('label')) {
      var checked = evTarget.find('.ui.checkbox').checkbox('is checked');
    } else {
      var checked = evTarget.find('.ui.checkbox').checkbox('toggle').checkbox('is checked');
    }

    const item = this.item;
    if (checked) {
      templateInstance.bulkUpdateSelectedItems
        .set(selectedItems.concat([item._id]));
    } else {
      templateInstance.bulkUpdateSelectedItems
        .set(selectedItems.filter((id) => id !== item._id));
    }

    highlightSelected(templateInstance);
  },
  'contextmenu tr.js-item-in-store-for-store': function handleLongPress(event, templateInstance) {
    // This works like a long press
    event.preventDefault();

    const evTarget = $(event.currentTarget);

    if (!$(evTarget).hasClass('store-button')
      && !$(evTarget).hasClass('un-store-button')
      && !$(evTarget).hasClass('dg-icon')
      && !$(evTarget).is('label')
      && !$(evTarget).hasClass('checkbox-cell')
      && !$(evTarget).hasClass('checkbox')) {
      const item = this.item;
      templateInstance.selectedItem.set(item);
      MaterialItemDetailsModalMethods
        .init(item)
        .show();
    }
  },
  'click .store-button': function handleClick(event, templateInstance) {
    const itemRow = templateInstance
      .$(event.currentTarget)
      .closest('.js-item-in-store-for-store');

    const selectedItems = templateInstance.bulkUpdateSelectedItems.get();

    if (!(selectedItems && selectedItems.length)) {
      templateInstance.selectedItem.set(itemRow.data('item-id'));
    }

    openStoreModal();
  },
  'click .unstore-button': function handleClick(event, templateInstance) {
    const itemRow = templateInstance
      .$(event.currentTarget)
      .closest('.js-item-in-store-for-store');
    const selectedItems = templateInstance.bulkUpdateSelectedItems.get();

    if (selectedItems && selectedItems.length) {
      unStoreItems(selectedItems);
      clearSelections(templateInstance);
    } else {
      const id = itemRow.data('item-id');
      unStoreItems([id]);
    }
  },
  'click .details-button': function handleClick(event, templateInstance) {
    if (templateInstance.bulkUpdateSelectedItems.get().length !== 1) {
      return;
    }

    const item = Items.findOne(templateInstance.bulkUpdateSelectedItems.get()[0]);
    templateInstance.selectedItem.set(item);
    MaterialItemDetailsModalMethods
      .init(item)
      .show();
  },
  'click .group-store-checkbox': function handleClick(event, templateInstance) {
    const selectedItems = templateInstance.bulkUpdateSelectedItems.get();
    const checkbox = $(event.currentTarget);
    const itemId = checkbox.data('item-id');

    if (checkbox.checkbox('is checked')) {
      templateInstance.bulkUpdateSelectedItems
        .set(selectedItems.concat([itemId]));
    } else {
      templateInstance.bulkUpdateSelectedItems
        .set(selectedItems.filter((id) => id !== itemId));
    }
  },
  'click .not-stored-btn': function handleClick(event, templateInstance) {
    templateInstance.filterHelper.setValue('location', '');
    templateInstance.storedItemsOnly.set(false);
    clearSelections(templateInstance);
  },
  'click .stored-btn': function handleClick(event, templateInstance) {
    templateInstance.storedItemsOnly.set(true);
    clearSelections(templateInstance);
  },
  'click #clearButton': function handleClick(event, templateInstance) {
    templateInstance.filterHelper.resetFilters();
  },
  'click #csvExportStored': function handleClick(event, templateInstance) {
    getCsvReport(templateInstance, 'items.getStoredOrCanStoreAsCsv', 'StoredMaterialItems', true);
  },
  'click #csvExportNotStored': function handleClick(event, templateInstance) {
    getCsvReport(templateInstance, 'items.getStoredOrCanStoreAsCsv', 'NotStoredMaterialItems', false);
  },
});
