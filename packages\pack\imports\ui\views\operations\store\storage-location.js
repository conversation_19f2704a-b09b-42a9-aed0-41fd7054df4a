import { Template } from 'meteor/templating';
import './storage-location.html';

Template.storageLocation.helpers({
  isActiveLocation() {
    const selectedCcuLocationId = Session.get('store.selectedStorageLocationId');
    return selectedCcuLocationId === this._id;
  },
});

Template.storageLocation.events({
  'click .location-link-item': function onClick(event, templateInstance) {
    event.preventDefault();
    Session.set('store.selectedStorageLocationId', this._id);
  },
});
