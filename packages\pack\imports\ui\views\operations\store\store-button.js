import { Meteor } from 'meteor/meteor';
import { Template } from 'meteor/templating';
import './store-button.html';

Template.storeButton.helpers({
  isItemStored() {
    return this.isStored;
  },
  storageLocationStoredIn() {
    return this.storedIn.name;
  },
});

Template.storeButton.events({
  'click .store-button': function onClick() {
    const selectedStorageLocationId = Session.get('store.selectedStorageLocationId');
    Meteor.call('stock.storeItem', this._id, selectedStorageLocationId, (error) => {});
  },
  'click .remove-from-store-button': function onClick() {
    const selectedStorageLocationId = Session.get('store.selectedStorageLocationId');
    Meteor.call('stock.removeItemFromStore', this._id, selectedStorageLocationId, (error) => {});
  },
});
