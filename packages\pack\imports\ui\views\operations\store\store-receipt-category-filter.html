<template name="storeReceiptCategoryFilter">
  <div class="ui fluid container" id="storeReceiptCategoryFilter">
    <div class="ui compact stackable grid">
      <div class="row"></div>
      <div class="ui sixteen wide column left aligned">
        <div class="ui compact large menu filter-menu">
          {{#each receiptCategories}}
          <a class="link item {{isActive @index}}" data-tab="{{name}}" data-id="{{name}}">
                {{name}}
                <span class="ui label" style="pointer-events:none;">
                    {{countForReceiptCategory name }}
                </span>
              </a> {{/each}}
        </div>
      </div>
    </div>
    <div class="clearfix"></div>
    {{#each receiptCategory in receiptCategories}}
    <div class="ui basic tab left aligned segment {{isActive @index}} offshore-location-tab" style="padding-right: 0; margin-top: 1rem;overflow-x: auto;" data-tab="{{receiptCategory.name}}">
      <div class="ui secondary tabular menu">
        {{#each offshoreLocationsWithItemsForReceiptCategory receiptCategory.name }}
        <a class="item platform-item {{isActive @index}}" data-id="{{name}}">
                {{name}}
                <span class="ui label" style="pointer-events:none;">
                  {{countForOffshoreLocation receiptCategory.name name }}
                </span>
              </a> {{/each}}
      </div>
    </div>
    {{/each}}
  </div>
</template>