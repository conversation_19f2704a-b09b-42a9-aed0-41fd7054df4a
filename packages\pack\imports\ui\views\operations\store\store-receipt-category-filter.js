import './store-receipt-category-filter.html';

import { ReceiptCategoryFilterEventEmitter } from '../../../services/store/receipt-category-filter.event-emitter';
import { SiteProfileService } from '../../../services/company-site-profiles/site-profile.service';
import { Template } from 'meteor/templating';

const allOffshoreLocationsTab = Object.freeze({
  _id: 'All',
  name: 'All',
});

const getCurrentReceiptCategoryFilter = (templateInstance) =>
  templateInstance.$('.filter-menu .item.active').data('id');

const getCurrentOffshoreLocationFilter = (templateInstance) => {
  const name = templateInstance
    .$('.offshore-location-tab.active .menu .platform-item.active')
    .data('id');

  return name === allOffshoreLocationsTab.name ? null : name;
};

const fireFiltersChanged = (templateInstance) =>
  ReceiptCategoryFilterEventEmitter
    .filterChanged(
      getCurrentReceiptCategoryFilter(templateInstance),
      getCurrentOffshoreLocationFilter(templateInstance));

const getCount = (report, receiptCategory, viewStoredOnly, offshoreLocation = null) => {
  const countsForReceiptCategory = report.receiptCategories[receiptCategory];
  const getCountToDisplay = (viewingStored) => (viewingStored ? 'storedCount' : 'notStoredCount');

  if (!countsForReceiptCategory) return 0;

  if (offshoreLocation) {
    if (offshoreLocation === allOffshoreLocationsTab.name) {
      return countsForReceiptCategory[getCountToDisplay(viewStoredOnly)];
    }

    const offshoreLocationReport = countsForReceiptCategory
      .offshoreLocations[offshoreLocation];

    if (!offshoreLocationReport) return 0;

    return offshoreLocationReport[getCountToDisplay(viewStoredOnly)];
  }

  return countsForReceiptCategory[getCountToDisplay(viewStoredOnly)];
};

const getViewStoredOnly = (templateInstance) =>
  templateInstance.data.storedItemsOnly;

Template.storeReceiptCategoryFilter.onRendered(function onRendered() {
  const template = this;

  template.$('#storeReceiptCategoryFilter .filter-menu .item').tab();
});

Template.storeReceiptCategoryFilter.helpers({
  receiptCategories() {
    return SiteProfileService.receiptCategories();
  },
  offshoreLocationsWithItemsForReceiptCategory(receiptCategory) {
    const report = Template.instance().data.locationCounts;
    const countToDisplay = getViewStoredOnly(Template.instance()) ? 'storedCount' : 'notStoredCount';

    if (!report) return [];

    const offshoreLocations = SiteProfileService.offshoreLocations();

    const filteredOffshoreLocations = offshoreLocations.reduce((prev, cur) => {
      if (report.receiptCategories[receiptCategory]
        && report.receiptCategories[receiptCategory].offshoreLocations[cur.name]
        && report.receiptCategories[receiptCategory].offshoreLocations[cur.name][countToDisplay] > 0) {
        return prev.concat([cur]);
      }

      return prev;
    }, [allOffshoreLocationsTab]);

    return filteredOffshoreLocations;
  },
  isActive(index) {
    return index === 0 ? 'active' : '';
  },
  countForReceiptCategory(receiptCategory) {
    const report = Template.instance().data.locationCounts;
    return report ? getCount(report, receiptCategory, getViewStoredOnly(Template.instance())) : 0;
  },
  countForOffshoreLocation(receiptCategory, offshoreLocation) {
    const report = Template.instance().data.locationCounts;
    return report ? getCount(report, receiptCategory, getViewStoredOnly(Template.instance()), offshoreLocation) : 0;
  },
});

Template.storeReceiptCategoryFilter.events({
  'click .filter-menu .item': function onClick(event, templateInstance) {
    fireFiltersChanged(templateInstance);
  },
  'click .platform-item': function onClick(event, templateInstance) {
    const item = templateInstance.$(event.currentTarget);

    if (!item.hasClass('active')) {
      item.siblings().removeClass('active');
      item.addClass('active');
      fireFiltersChanged(templateInstance);
    }
  },
});
