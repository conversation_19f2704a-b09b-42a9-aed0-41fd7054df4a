<template name="store">
  <div class="ui container">
    <div class="ui grid">
      <div class="row" style="margin-bottom: 10px;">
        <div class="six wide column">
          <div class="ui large header left floated">
            <img class="tiny ui image" src="/images/client-logos/{{currentClient.logo}}" style="height:68px;" />
            <div class="content">
              {{currentClient.name}} - Store
            </div>
          </div>
        </div>
        <div class="ten wide middle aligned column">
          <div class="ui big secondary menu right floated">
            {{#each storageLocations}} {{> storageLocation}} {{/each}}

          </div>

        </div>
      </div>
    </div>

<div class = "ui stackable two column grid">
           <div class = "column">
             {{>reactiveTableFilter id="storeFilter" label="Filter..."}}
           </div>
           <div class = "right aligned column" style="padding-top:24px;">

      <div class="ui toggle checkbox" id="viewStoredItemsOnly">
        <input type="checkbox" name="viewStoredItemsOnly">
        <label>View Stored Items</label>
      </div>
    </div>
             </div>



    {{>reactiveTable settings=tableSettings}}
  </div>
</template>