import './store.html';
import './storage-location';
import './store-button';

import { CompanySiteProfiles } from '../../../../api/company-site-profiles/company-site-profiles';
import { Session } from 'meteor/session';
import { Template } from 'meteor/templating';

Template.store.onCreated(function onCreated() {
  const template = this;

  template.viewStoredItems = new ReactiveVar(false);
  template.clientFilter = new ReactiveTable.Filter('clientFilter', ['client._id']);
  template.storageLocationFilter = new ReactiveTable.Filter('storageLocationFilter', ['storedIn._id']);
  template.isStoredFilter = new ReactiveTable.Filter('isStoredFilter', ['isStored']);

  template.autorun(() => {
    const clientId = FlowRouter.getParam('clientId');
    template.clientFilter.set(clientId);
  });

  template.autorun(() => {
    const storageLocationId = Session.get('store.selectedStorageLocationId');

    if (template.viewStoredItems.get()) {
      template.isStoredFilter.set('true');
      template.storageLocationFilter.set(storageLocationId);
    } else {
      template.isStoredFilter.set('false');
      template.storageLocationFilter.set('');
    }
  });

  const site = CompanySiteProfiles.findOne();
  Session.set('store.selectedStorageLocationId', site.configuration.storageLocations[0]._id);
});

Template.store.onRendered(function onRendered() {
  const template = this;

  template.$('#viewStoredItemsOnly').checkbox({
    onChange() {
      const viewStoredItemsOnly = template.$('#viewStoredItemsOnly').checkbox('is checked');
      template.viewStoredItems.set(viewStoredItemsOnly);
    },
  });
});

Template.store.helpers({
  currentClient() {
    const siteProfile = CompanySiteProfiles.findOne();

    if (siteProfile) {
      const siteClients = siteProfile.configuration.clients;
      return _.find(siteClients, (client) => client._id === FlowRouter.getParam('clientId'));
    }
    return [];
  },
  storageLocations() {
    const siteProfile = CompanySiteProfiles.findOne();

    if (siteProfile) {
      const siteStorageLocations = siteProfile.configuration.storageLocations;
      return siteStorageLocations;
    }
    return [];
  },
  tableSettings() {
    return {
      collection: 'stock.receiptHistory',
      class: 'ui very compact fixed single line table',
      id: 'storeTable',
      rowsPerPage: 50,
      filters: ['storeFilter', 'clientFilter', 'storageLocationFilter', 'isStoredFilter'],
      showFilter: false,
      fields: [
        { key: 'receiptedAt', label: 'Receipted',
          fn(value, object, key) {
            return moment(value).format('DD/MM/YY');
          },
        },
        { key: 'vendor.name', label: 'Vendor' },
        { key: 'poNo', label: 'Po Number', headerClass: 'two wide' },
        { key: 'quantity', label: 'Qty', headerClass: 'one wide' },
        { key: 'packingUnit.name', label: 'Unit', headerClass: 'one wide' },
        { key: 'description', label: 'Description', headerClass: 'six wide' },
        { key: 'storeButton', label: '', tmpl: Template.storeButton, headerClass() {
            return 'three wide';
        }, cellClass: 'right aligned' },
      ],
    };
  },
});
