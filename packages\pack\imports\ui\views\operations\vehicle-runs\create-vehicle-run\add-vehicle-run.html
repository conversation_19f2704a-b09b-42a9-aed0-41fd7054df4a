<template name="addVehicleRun">
    <div class="ui container">

        <div class="ui vertical aligned two column grid">
            <div class="column">
                <div class="ui left aligned large header">
                    <img class="tiny ui image" src="/images/client-logos/{{currentClient.logo}}"/>
                    <div class="content">
                        {{currentClient.name}} - Add Vehicle Run
                    </div>
                </div>
            </div>

        </div>
        <div class="ui container">
            <div class="ui middle aligned very relaxed stackable grid">
                <div class="twelve wide column">
                    <form class="ui form">

                        <div class="eight wide field">
                            <label>Destination:</label>
                            <div class="ui fluid search multiple selection dropdown">
                                <input type="hidden" name="destination" value="">
                                <i class="dropdown icon"></i>
                                <div class="default text">
                                    Enter Destination(s)
                                </div>
                                <div class="menu">
                                    {{#each destinations}}
                                        <div class="item" data-value="{{_id}}">
                                            {{deliveryDestination}}
                                        </div>
                                    {{/each}}
                                </div>
                            </div>
                        </div>
                        <div class="eight wide field">
                            <label>Dispatch Date & Time:</label>
                            <div class="ui calendar" id="dispatchDatepicker">
                                <div class="ui input left icon">
                                    <i class="calendar icon"></i>
                                    <input type="text" placeholder="Date/Time">
                                </div>
                            </div>
                        </div>
                        <div class="grouped fields">
                            <label>Repeat Vehicle Run Every?</label>
                            <div class="inline fields">
                              <div class="field">
                                  <div class="ui radio checkbox">
                                      <input type="radio" name="frequency" checked="checked" value="{{occurences.Weekday}}">
                                      <label>Weekday</label>
                                  </div>
                              </div>
                                <div class="field">
                                    <div class="ui radio checkbox">
                                        <input type="radio" name="frequency" value="{{occurences.Never}}">
                                        <label>Never</label>
                                    </div>
                                </div>
                                <div class="field">
                                    <div class="ui radio checkbox">
                                        <input type="radio" name="frequency" value="{{occurences.Weekly}}">
                                        <label>Week</label>
                                    </div>
                                </div>
                                <div class="field">
                                    <div class="ui radio checkbox">
                                        <input type="radio" name="frequency" value="{{occurences.Day}}">
                                        <label>Day</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="eight wide field {{repeatedFieldState}}" id="untilField">
                            <label>Until</label>
                            <div class="ui calendar" id="untilDatepicker">
                                <div class="ui input left icon">
                                    <i class="calendar icon"></i>
                                    <input type="text" placeholder="Date/Time">
                                </div>
                            </div>
                        </div>
                    </form>
                </div>

            </div>
        </div>
        <div class="ui two column middle aligned very relaxed stackable grid">
            <div class="row">
                <div class="sixteen wide column">
                    <button class="ui big labeled icon button" id="backButton"><i class="angle left icon"></i>Back</button>
                    <button type="submit" class="ui big primary button {{canSubmit}}" id="forwardStepButton">Submit</button>
                </div>
            </div>
        </div>

    </div>
</template>
