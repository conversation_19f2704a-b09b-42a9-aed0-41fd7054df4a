import './add-vehicle-run.html';

import { $ } from 'meteor/jquery';
import { CompanySiteProfiles } from '../../../../../api/company-site-profiles/company-site-profiles';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import { Template } from 'meteor/templating';
import { _ } from 'meteor/underscore';

// Setup Datepicker.
// https://github.com/mdehoog/Semantic-UI/blob/49178d6d0321357b959ff56f424ea1b3ed76a6ed/src/definitions/modules/calendar.js#L902-L1279
const calendarSettingsFormatter = {
  date: (date, settings) => {
    if (_.isUndefined(date)) return '';
    const day = (`0${date.getDate()}`).slice(-2); // zero pad.
    const month = settings.text.monthsShort[date.getMonth()];
    const year = date.getFullYear();
    return day + '-' + month + '-' + year;
  },
  time: (date, settings) => {
    if (_.isUndefined(date)) return '';
    const hours = (`0${date.getHours()}`).slice(-2); // zero pad.
    const minutes = (`0${date.getMinutes()}`).slice(-2); // zero pad.
    return hours + ':' + minutes;
  },
};

const occurences = {
  Never: 'Never',
  Weekly: 'Weekly',
  Weekday: 'Weekday',
  Day: 'Day',
};

const occurenceFrequencyFormatter = (occurence) => {
  switch (occurence) {
    case occurences.Never:
      return 0;
    case occurences.Weekly:
      return 1;
    case occurences.Weekday:
      return 5;
    case occurences.Day:
      return 7;
    default:
      return 0;
  }
};

const updateDispatchStatus = (scheduledDateTime, templateInstance) => {
  templateInstance.dispatchEntered.set((scheduledDateTime !== null
    && scheduledDateTime !== undefined
    && scheduledDateTime !== ''));
};

const updateUntilStatus = (untilDateTime, templateInstance) => {
  templateInstance.untilEntered.set((untilDateTime !== null
    && untilDateTime !== undefined
    && untilDateTime !== ''));
};

const updateDisabledUntilDatePicker = (templateInstance) => {
  const form = templateInstance.$('form:first');
  const scheduledDateTime = form.find('#dispatchDatepicker').calendar('get date');
  const occurence = templateInstance.$('input[name=frequency]:checked').val();
  const frequency = occurenceFrequencyFormatter(occurence);
  if (frequency !== 0 && scheduledDateTime !== null) {
    templateInstance.repeated.set(true);
  } else {
    templateInstance.repeated.set(false);
    templateInstance.$('#untilDatepicker').calendar('clear');
  }
  updateDispatchStatus(scheduledDateTime, templateInstance);
};

Template.addVehicleRun.onCreated(function onCreated() {
  const template = this;
  template.repeated = new ReactiveVar(false);
  template.destinationEntered = new ReactiveVar(false);
  template.dispatchEntered = new ReactiveVar(false);
  template.untilEntered = new ReactiveVar(false);
});

Template.addVehicleRun.onRendered(function onRendered() {
  const template = this;
  template.$('.dropdown').dropdown();

  template.$('#dispatchDatepicker').calendar({
    type: 'datetime',
    ampm: false,
    formatter: calendarSettingsFormatter,
    onChange: () => {
      updateDisabledUntilDatePicker(template);
    },
  });

  template.$('#untilDatepicker').calendar({
    type: 'date',
    ampm: false,
    formatter: calendarSettingsFormatter,
    startCalendar: template.$('#dispatchDatepicker'),
    onChange: (text) => {
      updateUntilStatus(text, template);
    },
  });
});

Template.addVehicleRun.helpers({
  currentClient() {
    const siteProfile = CompanySiteProfiles.findOne();

    if (siteProfile) {
      const siteClients = siteProfile.configuration.clients;
      return _.find(siteClients, (client) => client._id === FlowRouter.getParam('clientId'));
    }
    return [];
  },
  destinations() {
    const siteProfile = CompanySiteProfiles.findOne();

    if (siteProfile) {
      const siteDestinations = siteProfile.configuration.destinationOptions;
      // Removed sorting so 'Sellafield Lillyhall Receipts' comes first as its most used.
      return siteDestinations;
    }
    return [];
  },
  repeatedFieldState() {
    const isRepeated = Template.instance().repeated.get();
    return isRepeated ? '' : 'disabled';
  },
  occurences() {
    return occurences;
  },
  canSubmit() {
    const templateInstance = Template.instance();
    const destinationEntered = templateInstance.destinationEntered.get();
    const dispatchEntered = templateInstance.dispatchEntered.get();
    const untilEntered = templateInstance.untilEntered.get();
    const isRepeated = templateInstance.repeated.get();
    const canSubmit = destinationEntered
    && dispatchEntered
    && ((isRepeated && untilEntered) || (!isRepeated && !untilEntered));
    return canSubmit ? '' : 'disabled';
  },
});

Template.addVehicleRun.events({
  'click #forwardStepButton': function onClick(event, templateInstance) {
    event.preventDefault();
    const clientId = FlowRouter.getParam('clientId');
    const form = templateInstance.$('form:first');
    let destinationIds = form.find('[name=destination]').val();
    // Split destination ids into array of ids
    destinationIds = destinationIds.split(',');
    const scheduledDateTime = form.find('#dispatchDatepicker').calendar('get date');
    const isRepeated = templateInstance.repeated.get();

    const occurenceFrequency = templateInstance.$('input[name=frequency]:checked').val();
    const occurenceInDaysPerWeek = occurenceFrequencyFormatter(occurenceFrequency);
    const repeatUntil = isRepeated && occurenceInDaysPerWeek !== 0
      ? form.find('#untilDatepicker').calendar('get date')
      : '';

    const requestProperties = {
      destinationIds,
      clientId,
      scheduledDateTime,
      isRepeated,
      occurenceInDaysPerWeek,
      repeatUntil,
    };

    Meteor.call(
      'vehicleRuns.create',
      requestProperties,
      (error) => {
        if (!error) {
          FlowRouter.go('vehicleRuns', { clientId: FlowRouter.getParam('clientId') });
        } else {
          console.log(error);
        }
      },
    );
  },
  'click #backButton': function onClick(event) {
    event.preventDefault();
    FlowRouter.go('vehicleRuns', { clientId: FlowRouter.getParam('clientId') });
  },
  'click [name="frequency"]': function onClick(event, templateInstance) {
    updateDisabledUntilDatePicker(templateInstance);
  },
  'change [name=destination]': function onChange(event, templateInstance) {
    event.preventDefault();
    const destinationInput = event.target;
    const isEntered = destinationInput.value !== undefined && destinationInput.value !== '';
    templateInstance.destinationEntered.set(isEntered);
  },
});
