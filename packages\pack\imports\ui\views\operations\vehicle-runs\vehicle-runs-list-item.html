<template name="vehicleRunsListItem">
    <div class="pickListListItem card fluid" id={{_id}}>
        <div class="ui large attached header">
            Run {{this.runIdentifierStr}} - {{this.manifestRef}}
        </div>
        <div class="middle aligned content">
            <div class="ui grid">
                <div class="row">
                    <div class="ui two wide middle aligned column">
                        <div class="ui statistic">
                            <div class="label">
                                {{day}}
                            </div>
                            <div class="value">
                                {{dayOfMonth}}
                            </div>
                            <div class="label">
                                {{monthName}}
                            </div>
                            <div class="label">
                                {{time}}
                            </div>
                        </div>
                    </div>
                    <div class="ui five wide middle aligned column">
                        {{#each destinations}}
                            <h4 class="header center aligned">{{deliveryDestination}}</h4>
                        {{/each}}
                    </div>
                    <div class="ui three wide middle aligned column">
                        {{#if hasAssignedVehicle}}
                        <div class="ui large labels">
                            {{#if this.vehicle.isClosed}}
                            <div class="ui red basic label"><i class="lock icon"></i>{{this.vehicle.vehicleRegistration}}</div>
                            {{else}}
                            <div class="ui blue basic label"><i class="shipping icon"></i>{{this.vehicle.vehicleRegistration}}</div>
                            {{/if}}
                        </div>
                        {{else}}
                        <div class="meta">
                            No Assigned Vehicle
                        </div>
                        {{/if}}
                    </div>
                    <div class="ui six wide middle aligned column">
                        <div class="ui three statistics ">
                            <div class="teal ui statistic">
                                <div class="value">
                                    {{noOfLoadedItems}}
                                </div>
                                <div class="label">
                                    Loaded
                                </div>
                            </div>
                            <div class="teal ui statistic">
                                <div class="value">
                                    {{noOfDeliveredItems}}
                                </div>
                                <div class="label">
                                    Delivered
                                </div>
                            </div>
                            <div class="teal ui statistic">
                                <div class="value">
                                    {{noOfReturnedItems}}
                                </div>
                                <div class="label">
                                    Returned
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
