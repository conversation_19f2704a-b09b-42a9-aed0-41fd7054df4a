import './vehicle-runs-list-item.html';

import { $ } from 'meteor/jquery';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Items } from '../../../../api/items/items';
import { Meteor } from 'meteor/meteor';
import { Session } from 'meteor/session';
import { Template } from 'meteor/templating';
import { _ } from 'meteor/underscore';
import { moment } from 'meteor/momentjs:moment';

Template.vehicleRunsListItem.onCreated(function onCreated() {
  this.totalItems = new ReactiveVar();
  this.totalItems.set(0);
  this.totalPackedItems = new ReactiveVar();
  this.totalPackedItems.set(0);
});

Template.vehicleRunsListItem.onRendered(function onRendered() {
  const template = this;
  template.subscribe('activeVehicleRunAndItems', template.data._id);
});

Template.vehicleRunsListItem.helpers({
  targetDispatchFormatted() {
    return moment(this.scheduledDateTime).format('DD/MM/YYYY HH:mm');
  },
  day() {
    return moment(this.scheduledDateTime).format('dddd');
  },
  dayOfMonth() {
    return moment(this.scheduledDateTime).format('DD');
  },
  monthName() {
    return moment(this.scheduledDateTime).format('MMMM');
  },
  year() {
    return moment(this.scheduledDateTime).format('YYYY');
  },
  time() {
    return moment(this.scheduledDateTime).format('HH:mm');
  },
  noOfLoadedItems() {
    if (this.items && this.items.length > 0) {
      return this.items.length;
    }
    return '0';
  },
  hasAssignedVehicle() {
    return this.vehicle.vehicleRegistration !== null;
  },
  noOfDeliveredItems() {
    const items = Items.find({ _id: { $in: this.items } }).fetch();
    if (items) {
      return _.filter(items, (item) => {
        if (item.isDeliveredToDestination) return true;
        if (item.quantityDelivered) return true;  // Include partial deliveries
        return false;
      }).length;
    }
    return 0;
  },
  noOfReturnedItems() {
    const items = Items.find({ _id: { $in: this.items } }).fetch();
    if (items) {
      return _.where(items, { isDeliveryReturned: true }).length;
    }
    return 0;
  },
});

Template.vehicleRunsListItem.events({
  'click .pickListListItem': function onClick(event, templateInstance) {
    event.preventDefault();
    const vehicleRunId = this._id;
    FlowRouter.go(
      'vehicleLoadingList',
      { clientId: FlowRouter.getParam('clientId'), vehicleRunId },
    );
  },
});
