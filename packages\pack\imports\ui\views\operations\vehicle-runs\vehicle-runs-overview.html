<template name='vehicleRunsOverview'>
  <div class="ui container">
        <div class="ui vertical aligned grid">
            <div class="row">
                <div class="ui seven wide column">
                    <div class="ui left aligned large header">
                        <img class="tiny ui image" src="/images/client-logos/{{currentClient.logo}}"/>
                        <div class="content">
                            {{currentClient.name}} - Packing Requests
                        </div>
                    </div>
                </div>
                <div class="ui five wide column">
                    <div class="large ui radio buttons">
                        <button class="ui active button periodButtons" id="lastThreeDaysButton">Last 3 Days</button>
                        <button class="ui button periodButtons" id="nextSevenDaysButton">Next 7 Days</button>
                        <button class="ui button periodButtons" id="thisMonthButton">This Month</button>
                    </div>
                </div>
                <div class="ui four wide column">
                    <button class="ui large primary right floated button disabled" id="addOrderButton">
                        <i class="plus square icon"></i> Add Request
                    </button>
                </div>
            </div>
            <div class="row">
                <div class="ui sixteen wide column">
                    <div class="ui fluid big icon input">
                        <input type="text" placeholder="Search by Manifest No" id="searchByRef" autofocus>
                        <i class="search icon"></i>
                    </div>
                </div>
            </div>
        </div>

        {{#if Template.subscriptionsReady}}
            {{> vehicleRunsList listOfVehicleRuns}}
        {{else}}
            <div class="ui active text loader">Loading Vehicle Runs...</div>
        {{/if}}
    </div>
</template>
