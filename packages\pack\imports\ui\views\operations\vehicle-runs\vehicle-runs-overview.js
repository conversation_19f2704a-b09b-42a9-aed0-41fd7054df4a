import './vehicle-runs-list';
import './vehicle-runs-overview.html';

import { CompanySiteProfiles } from '../../../../api/company-site-profiles/company-site-profiles';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Template } from 'meteor/templating';
import { VehicleRuns } from '../../../../api/vehicle-runs/vehicle-runs';
import { _ } from 'meteor/underscore';

const resetButtons = (eventTarget, templateInstance) => {
  templateInstance.$('.periodButtons').removeClass('active');
  templateInstance.$(eventTarget).addClass('active');
};

const timePeriods = {
  LAST_THREE_DAYS: 'LAST_THREE_DAYS',
  NEXT_SEVEN_DAYS: 'NEXT_SEVEN_DAYS',
  THIS_MONTH: 'THIS_MONTH',
};

Template.vehicleRunsOverview.onCreated(function onCreated() {
  const template = this;
  template.timePeriod = new ReactiveVar(timePeriods.LAST_THREE_DAYS);
  template.refString = new ReactiveVar(null);

  template.autorun(() => {
    const timePeriod = template.timePeriod.get();
    const refString = template.refString.get();
    template.subscribe('siteVehicleRuns', timePeriod, refString);
  });
});

Template.vehicleRunsOverview.helpers({
  currentClient() {
    const siteProfile = CompanySiteProfiles.findOne();
    if (siteProfile) {
      const siteClients = siteProfile.configuration.clients;
      return _.find(siteClients, (client) => client._id === FlowRouter.getParam('clientId'));
    }
    return [];
  },
  listOfVehicleRuns() {
    const vehicleRuns = VehicleRuns.find({}, { sort: { scheduledDate: -1, scheduledDateTime: -1 } });
    return vehicleRuns;
  },
});

Template.vehicleRunsOverview.events({
  'click #addOrderButton': function onClick() {
    FlowRouter.go('addVehicleRun', { clientId: FlowRouter.getParam('clientId') });
  },
  'click #lastThreeDaysButton': function onClick(event, templateInstance) {
    resetButtons(event.target, templateInstance);
    templateInstance.timePeriod.set(timePeriods.LAST_THREE_DAYS);
  },
  'click #nextSevenDaysButton': function onClick(event, templateInstance) {
    resetButtons(event.target, templateInstance);
    templateInstance.timePeriod.set(timePeriods.NEXT_SEVEN_DAYS);
  },
  'click #thisMonthButton': function onClick(event, templateInstance) {
    resetButtons(event.target, templateInstance);
    templateInstance.timePeriod.set(timePeriods.THIS_MONTH);
  },
  'input #searchByRef': function onInput(event, templateInstance) {
    const searchText = event.target.value;
    templateInstance.refString.set(searchText);
  },
});
