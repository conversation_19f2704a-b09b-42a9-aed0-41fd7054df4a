<template name="changePrinterModal">
  <div class="ui small modal change-printer-modal">    
    {{> changePrinterModalContents }}
  </div>
</template>
<!-- This separation of templates is required so we can still use Meteor's template callbacks -->
<template name="changePrinterModalContents">

    <div class="header">
      Print Label
    </div>
    <div class="content">
      <div class="ui grid">
        <div class="row">
          <div class="four wide column">
            <i class="huge print icon"></i>
          </div>
          <div class="twelve wide column">
            <div class="fluid ui blue dropdown floating button change-printer-modal-dropdown">
                <span class="text">{{selectedPrinter}}</span>
                <div class="menu">
                  {{#each availablePrinters}}
                    <div class="item" data-value="{{_id}}">
                      {{name}}
                    </div>
                  {{/each}}
                </div>
              </div>
          </div>
        </div>
      </div>
    </div>
    <div class="actions">
      <button class="ui button cancel">Skip</button>
      <div class="ui positive right labeled icon button {{#unless isPrinterSelected}}disabled{{/unless}}">
        Print
        <i class="print icon"></i>
      </div>
    </div>
</template>