import './change-printer-modal.html';

import { PrintingService } from '../../services/printing/printing.service';
import { SiteProfileService } from '../../services/company-site-profiles/site-profile.service';
import { Template } from 'meteor/templating';

Template.changePrinterModalContents.onRendered(() => {
  // Dropdown is now initialised every time printer-selection modal is shown.
  // - attempt to fix reported issue where dropdown occasionally not enabling Print button.
});

Template.changePrinterModalContents.helpers({
  availablePrinters() {
    return SiteProfileService.printers();
  },
  selectedPrinter() {
    const currentActivePrinter = PrintingService.getUsersActivePrinter();

    if (currentActivePrinter) {
      return currentActivePrinter.name;
    }

    return 'Select Printer...';
  },
  isPrinterSelected() {
    const currentActivePrinter = PrintingService.getUsersActivePrinter();
    return (currentActivePrinter);
  },
});
