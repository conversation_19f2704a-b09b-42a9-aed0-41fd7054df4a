import { BaseModalMixin } from '../../modal/base-modal-mixin';
import { EventEmitter } from 'events';
import { PrintingService } from '../../services/printing/printing.service';

const eventEmitter = new EventEmitter();

const initialisePrinterDropdown = () => {
  $('.change-printer-modal-dropdown').dropdown({
    action: 'combo',
    showOnFocus: false,
    onChange: (value) => {
      PrintingService.setUsersActivePrinter(value);
    },
  });
};

export const ChangePrinterModalMethods = Object.assign({
  className: '.ui.modal.change-printer-modal',
  events: Object.freeze({
    printerSelected: 'printerSelected',
  }),

  init(approve, deny) {
    $(this.className).modal({
      allowMultiple: false,
      onApprove: approve,
      onDeny: deny,
      onShow: initialisePrinterDropdown,
    });

    return this;
  },

  throwPrinterSelected() {
    eventEmitter.emit(this.events.printerSelected);
  },

  onceOnPrinterSelected(callback) {
    const printerSelectedCallback = () => {
      callback();
      // Only perform this callback once for the event, then remove the listener
      eventEmitter.removeListener(this.events.printerSelected, printerSelectedCallback);
    };
    eventEmitter.on(this.events.printerSelected, printerSelectedCallback);
  },

  showAndWaitForPrinterSelection(callback) {
    this.init(
      () => callback(),
      () => {},
    ).show();
  },
}, BaseModalMixin);
