<template name="clientReceiptHistory">
    <div class="ui container" style="height: 100%;overflow:hidden;">
        <div class="ui vertical aligned two column grid">
            <div class="column">
                <div class="ui left aligned large header">
                    <img class="tiny ui image" src="/images/client-logos/{{currentClient.logo}}" style="height:68px;" />
                    <div class="content">
                        {{currentClient.name}} - Receipt History
                    </div>
                </div>
            </div>
        </div>
        <div class="ui container" style="height: 100%;overflow:hidden;margin-top:10px;">
          <div class = "ui stackable two column grid">
            <div class = "column" style="padding-top:3.2%;">
              {{>reactiveTableFilter id="historyFilter" label="Filter..."}}
            </div>
            <div class = "column">
              <form class = "ui form">
                <div class = "two fields">
                <div class = "eight wide field">
                  <label>From Date</label>
                  <div class = "ui calendar" id = "fromDatepicker">
                    <div class = "ui input left icon">
                      <i class = "calendar icon"></i>
                      <input type = "text" placeholder="Date" />
                    </div>
                  </div>
                </div>
                <div class = "eight wide field">
                  <label>To Date</label>
                  <div class = "ui calendar" id = "toDatepicker">
                    <div class = "ui input left icon">
                      <i class = "calendar icon"></i>
                      <input type = "text" placeholder="Date" id="toDate" />
                    </div>
                  </div>
                </div>
              </div>
              </form>
            </div>
          </div>
            {{>reactiveTable settings=getTableSettings}}
        </div>
    </div>
</template>
