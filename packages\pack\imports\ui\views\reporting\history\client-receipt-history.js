import './client-receipt-history.html';

import { CompanySiteProfiles } from '../../../../api/company-site-profiles/company-site-profiles';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Template } from 'meteor/templating';
import { _ } from 'meteor/underscore';

Template.clientReceiptHistory.onCreated(function onCreated() {
  const template = this;
  template.filter = new ReactiveTable.Filter('clientFilter', ['client._id']);
  template.fromDateFilter = new ReactiveTable.Filter('fromDateFilter', ['receiptedAt']);
  template.toDateFilter= new ReactiveTable.Filter('toDateFilter', ['receiptedAt']);
  template.fromDate = new ReactiveVar();
  template.toDate = new ReactiveVar();
  template.autorun(() => {
    const clientId = FlowRouter.getParam('clientId');
    template.filter.set(clientId);
  });
});

Template.clientReceiptHistory.onRendered(function onRendered() {
  const template = this;
  template.$('#fromDatepicker').calendar({
    type: 'date',
    endCalendar: template.$('#toDatepicker'),
    formatter: {
      date: function formatDate(date, settings) {
        if (_.isUndefined(date)) return '';
        template.fromDateFilter.set({ $gte: moment(date).utc().toDate() });
        var day = date.getDate();
        var month = settings.text.monthsShort[date.getMonth()];
        var year = date.getFullYear();
        return day + ' ' + month + ' ' + year;
      },
    },
  });
  template.$('#toDatepicker').calendar({
    type: 'date',
    startCalendar: template.$('#fromDatepicker'),
    formatter: {
      date: function formatDate(date, settings) {
        if (_.isUndefined(date)) return '';
        template.toDateFilter.set({ $lte: moment(date).utc().toDate() });
        var day = date.getDate();
        var month = settings.text.monthsShort[date.getMonth()];
        var year = date.getFullYear();
        return day + ' ' + month + ' ' + year;
      },
    },
  });
});


Template.clientReceiptHistory.helpers({
  getTableSettings() {
    return {
      collection: 'stock.receiptHistory',
      class: 'ui very compact table',
      id: 'historyTable',
      rowsPerPage: 50,
      filters: ['historyFilter', 'clientFilter', 'fromDateFilter', 'toDateFilter'],
      showFilter: false,
      fields: [
        { key: 'receiptedAt', label: 'Receipted Date',
          fn(value, object, key) {
            return moment(value).format('DD/MM/YYYY HH:mm');
          },
        },
        { key: 'receiptNo', label: 'Receipt No' },
        { key: 'isBackload', label: 'Backloaded Item',
          fn(value, object, key) {
            if (value) {
              return 'Yes';
            }
            return 'No';
          },
        },
        { key: 'vendor.name', label: 'Vendor',
          fn(value, object, key) {
            if (value) {
              return value;
            }
            return '-';
          },
        },
        { key: 'vendorDeliveryNo', label: 'Vendor Delivery No' },
        { key: 'poNo', label: 'Po Number' },
        { key: 'quantity', label: 'Packing Quantity' },
        { key: 'packingUnit.name', label: 'Packing Unit' },
        { key: 'contentQuantity', label: 'Content Quantity' },
        { key: 'contentUnit.name', label: 'Content Unit' },
        { key: 'description', label: 'Description' },
        { key: 'isPacked', label: 'Packed',
          fn(value, object, key) {
            if (value) {
              return 'Yes';
            }
            return 'No';
          },
        },
        { key: 'packedAt', label: 'Packed Date',
          fn(value, object, key) {
            if (value) {
              return moment(value).format('DD/MM/YYYY HH:mm');
            }
            return '-';
          },
        },
      ],
    };
  },
  currentClient() {
    const siteProfile = CompanySiteProfiles.findOne();

    if (siteProfile) {
      const siteClients = siteProfile.configuration.clients;
      return _.find(siteClients, client => client._id === FlowRouter.getParam('clientId'));
    }
    return [];
  },
});
