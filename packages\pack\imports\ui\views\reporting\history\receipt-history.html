<template name="receiptHistory">
  <div class="ui default-padding fluid container" style="height: 95%;">
    <div class="ui vertical aligned two column grid">
      <div class="column">

            {{> clientHeader headerText="Receipt Register"}}

      </div>
      <div class="right aligned column">
        <div class="ui labeled icon basic teal button" id="materialExportButton">
          <i class="file outline icon"></i> Export Materials
        </div>
        <div class="ui labeled icon basic blue button" id="cargoExportButton">
          <i class="file outline icon"></i> Export Cargo
        </div>
      </div>
    </div>
    <div class="ui horizontal divider"></div>
    <div class="ui fluid container" style="height: 95%;">
      <div class="ui stackable two column grid">
        <div class="six wide column" style="padding-top: 2.6rem;">
          {{>reactiveTableFilter id="historyFilter" label="Filter..."}}
        </div>
        <div class="six wide column">
          <form class="ui form">
            <div class="two fields">
              <div class="eight wide field">
                <label>From Date</label>
                <div class="ui calendar" id="fromDatepicker">
                  <div class="ui input left icon">
                    <i class="calendar icon"></i>
                    <input type="text" placeholder="Date" id="fromDate" />
                  </div>
                </div>
              </div>
              <div class="eight wide field">
                <label>To Date</label>
                <div class="ui calendar" id="toDatepicker">
                  <div class="ui input left icon">
                    <i class="calendar icon"></i>
                    <input type="text" placeholder="Date" id="toDate" />
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>

      </div>
      <div class="ui pointing secondary menu" id="tabs">
        <a class="active item" data-tab="receipts">Materials</a>
        <a class="item" data-tab="purchase-orders">Cargo Received</a>
      </div>
      <div class="ui active tab basic segment no-side-padding scroll-x" data-tab="receipts">
      {{>reactiveTable settings=getMaterialItemsTableSettings}}
      </div>
      <div class="ui tab basic segment no-side-padding scroll-x" data-tab="purchase-orders">
      {{>reactiveTable settings=getCargoItemsTableSettings}}
      </div>
      {{#unless getIsTableReady}}
      <div class="ui active text loader">
        Loading
      </div>
      {{/unless}}
    </div>
  </div>

  {{> materialItemDetailsModal selectedItem=getSelectedItem showAudit=true}}
  {{> cargoItemDetailsModal selectedCargoItem=getSelectedCargoItem}}

</template>