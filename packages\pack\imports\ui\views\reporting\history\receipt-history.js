import './receipt-history.html';
// Components used inside the template.
import '../../../components/client-header';
import '../../../components/material-item-details-modal';
import '../../../components/cargo/cargo-item-details-modal';
import { MaterialItemDetailsModalMethods } from '../../../components/material-item-details-modal.methods';
import { CompanySiteProfiles } from '../../../../api/company-site-profiles/company-site-profiles';
import { DISPLAY_DATE_FORMAT, DISPLAY_DATETIME_FORMAT } from '../../../../shared/lib/constants';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { CargoItemDetailsModalMethods } from '../../../components/cargo/cargo-item-details-modal.methods';
import { ReceiptTypes } from '../../../../api/items/receipt.types';
import { ReactiveTable } from 'meteor/fcallem:reactive-table-semantic';
import { Template } from 'meteor/templating';
import { _ } from 'meteor/underscore';
import moment from 'moment';

const datepickerDateFormatter = (date, settings) => {
  const day = date.getDate();
  const month = settings.text.monthsShort[date.getMonth()];
  const year = date.getFullYear();
  return `${day} ${month} ${year}`;
};

const tableSettings = (collection, tableClass, rowClass, fields) => ({
  collection,
  class: 'ui very compact selectable cell striped table no-select',
  id: tableClass,
  rowsPerPage: 50,
  filters: ['historyFilter', 'fromDateFilter', 'toDateFilter', 'clientFilter'],
  showFilter: false,
  useFontAwesome: true,
  rowClass,
  fields,
});

const getCsvReport = (template, serverMethod, reportPrefix) => {
  const fromDate = template.fromDateFilter.get().$gte;
  const toDate = template.toDateFilter.get().$lte;

  const fromDateStr = moment(fromDate).format('YYYY-MM-DD');
  const toDateStr = moment(toDate).format('YYYY-MM-DD');

  const fileName = `${reportPrefix}_${fromDateStr}-to-${toDateStr}.csv`;

  const params = {
    reportStartDateStr: fromDateStr,
    reportEndDateStr: toDateStr,
    dateToFilterBy: 'receivedDate',
  };

  Meteor.call(serverMethod, params, (err, fileContent) => {
    if (fileContent) {
      const a = document.createElement('a');
      document.body.appendChild(a);
      const blob = new Blob([fileContent], { type: 'text/plain;charset=utf-8' });
      const url = window.URL.createObjectURL(blob);
      a.href = url;
      a.download = fileName;
      a.click();
      window.URL.revokeObjectURL(url);
    }
  });
};

Template.receiptHistory.onCreated(function onCreated() {
  const template = this;
  /* Setup Reactivte Table Filters */
  template.filter = new ReactiveTable.Filter('clientFilter', ['client._id']);
  template.fromDateFilter = new ReactiveTable.Filter('fromDateFilter', ['receivedDate']);
  template.toDateFilter = new ReactiveTable.Filter('toDateFilter', ['receivedDate']);

  /* Template Reactive Vars */
  template.selectedItem = new ReactiveVar(null);
  template.selectedPo = new ReactiveVar(null);
  template.selectedCargoItem = new ReactiveVar(null);
  template.fromDate = new ReactiveVar();
  template.toDate = new ReactiveVar();
  template.tableFields = new ReactiveVar();
  template.isTableReady = new ReactiveVar(false);

  /* AutoRun Block Listens for Changes to Client Id */
  template.autorun(() => {
    const clientId = FlowRouter.getParam('clientId');
    template.filter.set(clientId);
  });
});

Template.receiptHistory.onRendered(function onRendered() {
  const template = this;
  template.$('#fromDatepicker').calendar({
    type: 'date',
    endCalendar: template.$('#toDatepicker'),
    formatter: {
      date: (date, settings) => {
        if (_.isUndefined(date)) return '';
        template.fromDateFilter.set({ $gte: moment(date).startOf('d').toDate() });
        return datepickerDateFormatter(date, settings);
      },
    },
  });
  template.$('#toDatepicker').calendar({
    type: 'date',
    startCalendar: template.$('#fromDatepicker'),
    formatter: {
      date: (date, settings) => {
        if (_.isUndefined(date)) return '';
        template.toDateFilter.set({ $lte: moment(date).endOf('d').toDate() });
        return datepickerDateFormatter(date, settings);
      },
    },
  });

  template.$('input.reactive-table-input:first').focus();

  const today = moment(new Date()).subtract(1, 'months').startOf('month');
  template.$('#fromDatepicker').calendar('set date', today.toDate());

  const checkForTableInterval = Meteor.setInterval(() => {
    if (template.view.isDestroyed !== false) {
      Meteor.clearInterval(checkForTableInterval);
      return;
    }

    if (template.$('.reactive-table').length) {
      template.isTableReady.set(true);
      Meteor.clearInterval(checkForTableInterval);
    }
  }, 100);

  template.$('#tabs .item').tab();
});

Template.receiptHistory.helpers({
  getMaterialItemsTableSettings() {
    const dateFormatStr = DISPLAY_DATE_FORMAT;
    const dateTimeFormatStr = DISPLAY_DATETIME_FORMAT;
    const rowClass = (item) => (item.isPacked ? 'warning' : '');
    const fields = [
      {
        key: 'receiptNo',
        label: 'Receipt No.',
        sortOrder: 0,
        sortDirection: 'descending',
        cellClass: 'bold whitespace-nowrap',
      },
      {
        key: 'quantity',
        label: 'Qty',
      },
      {
        key: 'packageType',
        label: 'Package Type',
        cellClass: 'whitespace-nowrap',
        fn: (value, object) => {
          if (value) {
            return value;
          }
          return '-';
        },
      },
      {
        key: 'weightKg',
        label: 'Wt(Kg)',
        cellClass: 'whitespace-nowrap',
        fn: (value, object) => {
          if (value) {
            return value;
          }
          return '-';
        },
      },
      {
        key: 'description',
        label: 'Description',
        cellClass: 'whitespace-nowrap',
        fn: (value, object) => {
          if (value) {
            return _.unescape(value)
              .replace(/&GT;/ig, '>')
              .replace(/&LT;/ig, '<')
              .replace(/&gt/ig, '>')
              .replace(/&lt/ig, '<');
          }
          return '-';
        },
      },
      {
        key: 'ccu',
        label: 'CCU',
        cellClass: 'whitespace-nowrap',
      },
      {
        key: 'receiptType',
        label: 'Receipt Stage',
        cellClass: 'whitespace-nowrap',
        fn: (value, object) => {
          if (value == ReceiptTypes.chemReceipt) {
            return 'Receipted';
          }
          return 'Not Removed';
        },
      },
      {
        key: 'receivedDate',
        label: 'CCU Received Date',
        cellClass: 'whitespace-nowrap',
        fn: (value) => (moment(value).format(dateFormatStr)),
      },
      {
        key: 'materialReceiptDateTime',
        label: 'Material Receipt',
        cellClass: 'whitespace-nowrap',
        fn: (value, object) => {
          const dateTimeStr = moment(value).format(dateTimeFormatStr);
          if (dateTimeStr == 'Invalid date') {
            return '-';
          }
          return dateTimeStr;
        },
      },
      {
        key: 'offshoreClient',
        label: 'Operator',
        cellClass: 'whitespace-nowrap',
      },
      {
        key: 'offshoreLocation',
        label: 'Offshore Location',
        cellClass: 'whitespace-nowrap',
      },
      {
        key: 'isWaste',
        label: 'IsWaste',
        cellClass: 'whitespace-nowrap',
        fn: (value, object) => {
          if (value) {
            return 'Yes';
          }
          return 'No';
        },
      },
      {
        key: 'wasteDescription',
        label: 'Waste Description',
        cellClass: 'whitespace-nowrap',
        fn: (value, object) => {
          if (value) {
            return _.unescape(value)
              .replace(/&GT;/ig, '>')
              .replace(/&LT;/ig, '<')
              .replace(/&gt/ig, '>')
              .replace(/&lt/ig, '<');
          }
          return '-';
        },
      },
      {
        key: 'createdBy',
        label: 'Receipted By',
        cellClass: 'whitespace-nowrap',
      },
      {
        key: 'isStored',
        label: 'Item Stored',
        cellClass: 'whitespace-nowrap',
        fn: (value, object) => {
          let returnVal = '-';
          if (value) {
            const storeDate = moment(object.storedDate);
            if (storeDate.isValid()) {
              returnVal = storeDate.format(dateFormatStr);
            } else {
              returnVal = 'Yes';
            }
          }
          return returnVal;
        },
      },
      {
        key: 'location',
        label: 'Stored Location',
        cellClass: 'whitespace-nowrap',
        fn: (value, object) => {
          let returnVal = value;
          if (object.subLocation) {
            returnVal = `${value} - ${object.subLocation}`;
          }
          return returnVal;
        },
      },
      {
        key: 'isPacked',
        label: 'Item Packed',
        cellClass: 'whitespace-nowrap',
        fn: (value, object) => {
          let returnVal = '-';
          if (value) {
            const storeDate = moment(object.packedDate);
            if (storeDate.isValid()) {
              returnVal = storeDate.format(dateFormatStr);
            } else {
              returnVal = 'Yes';
            }
          }
          return returnVal;
        },
      },
      {
        key: 'materialManifestNo',
        label: 'Manifest No.',
        cellClass: 'whitespace-nowrap',
      },
      {
        key: 'packingUnitIdentifier',
        label: 'Packed Into',
        cellClass: 'whitespace-nowrap',
      },
      {
        key: 'isDispatched',
        label: 'Item Dispatched',
        cellClass: 'whitespace-nowrap',
        fn: (value, object) => {
          let returnVal = '-';
          if (value) {
            const storeDate = moment(object.dispatchedDate);
            if (storeDate.isValid()) {
              returnVal = storeDate.format(dateFormatStr);
            } else {
              returnVal = 'Yes';
            }
          }
          return returnVal;
        },
      },
    ];

    return tableSettings('items.receiptHistory', 'historyTable', rowClass, fields);
  },
  getCargoItemsTableSettings() {
    const dateFormatStr = DISPLAY_DATE_FORMAT;
    const fields = [
      {
        key: 'identifier',
        label: 'Ccu',
        sortOrder: 0,
        sortDirection: 'descending',
        cellClass: 'bold whitespace-nowrap',
      },
      {
        key: 'offshoreClient',
        label: 'Operator',
        cellClass: 'whitespace-nowrap',
      },
      {
        key: 'offshoreLocation',
        label: 'Offshore Location',
        cellClass: 'whitespace-nowrap',
      },
      {
        key: 'receivedDate',
        label: 'Received Date',
        cellClass: 'whitespace-nowrap',
        fn: (value) => (moment(value).format(dateFormatStr)),
      },
      {
        key: 'description',
        label: 'Description',
        cellClass: 'whitespace-nowrap',
      },
      {
        key: 'receiptNo',
        label: 'Base Receipt No.',
        cellClass: 'whitespace-nowrap',
      },
      {
        key: 'noOfLines',
        label: 'No. Lines Receipted',
        cellClass: 'whitespace-nowrap',
        fn: (value, object) => {
          const receiptedLines = object.linesReceipted || [];
          const noOfLines = value;

          if (noOfLines) {
            return `${receiptedLines.length} / ${noOfLines}`;
          }

          return 0;
        },
      },
    ];

    return tableSettings('cargo.receiptHistory', 'cargoItemHistoryTable', null, fields);
  },
  currentClient() {
    const siteProfile = CompanySiteProfiles.findOne();
    if (siteProfile) {
      const siteClients = siteProfile.configuration.clients;
      return _.find(siteClients, (client) => client._id === FlowRouter.getParam('clientId'));
    }
    return [];
  },
  getSelectedItem() {
    return Template.instance().selectedItem.get();
  },
  getSelectedPo() {
    return Template.instance().selectedPo.get();
  },
  getSelectedCargoItem() {
    return Template.instance().selectedCargoItem.get();
  },
  getIsTableReady() {
    return Template.instance().isTableReady.get();
  },
});

Template.receiptHistory.events({
  'click #materialExportButton': function onClick() {
    const template = Template.instance();
    getCsvReport(template, 'items.getItemsAsCsvForDateRange', 'MaterialItemsReceiptHistory');
  },
  'click #cargoExportButton': function onClick() {
    const template = Template.instance();
    getCsvReport(template, 'cargo.getCargoAsCsvForDateRange', 'CargoItemsReceiptHistory');
  },
  'click #historyTable tbody tr': function onClick(event, templateInstance) {
    const item = this;

    templateInstance.selectedItem.set(item);

    MaterialItemDetailsModalMethods
      .init(item)
      .show();
  },
  'click #cargoItemHistoryTable tbody tr': function onClick(event, templateInstance) {
    const cargoItem = this;
    templateInstance.selectedCargoItem.set(cargoItem);

    CargoItemDetailsModalMethods
      .init(cargoItem._id)
      .show();
  },
});
