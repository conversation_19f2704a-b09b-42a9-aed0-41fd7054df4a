import './intake-report-item.html';

import { Template } from 'meteor/templating';
import moment from 'moment';
import { _ } from 'meteor/underscore';

const dateFormatted = (date) => {
  return date
    ? moment(date).format('DD-MMM-YYYY HH:mm')
    : '-';
};

Template.intakeReportItem.helpers({
  rowClass() {
    return this.isDeliveredToDestination
      ? 'warning'
      : '';
  },
  formattedReceivedDate() {
    return dateFormatted(this.receivedDate);
  },
  isRcvdAtClientStoresFormatted() {
    const defaultVal = '-';
    const isRcvd = this.isReceivedAtClientStores;
    const date = this.receivedAtClientStoresDateStr;

    if (!isRcvd || !(date && date.length)) {
      return defaultVal;
    }
    const isDate = moment(date).isValid();

    if (!isDate && isRcvd) {
      return 'Yes';
    }

    return dateFormatted(date);
  },
  qtyFormatted() {
    const qty = this.quantity;

    if (this.receiptHasBeenSplit) {
      return `${qty} ( ${this.totalReceiptedQuantity} )`;
    }

    return qty;
  },
  contractorShelfDateFormatted() {
    return dateFormatted(this.contractorShelfDateStr);
  },
  deliveredDateFormatted() {
    return dateFormatted(this.deliveredDate);
  },
  descriptionUnescaped() {
    return _.unescape(this.description)
      .replace(/&GT;/ig, '>')
      .replace(/&LT;/ig, '<')
      .replace(/&gt/ig, '>')
      .replace(/&lt/ig, '<');
  },
  hyphenIfEmpty(str) {
    if (str && str.length) {
      return str;
    }

    return '-';
  },
  boolToStr(bool) {
    return bool ? 'Yes' : 'No';
  },
});
