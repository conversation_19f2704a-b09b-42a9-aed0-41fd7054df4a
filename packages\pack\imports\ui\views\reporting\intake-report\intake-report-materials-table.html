<template name="intakeReportMaterialsTable">
    <table id="intakeReportMaterialsTable" class="ui very compact selectable cell striped single line table no-select">
      <thead>
        <tr>
            <th>Receipt No</th>
            <th>Receipted</th>
            <th>Offshore Client</th>
            <th>Offshore Location</th>
            <th>Voyage No</th>
            <th>Manifest No</th>
            <th>CCU</th>
            <th>Qty</th>
            <th>Package Type</th>
            <th>Weight</th>
            <th>Description</th>
            <th>EURAL/EWC Code</th>
            <th>Hazard Class</th>
            <th>Hazard Subclass</th>
            <th>UnNo</th>
        </tr>
      </thead>
      <tbody>
        {{#if Template.subscriptionsReady}}
          {{#each items}}
            {{> intakeReportItem }}
          {{/each}}
          {{#if noItems}}
          <tr><td colspan="20"> No Items </td></tr>
          {{/if}}
        {{else}}
          <tr><td colspan="20"> Loading... </td></tr>
        {{/if}}
      </tbody>
    </table>
  </template>