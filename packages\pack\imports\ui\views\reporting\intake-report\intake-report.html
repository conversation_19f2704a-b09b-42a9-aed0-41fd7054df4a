<template name="intakeReport">
  <div class="ui default-padding fluid container" style="height: 100%;margin-right: 2rem;">
    <div class="ui vertical aligned two column grid">
      <div class="column">
        {{> clientHeader headerText="Intake Report"}}
      </div>
      <div class="right aligned column">
        <div class="buttons">
          <div class="ui labeled icon basic teal button {{downloadingCsv}}" id="csvExportButton">
            <i class="file outline icon"></i>
            Export Materials
          </div>
          <div class="ui labeled icon basic blue button {{downloadingCsv}}" id="csvCargoExportButton">
            <i class="file outline icon"></i>
            Export Cargo
          </div>
        </div>
      </div>
    </div>
    <div class="ui stackable two column grid">
      <div class="column">
        <form class="ui form">
          <div class="two fields">
            <div class="eight wide field">
              <label>Filter</label>
              <div class="ui fluid icon input">
                <input type="text" placeholder="Filter..." id="searchInput" autofocus>
                <i class="inverted circular search link icon"></i>
              </div>
            </div>
            <div class="eight wide field">
                <label>Offshore Client</label>
                
                <select class="ui dropdown" name="offshoreClients">

                  <option value="ALL">All Offshore Clients</option>
                  {{#each offshoreClients}}
                    <option value="{{this._id}}">{{this.name}}</option>
                  {{/each}}
                </select>
            </div>
          </div>
        </form>
      </div>
      <div class="column">
        <form class="ui form">
          <div class="two fields">
            <div class="eight wide field">
              <label>From Date</label>
              <div class="ui calendar" id="fromDatepicker">
                <div class="ui input left icon">
                  <i class="calendar icon"></i>
                  <input type="text" placeholder="Date" />
                </div>
              </div>
            </div>
            <div class="eight wide field">
              <label>To Date</label>
              <div class="ui calendar" id="toDatepicker">
                <div class="ui input left icon">
                  <i class="calendar icon"></i>
                  <input type="text" placeholder="Date" id="toDate" />
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>

    <div class="ui grid">
      <div class="eight wide column" style="padding-bottom:0px;">
        <div class="ui pointing secondary menu" id="tabs">
          <a class="active item" data-tab="materials">Materials</a>
          <a class="item" data-tab="cargo">Cargo Items</a>
        </div>
      </div>
      <div class="eight wide column" style="padding-bottom:0px;">
        <form class="ui form">
          <div class="fields" style="float: right; padding-bottom: 2px;">
            <div class="field">
              <label>Sort</label>
              <div class="ui compact mini dropdown">
                <input type="hidden" name="sortBy" value="receiptNo">
                <div class="text"></div>
                <i class="dropdown icon"></i>
                <div class="menu">
                    <div class="item active" data-value="receiptNo">Receipt No</div>
                  <div class="item" data-value="ccu">CCU</div>
                  <div class="item" data-value="offshoreClient">Offshore Client</div>
                </div>
              </div>
            </div>
            <div class="field">
              <label>Direction</label>
              <button class="ui compact mini icon button sort-direction">
                <i class="{{direction}} icon"></i>
              </button>
            </div>
            <div class="field">
              <label>Results per Page</label>
              <div class="ui compact mini dropdown">
                <input type="hidden" name="resultsPerPage" value="10">
                <div class="text"></div>
                <i class="dropdown icon"></i>
                <div class="menu">
                  <div class="item" data-value="10">10</div>
                  <div class="item" data-value="20">20</div>
                  <div class="item" data-value="50">50</div>
                  <div class="item" data-value="500">500</div>
                </div>
              </div>
            </div>
            <div class="field">
              <label>Page</label>
              <div class="ui compact mini dropdown">
                <input type="hidden" name="pageNo" value="1">
                <div class="text"></div>
                <i class="dropdown icon"></i>
                <div class="menu">
                  {{#each pages}}
                  <div class="item" data-value="{{this}}">{{this}}</div>
                  {{/each}}
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
    
      <div class="ui active tab basic segment no-side-padding" data-tab="materials" style="overflow-x:auto;">
        {{#if Template.subscriptionsReady}}
          {{> intakeReportMaterialsTable items=items }}
        {{else}}
          <div class="ui active text loader"></div>
        {{/if}}
      </div>
      <div class="ui tab basic segment no-side-padding" data-tab="cargo" style="overflow-x:auto;">
        {{#if Template.subscriptionsReady}}
          {{> intakeReportCargoTable cargoItems=cargoItems }}
        {{else}}
          <div class="ui active text loader"></div>
        {{/if}}
      </div>

  </div>
</template>