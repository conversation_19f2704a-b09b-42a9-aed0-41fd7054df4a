import './intake-report.html';

import './intake-report-materials-table';
import './intake-report-cargo-table';
import '../../../components/client-header';
import { CompanySiteProfiles } from '../../../../api/company-site-profiles/company-site-profiles';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { GetCountForHistory } from '../../../../api/items/queries/get-count-for-history';
import { GetItemsAsCsvForOffshoreClientAndDateRange } from '../../../../api/items/queries/get-items-as-csv-for-offshore-client-and-date-range';
import { GetCargoAsCsvForOffshoreClientAndDateRange } from '../../../../api/cargo/queries/get-cargo-as-csv-for-offshore-client-and-date-range';
import { Items } from '../../../../api/items/items';
import { Cargo } from '../../../../api/cargo/cargo';
import { ReactiveVar } from 'meteor/reactive-var';
import { Template } from 'meteor/templating';
import { _ } from 'meteor/underscore';
import moment from 'moment';
import { SiteProfileService } from '../../../services/company-site-profiles/site-profile.service';

const updatePages = (filter, resultsPerPage, templateInstance) => {
  GetCountForHistory.call({ filter, resultsPerPage }, (err, res) => {
    if (!err) {
      const noOfItems = res;

      const pageSize = resultsPerPage;

      const noOfPages = Math.ceil(noOfItems / pageSize);

      templateInstance.noOfPages.set(noOfPages);
    }
  });
};

Template.intakeReport.onCreated(function onCreated() {
  const template = this;
  template.dateToFilter = new ReactiveVar('receivedDate');
  template.fromDate = new ReactiveVar(moment()
    .startOf('day')
    .toDate());
  template.toDate = new ReactiveVar(moment()
    .endOf('day')
    .toDate());
  template.offshoreClient = new ReactiveVar(null);
  template.tableFields = new ReactiveVar();
  template.selectedItem = new ReactiveVar(null);

  template.downloadingCsv = new ReactiveVar(false);

  // New Code
  template.noOfPages = new ReactiveVar(1);

  template.filter = new ReactiveVar({
    clientId: FlowRouter.getParam('clientId'),
    fromDate: template.fromDate.get(),
    toDate: template.toDate.get(),
    dateToFilter: template.dateToFilter.get(),
    query: null,
    sortBy: 'receiptNo',
    sortDirection: 'desc',
  });

  template.resultsPerPage = new ReactiveVar(10);
  template.pageNo = new ReactiveVar(1);

  template.autorun(() => {
    const filter = template.filter.get();
    const resultsPerPage = template.resultsPerPage.get();
    const pageNo = template.pageNo.get();

    template.subscribe('items.history', { pageSize: resultsPerPage, page: pageNo, filter });
    template.subscribe('cargo.history', { pageSize: resultsPerPage, page: pageNo, filter });

    updatePages(filter, resultsPerPage, template);
  });
});

const updateFilters = (
  fromDate,
  toDate,
  dateToFilter,
  offshoreClient,
  templateInstance,
) => {
  const filter = templateInstance.filter.get();

  filter.fromDate = fromDate;
  filter.toDate = toDate;
  filter.dateToFilter = dateToFilter;
  filter.offshoreClient = offshoreClient;
  templateInstance.filter.set(filter);
};

Template.intakeReport.onRendered(function onRendered() {
  const template = this;
  template.$('.checkbox').checkbox();

  template.$('.dropdown').dropdown();

  template.$('#fromDatepicker').calendar({
    type: 'date',
    today: true,
    endCalendar: template.$('#toDatepicker'),
    formatter: {
      date: (date, settings) => {
        if (_.isUndefined(date)) {
          template.fromDate.set(null);
          return '';
        }
        template.fromDate.set(
          moment(date)
            .startOf('day')
            .toDate(),
        );
        updateFilters(
          template.fromDate.get(),
          template.toDate.get(),
          template.dateToFilter.get(),
          template.offshoreClient.get(),
          template,
        );
        const day = date.getDate();
        const month = settings.text.monthsShort[date.getMonth()];
        const year = date.getFullYear();
        return `${day} ${month} ${year}`;
      },
    },
  });
  template.$('#toDatepicker').calendar({
    type: 'date',
    startCalendar: template.$('#fromDatepicker'),
    formatter: {
      date: (date, settings) => {
        if (_.isUndefined(date)) {
          template.toDate.set(null);
          return '';
        }
        template.toDate.set(
          moment(date)
            .endOf('day')
            .toDate(),
        );
        updateFilters(
          template.fromDate.get(),
          template.toDate.get(),
          template.dateToFilter.get(),
          template.offshoreClient.get(),
          template,
        );
        const day = date.getDate();
        const month = settings.text.monthsShort[date.getMonth()];
        const year = date.getFullYear();
        return `${day} ${month} ${year}`;
      },
    },
  });
  template.$('#fromDatepicker').calendar('set date', template.fromDate.get());
  template.$('#toDatepicker').calendar('set date', template.toDate.get());
  template.$('#searchInput').focus();
  template.$('#tabs .item').tab();
});

Template.intakeReport.helpers({
  items() {
    const filter = Template.instance().filter.get();
    const sortBy = filter.sortBy;
    const sortDirection = filter.sortDirection === 'desc' ? -1 : 1;
    return Items.find(
      {},
      {
        sort: {
          [sortBy]: sortDirection,
        },
      },
    );
  },
  cargoItems() {
    const filter = Template.instance().filter.get();
    const sortBy = filter.sortBy;
    const sortDirection = filter.sortDirection === 'desc' ? -1 : 1;
    return Cargo.find(
      {},
      {
        sort: {
          [sortBy]: sortDirection,
        },
      },
    );
  },
  pages() {
    const noOfPages = Template.instance().noOfPages.get();

    return Array(noOfPages)
      .fill()
      .map((e, i) => i + 1);
  },
  downloadingCsv() {
    return Template.instance().downloadingCsv.get() ? 'loading disabled' : '';
  },
  direction() {
    const filter = Template.instance().filter.get();

    return filter.sortDirection === 'desc'
      ? 'sort content descending'
      : 'sort content ascending';
  },
  currentClient() {
    const siteProfile = CompanySiteProfiles.findOne();

    if (siteProfile) {
      const siteClients = siteProfile.configuration.clients;
      return _.find(
        siteClients,
        (client) => client._id === FlowRouter.getParam('clientId'),
      );
    }
    return [];
  },
  getSelectedItem() {
    return Template.instance().selectedItem.get();
  },
  offshoreClients() {
    return SiteProfileService.offshoreClients()
  },
});

Template.intakeReport.events({
  'click #csvExportButton': function onClick(event, templateInstance) {
    templateInstance.downloadingCsv.set(true);

    const fromDate = templateInstance.$('#fromDatepicker').calendar('get date');
    const toDate = templateInstance.$('#toDatepicker').calendar('get date');
    const dateToFilter = templateInstance.dateToFilter.get();
    const offshoreClient = templateInstance.offshoreClient.get();
    const offshoreClientLabel = offshoreClient || 'ALL CLIENTS';

    const fromDateStr = moment(fromDate).format('YYYY-MM-DD');
    const toDateStr = moment(toDate).format('YYYY-MM-DD');

    let filename = 
        `MaterialsIntakeReport_${offshoreClientLabel}_${fromDateStr}-to-` +
        `${toDateStr}.csv`;

    GetItemsAsCsvForOffshoreClientAndDateRange.call(
      {
        reportStartDateStr: fromDateStr,
        reportEndDateStr: toDateStr,
        dateToFilterBy: dateToFilter,
        offshoreClient,
      },
      (err, fileContent) => {
        templateInstance.downloadingCsv.set(false);

        if (true) {
          const a = document.createElement('a');
          document.body.appendChild(a);
          const blob = new Blob([fileContent], {
            type: 'text/csv;charset=utf-8',
          });
          if (window.navigator && window.navigator.msSaveOrOpenBlob) {
            window.navigator.msSaveOrOpenBlob(blob, filename);
          } else {
            const url = window.URL.createObjectURL(blob);
            a.href = url;
            a.download = filename;
            a.click();
            window.URL.revokeObjectURL(url);
          }
        }
      },
    );
  },
  'click #csvCargoExportButton': function onClick(event, templateInstance) {
    templateInstance.downloadingCsv.set(true);

    const fromDate = templateInstance.$('#fromDatepicker').calendar('get date');
    const toDate = templateInstance.$('#toDatepicker').calendar('get date');
    const dateToFilter = templateInstance.dateToFilter.get();
    const offshoreClient = templateInstance.offshoreClient.get();
    const offshoreClientLabel = offshoreClient || 'ALL CLIENTS';

    const fromDateStr = moment(fromDate).format('YYYY-MM-DD');
    const toDateStr = moment(toDate).format('YYYY-MM-DD');

    let filename = 
        `CargoItemsIntakeReport_${offshoreClientLabel}_${fromDateStr}-to-` +
        `${toDateStr}.csv`;

    GetCargoAsCsvForOffshoreClientAndDateRange.call(
      {
        reportStartDateStr: fromDateStr,
        reportEndDateStr: toDateStr,
        dateToFilterBy: dateToFilter,
        offshoreClient,
      },
      (err, fileContent) => {
        templateInstance.downloadingCsv.set(false);

        if (true) {
          const a = document.createElement('a');
          document.body.appendChild(a);
          const blob = new Blob([fileContent], {
            type: 'text/csv;charset=utf-8',
          });
          if (window.navigator && window.navigator.msSaveOrOpenBlob) {
            window.navigator.msSaveOrOpenBlob(blob, filename);
          } else {
            const url = window.URL.createObjectURL(blob);
            a.href = url;
            a.download = filename;
            a.click();
            window.URL.revokeObjectURL(url);
          }
        }
      },
    );
  },
  'change input[name="dateToFilter"]': function onChange(event, templateInstance) {
    const dateToFilter = templateInstance.$(event.currentTarget).val();

    templateInstance.dateToFilter.set(dateToFilter);

    updateFilters(
      templateInstance.fromDate.get(),
      templateInstance.toDate.get(),
      dateToFilter,
      template.offshoreClient.get(),
      templateInstance,
    );
  },
  'change input[name="resultsPerPage"]': function onChange(event, templateInstance) {
    const newValue = templateInstance.$(event.currentTarget).val();

    const valAsInt = parseInt(newValue, 10);

    templateInstance.resultsPerPage.set(valAsInt);
  },
  'change input[name="pageNo"]': function onChange(event, templateInstance) {
    const newValue = templateInstance.$(event.currentTarget).val();

    const valAsInt = parseInt(newValue, 10);

    templateInstance.pageNo.set(valAsInt);
  },
  'change select[name="offshoreClients"]': function onChange(event, templateInstance) {
    const selectedOffshoreClientId = templateInstance.$(event.currentTarget).val();
    const selectedOffshoreClientName = templateInstance.$('select[name="offshoreClients"] option:selected').text();

    templateInstance.offshoreClient.set(selectedOffshoreClientId != 'ALL' ? selectedOffshoreClientName : null);

    updateFilters(
      templateInstance.fromDate.get(),
      templateInstance.toDate.get(),
      templateInstance.dateToFilter.get(),
      templateInstance.offshoreClient.get(),
      templateInstance,
    );
  },
  'input input#searchInput': _.debounce(function onInput(event, templateInstance) {
    // Rate limit on text inputs
    const filters = templateInstance.filter.get();
    const query = templateInstance
      .$(event.currentTarget)
      .val()
      .trim();

    filters.query = query;
    templateInstance.$('.page-no-dropdown').dropdown('set selected', '1');

    templateInstance.filter.set(filters);
  }),
  'change input[name="sortBy"]': function onChange(event, templateInstance) {
    const sortBy = templateInstance.$(event.currentTarget).val();

    const filter = templateInstance.filter.get();

    filter.sortBy = sortBy;

    templateInstance.filter.set(filter);
  },
  'click .sort-direction': function onClick(event, templateInstance) {
    event.preventDefault();
    const filter = templateInstance.filter.get();
    const sortDirection = filter.sortDirection;

    if (sortDirection === 'desc') {
      filter.sortDirection = 'asc';
    } else {
      filter.sortDirection = 'desc';
    }

    templateInstance.filter.set(filter);
  },
});
