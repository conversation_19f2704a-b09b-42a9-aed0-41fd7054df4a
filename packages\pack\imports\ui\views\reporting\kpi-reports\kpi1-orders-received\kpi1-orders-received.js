import { Meteor } from 'meteor/meteor';
import { Template } from 'meteor/templating';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { moment } from 'meteor/momentjs:moment';
import './kpi1-orders-received.html';
import './kpi1-orders-received-table';
import '../shared/standard-kpi-layout';

const isoDateFormatStr = 'YYYY-MM-DD';
const displayDateFormatStr = 'DD-MMM-YYYY';

Template.kpi1OrdersReceived.onCreated(function onCreated() {
  const template = this;
  template.reportData = new ReactiveVar({});
  template.reportDataReceived = new ReactiveVar(false);

  const today = moment();
  const fromDateDefault = moment().subtract(4, 'weeks').startOf('isoweek'); // Monday 4 weeks ago.
  const fromDateStr = fromDateDefault.format(isoDateFormatStr);
  const toDateStr = today.format(isoDateFormatStr);
  template.fromDate = new ReactiveVar(fromDateStr);
  template.toDate = new ReactiveVar(toDateStr);

  const onComplete = (error, result) => {
    if (error) {
      console.log(`Get Report Fail ${JSON.stringify(error, null, 2)}`);
      console.error(error);
    } else {
      template.reportData.set(result);
      template.reportDataReceived.set(true);
    }
  };

  template.autorun(() => {
    const reportParams = {
      clientId: FlowRouter.getParam('clientId'),
      fromDate: template.fromDate.get(),
      toDate: template.toDate.get(),
    };

    Meteor.call('items.getDayCountsReport',
      reportParams,
      onComplete);
  });
});

Template.kpi1OrdersReceived.helpers({
  kpiReportLayout() {
    return {
      pageHeader: 'Number of Orders Received by LSP',
      fromDate: Template.instance().reportData.get().fromDate,
      toDate: Template.instance().reportData.get().toDate,
      reportRunDateTime: Template.instance().reportData.get().reportRunDateTime,
      chartDetails: {
        chartData: Template.instance().reportData.get().dayCountsReportData,
        xAxisFieldName: 'date',
        datePeriod: 'DD',
        isStacked: true,
        isNoColumnSpacing: false,
        columns: [
          {
            title: 'Number of Sellafield Receipts',
            dataField: 'slItemsReceived',
          },
          {
            title: 'Number of Contractor Receipts',
            dataField: 'contractorItemsReceived',
            color: '#4286f4',
          },
        ],
      },
      tableReference: 'kpi1OrdersReceivedTable',
    };
  },
  canDisplayChart() {
    return Template.instance().reportDataReceived.get();
  },
});

Template.kpi1OrdersReceived.events({
  'click .js-refresh-report': function onClick(event, templateInstance) {
    const fromDateStr = templateInstance.$('[name=fromDate]').val();
    const fromDate = moment(fromDateStr, displayDateFormatStr).format(isoDateFormatStr);

    const toDateStr = templateInstance.$('[name=toDate]').val();
    const toDate = moment(toDateStr, displayDateFormatStr).format(isoDateFormatStr);

    templateInstance.fromDate.set(fromDate);
    templateInstance.toDate.set(toDate);
  },
});
