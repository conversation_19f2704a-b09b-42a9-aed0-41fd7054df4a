import { Meteor } from 'meteor/meteor';
import { Template } from 'meteor/templating';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { moment } from 'meteor/momentjs:moment';
import './kpi3-orders-delivered.html';
import '../shared/standard-kpi-layout';
import './kpi3-orders-delivered-table';

const isoDateFormatStr = 'YYYY-MM-DD';
const displayDateFormatStr = 'DD-MMM-YYYY';

Template.kpi3OrdersDelivered.onCreated(function onCreated() {
  const template = this;

  template.reportData = new ReactiveVar({});
  template.reportDataReceived = new ReactiveVar(false);

  const today = moment();
  const fromDateDefault = moment().subtract(8, 'weeks').startOf('month'); // 1st day of month at least 8 weeks ago.
  const fromDateStr = fromDateDefault.format(isoDateFormatStr);
  const toDateStr = today.format(isoDateFormatStr);
  template.fromDate = new ReactiveVar(fromDateStr);
  template.toDate = new ReactiveVar(toDateStr);

  const onComplete = (error, result) => {
    if (error) {
      console.log(`Get Report Fail ${JSON.stringify(error, null, 2)}`);
      console.error(error);
    } else {
      template.reportData.set(result);
      template.reportDataReceived.set(true);
    }
  };

  template.autorun(() => {
    const reportParams = {
      clientId: FlowRouter.getParam('clientId'),
      fromDate: template.fromDate.get(),
      toDate: template.toDate.get(),
    };

    Meteor.call('items.getDayCountsReport',
      reportParams,
      onComplete);
  });
});

Template.kpi3OrdersDelivered.helpers({
  kpiReportLayout() {
    return {
      pageHeader: 'Number of Orders Delivered by LSP per Week',
      fromDate: Template.instance().reportData.get().fromDate,
      toDate: Template.instance().reportData.get().toDate,
      reportRunDateTime: Template.instance().reportData.get().reportRunDateTime,
      chartDetails: {
        chartData: Template.instance().reportData.get().weekCountsReportData,
        xAxisFieldName: 'date',
        datePeriod: 'WW',
        isStacked: false,
        isNoColumnSpacing: true,
        columns: [
          {
            title: 'Total Orders Received (per week)',
            dataField: 'totalItemsReceived',
            color: '#4286f4',
          },
          {
            title: 'Total Orders Delivered (per week)',
            dataField: 'totalItemsDelivered',
            color: '#ff6600',
          },
        ],
      },
      tableReference: 'kpi3OrdersDeliveredTable',
    };
  },
  canDisplayChart() {
    return Template.instance().reportDataReceived.get();
  },
});

Template.kpi3OrdersDelivered.events({
  'click .js-refresh-report': function onClick(event, templateInstance) {
    const fromDateStr = templateInstance.$('[name=fromDate]').val();
    const fromDate = moment(fromDateStr, displayDateFormatStr).format(isoDateFormatStr);

    const toDateStr = templateInstance.$('[name=toDate]').val();
    const toDate = moment(toDateStr, displayDateFormatStr).format(isoDateFormatStr);

    templateInstance.fromDate.set(fromDate);
    templateInstance.toDate.set(toDate);
  },
});
