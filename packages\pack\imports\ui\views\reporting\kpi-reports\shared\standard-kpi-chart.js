import { Template } from 'meteor/templating';
import { AmCharts } from 'meteor/em0ney:amcharts';
import './standard-kpi-chart.html';

/*
  Standard Kpi Stacked Bar Chart

  Pass configuration object to define details for specific implementation:

  chartDetails = {
    chartData,       // data to be provided for display
    xAxisFieldName,   // normally going to be 'date'
    datePeriod, // i.e. Monthly('MM'), Daily('DD'), Weekly
    columns: [{ // the columns to be shown
      title,  // Title to be displayed by column
      dataField,  // Name of data to be used from data source
      color,  // Optional color specification for column
    }],
    isNoColumnSpacing, // Should grouped columns be joined together
    isStacked,  // Should display as stacked chart
  }
*/

const createValueFieldSpecsForChartSpecification = (columns) => {
  const balloonText =
  '<b>[[title]]</b><br><span style="font-size:14px;">[[category]]: <b>[[value]]</b></span';

  const valueFields = [];

  _.each(columns, (column) => {
    const newValueField = {
      title: column.title,
      type: 'column',
      valueField: column.dataField,
      labelText: '[[value]]',
      fillAlphas: 0.8,
      balloonText,
    };
    if (column.color) newValueField.lineColor = column.color;
    valueFields.push(newValueField);
  });
  return valueFields;
};

const getAmChartsSpecification = (templateDataContext) => {
  const chartSpecs = {
    type: 'serial',
    dataDateFormat: 'YYYY-MM-DD',
    legend: {
      maxColumns: 1,
      position: 'right',
      useGraphSettings: true,
    },
    dataProvider: templateDataContext.chartData,
    valueAxes: [{
      stackType: templateDataContext.isStacked ? 'regular' : 'none',
      title: 'Orders',
    }],
    graphs: createValueFieldSpecsForChartSpecification(templateDataContext.columns),
    categoryField: templateDataContext.xAxisFieldName,
    categoryAxis: {
      minPeriod: templateDataContext.datePeriod,
      parseDates: true,
      gridPosition: 'start',
      position: 'left',
      labelRotation: 60,
      title: 'Dates',
      gridCount: templateDataContext.chartData.length,
      autoGridCount: false,
    },
  };

  if (templateDataContext.isNoColumnSpacing) chartSpecs.columnSpacing = 0;

  return chartSpecs;
};

Template.standardKpiChart.onRendered(function onRendered() {
  const template = this;
  // Setup auto-run with reactive dependency on Templates 'current' data context.
  template.autorun(() => {
    if (Template.currentData().chartData) {
      const kpiChart = AmCharts.makeChart(
        'kpiChart',
        getAmChartsSpecification(Template.currentData()));

      template.kpiChart = kpiChart;
      template.$('a[title="JavaScript charts"]').css('display', 'none');
    }
  });
});
