import './standard-kpi-layout.html';
import './standard-kpi-chart';

import { CompanySiteProfiles } from '../../../../../api/company-site-profiles/company-site-profiles';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Template } from 'meteor/templating';
import { moment } from 'meteor/momentjs:moment';

/*
  Standard kpi component for layout with chart and table.
  Pass object as follows to customise template
  details = {
    pageHeader, // Header for KPI report page
    chartDetails, // The settings for the chart (see standard-kpi-chart for more details)
    tableReference,   // The name of the table template to use
    (NOTE: rememember to import table on parent instance)
  }
*/

Template.standardKpiLayout.onRendered(function onRendered() {
  const template = this;
  template.$('#reportingTabMenu .item').tab();

  // Setup Datepickers.
  // https://github.com/mdehoog/Semantic-UI/blob/49178d6d0321357b959ff56f424ea1b3ed76a6ed/src/definitions/modules/calendar.js#L902-L1279
  const calendarSettings = {
    type: 'date',
    maxDate: moment().utc().toDate(), // today
    ampm: false,
    formatter: {
      date: (date, settings) => {
        if (_.isUndefined(date)) return '';
        const day = (`0${date.getDate()}`).slice(-2); // zero pad.
        const month = settings.text.monthsShort[date.getMonth()];
        const year = date.getFullYear();
        return day + '-' + month + '-' + year;
      },
    },
  };

  template.$('#fromDateDatepicker').calendar(calendarSettings);
  template.$('#toDateDatepicker').calendar(calendarSettings);

  template.autorun(() => {
    template.$('#fromDateDatepicker')
      .calendar('set date', moment(Template.currentData().fromDate, 'YYYY-MM-DD').toDate());

    template.$('#toDateDatepicker')
      .calendar('set date', moment(Template.currentData().toDate, 'YYYY-MM-DD').toDate());
  });
});

Template.standardKpiLayout.helpers({
  currentClient() {
    const siteProfile = CompanySiteProfiles.findOne();
    if (siteProfile) {
      const siteClients = siteProfile.configuration.clients;
      return _.find(siteClients, (client) => client._id === FlowRouter.getParam('clientId'));
    }
    return [];
  },
  pageHeader() {
    return Template.currentData().pageHeader;
  },
  chartDetails() {
    return Template.currentData().chartDetails;
  },
  tableReference() {
    return Template.currentData().tableReference;
  },
  reportDateTime() {
    return moment(Template.currentData().reportRunDateTime).format('DD-MMM-YYYY HH:mm:ss');
  },
});
