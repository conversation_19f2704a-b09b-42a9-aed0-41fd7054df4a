import { Template } from 'meteor/templating';
import { moment } from 'meteor/momentjs:moment';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { _ } from 'meteor/underscore';
import './report-test.html';

Template.reportTest.onCreated(function onCreated() {
  const template = this;
  template.reportResultsStr = new ReactiveVar('none');
});

Template.reportTest.onRendered(() => {
  const template = this;
});

Template.reportTest.events({
  'click .js-get-report-button': function (event, templateInstance) {
    const onComplete = (error, result) => {
      if (error) {
        console.log(`Get Report Fail ${JSON.stringify(error, null, 2)}`);
        console.error(error);
        alert(`Add Item Failed ${JSON.stringify(error, null, 2)}`);
      } else { // Success.
        console.log(`Get Report Success ${JSON.stringify(result, null, 2)}`);

        templateInstance.reportResultsStr.set(JSON.stringify(result, null, 2));
      }
    };

    Meteor.call(
      'items.getDayCountsReport',
      {
        clientId: FlowRouter.getParam('clientId'),
      },
      onComplete,
    );
  },
});

Template.reportTest.helpers({
  resultsText() {
    return Template.instance().reportResultsStr.get();
  },
});
