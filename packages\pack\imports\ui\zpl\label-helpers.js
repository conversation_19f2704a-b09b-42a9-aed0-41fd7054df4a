import { LABEL_CONSTS } from './label-consts';

const startDoc = () => '^XA\n';

const endDoc = () => '\n^XZ\n';

const addLogo = (x, y) => `^FO${x + 50},${y}^GFA,5060,5060,46,,::::::::::::::::::::::::iO07FF8,iN01JF8,iN03KF,iN07KFC,iN0MF8,iM01MFE,iM03NF8,iM07OF,iM0PFEI0C,iM0QFE07E,iL01TFE,iL03UF,iL03UF8,iL07UF8,K07IFC003JFE1LF87JFC3JFJ07IFI01IFI0FFI0FEJ07C07RFC,K07JF803JFE1LF87JFC3JFC001JF8007IFC00FFI0FEJ0FI0RFC,K07JFC03JFE1LF87JFC3KF003JF801JFE00FF800FEJ0CI03QFC,K07JFE03JFE1LF87JFC3KF807JF803KF00FFC00FEI018J0QFE,K07KF03JFE1LF87JFC3KF80KF807KF80FFC00FEI01K07PFE,K07KF83JFE1LF87JFC3KFC0KF80LFC0FFE00FEO01QF,K07F83FF83F8L0FFI07F8I03FC1FFC1FF80380FFC0FFC0IF00FEP0QF,K07F80FF83F8L0FFI07FJ03FC03FC1FEJ01FF003FE0IF00FEP03PF,K07F807FC3F8L0FFI07FJ03FC03FE1FEJ01FF001FE0IF80FEP01PF,K07F803FC3F8L0FFI07FJ03FC01FE1FEJ01FE001FF0IF80FEQ0PF8,K07F803FC3F8L0FFI07FJ03FC01FE1FEJ03FEI0FF0IFC0FEQ03OF8,K07F803FC3F8L0FFI07FJ03FC01FE1FF8I03FCI0FF0IFE0FEQ01OF8,K07F803FC3F8L0FFI07FJ03FC01FE0IFI03FCI0FF0IFE0FER07NF8,K07F803FC3JFCI0FFI07JF83FC01FE0IFE003FCI0FF0JF0FER03NF8,K07F803FC3JFCI0FFI07JF83FC03FC0JFC03FCI0FF0FF7F8FER01NF8,K07F807FC3JFCI0FFI07JF83FC07FC07JF03FCI0FF0FF7F8FES07MF8,K07F807F83JFCI0FFI07JF83FC1FF803JF83FCI0FF0FF3FCFES01MF8,K07F81FF83JFCI0FFI07JF83KF800JF83FCI0FF0FF1FEFET0MF8,K07KF83JFCI0FFI07JF83KFI01IFC3FCI0FF0FF1FEFET03LF8,K07KF03JF8I0FFI07JF03JFEJ03FFC3FCI0FF0FF0IFEU0LF8,K07JFE03F8L0FFI07FJ03JFCK07FE3FCI0FF0FF07FFEU03KF8,K07JFC03F8L0FFI07FJ03JFCK03FE3FCI0FF0FF07FFEV0KF,K07JF803F8L0FFI07FJ03JFEK01FE1FEI0FF0FF03FFEV01IFC,K07IFE003F8L0FFI07FJ03FC1FFK01FE1FE001FE0FF03FFEW01FE,K07FFEI03F8L0FFI07F8I03FC0FF8J01FE1FF001FE0FF01FFE,K07F8J03FCL0FFI07F8I03FC07FC1I03FE0FF807FE0FF00FFE,K07F8J03JFEI0FFI07JFE3FC07FC1F80FFC0FFE1FFC0FF00FFEK0F,K07F8J03KFI0FFI07JFE3FC03FE1KFC07KFC0FF007FEJ07FF8,K07F8J03KFI0FFI03JFE3FC01FE1KFC07KF80FF003FEJ0JF,K07F8J01KFI0FFI03JFE3FC01FF1KF803KF00FF003FEI01JFC,K07F8K0KFI0FFI01JFE3FC00FF1KF001JFE00FF001FEJ0KF8,K07F8K07JFI0FFJ0JFE3FC00FF9JFEI07IF800FFI0FEJ0KFE,K07F8K01JFI0FFJ03IFE3FC00FF83IFJ01FFEI0FFI0FEJ07KF8,iL07LF,iL03LFC,iL03MF,iL01MFEL06,iM0NFCJ03C,iM0OFC007FC,iN03RF8,iO07QF,iP0PFE,iP07OFC,iP01OF8,iQ07NF,iQ03MFE,iQ01MF8,iR07LF,iR03KFC,iR01KF,iS0JFC,iS03FFE,iS01F8,,:::::::::::::::::^FS`;

const mm = (x) => x * 0.0393701 * LABEL_CONSTS.DPI;
const inch = (x) => x * LABEL_CONSTS.DPI;

const addBarcode = (x, y, barCodeVal) => {
  const template = `
    ^FO ${LABEL_CONSTS.HORIZONTAL_MARGIN + mm(3)},${y}
      ^BY 4,3,120
      ^BC
        ^FD${barCodeVal}^FS;
    `;

  return template;
};

const addBox = (x, y, width, label, val, fontSize = LABEL_CONSTS.FONT_SIZE_VALS) => {
  let valY = y + 45;

  if (val.length > 12) {
    fontSize = LABEL_CONSTS.FONT_SIZE_KEYS;
  }

  if (fontSize > LABEL_CONSTS.FONT_SIZE_VALS) {
    valY = y + 30;
  }
  const boxHeight = mm(10);
  const template = `
  ^FO ${x + mm(4)}, ${y + (boxHeight / 3)}
    ^CF0,${LABEL_CONSTS.FONT_SIZE_KEYS}
    ^FD ${label} ^FS

  ^FO ${(x + width) - 10}, ${y + (boxHeight / 3)}, 1
    ^CF0,${fontSize}
    ^FD ${val} ^FS

  ^FO ${x}, ${y}
    ^GB ${width}, ${boxHeight}, 3,B,0
    ^FD ^FS
  `;

  return template;
};

const getLines = (label, val, lineBreak = 34, maxLines = 5) => {
  const requiredUsefulCharacters = 150;
  var lines = [];
  var words = val.trim().split(' ');

  // Push label first
  var running = [];
  if (label && label.length > 0) {
    running.push(label);
    running.push(' ');
  }

  words.forEach((word) => {
    var words = [];

    if (word.length > lineBreak) {
      const spaceLeft = running.join(' ').length + 1;

      var remaining = word;
      while (remaining.substr(0, lineBreak).length > 0) {
        const part = remaining.substr(0, lineBreak - 1);
        remaining = remaining.replace(part, '');

        if (part.length == lineBreak - 1) {
          words.push(part  + '-');
        } else {
          words.push(part);
        }
      }
    } else {
      words.push(word);
    }

    words.forEach((word) => {
      if (running.join(' ').length + word.length < lineBreak) {
        running.push(word);
      } else {
        lines.push(running.join(' '));
        running = [];
        running.push(word);
      }
    });
  });

  if (running.length != 0) {
    lines.push(running.join(' '));
  }

  if (maxLines !== null) {
    lines = lines.slice(0, maxLines);

    let lastLine = lines[lines.length - 1];

    if (lastLine.length <= (lineBreak - 3)) {
      lines[lines.length - 1] = lastLine;
    } else {
      var safety = 100;
      var parts = lastLine.split(' ');
      parts.pop();

      while (parts.join(' ').length > lineBreak - 3 && safety > 0) {
        parts.pop();
        safety--;
      }

      lines[lines.length - 1] = parts.join(' ') + '...';
    }
  }

  return lines;
};

const addMultiLine = (x, y, width, label, val, minUseful = 150) => {
  let valY = y + 45;

  const lines = getLines(label, val);
  const count = lines.length;
  const fontSize = LABEL_CONSTS.FONT_SIZE_KEYS;
  const padding = mm(10) / 3;
  const lineHeight = mm(10) / 3;
  const spacing = mm(2);

  const boxHeight = (padding * 2) + (spacing * (count - 1)) + (lineHeight * count);

  var template = '';

  // Lines
  for (var i=0; i < count; i++) {
    const yPos = y + padding + ((lineHeight + spacing) * (i));
    template = template + `
      ^FO ${x + mm(4)}, ${yPos}
        ^CF0,${LABEL_CONSTS.FONT_SIZE_KEYS}
        ^FD ${lines[i]} ^FS
    `;
  }

  // Box outline
  template = template + `
    ^FO ${x}, ${y}
    ^GB ${width}, ${boxHeight}, 3,B,0
    ^FD ^FS
  `;

  return {
    template: template,
    height: boxHeight,
  };
};

const addMultiBox = (x, y, width, label, val) => {
  const template = `
    ^FO ${x}, ${y + 15}
      ^CF0,${LABEL_CONSTS.FONT_SIZE_KEYS}
      ^FB ${width},2,1,C
      ^FD ${label} ^FS

    ^FO ${x}, ${y + 60}
      ^CF0,${LABEL_CONSTS.FONT_SIZE_VALS}
      ^FB ${width},2,2,C
      ^FD ${val} ^FS

    ^FO ${x}, ${y}
      ^GB ${width}, 200, 3,0
      ^FD ^FS
  `;

  return template;
};

const addCenterText = (y, text, size = 80) => {
  const template = `
    ^FO 0, ${y}
      ^CF0,${size}
      ^FB ${LABEL_CONSTS.PAGE_WIDTH},2,1,C
      ^FD ${text} ^FS
  `;

  return template;
};

const zplFonts = {
  sansSerif: 0,
  monospace: 1,
};

export const LabelHelpers = {
  startDoc, endDoc, addLogo, addBarcode, addBox, addMultiBox, addCenterText, addMultiLine,
};
