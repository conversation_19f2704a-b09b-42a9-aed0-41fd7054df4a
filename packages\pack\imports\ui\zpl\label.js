import { $ } from 'meteor/jquery';
import { Meteor } from 'meteor/meteor';
import { LabelHelpers } from './label-helpers';
import { LABEL_CONSTS } from './label-consts';

import { SiteProfileService } from
  '../services/company-site-profiles/site-profile.service';
import moment from 'moment';

const mm = (x) => x * 0.0393701 * LABEL_CONSTS.DPI;
const inch = (x) => x * LABEL_CONSTS.DPI;

export class ZplLabelService {
  static printBarcodeLabel(itemInfo, printerIp) {
    this.sendToPrinter(this.createLabel(itemInfo), printerIp);
  }

  static printBarcodeLabels(itemInfoArray, printerIp) {
    let data = '';

    for (const itemInfo of itemInfoArray) {
      data += this.createLabel(itemInfo);
    }

    this.sendToPrinter(data, printerIp);
  }

  static nullToEmptyString (el) {
    if (el === null || el === undefined) {
      return '';
    }

    return el;
  }

  static normalizeLabelFields(item) {
    var description = item.description;
    const isDangerous = ((item.unNo !== undefined && item.unNo !== null)
      || ( item.imoHazardClass !== undefined && item.imoHazardClass !== null)
      || ( item.imoSubClass !== undefined && item.imoSubClass !== null));

    if (isDangerous) {
      description = '';
      if (item.unNo) {
        if (item.unNo.substring(0, 2) == 'UN') {
          description += 'UN' + item.unNo.trim().substring(2).trim() + ', ';
        } else {
          description += 'UN' + item.unNo.trim() + ', ';
        }
      } else {
        description += 'UN N/P, ';
      }
      description += 'CL.' + ((item.imoHazardClass)? item.imoHazardClass : 'N/P');
      description += ((item.imoSubClass)? ' (' + item.imoSubClass + ')' : ' (N/P)');
      description += ' - ' + item.description;
    }

    const fields = [
      { key: 'Client', value: item.offshoreClient },
      { key: 'Platform', value: item.offshoreLocation },
      { key: 'CCU', value: item.ccu },
      { key: 'Manifest No', value: item.materialManifestNo },
      { key: '', value: description, isMultiLine: true },
      { key: 'Wt. (kg)', value: item.weightKg },
      { key: 'Qty', value: item.quantity }
    ];

    return fields;
  }

  static createLabel(itemInfo) {
    // ZPL emulator does not respond,
    // so successes will be treated as errors.

    // Start of label
    const standardGap = mm(10);
    const multiGap = mm(10);
    let docPosition = LABEL_CONSTS.VERTICAL_MARGIN;
    let label = '';
    itemInfo.receiptNo = this.nullToEmptyString(itemInfo.receiptNo);
    itemInfo.materialReceiptDateTime = this.nullToEmptyString(itemInfo.materialReceiptDateTime); // Material Receipted Date Time
    const fields = this.normalizeLabelFields(itemInfo);

    label += LabelHelpers.startDoc();
    label += LabelHelpers.addLogo(0, mm(0));

    docPosition += standardGap * 0.9;

    label += LabelHelpers.addCenterText(docPosition, itemInfo.receiptNo, 70);
    docPosition += standardGap;
    label += LabelHelpers.addCenterText(docPosition, moment(itemInfo.materialReceiptDateTime).format('YYYY-MM-DD HH:mm'), 40);
    docPosition += standardGap - (0.3 * standardGap);

    fields.forEach((f) => {
      if (!f.value) { f.value = ''; }
      if (f.isMultiLine) {
        if (f.value.length > 20) {
          const result = LabelHelpers.addMultiLine(LABEL_CONSTS.HORIZONTAL_MARGIN, docPosition, LABEL_CONSTS.PRINT_AREA_WIDTH, f.key, f.value);
          label += result.template;
          docPosition += result.height;
          return;
        } else {
          label += LabelHelpers.addBox(LABEL_CONSTS.HORIZONTAL_MARGIN, docPosition, LABEL_CONSTS.PRINT_AREA_WIDTH, f.key, f.value);
        }
      } else {
        if (f.key === 'Wt. (kg)') {
          if (f.value) {
            label += LabelHelpers.addBox(LABEL_CONSTS.HORIZONTAL_MARGIN, docPosition, LABEL_CONSTS.PRINT_AREA_WIDTH, f.key, `${f.value} kg`);
          } else { // If weight is not set, show dash instead of "<blank> kg".
            label += LabelHelpers.addBox(LABEL_CONSTS.HORIZONTAL_MARGIN, docPosition, LABEL_CONSTS.PRINT_AREA_WIDTH, f.key, ' - ');
          }
        } else {
          label += LabelHelpers.addBox(LABEL_CONSTS.HORIZONTAL_MARGIN, docPosition, LABEL_CONSTS.PRINT_AREA_WIDTH, f.key, f.value);
        }
      }

      docPosition += standardGap;
    });

    label += LabelHelpers.addBarcode(LABEL_CONSTS.HORIZONTAL_MARGIN, inch(5), itemInfo.receiptNo);

    const finalLabel = label += LabelHelpers.endDoc();

    // Check how many copies are needed
    const noOfCopies = itemInfo.noOfCopies || 1;

    return finalLabel.repeat(noOfCopies);
  }

  static sendToPrinter(data, printerIp) {
    const openWindowInCentre = (url, title, w, h) => {
      const dualScreenLeft = window.screenLeft != undefined ? window.screenLeft : screen.left;
      const dualScreenTop = window.screenTop != undefined ? window.screenTop : screen.top;

      const width = window.innerWidth ? window.innerWidth : document.documentElement.clientWidth ? document.documentElement.clientWidth : screen.width;
      const height = window.innerHeight ? window.innerHeight : document.documentElement.clientHeight ? document.documentElement.clientHeight : screen.height;

      const left = ((width / 2) - (w / 2)) + dualScreenLeft;
      const top = ((height / 2) - (h / 2)) + dualScreenTop;
      const newWindow = window.open(url, title, 'toolbar=no, menubar=no, scrollbars=no, width=' + w + ', height=' + h + ', top=' + top + ', left=' + left);

      // Puts focus on the newWindow
      if (window.focus) {
        newWindow.focus();
      }

      return newWindow;
    };

    const { printerProxy } = Meteor.settings.public;

    const printWindow = openWindowInCentre(printerProxy, 'Print Label', 400, 200);

    const isPrintDialogReady = setInterval(() => {
      const message = {
        isReadyCheck: true,
      };

      printWindow.postMessage(message, printerProxy);
    }, 100);

    const printWindowMessageReceiver = (event) => {
      if (event.data && event.data.isPrintDialogAckMsg) {
        clearInterval(isPrintDialogReady);
        const message = {
          url: printerIp,
          data,
        };

        printWindow.postMessage(message, printerProxy);

        window.removeEventListener('message', printWindowMessageReceiver, false);
      }
    };

    window.addEventListener('message', printWindowMessageReceiver, false);
  }
}
