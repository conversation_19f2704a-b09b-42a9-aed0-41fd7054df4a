import { Winston_Papertrail, <PERSON> as log } from 'meteor/wylio:winston-papertrail';

import { App } from './imports/shared/app';

if (Meteor.isServer) {
  // creating a global server logger
  const environmentString = App.inProductionMode ? 'Production' : 'Dev';

  if (App.inProductionMode) {
    log.info('App in production mode - adding papertrail integration.');
    log.add(Winston_Papertrail, {
      levels: {
        debug: 0,
        info: 1,
        warn: 2,
        error: 3,
        auth: 4,
      },
      colors: {
        debug: 'blue',
        info: 'green',
        warn: 'red',
        error: 'red',
        auth: 'red',
      },
      host: 'logs2.papertrailapp.com',
      port: 42155,
      handleExceptions: true,
      program: `Pack - ${environmentString}`,
      json: true,
      colorize: true,
      logFormat: (level, message) => `${level} : ${message} `,
    });
  } else {
    log.info('App in Dev mode - (no papertrail integration).');
  }

  log.info('=====> App Restarted <=====');
}
