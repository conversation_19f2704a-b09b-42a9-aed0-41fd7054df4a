const glob = Npm.require('glob');
const fs = Npm.require('fs');
const path = Npm.require('path');

const filesInDir = (src) => {
  const pathPrefix = 'packages/pack';
  const fullPath = path.join(pathPrefix, src);

  return (glob.sync(`${fullPath}/**/*`) || [])
    .filter((filePath) => filePath && fs.lstatSync(filePath).isFile())
    .map((filePath) => filePath.replace(pathPrefix, './'));
};

Package.describe({
  name: 'pack',
  version: '0.0.1',
  // Brief, one-line summary of the package.
  summary: '',
  // URL to the Git repository containing the source code for this package.
  git: '',
  // By default, Meteor will default to using README.md for documentation.
  // To avoid submitting documentation, set this field to null.
  documentation: 'README.md',
});

Npm.depends({
  '@babel/runtime': '7.24.1',
  bcrypt: '3.0.8',
  'body-parser': '1.15.2',
  events: '1.1.1',
  faker: '4.1.0',
  jsbarcode: '3.6.0',
  jquery: '2.2.4',
  'meteor-node-stubs': '1.2.9',
  mockdate: '2.0.2',
  moment: '2.18.1',
  sinon: '3.2.0',
  waypoints: '4.0.1',
  glob: '7.1.2',
});

Package.onUse((api) => {
  api.versionsFrom('2.6.1');
  api.use([
    'accounts-password',
    'akasha:adm-zip@0.4.8',
    'alanning:roles@1.3.0',
    'aldeed:template-extension@4.1.0',
    'aldeed:collection2-core@2.0.0',
    'blaze@2.3.2',
    'blaze-html-templates@1.2.1',
    'check',
    'clinical:csv@0.2.0',
    'collection-manager@0.0.1',
    'dburles:factory@1.1.0',
    'dynamic-import@0.2.0',
    'ecmascript',
    'em0ney:amcharts@3.17.3',
    'es5-shim',
    'fcallem:reactive-table-semantic@0.0.1',
    'harrison:papa-parse@1.1.7',
    'johnantoni:meteor-scrollto@0.0.2',
    'jspdf:core@1.1.135',
    'juliancwirko:postcss@1.2.0',
    'kadira:blaze-layout@2.3.0',
    'less@2.8.0',
    'matb33:collection-hooks@1.1.4',
    'mediator@0.0.1',
    'meteor',
    'meteor-base',
    'meteorhacks:aggregate@1.3.0',
    'meteorhacks:picker@1.0.3',
    'minifier-js@2.2.1',
    'mixmax:underscore-updates@0.2.3',
    'mobile-experience',
    'momentjs:moment@2.19.4',
    'mongo',
    'ndjoe:jsbarcode@0.0.5',
    'ostrio:flow-router-extra@3.9.0',
    'percolate:synced-cron@1.3.2',
    'raix:eventemitter@0.1.3',
    'reactive-var@1.0.11',
    'reywood:publish-composite@1.5.2',
    'router@0.0.1',
    'session@1.1.7',
    'sha@1.0.9',
    'shell-server',
    'standard-minifier-js',
    'tracker',
    'ui@1.0.13',
    'underscore',
    'url@1.1.0',
    'wylio:winston-papertrail@0.1.2',
    'xolvio:cleaner@0.3.1']);
  api.addFiles('./client/styles/pack.css', 'client');
  api.addFiles('./imports/startup/server/index.js', 'server');
  api.addAssets(filesInDir('/public'), ['client', 'server']);
  api.addAssets(filesInDir('/private'), 'server');
  api.mainModule('pack-package-client.js', 'client');
  api.mainModule('pack-package-server.js', 'server');
});

Package.onTest((api) => {
  api.use(['ecmascript', 'meteortesting:mocha']);
  api.use('pack');
  api.mainModule('pack-package-client-tests.js', 'client');
  api.mainModule('pack-package-server-tests.js', 'server');
});
