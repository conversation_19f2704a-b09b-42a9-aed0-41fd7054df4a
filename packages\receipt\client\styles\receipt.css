
/* CSS declarations go here */
input::-ms-clear {
    display: none !important;
}

.side-nav{
  overflow-y:hidden;
}
.picker__wrap{
  zoom: 0.9  !important;
}
.reactive-table-input{
  margin-bottom:0px !important;
}

.tablet.ios{
  font-size:19px !important;
}

.itemRightHandSide {
  height: 74px;
}
.tablet.ios .itemRightHandSide{
  height:75px;
}
.desktop .itemRightHandSide{
  height:71px;
}

.tablet.ios #planAndReceiveWorkItemButton{
    height:74px;
}

.desktop #planAndReceiveWorkItemButton{
    height:70px;
}

.tablet.ios #planWorkItemButton{
    height:62px;
}

.desktop #planWorkItemButton{
    height:55px;
}

.desktop{
  font-size:17px !important;
}


.picker__frame{
  min-width: 400px;
  width:400px;
}

.picker__today{ 
  width:33%;
}
.picker__clear {
  width:33%;
}

.picker__close{
  width:33%;
}

#petersonLogo:hover {
  background-color: rgba(0, 0, 0, 0.1); 
}
.container-row{
	margin-bottom:0px;
}

.container-list-header{
	text-align:center;
}

.container-panel{
	padding:0px;
}

.group-title-row{
	margin-bottom:0px;
  margin-top:10px;
}

.work-item-card{
	cursor: pointer;
}

.card-reveal .close-actions{
	display:inline !important;
}

.work-item-identifier{
	text-transform: uppercase;
    font-weight: 500;
}

.username{
  text-transform: uppercase;
}

#processWorkItemCard{
  height:325px;
}

.navbar-fixed{
  position: absolute !important;
}

.work-item-overview-list{
  height:100%;
}

.scrollable-work-item-list{
  height:90%;
  overflow-y:auto;
  padding: 5px;
}


html,
body{
	height:100%;
}

#main{
	height: 100%;
  padding-top:60px;
  z-index:1;     
  overflow-y: scroll;
}

#__blaze-root{
  height: 100%;
}

.work-item-overview-container{
	height:100%;
  margin-bottom: 0px !important;
}

nav i.material-icons {
  font-size: 2rem;
}


@media only screen and (max-width: 601px){
  .work-item-overview-list{
    height:50%;
  }

  .scrollable-work-item-list{
    height:90%;
    overflow-y:auto;
    padding: 5px;
    position: relative;
  }
}

@media only screen and (min-width: 602px){
  .work-item-overview-list{
    height:100%;
  }

  .scrollable-work-item-list{
    height:90%;
    overflow-y:auto;
    padding: 5px;
    position: relative;
  }
}

@media only screen and (max-width: 992px){
  .work-item-action{
    padding: 0 1.25rem;
  }

  .close-actions{
    padding: 0 1.25rem;
  }

  .action-button-reveal{
    padding:5px !important;
  }
}

@media only screen and (min-width: 993px){
  .work-item-action{
    padding: 0 1rem;
  }

  .close-actions{
    padding: 0 1rem;
  }

  .action-button-reveal{
    padding:10px !important;
  }
}

@media only screen and (max-width: 993px){

	.work-item-identifier{
		text-align: center;
	}
}

#vor-badge{
  line-height: 14px;
  left: 65px;
  border-radius: 10px;
  min-width: 1.5rem;
  padding:0px;
  margin-top:4px;
  position: absolute;
}

.form-card{
  overflow: visible !important;
}

.camera-popup{
  border-radius: 0px !important;

  
}

.camera-popup button{
    border-radius: 0px !important;
    background-color: #1a2b59 !important;
    border: 0px solid #464f75; 
  }

.camera-popup .photo-preview {
    width: 100% !important;
}

.timez__list {
  list-style: none;
  padding: 0.75em 0 0.75em;
  margin: 0;
}
/**
 * The times on the clock.
 */
.timez__list-item {
  border-bottom: 1px solid #dddddd;
  border-top: 1px solid #dddddd;
  margin-bottom: -1px;
  position: relative;
  background: #ffffff;
  padding: .75em 1.25em;
}
@media (min-height: 46.75em) {
  .timez__list-item {
    padding: .5em 1em;
  }
}
/* Hovered time */
.timez__list-item:hover {
  cursor: pointer;
  color: white;
  background: #00838f;
  border-color: #0089ec;
  z-index: 10;
}
/* Highlighted and hovered/focused time */
.timez__list-item--highlighted {
  border-color: #0089ec;
  z-index: 10;
}
.timez__list-item--highlighted:hover,
.timez--focused .timez__list-item--highlighted {
  cursor: pointer;
  color: #000000;
  background: #b1dcfb;
}
/* Selected and hovered/focused time */
.timez__list-item--selected,
.timez__list-item--selected:hover,
.timez--focused .timez__list-item--selected {
  background: #0089ec;
  color: #ffffff;
  z-index: 10;
}
/* Disabled time */
.timez__list-item--disabled,
.timez__list-item--disabled:hover,
.timez--focused .timez__list-item--disabled {
  background: #f5f5f5;
  border-color: #f5f5f5;
  color: #dddddd;
  cursor: default;
  border-color: #dddddd;
  z-index: auto;
}
/**
 * The clear button
 */
.timez--time .timez__button--clear {
  display: block;
  width: 80%;
  margin: 1em auto 0;
  padding: 1em 1.25em;
  background: none;
  border: 0;
  font-weight: 500;
  font-size: .67em;
  text-align: center;
  text-transform: uppercase;
  color: #666;
}
.timez--time .timez__button--clear:hover,
.timez--time .timez__button--clear:focus {
  color: #000000;
  background: #b1dcfb;
  background: #ee2200;
  border-color: #ee2200;
  cursor: pointer;
  color: #ffffff;
  outline: none;
}
.timez--time .timez__button--clear:before {
  top: -0.25em;
  color: #666;
  font-size: 1.25em;
  font-weight: bold;
}
.timez--time .timez__button--clear:hover:before,
.timez--time .timez__button--clear:focus:before {
  color: #ffffff;
  border-color: #ffffff;
}

/* ==========================================================================
   $BASE-timez
   ========================================================================== */
/**
 * Note: the root timez element should *NOT* be styled more than what’s here.
 */
.timez {
  font-size: 16px;
  text-align: left;
  line-height: 1.2;
  color: #000000;
  position: absolute;
  z-index: 10000;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}
/**
 * The timez input element.
 */
.timez__input {
  cursor: default;
}
/**
 * When the timez is opened, the input element is “activated”.
 */
.timez__input.timez__input--active {
  border-color: #0089ec;
}
/**
 * The holder is the only “scrollable” top-level container element.
 */
.timez__holder {
  width: 100%;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

/*!
 * Default mobile-first, responsive styling for pickadate.js
 * Demo: http://amsul.github.io/pickadate.js
 */
/**
 * Note: the root timez element should *NOT* be styled more than what’s here.
 */
/**
 * Make the holder and frame fullscreen.
 */
.timez__holder,
.timez__frame {
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  -webkit-transform: translateY(100%);
      -ms-transform: translateY(100%);
          transform: translateY(100%);
}
/**
 * The holder should overlay the entire screen.
 */
.timez__holder {
  position: fixed;
  transition: background 0.15s ease-out, -webkit-transform 0s 0.15s;
  transition: background 0.15s ease-out, transform 0s 0.15s;
  -webkit-backface-visibility: hidden;
}
/**
 * The frame that bounds the box contents of the timez.
 */
.timez__frame {
  position: absolute;
  margin: 0 auto;
  min-width: 256px;
  max-width: 666px;
  width: 100%;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
  filter: alpha(opacity=0);
  -moz-opacity: 0;
  opacity: 0;
  transition: all 0.15s ease-out;
}
@media (min-height: 33.875em) {
  .timez__frame {
    overflow: visible;
    top: auto;
    bottom: -100%;
    max-height: 80%;
  }
}
@media (min-height: 40.125em) {
  .timez__frame {
    margin-bottom: 7.5%;
  }
}
/**
 * The wrapper sets the stage to vertically align the box contents.
 */
.timez__wrap {
  display: table;
  width: 100%;
  height: 100%;
}
@media (min-height: 33.875em) {
  .timez__wrap {
    display: block;
  }
}
/**
 * The box contains all the timez contents.
 */
.timez__box {
  background: #ffffff;
  display: table-cell;
  vertical-align: middle;
}
@media (min-height: 26.5em) {
  .timez__box {
    font-size: 1.25em;
  }
}
@media (min-height: 33.875em) {
  .timez__box {
    display: block;
    font-size: 1.33em;
    border: 1px solid #777777;
    border-top-color: #898989;
    border-bottom-width: 0;
    border-radius: 5px 5px 0 0;
    box-shadow: 0 12px 36px 16px rgba(0, 0, 0, 0.24);
  }
}
@media (min-height: 40.125em) {
  .timez__box {
    font-size: 1.5em;
    border-bottom-width: 1px;
    border-radius: 5px;
  }
}
/**
 * When the timez opens...
 */
.timez--opened .timez__holder {
  -webkit-transform: translateY(0);
      -ms-transform: translateY(0);
          transform: translateY(0);
  background: transparent;
  -ms-filter: "progid:DXImageTransform.Microsoft.gradient(startColorstr=#1E000000,endColorstr=#1E000000)";
  zoom: 1;
  background: rgba(0, 0, 0, 0.32);
  transition: background 0.15s ease-out;
}
.timez--opened .timez__frame {
  -webkit-transform: translateY(0);
      -ms-transform: translateY(0);
          transform: translateY(0);
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
  filter: alpha(opacity=100);
  -moz-opacity: 1;
  opacity: 1;
}
@media (min-height: 33.875em) {
  .timez--opened .timez__frame {
    top: auto;
    bottom: 0;
  }
}


/* ==========================================================================
   $DEFAULT-TIME-timez
   ========================================================================== */
/**
 * The frame the bounds the time timez.
 */
.timez--time .timez__frame {
  min-width: 256px;
  max-width: 320px;
}
/**
 * The timez box.
 */
.timez--time .timez__box {
  font-size: 1em;
  background: #f2f2f2;
  padding: 0;
}
@media (min-height: 40.125em) {
  .timez--time .timez__box {
    margin-bottom: 5em;
  }
}

.tt-hint{
  opacity: 0.75 !important;
}

.twitter-typeahead{
  width:100%;
}

.typeahead,
.tt-query,
.tt-hint {
  width: 396px;
  padding: 8px 12px;
  line-height: 30px;
  border: 2px solid #ccc;
  -webkit-border-radius: 0px;
     -moz-border-radius: 0px;
          border-radius: 0px;
  outline: none;
}

.typeahead {
  background-color: #fff;
}

.tt-query {
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
     -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
          box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}

.tt-hint {
  color: #999
}

.tt-menu {
  width: 100%;
  margin: -15px 0;
  padding: 8px 0;
  background-color: #fff;
  border: 1px solid #ccc;
  border: 1px solid rgba(0, 0, 0, 0.2);
  -webkit-border-radius: 0px;
     -moz-border-radius: 0px;
          border-radius: 0px;
  -webkit-box-shadow: 0 5px 10px rgba(0,0,0,.2);
     -moz-box-shadow: 0 5px 10px rgba(0,0,0,.2);
          box-shadow: 0 5px 10px rgba(0,0,0,.2);
}

.tt-suggestion {
  padding: 3px 20px;
  font-size: 18px;
  line-height: 24px;
}

.tt-suggestion > strong {
  font-weight: 600;
}


.admin-mode{
  border-left-color: red;
  border-left-width: 5px;
  border-left-style: solid;
  border-right-color: red;
  border-right-width: 5px;
  border-right-style: solid;
}

.input-field label{
  color: #7B7B7B;
}

.dashboard-summary-container{
  cursor: pointer;
  padding: 5px;
}

#toDashboard{
  cursor: pointer;
}

.tablet [type="checkbox"] + label:before, [type="checkbox"]:not(.filled-in) + label:after{
    width:24px;
    height:24px;
}

.s-alert-info {
    min-width:325px;
    max-width: 325px;
}

.s-alert-effect-scale .s-alert-close::before, .s-alert-effect-scale .s-alert-close::after{
    background: #7a003b;
}

.s-alert-effect-scale .s-alert-close:hover::before, .s-alert-effect-scale .s-alert-close:hover::after{
    background: #7a003b;
}

.overview-filter-search-input {
  background-color: white !important;
  padding-left: 1rem !important;
  padding-top: 0.2rem !important;
  padding-bottom: 0.2rem !important;
  color: initial !important;
}

.ncr-select-container > .select2 {
  margin-left: 0 !important;
  width: 100% !important;
  color: #9e9e9e !important;
  font-size: 1rem !important;
}

.select2-container--default .select2-results__option--highlighted[aria-selected], div.tagsinput span.tag {
    background-color: #1a2b59 !important;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: #1a2b59 !important;
}

.select2-results__group {
  font-weight: bold !important;
}

.input-field label .active {
  font-size: 0.8rem;
  -webkit-transform: translateY(-140%);
  transform: translateY(-140%);
}

.select2-selection:focus {
  border-bottom: 1px solid #26a69a !important;
  border-bottom-width: 1px !important;
  border-bottom-style: solid !important;
  border-bottom-color: rgb(38, 166, 154) !important;
  box-shadow: 0 1px 0 0 #26a69a !important;
}

.select2-selection[aria-expanded="true"] {
  border-bottom: 1px solid #26a69a !important;
}

.green-color {
  color: #26a69a !important;
}

.failed-inspection {
  border: 1px groove red;
}

.failed-inspection > div.container-row > div.col > div.row {
  background-color: #f44336;
  color: white;
}

.center-aligned-column {
  text-align: center;
}

h5 {
  font-family: "Roboto", sans-serif;
  font-size: 1.64rem;
}