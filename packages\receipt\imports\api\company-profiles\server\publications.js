import { Meteor } from 'meteor/meteor';
import { CompanyProfiles } from '../company-profiles';

Meteor.publish('currentUserCompanyProfile', function currentUserCompanyProfile() {
  if (this.userId) {
    var user = Meteor.users.findOne(this.userId);

    var userProfile = user.profile;
    var userCompanyProfileId = userProfile.companyProfileId;

    return CompanyProfiles.find({ _id: userCompanyProfileId });
  }
  return [];
});
