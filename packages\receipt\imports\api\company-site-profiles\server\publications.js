import { Meteor } from 'meteor/meteor';
import { CompanySiteProfiles } from '../company-site-profiles';

Meteor.publish('currentUserCompanySiteProfiles', function currentUserCompanySiteProfiles() {
  if (this.userId) {
    const user = Meteor.users.findOne(this.userId);
    const userProfile = user.profile;
    const userSites = userProfile
      .sites
      .map((x) => x.companySiteProfileId);

    return CompanySiteProfiles.find({
      _id: {
        $in: userSites,
      },
    });
  }
  return [];
});
