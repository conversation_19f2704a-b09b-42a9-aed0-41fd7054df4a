import { CompanyProfiles } from '../../company-profiles/company-profiles';
import { CompanySiteProfiles } from '../../company-site-profiles/company-site-profiles';
import { Meteor } from 'meteor/meteor';
import { WorkItemEventStates } from '../../../shared/work-item-event-states';
import { WorkItemEvents } from '../../work-item-events/work-item-events';

var zeroFillForMissingClients = function getZeroFillForMissingClients(clients, clientCounts) {
  _.each(clients, function (client) {
    var found = false;

    _.find(clientCounts, function (clientCount) {
      if (clientCount._id.clientName === client.name) {
        found = true;
      }
    });

    if (!found) {
      clientCounts.push({
        _id: {
          clientId: client._id,
          clientName: client.name,
        },
        averageTurnaround: 0,
        minTurnaround: 0,
        maxTurnaround: 0,
        processed: 0,
      });
    }
  });

  return clientCounts;
};

var zeroFillForMissingWorkItemTypes = function getZeroFillForMissingWorkItemTypes(workItemTypes, workItemTypeCounts) {
  _.each(workItemTypes, function(workItemType) {
    var found = false;

    _.find(workItemTypeCounts, function(workItemTypeCount) {
      if (workItemTypeCount._id.workItemType === workItemType.name) {
        found = true;
      }
    });

    if (!found) {
      workItemTypeCounts.push({
        _id: {
          workItemType: workItemType.name,
        },
        averageTurnaround: 0,
        minTurnaround: 0,
        maxTurnaround: 0,
        processed: 0,
      });
    }
  });

  return workItemTypeCounts;
};

Meteor.methods({
  completedToCollectedPerClientReport(companyId, siteId, fromDate, toDate) {
    var momentFrom = moment(fromDate);
    var momentTo = moment(toDate);

    var periodInDays = momentTo.diff(momentFrom, 'days');

    var groupingKey;

    var aggregatePipeline = [
      {
        $match: {
          deleted: { $exists: false },
          isLatest: true,
          siteId: siteId,
          companyId: companyId,
          state: {
            $in: [
              WorkItemEventStates.COLLECTED,
            ],
          },
          'lifecycleData.collected.timestamp': {
            $gte: fromDate,
            $lte: toDate,
          },
        },
      },
      {
        $project: {
          clientId: '$lifecycleData.planned.client._id',
          clientName: '$lifecycleData.planned.client.name',
          timeDifference: {
            '$subtract': ['$lifecycleData.collected.timestamp', '$lifecycleData.completed.timestamp'],
          },
        },
      },
      {
        $group: {
          _id: {
            clientId: '$clientId',
            clientName: '$clientName',
          },
          averageTurnaround: { $avg: '$timeDifference' },
          minTurnaround: { $min: '$timeDifference' },
          maxTurnaround: { $max: '$timeDifference' },
          processed: { $sum: 1 },
        },
      }];

    var clientAverages = WorkItemEvents
      .aggregate(aggregatePipeline);

    var companySiteProfile = CompanySiteProfiles.findOne({ _id: siteId });
    var allClients = companySiteProfile.configuration.clients;
    zeroFillForMissingClients(allClients, clientAverages);

    return clientAverages;
  },

  completedToCollectedPerWorkItemTypeReport(companyId, siteId, fromDate, toDate, client) {
    var momentFrom = moment(fromDate);
    var momentTo = moment(toDate);

    var periodInDays = momentTo.diff(momentFrom, 'days');

    var groupingKey;

    var aggregatePipeline = [
      {
        $match: {
          deleted: { $exists: false },
          isLatest: true,
          siteId: siteId,
          companyId: companyId,
          state: {
            $in: [
              WorkItemEventStates.COLLECTED,
            ],
          },
          'lifecycleData.collected.timestamp': {
            $gte: fromDate,
            $lte: toDate,
          },
          'lifecycleData.planned.client._id': client,
        },
      },
      {
        $project: {
          workItemType: { $ifNull: ['$lifecycleData.planned.workItemType.name', 'Unknown'] },
          timeDifference: {
            '$subtract': ['$lifecycleData.collected.timestamp', '$lifecycleData.completed.timestamp'],
          },
        },
      },
      {
        $group: {
          _id: {
            workItemType: '$workItemType',
          },
          averageTurnaround: { $avg: '$timeDifference' },
          minTurnaround: { $min: '$timeDifference' },
          maxTurnaround: { $max: '$timeDifference' },
          processed: { $sum: 1 },
        },
      }];

    var workItemTypeAverages = WorkItemEvents
      .aggregate(aggregatePipeline);

    var companySiteProfile = CompanySiteProfiles.findOne({ _id: siteId });
    zeroFillForMissingWorkItemTypes(companySiteProfile.configuration.workItemTypes, workItemTypeAverages);

    return workItemTypeAverages;
  },
});
