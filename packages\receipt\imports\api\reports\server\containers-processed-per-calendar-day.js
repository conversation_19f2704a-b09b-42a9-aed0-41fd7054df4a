import { LoggerFactory } from '../../../shared/logger-factory';
import { Meteor } from 'meteor/meteor';
import { WorkItemEventStates } from '../../../shared/work-item-event-states';
import { WorkItemEvents } from '../../work-item-events/work-item-events';
import { moment } from 'meteor/momentjs:moment';

const logger = LoggerFactory.getLogger(__filename);

Meteor.methods({
  containersProcessedPerCalendarDay(companyId, siteId, fromDate, toDate, client) {
    const filteredOnSiteEventsQuery = {
      isLatest: true,
      deleted: {
        $exists: false,
      },
      $or: [
        {
          state: {
            $nin: [
              WorkItemEvents.COLLECTED,
              WorkItemEvents.PLANNED,
            ],
          },
        },
        {
          timestamp: {
            $gte: fromDate,
          },
          state: WorkItemEvents.COLLECTED,
        },
      ],
    };

    if (client) {
      filteredOnSiteEventsQuery['lifecycleData.planned.client._id'] = client;
    }

    const filteredOnSiteEvents =
      WorkItemEvents.find(filteredOnSiteEventsQuery).fetch();

    const filteredAvailableToCollectEventsQuery = {
      isLatest: true,
      deleted: {
        $exists: false,
      },
      $or: [
        {
          state: WorkItemEventStates.COMPLETED,
        },
        {
          timestamp: {
            $gte: fromDate,
          },
          state: WorkItemEventStates.COLLECTED,
        },
      ],
    };

    if (client) {
      filteredAvailableToCollectEventsQuery['lifecycleData.planned.client._id'] = client;
    }

    const filteredAvailableToCollectEvents =
      WorkItemEvents.find(filteredAvailableToCollectEventsQuery).fetch();

    const stateCountAggregateQuery = {
      deleted: { $exists: false },
      siteId,
      companyId,
      state: {
        $in: [
          WorkItemEventStates.RECEIVED,
          WorkItemEventStates.COMPLETED,
          WorkItemEventStates.COLLECTED,
        ],
      },
      timestamp: {
        $gte: fromDate,
        $lte: toDate,
      },
    };

    if (client) {
      stateCountAggregateQuery['lifecycleData.planned.client._id'] = client;
    }

    const aggregatePipeline = [
      {
        $match: stateCountAggregateQuery,
      },
      {
        $group: {
          _id: {
            state: '$state',
            day: { $dayOfMonth: '$timestamp' },
          },
          total: { $sum: 1 },
        },
      },
      {
        $project: {
          state: '$_id.state',
          day: '$_id.day',
          total: '$total',
        },
      },
    ];

    const containerInfo = WorkItemEvents
      .aggregate(aggregatePipeline);

    let daysInMonth = moment(fromDate).daysInMonth();
    if (moment(fromDate).format('MM') === moment().format('MM')) {
      daysInMonth = parseInt(moment().format('DD'), 10);
    }

    const containerSummary = [];

    const monthNumber = moment(fromDate).format('MM');
    const yearNumber = moment(fromDate).format('YYYY');
    for (let dayNumber = 1; dayNumber <= daysInMonth; dayNumber++) {
      const dayObj = {
        day: dayNumber,
        containersIn: 0,
        containersProcessed: 0,
        containersOut: 0,
        totalNumberOfContainersOnSite: 0,
        totalNumberOfContainersAvailableToCollect: 0,
      };

      for (const event of containerInfo) {
        const isContainerIn = event.state === WorkItemEventStates.RECEIVED;
        const isContainerProcessed = event.state === WorkItemEventStates.COMPLETED;
        const isContainerOut = event.state === WorkItemEventStates.COLLECTED;
        if (event.day === dayNumber) {
          if (isContainerIn) {
            dayObj.containersIn = event.total;
          }
          if (isContainerProcessed) {
            dayObj.containersProcessed = event.total;
          }
          if (isContainerOut) {
            dayObj.containersOut = event.total;
          }
        }
      }
      const relevantDate = moment(`${yearNumber}-${monthNumber}-${dayNumber <= 9 ? `0${dayNumber}` : dayNumber}T21:00:00Z`).toDate();
      for (const event of filteredOnSiteEvents) {
        const isReceivedBeforeRelevantDate = event.lifecycleData.received.timestamp < relevantDate;
        const isCollectedAfterRelevantDate = event.lifecycleData.collected && event.lifecycleData.collected.timestamp > relevantDate;
        const isNotCollected = !event.lifecycleData.collected;
        if (isReceivedBeforeRelevantDate && (isCollectedAfterRelevantDate || isNotCollected)) {
          dayObj.totalNumberOfContainersOnSite++;
        }
      }
      for (const event of filteredAvailableToCollectEvents) {
        const isCompletedBeforeRelevantDate = event.lifecycleData.completed.timestamp < relevantDate;
        const isCollected = event.lifecycleData.collected;
        const isCollectedAfterRelevantDate = isCollected && event.lifecycleData.collected.timestamp > relevantDate;
        if (isCompletedBeforeRelevantDate && (!isCollected || isCollectedAfterRelevantDate)) {
          dayObj.totalNumberOfContainersAvailableToCollect++;
        }
      }
      containerSummary.push(dayObj);
    }
    containerSummary.sort((a, b) => a.day - b.day);

    return containerSummary;
  },
});
