import { LoggerFactory } from '../../../shared/logger-factory';
import { Meteor } from 'meteor/meteor';
import { WorkItemEventStates } from '../../../shared/work-item-event-states';
import { WorkItemEvents } from '../../work-item-events/work-item-events';

const logger = LoggerFactory.getLogger(__filename);

Meteor.methods({
  containersProcessedReport(companyId, siteId, fromDate, toDate, client) {
    const query = {
      deleted: { $exists: false },
      siteId,
      companyId,
      state: {
        $in: [
          WorkItemEventStates.RECEIVED,
          WorkItemEventStates.COMPLETED,
          WorkItemEventStates.COLLECTED,
        ],
      },
      timestamp: {
        $gte: fromDate,
        $lte: toDate,
      },
    };

    if (client) {
      query['lifecycleData.planned.client._id'] = client;
    }

    const allProcessedEventsInMonth = WorkItemEvents
      .find(query)
      .fetch();

    const containersProcessedSummary = {
      in: allProcessedEventsInMonth.filter((x) => x.state === WorkItemEventStates.RECEIVED).length,
      processed: allProcessedEventsInMonth.filter((x) => x.state === WorkItemEventStates.COMPLETED).length,
      out: allProcessedEventsInMonth.filter((x) => x.state === WorkItemEventStates.COLLECTED).length,
    };

    return containersProcessedSummary;
  },
});
