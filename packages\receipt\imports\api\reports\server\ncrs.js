import { CompanyProfiles } from '../../company-profiles/company-profiles';
import { CompanySiteProfiles } from '../../company-site-profiles/company-site-profiles';
import { Meteor } from 'meteor/meteor';
import { WorkItemEventStates } from '../../../shared/work-item-event-states';
import { WorkItemEvents } from '../../work-item-events/work-item-events';

const zeroFillForMissingClients = function getZeroFillForMissingClients(clients, clientCounts) {
  _.each(clients, (client) => {
    var found = false;

    _.find(clientCounts, function (clientCount) {
      if (clientCount._id.clientName === client.name) {
        found = true;
      }
    });

    if (!found) {
      clientCounts.push({
        _id: {
          clientId: client._id,
          clientName: client.name,
        },
        arrivalFails: 0,
        departureFails: 0,
        emptyFails: 0,
        fails: 0,
        processed: 0,
      });
    }
  });

  return clientCounts;
};

const zeroFillForMissingInstallations = function getZeroFillForMissingInstallations(installations, installationCounts) {
  _.each(installations, (installation) => {
    var found = false;

    _.find(installationCounts, function (installationCount) {
      if (installationCount._id.osshoreInstallationName === installation) {
        found = true;
      }
    });

    if (!found) {
      installationCounts.push({
        _id: {
          offshoreInstallationName: installation,
        },
        arrivalFails: 0,
        departureFails: 0,
        emptyFails: 0,
        fails: 0,
        processed: 0,
      });
    }
  });

  return installationCounts;
};

var zeroFillForMissingWorkItemTypes = function getZeroFillForMissingWorkItemTypes(workItemTypes, workItemTypeCounts) {
  _.each(workItemTypes, (workItemType) => {
    var found = false;

    _.find(workItemTypeCounts, function (workItemTypeCount) {
      if (workItemTypeCount._id.workItemType === workItemType.name) {
        found = true;
      }
    });

    if (!found) {
      workItemTypeCounts.push({
        _id: {
          workItemType: workItemType.name,
        },
        arrivalFails: 0,
        departureFails: 0,
        emptyFails: 0,
        fails: 0,
        processed: 0,
      });
    }
  });

  return workItemTypeCounts;
};

Meteor.methods({
  ncrsPerClientReport(companyId, siteId, fromDate, toDate) {
    var momentFrom = moment(fromDate);
    var momentTo = moment(toDate);

    var periodInDays = momentTo.diff(momentFrom, 'days');

    var groupingKey;

    var aggregatePipeline = [
      {
        $match: {
          deleted: { $exists: false },
          isLatest: true,
          siteId,
          companyId,
          state: {
            $in: [
              WorkItemEventStates.COMPLETED,
              WorkItemEventStates.COLLECTED,
            ],
          },
          'lifecycleData.completed.timestamp': {
            $gte: fromDate,
            $lte: toDate,
          },
        },
      },
      {
        $project: {
          clientName: { $ifNull: ['$latestVorInformation.vendorName', 'Vendor-Not-Supplied'] },
          clientId: '$latestVorInformation.masterVendorId',
          fails: '$lifecycleData.marshallingYardInspection.fails',
          failed: { $cond: { if: { $gt: ['$lifecycleData.marshallingYardInspection.fails', 0] }, then: 1, else: 0 } },
        },
      },
      {
        $group: {
          _id: {
            clientId: '$clientId',
            clientName: '$clientName',
          },
          failed: { $sum: '$failed' },
          processed: { $sum: 1 },
          totalFailures: { $sum: '$fails' },
        },
      }];

    const clientFails = WorkItemEvents
      .aggregate(aggregatePipeline);

    return clientFails.filter((x) => x.totalFailures > 0);
  },
  vendorBreakdownOfTypesOfFailure(companyId, siteId, vendorName, fromDate, toDate) {
    const momentFrom = moment(fromDate);
    const momentTo = moment(toDate);

    const periodInDays = momentTo.diff(momentFrom, 'days');

    const vendorNameToFilterBy = vendorName === 'Vendor-Not-Supplied' ? null : vendorName;

    const ncrPropertiesToCheck = [
      'doesNotRequireInspection',
      'freeFromExcessiveCorrosionOrHoles',
      'allDrainageHolesAreClear',
      'liftingSetsProperlyFitted',
      'slingsVisuallyInspected',
      'droppedObjectsRemoved',
      'hasDestinationLabelBeenAdded',
      'itemsSecured',
      'dgLabelsArePresent',
      'retainingNetSecure',
      'snagHazardsPrevented',
      'doorsSecured',
      'loadLiftsHorizontally',
      'weightBelowMaximumWeight',
      'heavyLiftTagHasBeenAdded',
      'tubularsSlungCorrectly',
      'fulfilledOperatorsRequirementsForHiredAndPortable',
    ];

    const companySiteProfile = CompanySiteProfiles.findOne({
      _id: siteId,
    });

    const failureReasons =
      Object.keys(companySiteProfile.configuration.inspectionFailureReasons).reduce((previous, key) => {
        return previous.concat(companySiteProfile.configuration.inspectionFailureReasons[key]);
      }, []);

    const initialResult = ncrPropertiesToCheck.map((check) => {
      return {
        ncrCheck: check,
        itemsFailedCheck: 0,
        workItems: [],
      };
    }).concat(
      failureReasons.map((reason) => {
        return {
          ncrCheck: reason,
          itemsFailedCheck: 0,
          workItems: [],
        };
      }));

    const vendorItems = WorkItemEvents.find({
      deleted: { $exists: false },
      isLatest: true,
      siteId,
      companyId,
      state: {
        $in: [
          WorkItemEventStates.COMPLETED,
          WorkItemEventStates.COLLECTED,
        ],
      },
      'lifecycleData.completed.timestamp': {
        $gte: fromDate,
        $lte: toDate,
      },
      'latestVorInformation.vendorName': vendorNameToFilterBy,
    }, { fields: { identifier: 1, lifecycleData: 1, lifecycleId: 1 } }).fetch();

    if (vendorItems && vendorItems.length) {
      return vendorItems.reduce((acc, item) => {
        const itemNcrResult = item.lifecycleData.marshallingYardInspection;

        ncrPropertiesToCheck.forEach((ncrCheck) => {
          const itemFailedCheck = itemNcrResult[ncrCheck] === false;

          if (itemFailedCheck) {
            const index = acc.findIndex((category) => category.ncrCheck === ncrCheck);

            acc[index].workItems.push(
              {
                identifier: item.identifier,
                lifecycleId: item.lifecycleId,
                timestamp: itemNcrResult.timestamp,
              },
            );

            acc[index].itemsFailedCheck++;
          }
        });

        failureReasons.forEach((reason) => {
          const itemHasFailureReason =
            itemNcrResult.failureReasons &&
            itemNcrResult.failureReasons.length > 0 &&
            itemNcrResult.failureReasons.includes(reason);

          if (itemHasFailureReason) {
            console.log(reason);
            const index = acc.findIndex((category) => category.ncrCheck === reason);

            acc[index].workItems.push(
              {
                identifier: item.identifier,
                lifecycleId: item.lifecycleId,
                timestamp: itemNcrResult.timestamp,
              });

            acc[index].itemsFailedCheck++;
          }
        });

        return acc;
      }, initialResult).filter((x) => x.itemsFailedCheck > 0);
    }

    return initialResult;
  },

  ncrsPerInstallationReport(companyId, siteId, fromDate, toDate) {
    const momentFrom = moment(fromDate);
    const momentTo = moment(toDate);

    const periodInDays = momentTo.diff(momentFrom, 'days');

    var groupingKey;

    var aggregatePipeline = [
      {
        $match: {
          deleted: { $exists: false },
          isLatest: true,
          siteId,
          companyId,
          state: {
            $in: [
              WorkItemEventStates.COMPLETED,
            ],
          },
          'lifecycleData.completed.timestamp': {
            $gte: fromDate,
            $lte: toDate,
          },
        },
      },
      {
        $project: {
          offshoreInstallationName: '$lifecycleData.planned.clientLocation',
          fails: { $add: [
            '$lifecycleData.empty.fails'] },
          emptyFails: '$lifecycleData.empty.fails',
        },
      },
      {
        $group: {
          _id: {
            offshoreInstallationName: '$offshoreInstallationName',
          },
          fails: { $sum: '$fails' },
          emptyFails: { $sum: '$emptyFails' },
          processed: { $sum: 1 },
        },
      }];

    const installationFails = WorkItemEvents
      .aggregate(aggregatePipeline);

    const companySiteProfile = CompanySiteProfiles.findOne({
      _id: siteId,
    });

    const allInstallations =
        companySiteProfile.configuration.locations.map((x) => x.name);

    zeroFillForMissingInstallations(allInstallations, installationFails);

    return installationFails;
  },
  ncrsPerWorkItemTypeReport(companyId, siteId, fromDate, toDate, client) {
    var momentFrom = moment(fromDate);
    var momentTo = moment(toDate);

    var periodInDays = momentTo.diff(momentFrom, 'days');

    var groupingKey;

    var aggregatePipeline = [
      {
        $match: {
          deleted: { $exists: false },
          isLatest: true,
          siteId,
          companyId,
          state: {
            $in: [
              WorkItemEventStates.COLLECTED,
            ],
          },
          'lifecycleData.collected.timestamp': {
            $gte: fromDate,
            $lte: toDate,
          },
          'lifecycleData.planned.client._id': client,
        },
      },
      {
        $project: {
          workItemType: { $ifNull: ['$lifecycleData.planned.workItemType.name', 'Unknown'] },
          fails: { $add: ['$lifecycleData.arrivalInspection.fails',
            '$lifecycleData.departureInspection.fails',
            '$lifecycleData.empty.fails'] },
          arrivalFails: '$lifecycleData.arrivalInspection.fails',
          departureFails: '$lifecycleData.departureInspection.fails',
          emptyFails: '$lifecycleData.empty.fails',
        },
      },
      {
        $group: {
          _id: {
            workItemType: '$workItemType',
          },
          fails: { $sum: '$fails' },
          arrivalFails: { $sum: '$arrivalFails' },
          departureFails: { $sum: '$departureFails' },
          emptyFails: { $sum: '$emptyFails' },
          processed: { $sum: 1 },
        },
      }];

    var workItemTypeAverages = WorkItemEvents
      .aggregate(aggregatePipeline);

    var companySiteProfile = CompanySiteProfiles.findOne({ _id: siteId });
    zeroFillForMissingWorkItemTypes(companySiteProfile.configuration.workItemTypes, workItemTypeAverages);

    return workItemTypeAverages;
  },
});
