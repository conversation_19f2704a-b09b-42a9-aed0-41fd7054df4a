import { CompanyProfiles } from '../../company-profiles/company-profiles';
import { CompanySiteProfiles } from '../../company-site-profiles/company-site-profiles';
import { Meteor } from 'meteor/meteor';
import { WorkItemEventStates } from '../../../shared/work-item-event-states';
import { WorkItemEvents } from '../../work-item-events/work-item-events';

var defaultTargetTimeInHoursForEventsWithNoType = 120;

var zeroFillForMissingInstallations = function getZeroFillForMissingInstallations(installations, result) {
  _.each(installations, function (offshoreInstallation) {
    var found = false;

    _.find(result, function (clientResult) {
      if (clientResult.offshoreInstallationName === offshoreInstallation) {
        found = true;
      }
    });

    if (!found) {
      result.push({
        offshoreInstallationName: offshoreInstallation,
        totalProcessed: 0,
        totalBeyondTargetTime: 0,
      });
    }
  });

  return result;
};

var zeroFillForMissingWorkItemTypes = function getZeroFillForMissingWorkItemTypes(workItemTypes, result) {
  _.each(workItemTypes, function(workItemType) {
    var found = false;

    _.find(result, function(workItemTypeCount) {
      if (workItemTypeCount.workItemType === workItemType.name) {
        found = true;
      }
    });

    if (!found) {
      result.push({
        workItemType: workItemType.name,
        totalProcessed: 0,
        totalBeyondTargetTime: 0,
      });
    }
  });

  return result;
};

var getEventsWhereTurnaroundCompleted = function getEventsWhereTurnaroundCompleted(companyId, siteId, fromDate, toDate, client) {
  var momentFrom = moment(fromDate);
  var momentTo = moment(toDate);

  var query = {
    deleted: { $exists: false },
    isLatest: true,
    state: {
      $in: [
        WorkItemEventStates.COMPLETED,
        WorkItemEventStates.COLLECTED,
      ]
    },
    'lifecycleData.completed.timestamp': {
      $gte: fromDate,
      $lte: toDate,
    },
    companyId: companyId,
    siteId: siteId,
  };

  if (client) {
    query['lifecycleData.planned.client._id'] = client;
  }

  var collectedInMonth = WorkItemEvents.find(
    query,
    {
      fields: {
        'lifecycleData.planned.client._id': 1,
        'lifecycleData.planned.client.name': 1,
        'lifecycleData.planned.workItemType.name': 1,
        'lifecycleData.planned.workItemType._id': 1,
        'lifecycleData.planned.type._id': 1,
        'lifecycleData.completed.timestamp': 1,
        'lifecycleData.planned.dischargeTimestamp': 1,
        'lifecycleData.planned.clientLocation': 1,
      },
    },
  ).fetch();

  console.log(JSON.stringify(collectedInMonth));
  return collectedInMonth;
};

var getAssociatedTargetTimesForEvents = function getAssociatedTargetTimesForEvents(collectedInMonth, siteProfile) {
  var turnaroundsWithTargetTimes = _.map(collectedInMonth, function(clientTurnaround) {
    var targetTime = defaultTargetTimeInHoursForEventsWithNoType;

    targetTime = 120;

    var collected = moment(clientTurnaround.lifecycleData.completed.timestamp);
    var received = moment(clientTurnaround.lifecycleData.planned.dischargeTimestamp);

    var duration = moment.duration(collected.diff(received));
    var durationHours = duration.asHours();

    console.log(clientTurnaround);
    return {
      offshoreInstallationName: clientTurnaround.lifecycleData.planned.clientLocation,
      actualTime: durationHours,
      targetTime: targetTime,
      beyondTargetTime: durationHours > targetTime,
    };
  });

  return turnaroundsWithTargetTimes;
};

var internalTurnaroundBeyondTargetTime = function getInternalTurnaroundBeyondTargetTime(companyId, siteId, fromDate, toDate, client) {
  var eventsWhereTurnaroundCompleted = getEventsWhereTurnaroundCompleted(companyId, siteId, fromDate, toDate, client);

  var siteProfile = CompanySiteProfiles.findOne({ _id: siteId });

  var eventsWithTargetTimes = getAssociatedTargetTimesForEvents(eventsWhereTurnaroundCompleted, siteProfile);

  var result = {
    totalProcessed: eventsWithTargetTimes.length,
    totalBeyondTargetTime: _(eventsWithTargetTimes).reduce(function (memo, turnaround) {
      if (turnaround.beyondTargetTime) {
        return memo + 1;
      }
      return memo;
    }, 0),
  };

  return result;
};

var clientGroupedInternalTurnaroundBeyondTargetTime = function(companyId, siteId, fromDate, toDate) {
  var eventsWhereTurnaroundCompleted = getEventsWhereTurnaroundCompleted(companyId, siteId, fromDate, toDate);

  var siteProfile = CompanySiteProfiles.findOne({ _id: siteId });

  var eventsWithTargetTimes = getAssociatedTargetTimesForEvents(eventsWhereTurnaroundCompleted, siteProfile);

  var clientGroupedEvents = _(eventsWithTargetTimes)
    .groupBy(function(eventWithTargetTime) {  return eventWithTargetTime.offshoreInstallationName._id; });

  var clientTargetTimes = siteProfile.configuration.clientTargetTimes;
  var workItemTypes = siteProfile.configuration.workItemTypes;

  var result = _(clientGroupedEvents)
    .map(function(group, client) {
      return {
        offshoreInstallationName: group[0].offshoreInstallationName,
        totalProcessed: group.length,
        totalBeyondTargetTime: _(group).reduce(function(memo, turnaround) {
          if (turnaround.beyondTargetTime) {
            return memo + 1;
          }
          return memo ;
        }, 0) ,
        clientTargetTimes: [],
      };
    });

  var allInstallations = siteProfile.configuration.locations.map(x => x.name);
  zeroFillForMissingInstallations(allInstallations, result);

  return result;
};

var workItemTypeGroupedInternalTurnaroundBeyondTargetTime = function(companyId, siteId, fromDate, toDate, client) {
  var eventsWhereTurnaroundCompleted = getEventsWhereTurnaroundCompleted(companyId, siteId, fromDate, toDate, client);

  var siteProfile = CompanySiteProfiles.findOne({ _id: siteId });

  var eventsWithTargetTimes = getAssociatedTargetTimesForEvents(eventsWhereTurnaroundCompleted, siteProfile);

  var workItemTypeGroupedEvents = _(eventsWithTargetTimes)
    .groupBy(function(eventWithTargetTime) {  return eventWithTargetTime.workItemType ?  eventWithTargetTime.workItemType.name : 'Unknown'; });

  var clientTargetTimes = siteProfile.configuration.clientTargetTimes;
  var workItemTypes = siteProfile.configuration.workItemTypes;

  var result = _(workItemTypeGroupedEvents)
    .map(function (group, workItemType) {
      return {
        workItemType: workItemType,
        totalProcessed: group.length,
        totalBeyondTargetTime: _(group).reduce(function (memo, turnaround) {
          if (turnaround.beyondTargetTime) {
            return memo + 1;
          }
          return memo ;
        }, 0) ,
        clientTargetTimes: _(clientTargetTimes).filter(function (targetTime) {
          return targetTime.clientId === group[0].client._id;
        }),
      };
    });

  zeroFillForMissingWorkItemTypes(siteProfile.configuration.workItemTypes, result);

  return result;
};

Meteor.methods({
  internalTurnaroundBeyondTargetTimeSummaryReport(companyProfileId, siteId, fromDate, toDate, client) {
    return internalTurnaroundBeyondTargetTime(companyProfileId, siteId, fromDate, toDate, client);
  },
});

Meteor.methods({
  internalTurnaroundBeyondTargetTimePerClientReport(companyProfileId, siteId, fromDate, toDate) {
    return clientGroupedInternalTurnaroundBeyondTargetTime(companyProfileId, siteId, fromDate, toDate);
  },

  internalTurnaroundBeyondTargetTimePerInstallationReport(companyProfileId, siteId, fromDate, toDate) {
    return clientGroupedInternalTurnaroundBeyondTargetTime(companyProfileId, siteId, fromDate, toDate);
  },

  internalTurnaroundBeyondTargetTimePerWorkItemTypeReport(companyProfileId, siteId,  fromDate, toDate, client) {
    return workItemTypeGroupedInternalTurnaroundBeyondTargetTime(companyProfileId, siteId, fromDate, toDate, client);
  },
});
