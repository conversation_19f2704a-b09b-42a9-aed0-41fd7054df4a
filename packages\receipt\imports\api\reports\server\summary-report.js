import { CompanyProfiles } from '../../company-profiles/company-profiles';
import { CompanySiteProfiles } from '../../company-site-profiles/company-site-profiles';
import { Meteor } from 'meteor/meteor';
import { WorkItemEventStates } from '../../../shared/work-item-event-states';
import { WorkItemEvents } from '../../work-item-events/work-item-events';

Meteor.methods({
  summaryReport(companyId, siteId, fromDate, toDate, client) {
    var momentFrom = moment(fromDate);
    var momentTo = moment(toDate);

    var periodInDays = momentTo.diff(momentFrom, 'days');

    var groupingKey;

    var query = {
      deleted: { $exists: false },
      isLatest: true,
      siteId,
      companyId,
      state: {
        $in: [
          WorkItemEventStates.COMPLETED,
          WorkItemEventStates.COLLECTED,
        ],
      },
      'lifecycleData.completed.timestamp': {
        $gte: fromDate,
        $lte: toDate,
      },
    };

    if (client) {
      query['lifecycleData.planned.client._id'] = client;
    }

    var aggregatePipeline = [
      {
        $match: query,
      },
      {
        $project: {
          month: { $month: '$lifecycleData.completed.timestamp' },
          client: '$lifecycleData.planned.client.name',
          receivedToCollected: {
            $subtract: ['$lifecycleData.completed.timestamp', '$lifecycleData.received.timestamp'],
          },
          receivedToCompleted: {
            $subtract: ['$lifecycleData.completed.timestamp', '$lifecycleData.planned.dischargeTimestamp'],
          },
          completedToCollected: {
            $subtract: ['$lifecycleData.completed.timestamp', '$lifecycleData.completed.timestamp'],
          },
          ncrFails: { $add: [
            '$lifecycleData.empty.fails'] },
        },
      },
      {
        $group: {
          _id: {
            month: '$month',
          },
          averageReceivedToCollected: { $avg: '$receivedToCollected' },
          averageReceivedToCompleted: { $avg: '$receivedToCompleted' },
          averageCompletedToCollected: { $avg: '$completedToCollected' },
          totalNcrFails: { $sum: '$ncrFails' },
        },
      }];

    var summary = WorkItemEvents
      .aggregate(aggregatePipeline);

    return summary.length > 0 ? summary[0] : {};
  },
});
