import { Meteor } from 'meteor/meteor';
import { WorkItems } from '../../work-items/work-items';

Meteor.methods({
  searchWorkItems(query, options) {
    options = options || {};

    // guard against client-side DOS: hard limit to 20
    if (options.limit) {
      options.limit = Math.min(20, Math.abs(options.limit));
    } else {
      options.limit = 20;
    }

    options.sort = { isoScore: -1 };

    var regex = buildRegExp(query);

    var selector = { Name: regex };

    return WorkItems.find(selector, options).fetch();
  },
});

function buildRegExp(searchText) {
  var parts = searchText.trim().split(/[ \-\:]+/);
  return new RegExp('(' + parts.join('|') + ')', 'ig');
}
