import { Meteor } from 'meteor/meteor';
import VorEventClient from '../../../integrations/server/vor/vor-events/vor-event-client';
import VorEventReplayer from '../../../integrations/server/vor/vor-events/vor-event-replayer';

Meteor.methods({
  replayAllVorEvents() {
    const vorEvents = VorEventReplayer.getAllVorEvents();
    vorEvents.forEach((vorEvent) => VorEventClient.postVorEvent(vorEvent));
  },
});

Meteor.methods({
  getAllVorEvents() {
    return VorEventReplayer.getAllVorEvents();
  },
});
