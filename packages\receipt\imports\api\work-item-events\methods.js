import { CSV } from 'meteor/clinical:csv';
import { Meteor } from 'meteor/meteor';
import { WorkItemActions } from '../../shared/work-item-actions';
import { WorkItemEventStates } from '../../shared/work-item-event-states';
import { WorkItemEvents } from '../work-item-events/work-item-events';

Meteor.methods({
  createWorkItemEvent(companyId, siteId, currentWorkItemEvent, state, lifecycleEventDataProvided) {
    let lifecycleEvent = '';
    if (state === WorkItemEventStates.PLANNED) {
      lifecycleEvent = 'planned';
    } else if (state === WorkItemEventStates.RECEIVED) {
      lifecycleEvent = 'received';
    } else if (state === WorkItemEventStates.INPROGRESS) {
      lifecycleEvent = 'started';
    } else if (state === WorkItemEventStates.COMPLETED) {
      lifecycleEvent = 'completed';
    } else if (state === WorkItemEventStates.COLLECTED) {
      lifecycleEvent = 'collected';
    }

    const lifecycleEventData = lifecycleEventDataProvided;
    const utcTimestamp = moment()
      .utc()
      .toDate();
    const user = Meteor.user();
    lifecycleEventData.timestamp = utcTimestamp;
    lifecycleEventData.user = user.username;

    let newWorkItemEvent;

    if (currentWorkItemEvent._id) {
      const previousWorkItemEvent = WorkItemEvents.findOne(currentWorkItemEvent._id);

      if (!previousWorkItemEvent.lifecycleData[lifecycleEvent]) {
        previousWorkItemEvent.lifecycleData[lifecycleEvent] = {};
      }

      _.extend(previousWorkItemEvent.lifecycleData[lifecycleEvent], lifecycleEventData);

      if (!previousWorkItemEvent.lifecycleEvents) {
        previousWorkItemEvent.lifecycleEvents = [];
      }
      lifecycleEventData.name = lifecycleEvent;
      previousWorkItemEvent.lifecycleEvents.push(lifecycleEventData);

      const nextWorkItemEvent = {
        user: user.username,
        timestamp: utcTimestamp,
        identifier: previousWorkItemEvent.identifier,
        state,
        lifecycleId: previousWorkItemEvent.lifecycleId,
        lifecycleData: previousWorkItemEvent.lifecycleData,
        lifecycleEvents: previousWorkItemEvent.lifecycleEvents,
        isLatest: true,
        companyId,
        siteId,
        vorEvents: previousWorkItemEvent.vorEvents,
        latestVorInformation: previousWorkItemEvent.latestVorInformation,
        createdByVor: previousWorkItemEvent.createdByVor,
      };

      WorkItemEvents.update(currentWorkItemEvent._id, { $set: { isLatest: false } }, function (
        error,
      ) {
        if (error) {
          console.log(error);
        }
      });

      newWorkItemEvent = nextWorkItemEvent;
    } else {
      const initialWorkItemEvent = {
        identifier: currentWorkItemEvent.identifier,
        timestamp: utcTimestamp,
        user: user.username,
        lifecycleData: {},
        state,
        lifecycleId: new Mongo.ObjectID()._str,
        isLatest: true,
        companyId,
        siteId,
      };

      if (!initialWorkItemEvent.lifecycleData[lifecycleEvent]) {
        initialWorkItemEvent.lifecycleData[lifecycleEvent] = {};
      }

      const latestVorInformation = {};
      if (lifecycleEventDataProvided.vendorName) {
        latestVorInformation.vendorName = lifecycleEventDataProvided.vendorName;
      }

      if (lifecycleEventDataProvided.direction) {
        latestVorInformation.direction = lifecycleEventDataProvided.direction;
      }

      if (lifecycleEventDataProvided.plannedDateTime) {
        latestVorInformation.plannedDateTime = moment(
          lifecycleEventDataProvided.plannedDateTime,
        ).format();
      }

      if (lifecycleEventDataProvided.clientLocation) {
        latestVorInformation.offshoreInstallationName = lifecycleEventDataProvided.clientLocation;
      }

      initialWorkItemEvent.latestVorInformation = latestVorInformation;

      _.extend(initialWorkItemEvent.lifecycleData[lifecycleEvent], lifecycleEventData);

      if (!initialWorkItemEvent.lifecycleEvents) {
        initialWorkItemEvent.lifecycleEvents = [];
      }
      lifecycleEventData.name = lifecycleEvent;
      initialWorkItemEvent.lifecycleEvents.push(lifecycleEventData);

      newWorkItemEvent = initialWorkItemEvent;
    }

    const workItemId = WorkItemEvents.insert(newWorkItemEvent);

    return WorkItemEvents.findOne(workItemId);
  },
  markIncomingMultipleWorkItems(currentWorkItemEvents) {
    const user = Meteor.user();
    const utcTimestamp = moment()
      .utc()
      .toDate();
    const workItemEventsToUpdate =
      WorkItemEvents
        .find({ _id: { $in: currentWorkItemEvents.map((x) => x._id) } })
        .fetch();

    if (currentWorkItemEvents[0].state === WorkItemEventStates.RECEIVED) {
      stateToReinstateAsLatest = WorkItemEventStates.PLANNED;
    }

    WorkItemEvents.update(
      {
        lifecycleId: { $in: workItemEventsToUpdate.map((x) => x.lifecycleId) },
        isLatest: true,
        deleted: { $exists: false },
      },
      {
        $set: {
          state: stateToReinstateAsLatest,
        },
        $unset: {
          'lifecycleData.arrivalInspection': '',
          'lifecycleData.received.timestamp': '',
          'lifecycleData.received.user': '',
        },
      },
      {
        multi: true,
      },
    );
  },
  inspectMultipleWorkItems(
    companyId,
    siteId,
    currentWorkItemEvents,
    action,
    lifecycleActionDataProvided,
  ) {
    const inspectionAction = action;
    const utcTimestamp = moment()
      .utc()
      .toDate();
    const user = Meteor.user();

    const lifecycleDataBase = {
      timestamp: utcTimestamp,
      user: user.username,
    };
    const inspectionLifecycleData = Object.assign(lifecycleActionDataProvided, lifecycleDataBase);
    const completedlifecycleData = lifecycleDataBase;

    const workItemEventsToUpdate =
      WorkItemEvents
        .find({ _id: { $in: currentWorkItemEvents.map((x) => x._id) } })
        .fetch();

    const workItemEventsToUpdateWithNewLifecycleData = workItemEventsToUpdate.map((x) => {
      const workItemEvent = x;
      workItemEvent.lifecycleData[action] = inspectionLifecycleData;
      workItemEvent.lifecycleData.completed = completedlifecycleData;
      if (!workItemEvent.lifecycleEvents) {
        workItemEvent.lifecycleEvents = [];
      }
      inspectionLifecycleData.name = action;
      completedlifecycleData.name = 'completed';
      workItemEvent.lifecycleEvents.push(inspectionLifecycleData);
      workItemEvent.lifecycleEvents.push(completedlifecycleData);
      return workItemEvent;
    });

    const previouslyLatestEvents =
      workItemEventsToUpdateWithNewLifecycleData.map((x) => x._id);

    WorkItemEvents.update(
      { _id: { $in: previouslyLatestEvents } },
      {
        $set: {
          [`lifecycleData.${action}`]: inspectionLifecycleData,
          isLatest: false,
        },
      },
      { multi: true },
      (error) => {
        if (error) {
          console.log(error);
        } else {
          workItemEventsToUpdateWithNewLifecycleData.forEach((x) => {
            const existingEvent = WorkItemEvents.findOne({
              lifecycleId: x.lifecycleId,
              state: 'COMPLETED',
              isLatest: true,
            });

            if (!existingEvent) {
              const nextWorkItemEvent = {
                user: user.username,
                timestamp: utcTimestamp,
                identifier: x.identifier,
                state: 'COMPLETED',
                lifecycleId: x.lifecycleId,
                lifecycleData: x.lifecycleData,
                lifecycleEvents: x.lifecycleEvents,
                isLatest: true,
                companyId,
                siteId,
                vorEvents: x.vorEvents,
                latestVorInformation: x.latestVorInformation,
                createdByVor: x.createdByVor,
              };

              WorkItemEvents.upsert({
                lifecycleId: nextWorkItemEvent.lifecycleId,
                state: 'COMPLETED',
                isLatest: true,
              }, nextWorkItemEvent, (error, result) => {
                if (!error) {
                  if (Meteor.isServer && result.insertedId) {
                    Meteor.call(
                      'sendEmail',
                      {
                        identifier: x.identifier,
                        lifecycleId: x.lifecycleId,
                        siteId: x.siteId,
                        ncrType: inspectionAction,
                      },
                      () => {},
                    );
                  }
                }
              });
            }
          });
        }
      },
    );
  },
  collectMultipleWorkItems(
    companyId,
    siteId,
    currentWorkItemEvents,
    receiveEventDataProvided,
    inspectionEventDataProvided,
  ) {
    const receiveEventData = receiveEventDataProvided;
    const inspectionEventData = inspectionEventDataProvided;
    const utcTimestamp = moment()
      .utc()
      .toDate();
    const user = Meteor.user();

    receiveEventData.timestamp = utcTimestamp;
    receiveEventData.user = user.username;
    inspectionEventData.timestamp = utcTimestamp;
    inspectionEventData.user = user.username;

    const resultantWorkItemEvents = [];

    WorkItemEvents.update(
      { _id: { $in: currentWorkItemEvents.map((x) => x._id) } },
      { $set: { isLatest: false } },
      { multi: true },
      (error) => {
        if (error) {
          console.log(error);
        } else {
          currentWorkItemEvents.forEach((currentWorkItemEvent) => {
            const previousWorkItemEvent = WorkItemEvents.findOne(currentWorkItemEvent._id);
            if (!previousWorkItemEvent.lifecycleData['collected']) {
              previousWorkItemEvent.lifecycleData['collected'] = {};
            }

            _.extend(previousWorkItemEvent.lifecycleData['collected'], receiveEventData);

            if (!previousWorkItemEvent.lifecycleData['departureInspection']) {
              previousWorkItemEvent.lifecycleData['departureInspection'] = {};
            }
            _.extend(previousWorkItemEvent.lifecycleData['departureInspection'], inspectionEventData);

            if (!previousWorkItemEvent.lifecycleEvents) {
              previousWorkItemEvent.lifecycleEvents = [];
            }
            receiveEventData.name = 'collected';
            previousWorkItemEvent.lifecycleEvents.push(receiveEventData);
            inspectionEventData.name = 'departureInspection';
            previousWorkItemEvent.lifecycleEvents.push(inspectionEventData);

            const collectItemEvent = {
              user: user.username,
              timestamp: utcTimestamp,
              identifier: previousWorkItemEvent.identifier,
              state: WorkItemEventStates.COLLECTED,
              lifecycleId: previousWorkItemEvent.lifecycleId,
              lifecycleData: previousWorkItemEvent.lifecycleData,
              lifecycleEvents: previousWorkItemEvent.lifecycleEvents,
              isLatest: true,
              companyId,
              siteId,
              vorEvents: previousWorkItemEvent.vorEvents,
              latestVorInformation: previousWorkItemEvent.latestVorInformation,
              createdByVor: previousWorkItemEvent.createdByVor,
              linesReceipted: previousWorkItemEvent.linesReceipted,
            };

            const existingCollectedEvent = WorkItemEvents.findOne({
              state: WorkItemEventStates.COLLECTED,
              lifecycleId: previousWorkItemEvent.lifecycleId,
              isLatest: true,
            });

            if (!existingCollectedEvent) {
              WorkItemEvents.upsert({
                state: WorkItemEventStates.COLLECTED,
                lifecycleId: previousWorkItemEvent.lifecycleId,
                isLatest: true,
              },
              collectItemEvent, (error) => {
                if (error) {
                  console.log(error);
                } else {
                  if (collectItemEvent.lifecycleData['departureInspection'].completed && Meteor.isServer) {
                    Meteor.call(
                      'sendEmail',
                      {
                        siteId,
                        identifier: collectItemEvent.identifier,
                        lifecycleId: collectItemEvent.lifecycleId,
                        ncrType: 'departureInspection',
                      },
                      () => {},
                    );
                  }
                }
              },
              );
            }
          });
        }
      },
    );
  },

  receiveMultipleWorkItems(
    companyId,
    siteId,
    currentWorkItemEvents,
    receiveEventDataProvided,
    inspectionEventDataProvided,
  ) {
    const receiveEventData = receiveEventDataProvided;
    const inspectionEventData = inspectionEventDataProvided;
    const utcTimestamp = moment()
      .utc()
      .toDate();
    const user = Meteor.user();
    receiveEventData.timestamp = utcTimestamp;
    receiveEventData.user = user.username;
    inspectionEventData.timestamp = utcTimestamp;
    inspectionEventData.user = user.username;

    WorkItemEvents.update(
      { _id: { $in: currentWorkItemEvents.map((x) => x._id) } },
      { $set: { isLatest: false } },
      { multi: true },
      (error) => {
        if (error) {
          console.log(error);
        } else {
          currentWorkItemEvents.forEach((currentWorkItemEvent) => {
            const previousWorkItemEvent = WorkItemEvents.findOne(currentWorkItemEvent._id);
            if (!previousWorkItemEvent.lifecycleData['received']) {
              previousWorkItemEvent.lifecycleData['received'] = {};
            }

            _.extend(previousWorkItemEvent.lifecycleData['received'], receiveEventData);

            if (!previousWorkItemEvent.lifecycleData['arrivalInspection']) {
              previousWorkItemEvent.lifecycleData['arrivalInspection'] = {};
            }
            _.extend(previousWorkItemEvent.lifecycleData['arrivalInspection'], inspectionEventData);

            if (!previousWorkItemEvent.lifecycleEvents) {
              previousWorkItemEvent.lifecycleEvents = [];
            }
            receiveEventData.name = 'received';
            previousWorkItemEvent.lifecycleEvents.push(receiveEventData);
            inspectionEventData.name = 'arrivalInspection';
            previousWorkItemEvent.lifecycleEvents.push(inspectionEventData);

            const receiveItemEvent = {
              user: user.username,
              timestamp: utcTimestamp,
              identifier: previousWorkItemEvent.identifier,
              state: WorkItemEventStates.RECEIVED,
              lifecycleId: previousWorkItemEvent.lifecycleId,
              lifecycleData: previousWorkItemEvent.lifecycleData,
              lifecycleEvents: previousWorkItemEvent.lifecycleEvents,
              isLatest: true,
              companyId,
              siteId,
              vorEvents: previousWorkItemEvent.vorEvents,
              latestVorInformation: previousWorkItemEvent.latestVorInformation,
              createdByVor: previousWorkItemEvent.createdByVor,
              linesReceipted: previousWorkItemEvent.linesReceipted,
            };

            const existingReceiveWorkItemEvent =
              WorkItemEvents.findOne({
                state: WorkItemEventStates.RECEIVED,
                lifecycleId: previousWorkItemEvent.lifecycleId,
                isLatest: true,
              });

            if (!existingReceiveWorkItemEvent) {
              WorkItemEvents.upsert({
                state: WorkItemEventStates.RECEIVED,
                lifecycleId: previousWorkItemEvent.lifecycleId,
                isLatest: true,
              },
              receiveItemEvent,
              (error) => {
                if (error) {
                  console.log(error);
                } else {
                  if (receiveItemEvent.lifecycleData['arrivalInspection'].completed && Meteor.isServer) {
                    Meteor.call(
                      'sendEmail',
                      {
                        siteId: siteId,
                        identifier: receiveItemEvent.identifier,
                        lifecycleId: receiveItemEvent.lifecycleId,
                        ncrType: 'arrivalInspection',
                      },
                      () => {},
                    );
                  }
                }
              },
              );
            }
          });
        }
      },
    );
  },
  startWorkOnMultipleWorkItems(
    companyId,
    siteId,
    currentWorkItemEvents,
    inProgressEventDataProvided,
  ) {
    const inProgressEventData = inProgressEventDataProvided;
    const utcTimestamp = moment()
      .utc()
      .toDate();
    const user = Meteor.user();
    inProgressEventData.timestamp = utcTimestamp;
    inProgressEventData.user = user.username;

    WorkItemEvents.update(
      { _id: { $in: currentWorkItemEvents.map((x) => x._id) } },
      { $set: { isLatest: false } },
      { multi: true },
      (error) => {
        if (error) {
          console.log(error);
        } else {
          currentWorkItemEvents.forEach((currentWorkItemEvent) => {
            const previousWorkItemEvent = WorkItemEvents.findOne(currentWorkItemEvent._id);
            if (!previousWorkItemEvent.lifecycleData['inprogress']) {
              previousWorkItemEvent.lifecycleData['inprogress'] = {};
            }

            _.extend(previousWorkItemEvent.lifecycleData['inprogress'], inProgressEventData);

            if (!previousWorkItemEvent.lifecycleEvents) {
              previousWorkItemEvent.lifecycleEvents = [];
            }
            inProgressEventData.name = 'inprogress';
            previousWorkItemEvent.lifecycleEvents.push(inProgressEventData);

            const inProgressItemEvent = {
              user: user.username,
              timestamp: utcTimestamp,
              identifier: previousWorkItemEvent.identifier,
              state: WorkItemEventStates.INPROGRESS,
              lifecycleId: previousWorkItemEvent.lifecycleId,
              lifecycleData: previousWorkItemEvent.lifecycleData,
              lifecycleEvents: previousWorkItemEvent.lifecycleEvents,
              isLatest: true,
              companyId,
              siteId,
              vorEvents: previousWorkItemEvent.vorEvents,
              latestVorInformation: previousWorkItemEvent.latestVorInformation,
              createdByVor: previousWorkItemEvent.createdByVor,
              linesReceipted: previousWorkItemEvent.linesReceipted,
            };

            const existingInProgressWorkItemEvent =
              WorkItemEvents.findOne({
                state: WorkItemEventStates.INPROGRESS,
                lifecycleId: previousWorkItemEvent.lifecycleId,
                isLatest: true,
              });

            if (!existingInProgressWorkItemEvent) {
              WorkItemEvents.upsert({
                state: WorkItemEventStates.INPROGRESS,
                lifecycleId: previousWorkItemEvent.lifecycleId,
                isLatest: true,
              },
              inProgressItemEvent,
              (error) => {
                if (error) {
                  console.log(error);
                }
              },
              );
            }
          });
        }
      },
    );
  },
  completeMultipleWorkItems(
    companyId,
    siteId,
    currentWorkItemEvents,
    completedEventDataProvided,
  ) {
    const completedEventData = completedEventDataProvided;
    const utcTimestamp = moment()
      .utc()
      .toDate();
    const user = Meteor.user();
    completedEventData.timestamp = utcTimestamp;
    completedEventData.user = user.username;

    WorkItemEvents.update(
      { _id: { $in: currentWorkItemEvents.map((x) => x._id) } },
      { $set: { isLatest: false } },
      { multi: true },
      (error) => {
        if (error) {
          console.log(error);
        } else {
          currentWorkItemEvents.forEach((currentWorkItemEvent) => {
            const previousWorkItemEvent = WorkItemEvents.findOne(currentWorkItemEvent._id);
            if (!previousWorkItemEvent.lifecycleData['completed']) {
              previousWorkItemEvent.lifecycleData['completed'] = {};
            }

            _.extend(previousWorkItemEvent.lifecycleData['completed'], completedEventData);

            if (!previousWorkItemEvent.lifecycleEvents) {
              previousWorkItemEvent.lifecycleEvents = [];
            }
            completedEventData.name = 'completed';
            previousWorkItemEvent.lifecycleEvents.push(completedEventData);

            const completedItemEvent = {
              user: user.username,
              timestamp: utcTimestamp,
              identifier: previousWorkItemEvent.identifier,
              state: WorkItemEventStates.COMPLETED,
              lifecycleId: previousWorkItemEvent.lifecycleId,
              lifecycleData: previousWorkItemEvent.lifecycleData,
              lifecycleEvents: previousWorkItemEvent.lifecycleEvents,
              isLatest: true,
              companyId,
              siteId,
              vorEvents: previousWorkItemEvent.vorEvents,
              latestVorInformation: previousWorkItemEvent.latestVorInformation,
              createdByVor: previousWorkItemEvent.createdByVor,
              linesReceipted: previousWorkItemEvent.linesReceipted,
            };

            const existingCompletedItemEvent =
              WorkItemEvents.findOne({
                state: WorkItemEventStates.COMPLETED,
                lifecycleId: previousWorkItemEvent.lifecycleId,
                isLatest: true,
              });

            if (!existingCompletedItemEvent) {
              WorkItemEvents.upsert({
                state: WorkItemEventStates.COMPLETED,
                lifecycleId: previousWorkItemEvent.lifecycleId,
                isLatest: true,
              },
              completedItemEvent,
              (error) => {
                if (error) {
                  console.log(error);
                }
              },
              );
            }
          });
        }
      },
    );
  },
  updateWorkItemEvent(workItemEventId, action, lifecycleActionDataProvided) {
    const utcTimestamp = moment()
      .utc()
      .toDate();
    const user = Meteor.user();

    const lifecycleActionData = lifecycleActionDataProvided;
    lifecycleActionData.timestamp = utcTimestamp;
    lifecycleActionData.user = user.username;

    const currentWorkItemEvent = WorkItemEvents.findOne(workItemEventId);

    if (!currentWorkItemEvent.lifecycleData[action]) {
      currentWorkItemEvent.lifecycleData[action] = {};
    }

    _.extend(currentWorkItemEvent.lifecycleData[action], lifecycleActionData);

    WorkItemEvents.update(
      workItemEventId,
      {
        $set: {
          lifecycleData: currentWorkItemEvent.lifecycleData,
        },
      },
      (error) => {
        if (error) {
          console.log(error);
        } else {
          if (Meteor.isServer) {
            Meteor.call(
              'sendEmail',
              {
                identifier: currentWorkItemEvent.identifier,
                lifecycleId: currentWorkItemEvent.lifecycleId,
                siteId: currentWorkItemEvent.siteId,
                ncrType: action,
              },
              () => {},
            );
          }
        }
      },
    );
  },
  setLifecycleAsDeleted(lifecycleIds) {
    const user = Meteor.user();
    const utcTimestamp = moment()
      .utc()
      .toDate();

    WorkItemEvents.update(
      {
        lifecycleId: { $in: lifecycleIds },
      },
      {
        $set: {
          deleted: true,
          deletedBy: user.username,
          deletedAt: utcTimestamp,
        },
      },
      {
        multi: true,
      },
    );
  },
  setLastEventAsDeleted(currentWorkItemEvents) {
    const user = Meteor.user();
    const utcTimestamp = moment()
      .utc()
      .toDate();
    const workItemEventsToUpdate =
      WorkItemEvents
        .find({ _id: { $in: currentWorkItemEvents.map((x) => x._id) } })
        .fetch();

    let eventStatesToDelete;
    let stateToReinstateAsLatest;

    if (currentWorkItemEvents[0].state === WorkItemEventStates.COLLECTED) {
      eventStatesToDelete = [WorkItemEventStates.COLLECTED];
      stateToReinstateAsLatest = WorkItemEventStates.COMPLETED;
    } else if (currentWorkItemEvents[0].state === WorkItemEventStates.COMPLETED) {
      eventStatesToDelete = [WorkItemEventStates.COMPLETED];
      stateToReinstateAsLatest = WorkItemEventStates.INPROGRESS;
    } else if (currentWorkItemEvents[0].state === WorkItemEventStates.INPROGRESS) {
      eventStatesToDelete = [WorkItemEventStates.INPROGRESS, WorkItemEventStates.RECEIVED]; // These two events get fired together in trinidad
      stateToReinstateAsLatest = WorkItemEventStates.PLANNED;
    } else if (currentWorkItemEvents[0].state === WorkItemEventStates.RECEIVED) {
      eventStatesToDelete = [WorkItemEventStates.RECEIVED];
      stateToReinstateAsLatest = WorkItemEventStates.PLANNED;
    }

    WorkItemEvents.update(
      {
        lifecycleId: { $in: workItemEventsToUpdate.map((x) => x.lifecycleId) },
        state: { $in: eventStatesToDelete },
        deleted: { $exists: false },
      },
      {
        $set: {
          deleted: true,
          deletedBy: user.username,
          deletedAt: utcTimestamp,
          isLatest: false,
        },
      },
      {
        multi: true,
      },
      () => {
        WorkItemEvents.update(
          {
            lifecycleId: { $in: workItemEventsToUpdate.map((x) => x.lifecycleId) },
            state: stateToReinstateAsLatest,
            deleted: { $exists: false },
          },
          {
            $set: {
              isLatest: true,
            },
          },
          {
            multi: true,
          },
          () => {},
        );
      },
    );
  },
  updateWorkItemEventActionImages(workItemEventId, action, imagesCollection, newImage) {
    console.log(workItemEventId, action, imagesCollection, newImage);

    const currentWorkItemEvent = WorkItemEvents.findOne(workItemEventId);

    if (!currentWorkItemEvent.lifecycleData[action]) {
      currentWorkItemEvent.lifecycleData[action] = {};
    }

    if (!currentWorkItemEvent.lifecycleData[action][imagesCollection]) {
      currentWorkItemEvent.lifecycleData[action][imagesCollection] = [];
    }

    currentWorkItemEvent.lifecycleData[action][imagesCollection].push(newImage);

    WorkItemEvents.update(
      workItemEventId,
      {
        $set: {
          lifecycleData: currentWorkItemEvent.lifecycleData,
        },
      },
      (error) => {
        if (error) {
          console.log(error);
        }
      },
    );
  },
  updateWorkItemDetails(workItemEventId, updatedDetails) {
    const currentWorkItemEvent = WorkItemEvents.findOne(workItemEventId);

    currentWorkItemEvent.identifier = updatedDetails.identifier;
    currentWorkItemEvent.lifecycleData.planned.client = updatedDetails.client;
    currentWorkItemEvent.lifecycleData.planned.workItemType = updatedDetails.workItemType;
    currentWorkItemEvent.lifecycleData.planned.clientLocation = updatedDetails.clientLocation;
    currentWorkItemEvent.lifecycleData.planned.contents = updatedDetails.contents;
    currentWorkItemEvent.lifecycleData.received.truckNoPlate = updatedDetails.truckNoPlate;
    currentWorkItemEvent.lifecycleData.received.tareWeight = updatedDetails.tareWeight;
    currentWorkItemEvent.lifecycleData.received.grossWeight = updatedDetails.grossWeight;
    currentWorkItemEvent.lifecycleData.received.netWeight = updatedDetails.netWeight;

    WorkItemEvents.update(workItemEventId, currentWorkItemEvent, (error) => {
      if (error) {
        console.log(error);
      }
    });
  },
  'workItemEvents.exportAsCsv': function exportAsCsv(fromDate, toDate) {
    const eventsToExport = WorkItemEvents.find({
      isLatest: true,
      deleted: { $exists: false },
      'lifecycleData.planned.plannedDateTime': { $gte: fromDate, $lte: toDate },
    })
      .fetch()
      .map((x) => ({
        'Container #': x.identifier,
        Description: x.lifecycleData.planned ? x.lifecycleData.planned.description : '',
        Asset: x.lifecycleData.planned ? x.lifecycleData.planned.clientLocation : '',
        Direction: x.latestVorInformation ? x.latestVorInformation.direction : '',
        Vendor: x.latestVorInformation ? x.latestVorInformation.vendorName : '',
        Incoming: x.lifecycleData.planned
          ? moment(x.lifecycleData.planned.plannedDateTime).format('DD/MM/YYYY HH:mm')
          : '',
        Received:
          x.lifecycleData.received && x.lifecycleData.received.timestamp
            ? moment(x.lifecycleData.received.timestamp).format('DD/MM/YYYY HH:mm')
            : '',
        Inspected: x.lifecycleData.marshallingYardInspection
          ? moment(x.lifecycleData.marshallingYardInspection.timestamp).format('DD/MM/YYYY HH:mm')
          : '',
        Collected: x.lifecycleData.collected
          ? moment(x.lifecycleData.collected.timestamp).format('DD/MM/YYYY HH:mm')
          : '',
        Status: x.state === 'COMPLETED' ? 'INSPECTED' : x.state,
      }));
    const heading = true; // Optional, defaults to true
    const delimiter = ','; // Optional, defaults to ",";
    return CSV.unparse(eventsToExport, heading, delimiter);
  },
  updateLinesWorkItem({ cargoLineId, materialLineId }) {
    try {
      WorkItemEvents.update(
        {
          'latestVorInformation.cargoLineId': cargoLineId, isLatest: true, deleted: { $exists: false }, state: 'INPROGRESS',
        },
        { $push: { linesReceipted: materialLineId } },
      );
    } catch (error) {
      console.log(error);
    }
  },
});
