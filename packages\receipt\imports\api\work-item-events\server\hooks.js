import { CompanySiteProfiles } from '../../company-site-profiles/company-site-profiles';
import VorEventBuilder from '../../../integrations/server/vor/vor-events/vor-event-builder';
import VorEventClient from '../../../integrations/server/vor/vor-events/vor-event-client';
import { WorkItemActions } from '../../../shared/work-item-actions';
import { WorkItemEventStates } from '../../../shared/work-item-event-states';
import { WorkItemEvents } from '../work-item-events';
import { Mediator } from 'meteor/mediator';
import { findWorkItemEventsInOperationalWorkItemEvents, updateWorkItemEvents } from '../../../integrations/server/vor/vor-incoming-items/vor-incoming-item-message-consumer';

import { LoggerFactory } from '../../../shared/logger-factory';

const logger = LoggerFactory.getLogger(__filename);

const sendVorEventsOnInsert = (workItemEvent) => {
  if (workItemEvent.state === WorkItemEventStates.RECEIVED ||
    workItemEvent.state === WorkItemEventStates.COMPLETED ||
    workItemEvent.state === WorkItemEventStates.COLLECTED) {
    const siteForWorkItem = CompanySiteProfiles.findOne(workItemEvent.siteId);
    const customer = siteForWorkItem.dedicatedCustomerName;

    const vorEvent = VorEventBuilder.buildVorEvent(customer, workItemEvent);
    VorEventClient.postVorEvent(vorEvent);
  }
};

const sendVorEventsOnUpdate = (workItemEvent, previousWorkItemEvent) => {
  const workItemBeforeUpdate = previousWorkItemEvent;
  const workItemAfterUpdate = workItemEvent;
  const siteForWorkItem = CompanySiteProfiles.findOne(workItemAfterUpdate.siteId);
  const customer = siteForWorkItem.dedicatedCustomerName;

  if ((workItemAfterUpdate.deleted && !workItemBeforeUpdate.deleted) &&
    (workItemAfterUpdate.state === WorkItemEventStates.RECEIVED ||
    workItemAfterUpdate.state === WorkItemEventStates.COMPLETED ||
    workItemAfterUpdate.state === WorkItemEventStates.COLLECTED)) {
    const vorEvent = VorEventBuilder.buildVorCancellationEvent(customer, workItemAfterUpdate);
    VorEventClient.postVorEvent(vorEvent);
  }
};

const sendPackingUpdate = (workItemEvent) => {
  if (workItemEvent.isLatest) {
    if (workItemEvent.state === WorkItemEventStates.RECEIVED) {
      // Only send if this is the 'latest' workitem event.
      // Work Item has been moved to received in Receipt and the cargo is hidden from Receipt.
      console.log(`[Receipt] WorkItemEventHook - Sending notification of container in received again to Pack, workItem.state: ${workItemEvent.state}, CCU: ${workItemEvent.identifier}.`);
      Mediator.publish(
        Mediator.events.CONTAINER_RETURNED_TO_PREP,
        workItemEvent.latestVorInformation, // Note '.latestVorInformation' is actually latest info on container & contents from Flow.
      );
    }
    if (workItemEvent.state === WorkItemEventStates.INPROGRESS) {
      // Only send if this is the 'latest' workitem event.
      // Don't send when INPROGRESS is updated to isLatest:false.
      console.log(`[Receipt] WorkItemEventHook - Sending workItemEvent.latestVorInformation info to Pack, workItem.state: ${workItemEvent.state}, CCU: ${workItemEvent.identifier}.`);
      Mediator.publish(
        Mediator.events.CONTAINER_UPDATE,
        workItemEvent.latestVorInformation, // Note '.latestVorInformation' is actually latest info on container & contents from Flow.
      );
    }
    if (workItemEvent.state === WorkItemEventStates.COMPLETED) {
      // Only send if this is the 'latest' workitem event.
      // Cargo Item has been moved to completed in Receipt.
      console.log(`[Receipt] WorkItemEventHook - Sending notification of completion of waste removal to Pack, workItem.state: ${workItemEvent.state}, CCU: ${workItemEvent.identifier}.`);
      Mediator.publish(
        Mediator.events.CONTAINER_COMPLETED_WASTE_REMOVAL,
        workItemEvent.latestVorInformation, // Actually latest info on container & contents from Flow.
      );
    }
  }
};

WorkItemEvents.after.insert((userId, workItemEvent) => {
  sendVorEventsOnInsert(workItemEvent);
  sendPackingUpdate(workItemEvent);
});

WorkItemEvents.after.update(function (userId, doc, fieldNames, modifier, options) {
  sendVorEventsOnUpdate(doc, this.previous);
  sendPackingUpdate(doc);
}, { fetchPrevious: true });

WorkItemEvents.before.insert((userId, workItemEvent) => {
  // Check if there are any workItemEvents with the same Cargo Line Id already exist in the database.
  eventCargoLineId = workItemEvent.latestVorInformation.cargoLineId;
  const site = CompanySiteProfiles.findOne({
    $or: [{ vorId:  workItemEvent.toLocationNormalisedId }, { vorIds:  workItemEvent.toLocationNormalisedId }],
  });

  cargoLineIdAsObjectAttributeInArray = [{ cargoLineId: eventCargoLineId }];
  const operationalWorkItemsEventsForAllItems = findWorkItemEventsInOperationalWorkItemEvents(cargoLineIdAsObjectAttributeInArray, site._id);
  logger.info(operationalWorkItemsEventsForAllItems);

  const eventsForThisItem = operationalWorkItemsEventsForAllItems.filter(
    (x) => x.latestVorInformation.cargoLineId === eventCargoLineId);

  // Soft delete all workItemEvents in the database created within a pre-defined period of time of the new event's creation.
  const eventTimestamp = workItemEvent.timestamp;
  if (eventsForThisItem.length != 0) {
    const timeDeltaThreshold = 2000; // milliseconds
    logger.info(`${eventsForThisItem.length} duplicate event(s) found in db.`);
    logger.info(`Comparing times to see if they were created within ${timeDeltaThreshold / 1000} seconds of each other and soft deleting the older ones if they do.`);

    eventsForThisItem.forEach((event, index) => {
      timeDelta = eventTimestamp - event.timestamp;
      if (timeDelta <= timeDeltaThreshold) {
        // Mark older event as soft deleted and update the database
        const workItemEventToUpdate = event;
        workItemEventToUpdate.deleted = true;

        logger.info(`Attempting to update duplicate workItemEvent for ${event.identifier} as soft-deleted (created ${timeDelta} milliseconds before latest).`);
        updateWorkItemEvents(workItemEventToUpdate, event, event.existingVorInformation, event.latestVorInformation);
      }
    });
  }
});
