import { WorkItemEventStates } from '../../../shared/work-item-event-states';
import { WorkItemEvents } from '../work-item-events';

const fieldsNotForClient = {
  fields: {
    vorEvents: 0,
    'latestVorInformation.id': 0,
    'latestVorInformation.cargoGroupId': 0,
    'latestVorInformation.cargoLineId': 0,
    'latestVorInformation.quantity': 0,
    'latestVorInformation.identifier': 0,
    'latestVorInformation.fromLocationId': 0,
    'latestVorInformation.fromLocationName': 0,
    'latestVorInformation.fromDistrict': 0,
    'latestVorInformation.toLocationId': 0,
    'latestVorInformation.toLocationName': 0,
    'latestVorInformation.toDistrict': 0,
    'latestVorInformation.clientId': 0,
    'latestVorInformation.expectedTime': 0,
    'latestVorInformation.isPriority': 0,
    'latestVorInformation.comments': 0,
    'latestVorInformation.trailerNo': 0,
    'latestVorInformation.vessel': 0,
    'latestVorInformation.offshoreInstallationName': 0,
    'latestVorInformation.offshoreInstallationId': 0,
    'latestVorInformation.orderNo': 0,
    'latestVorInformation.isARedirect': 0,
    'latestVorInformation.allocatedDateTime': 0,
    'latestVorInformation.collectedDateTime': 0,
    'latestVorInformation.deliveredDateTime': 0,
    'latestVorInformation.vehicleId': 0,
    'latestVorInformation.vehicleRegNo': 0,
    'latestVorInformation.trailerId': 0,
    'latestVorInformation.trailerRegNo': 0,
    'latestVorInformation.driverId': 0,
    'latestVorInformation.driverName': 0,
    'latestVorInformation.isAllocatedToSubcontractor': 0,
    'latestVorInformation.subcontractorName': 0,
    'latestVorInformation.expectedDateTime': 0,
    'latestVorInformation.normalisedId': 0,
    'latestVorInformation.fromLocationAddress': 0,
    'latestVorInformation.fromLocationAliases': 0,
    'latestVorInformation.fromLocationDetectedType': 0,
    'latestVorInformation.fromLocationNormalisedId': 0,
    'latestVorInformation.toLocationAddress': 0,
    'latestVorInformation.toLocationAliases': 0,
    'latestVorInformation.toLocationDetectedType': 0,
    'latestVorInformation.toLocationNormalisedId': 0,
    'latestVorInformation.clientNormalisedId': 0,
    'latestVorInformation.clientAddress': 0,
    'latestVorInformation.clientAliases': 0,
    'latestVorInformation.dischargeTimestamp': 0,
    'latestVorInformation.manifestNo': 0,
    'latestVorInformation.weight': 0,
    'latestVorInformation.masterVendorId': 0,
    'latestVorInformation.isCancelled': 0,
  },
};

ReactiveTable.publish(
  'latest-work-item-events',
  WorkItemEvents,
  { isLatest: true, deleted: { $exists: false } },
  {
    enableRegex: false,
    disablePageCountReactivity: true,
    disableRowReactivity: true,
    fields: {
      timestamp: 0,
      user: 0,
      'lifecycleData.started': 0,
      'lifecycleData.clean': 0,
    },
  },
);

Meteor.publish('latestWorkItemEvents', function latestWorkItemEvents(companyId, siteId) {
  const startOfDay = moment()
    .startOf('day')
    .toDate();
  return WorkItemEvents.find(
    {
      deleted: { $exists: false },
      isLatest: true,
      companyId,
      siteId,
      $or: [
        {
          state: {
            $in: [
              WorkItemEventStates.PLANNED,
              WorkItemEventStates.RECEIVED,
              WorkItemEventStates.INPROGRESS,
              WorkItemEventStates.COMPLETED,
            ],
          },
        },
        {
          state: {
            $in: [WorkItemEventStates.COLLECTED],
          },
          timestamp: {
            $gt: startOfDay,
          },
        },
      ],
    },
    fieldsNotForClient,
  );
});

Meteor.publish('latestWorkItemEvent', (lifecycleId) =>
  WorkItemEvents.find(
    {
      deleted: { $exists: false },
      isLatest: true,
      state: {
        $in: [
          WorkItemEventStates.PLANNED,
          WorkItemEventStates.RECEIVED,
          WorkItemEventStates.INPROGRESS,
          WorkItemEventStates.COMPLETED,
          WorkItemEventStates.COLLECTED,
        ],
      },
      lifecycleId,
    },
    fieldsNotForClient,
  ));

Meteor.publish('latestWorkItemEventForMultiple', (lifecycleIds) =>
  WorkItemEvents.find(
    {
      deleted: { $exists: false },
      isLatest: true,
      state: {
        $in: [
          WorkItemEventStates.PLANNED,
          WorkItemEventStates.RECEIVED,
          WorkItemEventStates.INPROGRESS,
          WorkItemEventStates.COMPLETED,
          WorkItemEventStates.COLLECTED,
        ],
      },
      lifecycleId: { $in: lifecycleIds },
    },
    fieldsNotForClient,
  ));
