Meteor.methods({
  uploadFile(file, fileIdentifier, onFileUploadSuccessfulFn) {
    var response;
    if (file === void 0) {
      throw new Meteor.Error(500, 'Missing File', '', '');
    }

    response = file.azureUpload(
      fileIdentifier,
      Meteor.settings.private.azureBlobStorage.accountName,
      Meteor.settings.private.azureBlobStorage.accountKey,
      Meteor.settings.private.azureBlobStorage.containerName,
    );

    return console.log('File url: ' + response.url);
  },
});
