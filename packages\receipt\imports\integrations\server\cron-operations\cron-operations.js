import { Meteor } from 'meteor/meteor';
import { Synced<PERSON>ron } from 'meteor/percolate:synced-cron';
import { Email } from 'meteor/email';
import { WorkItemEvents } from '/imports/api/work-item-events/work-item-events';
import { CompanySiteProfiles } from '/imports/api/company-site-profiles/company-site-profiles';
import { App } from '/imports/shared/app';
import '../email/email-templates/completed-warning-email';

// Defines the number of completed containers needed to send warning
const NUMBER_OF_COMPLETED_ITEMS_TO_WARN = 5;

// Constructs email options
function constructEmail(emailAddress, completedItems) {
  let options;
  if (completedItems.length > 0) {
    const completedItemsCleaned = completedItems.map((x) => ({
      Company: x.lifecycleData.planned.client.name,
      Location: x.lifecycleData.planned.clientLocation,
      CCU: x.identifier,
      Received: x.lifecycleData.received.timestamp,
      Completed: x.lifecycleData.completed.timestamp,
    }));

    const csvContents = Papa.unparse(completedItemsCleaned);
    options = {
      from: '<EMAIL>',
      to: emailAddress,
      subject: 'Completed Units Requiring Collection (PETERSON)',
      html: 'Please find attached a list of the containers requiring collection by Peterson',
      attachments: [
        {
          fileName: `Requiring_Collection_By_Peterson_${moment().format('DDMMYYYY')}.csv`,
          contents: csvContents,
        },
      ],
    };
  } else {
    options = {
      from: '<EMAIL>',
      to: emailAddress,
      subject: 'No Units Requiring Collection (PETERSON)',
      html: 'There are currently no units requiring collection by Peterson.',
    };
  }
  return options;
}

function constructAscoEmail(emailAddress, completedAscoItems) {
  let options;
  if (completedAscoItems.length > 0) {
    const completedItemsCleaned = completedAscoItems.map((x) => ({
      Company: x.lifecycleData.planned.client.name,
      Location: x.lifecycleData.planned.clientLocation,
      CCU: x.identifier,
      Received: x.lifecycleData.received.timestamp,
      Completed: x.lifecycleData.completed.timestamp,
    }));

    const csvContents = Papa.unparse(completedItemsCleaned);
    options = {
      from: '<EMAIL>',
      to: emailAddress,
      subject: 'Completed Units Requiring Collection (ASCO)',
      html: 'Please find attached a list of the containers requiring collection by Asco',
      attachments: [
        {
          fileName: `Requiring_Collection_By_Asco_${moment().format('DDMMYYYY')}.csv`,
          contents: csvContents,
        },
      ],
    };
  } else {
    options = {
      from: '<EMAIL>',
      to: emailAddress,
      subject: 'No Units Requiring Collection (ASCO)',
      html: 'There are currently no units requiring collection by Asco.',
    };
  }

  return options;
}

// Function called from cron email operation
function sendEmail(details) {
  if (false) {
    const completedItems = WorkItemEvents.find({
      isLatest: true,
      state: 'COMPLETED',
      $or: [{ 'lifecycleData.completed.responsibleForCollection': 'peterson' },
        { 'lifecycleData.completed.responsibleForCollection': { $exists: false } }],
    }).fetch();

    if (completedItems.length > 0) {
      const emailOptions = constructEmail(details.email, completedItems);
      Email.send(emailOptions);
    } else {
      console.log('No peterson items for collection');
    }
  } else {
    console.log('cron-operation: COLLECTION EMAIL WARNING NOT SENT (DEV MODE)');
  }
}

function sendAscoEmail(details) {
  const siteProfile = CompanySiteProfiles.findOne({ identifier: details.siteIdentifier });
  // check that we have an email address to send to
  const siteTransportEmail = siteProfile.configuration.transportEmail;
  if (App.inDevMode) {
    console.log('cron-operation: COLLECTION EMAIL WARNING NOT SENT (DEV MODE)');
  } else if (!siteTransportEmail) {
    console.log('cron-operation: COLLECTION EMAIL WARNING NOT SENT. ' +
      `No Email Address attached to site:${details.siteIdentifier}`);
  } else {
    const completedAscoItems = WorkItemEvents.find({
      isLatest: true,
      siteId: siteProfile._id,
      state: 'COMPLETED',
      deleted: { $exists: false },
      'lifecycleData.completed.responsibleForCollection': 'asco',
    }).fetch();

    const emailOptions = constructAscoEmail(siteTransportEmail, completedAscoItems);
    Email.send(emailOptions);
  }
}

function scheduleCompletedWarningMail() {
  // Get augean-tullos Site Information
  const siteIdentifier = 'augean-tullos';
  const siteProfile = CompanySiteProfiles.findOne({ identifier: siteIdentifier });
  const siteTransportEmailReminderTime = siteProfile.configuration.transportReminderTime;

  if (siteTransportEmailReminderTime) {
  // Create Cron Task
    SyncedCron.add({
      name: 'ASCO_FOR_COLLECTION_EMAIL',
      schedule(parser) {
        return parser.recur().on(siteTransportEmailReminderTime).time();
      },
      job() {
        sendAscoEmail({
          siteIdentifier,
        });
        return 'ASCO_FOR_COLLECTION_EMAIL';
      },
    });
  } else {
    console.log(`CompletedWarningEmail NOT SCHEDULED for site ${siteIdentifier}. ` +
      `ReminderTime: <${siteTransportEmailReminderTime}>.`);
  }
}

Meteor.startup(function onStartup() {
  console.log('Adding Cron Jobs..');
  scheduleCompletedWarningMail();
  SyncedCron.start();
});
