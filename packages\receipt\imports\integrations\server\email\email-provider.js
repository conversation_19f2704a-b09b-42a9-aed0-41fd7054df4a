import './email-templates/arrival-inspection-NCR';
import './email-templates/departure-inspection-NCR';
import './email-templates/empty-NCR';
import './email-templates/marshalling-yard-inspection-NCR';
import { SSR } from './ssr-service';

import { App } from '../../../shared/app';
import { CompanySiteProfiles } from '../../../api/company-site-profiles/company-site-profiles';
import { Meteor } from 'meteor/meteor';
import { WorkItemActions } from '../../../shared/work-item-actions';
import { WorkItemEvents } from '../../../api/work-item-events/work-item-events';

Meteor.methods({
  sendEmail(emailOptions) {
    console.log('sending email for ncr...');

    const userSite = CompanySiteProfiles.findOne({ _id: emailOptions.siteId });
    const workItemEvent = WorkItemEvents.findOne({ isLatest: true, lifecycleId: emailOptions.lifecycleId, deleted: { $exists: false } });

    let options;

    if (emailOptions.ncrType === WorkItemActions.ARRIVAL_INSPECTION &&
      workItemEvent.lifecycleData.arrivalInspection.fails > 0) {
      options = {
        from: '<EMAIL>',
        bcc: userSite.configuration.ncrEmailAddress,
        subject: `${emailOptions.identifier} Arrival Inspection Complete`,
        html: Meteor.call('renderArrivalInspectionNCR', emailOptions),
      };
    } else if (emailOptions.ncrType === WorkItemActions.DEPARTURE_INSPECTION &&
      workItemEvent.lifecycleData.departureInspection.fails > 0) {
      options = {
        from: '<EMAIL>',
        bcc: userSite.configuration.ncrEmailAddress,
        subject: `${emailOptions.identifier} Departure Inspection Complete`,
        html: Meteor.call('renderDepartureInspectionNCR', emailOptions),
      };
    } else if (emailOptions.ncrType === WorkItemActions.EMPTY &&
      workItemEvent.lifecycleData.empty.fails > 0) {
      options = {
        from: '<EMAIL>',
        bcc: userSite.configuration.ncrEmailAddress,
        subject: `${emailOptions.identifier} Empty Complete`,
        html: Meteor.call('renderEmptyNCR', emailOptions),
      };
    } else if (emailOptions.ncrType === WorkItemActions.MARSHALLING_YARD_INSPECTION &&
      workItemEvent.lifecycleData.marshallingYardInspection.fails > 0) {
      options = {
        from: '<EMAIL>',
        bcc: userSite.configuration.ncrEmailAddress,
        subject: `${App.inProductionMode ? '' : 'TEST '}${emailOptions.identifier} Marshalling Yard Inspection Complete`,
        html: Meteor.call('renderMarshallingYardInspectionNCR', emailOptions),
      };
    }
    if (options) {
      if (true) {
        Email.send(options);
      } else {
        console.log('Email suppressed as in dev mode. Email: ');
        console.log(options);
      }
    }
  },
});

Meteor.methods({
  renderArrivalInspectionNCR(options) {
    const ncrWorkItemEvent = WorkItemEvents.findOne({
      isLatest: true,
      lifecycleId: options.lifecycleId,
      deleted: { $exists: false },
    });
    return SSR.render('arrivalInspectionNCR', ncrWorkItemEvent);
  },
  renderDepartureInspectionNCR(options) {
    const ncrWorkItemEvent = WorkItemEvents.findOne({
      isLatest: true,
      lifecycleId: options.lifecycleId,
      deleted: { $exists: false },
    });
    return SSR.render('departureInspectionNCR', ncrWorkItemEvent);
  },
  renderEmptyNCR(options) {
    const ncrWorkItemEvent = WorkItemEvents.findOne({
      isLatest: true,
      lifecycleId: options.lifecycleId,
      deleted: { $exists: false },
    });
    return SSR.render('emptyNCR', ncrWorkItemEvent);
  },
  renderMarshallingYardInspectionNCR(options) {
    const mYardInspectionWorkItemEvent = WorkItemEvents.findOne({
      isLatest: true,
      lifecycleId: options.lifecycleId,
      deleted: { $exists: false },
    });
    return SSR.render('marshallingYardInspectionNCR', mYardInspectionWorkItemEvent);
  },
});
