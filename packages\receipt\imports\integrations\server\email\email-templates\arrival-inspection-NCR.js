import { Blaze } from 'meteor/blaze';
import { SSR } from '../ssr-service';

SSR.compileTemplate('arrivalInspectionNCR', Assets.getText('private/email-template-views/arrival-inspection-NCR.html'));

Blaze.Template.arrivalInspectionNCR.helpers({
  inspectionTimestamp() {
    return moment(this.lifecycleData.arrivalInspection.timestamp).format('DD/MM/YYYY HH:mm');
  },
  numberPassed() {
    return this.lifecycleData.arrivalInspection.passes;
  },
  numberFailed() {
    return this.lifecycleData.arrivalInspection.fails;
  },
  anyPassed() {
    return this.lifecycleData.arrivalInspection.passes > 0;
  },
  anyFailed() {
    return this.lifecycleData.arrivalInspection.fails > 0;
  },
  url() {
    return Meteor.settings.url;
  },
  imagesBaseUrl() {
    return Meteor.settings.private.azureBlobStorage.url;
  },
  iconsBaseUrl() {
    return `${Meteor.settings.private.azureBlobStorage.url}images/`;
  },
});
