import { Blaze } from 'meteor/blaze';
import { SSR } from '../ssr-service';

SSR.compileTemplate('completedWarningEmail', Assets.getText('email-template-views/completed-warning-email.html'));

Blaze.Template.completedWarningEmail.helpers({
  currentDate() {
    return moment().format('DD/MM/YYYY HH:mm');
  },
  numOfItemsRequiringCollection() {
    return this.length;
  },
  url() {
    return Meteor.settings.url;
  },
  iconsBaseUrl() {
    return `${Meteor.settings.private.azureBlobStorage.url}images/`;
  },
});
