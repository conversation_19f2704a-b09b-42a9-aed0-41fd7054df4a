import { Blaze } from 'meteor/blaze';
import { SSR } from '../ssr-service';

SSR.compileTemplate('departureInspectionNCR', Assets.getText('private/email-template-views/departure-inspection-NCR.html'));

Blaze.Template.departureInspectionNCR.helpers({
  inspectionTimestamp() {
    return moment(this.lifecycleData.departureInspection.timestamp).format('DD/MM/YYYY HH:mm');
  },
  numberPassed() {
    return this.lifecycleData.departureInspection.passes;
  },
  numberFailed() {
    return this.lifecycleData.departureInspection.fails;
  },
  anyPassed() {
    return this.lifecycleData.departureInspection.passes > 0;
  },
  anyFailed() {
    return this.lifecycleData.departureInspection.fails > 0;
  },
  url() {
    return Meteor.settings.url;
  },
  imagesBaseUrl() {
    return Meteor.settings.private.azureBlobStorage.url;
  },
  iconsBaseUrl() {
    return `${Meteor.settings.private.azureBlobStorage.url}images/`;
  },
});
