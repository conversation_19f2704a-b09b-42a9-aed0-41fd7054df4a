import { Blaze } from 'meteor/blaze';
import { SSR } from '../ssr-service';

SSR.compileTemplate('emptyNCR', Assets.getText('private/email-template-views/empty-NCR.html'));

Blaze.Template.emptyNCR.helpers({
  inspectionTimestamp() {
    return moment(this.lifecycleData.empty.timestamp).format('DD/MM/YYYY HH:mm');
  },
  numberPassed() {
    return this.lifecycleData.empty.passes;
  },
  numberFailed() {
    return this.lifecycleData.empty.fails;
  },
  anyPassed() {
    return this.lifecycleData.empty.passes > 0;
  },
  anyFailed() {
    return this.lifecycleData.empty.fails > 0;
  },
  url() {
    return Meteor.settings.url;
  },
  imagesBaseUrl() {
    return Meteor.settings.private.azureBlobStorage.url;
  },
  iconsBaseUrl() {
    return `${Meteor.settings.private.azureBlobStorage.url}images/`;
  },
});
