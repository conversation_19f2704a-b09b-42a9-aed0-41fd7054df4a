import { SSR } from '../ssr-service';
import { Blaze } from 'meteor/blaze';

SSR.compileTemplate('marshallingYardInspectionNCR', Assets.getText('private/email-template-views/marshalling-yard-inspection-NCR.html'));

Blaze.Template.marshallingYardInspectionNCR.helpers({
  inspectionTimestamp() {
    return moment(this.lifecycleData.marshallingYardInspection.timestamp)
      .format('DD/MM/YYYY HH:mm');
  },
  inspection() {
    return this.lifecycleData.marshallingYardInspection;
  },
  images() {
    return this.lifecycleData.empty;
  },
  numberPassed() {
    return this.lifecycleData.marshallingYardInspection.passes;
  },
  totalNumberFailed() {
    return this.lifecycleData.marshallingYardInspection.fails;
  },
  numberFailed() {
    const failureReasons = this.lifecycleData.marshallingYardInspection.failureReasons || [];

    return this.lifecycleData.marshallingYardInspection.fails - failureReasons.length;
  },
  anyFailed() {
    return this.lifecycleData.marshallingYardInspection.fails > 0;
  },
  anyPassed() {
    return this.lifecycleData.marshallingYardInspection.passes > 0;
  },
  anyFailReasons() {
    return this.lifecycleData.marshallingYardInspection.failureReasons &&
      this.lifecycleData.marshallingYardInspection.failureReasons.length > 0;
  },
  numberFailReasons() {
    return this.lifecycleData.marshallingYardInspection.failureReasons.length;
  },
  failReasonsFormatted() {
    return this.lifecycleData.marshallingYardInspection.failureReasons.map(reason => reason.toUpperCase());
  },
  url() {
    return Meteor.settings.url;
  },
  imagesBaseUrl() {
    return Meteor.settings.public.gcpStorage.url;
  },
  iconsBaseUrl() {
    return `${Meteor.settings.private.azureBlobStorage.url}images/`;
  },
});
