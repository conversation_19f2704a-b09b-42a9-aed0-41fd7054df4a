import { SpacebarsCompiler } from 'meteor/spacebars-compiler';
import { <PERSON> } from 'meteor/blaze';

// Adapted from: https://github.com/meteor/meteor/issues/11387#issuecomment-819835429
class SsrService {
  static render(templateName, data) {
    const renderFunc = data ? Blaze.toHTMLWithData : Blaze.toHTML;
    const template = Blaze.Template[templateName];

    if (!template) {
      throw new Error(`Template ${templateName} not found`);
    } else {
      return renderFunc(template, data);
    }
  }

  compileTemplate(name, content) {
    // eslint-disable-next-line no-eval
    const renderFunction = eval(`(function(view)
    {
      return ${SpacebarsCompiler.compile(content)}();
    })`);

    const template = new Blaze.Template(
      name,
      () => renderFunction(this),
    );

    Blaze.Template[name] = template;

    return template;
  }
}

export const SSR = new SsrService();
