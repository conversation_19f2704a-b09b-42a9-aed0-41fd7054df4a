import { Meteor } from 'meteor/meteor';

const stream = require('stream');

// TODO: Move config from json to Meteor settings
const gcpStorage = require('@google-cloud/storage')({
  projectId: Meteor.settings.private ? Meteor.settings.private.gcpStorage.projectId : '',
  keyFileName: Meteor.settings.private ? Assets.absoluteFilePath(Meteor.settings.private.gcpStorage.pathToKeyFile) : '',
});

const imageBucket = gcpStorage
  .bucket(Meteor.settings.private ? Meteor.settings.private.gcpStorage.bucketName : 'dev');

const generateFileName = (workItemEvent) => {
  const currentTimestamp = moment().utc().format();
  return `${workItemEvent.lifecycleId}_${currentTimestamp}.jpg`;
};

const dataUriToBuffer = (dataUri) => {
  if (!/^data\:/i.test(dataUri)) {
    throw new TypeError('`uri` does not appear to be a Data URI (must begin with "data:")');
  }

  // strip newlines
  const uri = dataUri.replace(/\r?\n/g, '');

  // split the URI up into the "metadata" and the "data" portions
  const firstComma = uri.indexOf(',');
  if (firstComma === -1 || firstComma <= 4) throw new TypeError('malformed data: URI');

  // remove the "data:" scheme and parse the metadata
  const meta = uri.substring(5, firstComma).split(';');

  let base64 = false;
  let charset = 'US-ASCII';
  for (let i = 0; i < meta.length; i++) {
    if (meta[i] === 'base64') {
      base64 = true;
    } else if (meta[i].indexOf('charset=') === 0) {
      charset = meta[i].substring(8);
    }
  }

  // get the encoded data portion and decode URI-encoded chars
  const data = unescape(uri.substring(firstComma + 1));

  const encoding = base64 ? 'base64' : 'ascii';
  const buffer = new Buffer(data, encoding);

  // set `.type` property to MIME type
  buffer.type = meta[0] || 'text/plain';

  // set the `.charset` property
  buffer.charset = charset;

  return buffer;
};

const uploadImageToBucketAsync = (base64JpegDataUri, workItemEvent) => {
  return new Promise((resolve, reject) => {
    // Create image buffer stream
    const imageBufferStream = new stream.PassThrough();
    imageBufferStream.end(dataUriToBuffer(base64JpegDataUri));

    // Create new blob in image bucket
    const gcpBlob = imageBucket.file(generateFileName(workItemEvent));

    // Create new write stream for blob
    imageBufferStream.pipe(gcpBlob.createWriteStream({
      metadata: {
        contentType: 'image/jpeg',
        metadata: {
          custom: 'metadata',
        },
        public: true,
      },
    })
      .on('error', (err) => {
        console.log(err);
        reject(err);
        return;
      })
      .on('finish', () => {
        resolve(gcpBlob.name);
        return;
      }));
  });
};

export const FileUploader = {
  imageUploadAsync: uploadImageToBucketAsync,
};

Meteor.methods({
  uploadImageFileAsync(base64JpegDataUri, workItemEvent) {
    if (Meteor.isServer) {
      return FileUploader
        .imageUploadAsync(base64JpegDataUri, workItemEvent);
    }

    return '';
  },
});
