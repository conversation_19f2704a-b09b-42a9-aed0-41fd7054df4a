import { CompanySiteProfiles } from '../../../../api/company-site-profiles/company-site-profiles';
import { WorkItemActions } from '../../../../shared/work-item-actions';
import { WorkItemEventStates } from '../../../../shared/work-item-event-states';

const payloadFormatVersion = 2;

function buildStateChangeVorEvent(vorEvent, workItemEvent) {
  let eventType = '';

  if (workItemEvent.state === WorkItemEventStates.COMPLETED) {
    eventType = 'readyForPickup'; //TODO: Make own state.
  } else if (workItemEvent.state === WorkItemEventStates.INPROGRESS) {
    eventType = 'started';
  } else {
    eventType = workItemEvent.state.toLowerCase();
  }

  if (workItemEvent.state === WorkItemEventStates.RECEIVED) {
    const receivedProps = {};

    vorEvent.event_type = workItemEvent.state.toLowerCase();

    _.extend(vorEvent, receivedProps);
  } else if (workItemEvent.state === WorkItemEventStates.COLLECTED) {
    const collectedProps = {};

    vorEvent.event_type = workItemEvent.state.toLowerCase();
    _.extend(vorEvent, collectedProps);
  }
  else if (workItemEvent.state === WorkItemEventStates.COMPLETED) {
    const inspectionProps = {};
    if (workItemEvent.lifecycleData.marshallingYardInspection) {
      const myardInspection = workItemEvent.lifecycleData.marshallingYardInspection;
      inspectionProps.inspection_passes = myardInspection.passes.toString();
      inspectionProps.inspection_fails = myardInspection.fails.toString();
    }
    vorEvent.event_type = 'arrivalInspection';
    _.extend(vorEvent, inspectionProps);
  }

  return vorEvent;
}

function buildActionCompleteVorEvent(vorEvent, workItemEvent, workItemAction) {
  if (workItemAction === WorkItemActions.ARRIVAL_INSPECTION) {
    _.extend(vorEvent, {
      time: moment(workItemEvent.lifecycleData.arrivalInspection.timestamp).utc().format(),
      event_type: 'arrivalInspection',
      inspection_passes: workItemEvent.lifecycleData.arrivalInspection.passes.toString(),
      inspection_fails: workItemEvent.lifecycleData.arrivalInspection.fails.toString(),
    });
  } else if (workItemAction === WorkItemActions.EMPTY) {
    const plannedContents = [].concat(workItemEvent.lifecycleData.planned.contents);
    const plannedContentsDescription = plannedContents.join(', ');

    const emptyProps = {
      time: moment(workItemEvent.lifecycleData.empty.timestamp).utc().format(),
      event_type: 'emptied',
      inspection_passes: workItemEvent.lifecycleData.empty.passes.toString(),
      inspection_fails: workItemEvent.lifecycleData.empty.fails.toString(),
    };

    if (plannedContentsDescription) {
      emptyProps.contents_removed_description = plannedContentsDescription;
    }

    _.extend(vorEvent, emptyProps);
  } else if (workItemAction === WorkItemActions.CLEAN) {
    const cleaningDone = [];

    if (workItemEvent.lifecycleData.clean.sweep) {
      cleaningDone.push('Sweep');
    }
    if (workItemEvent.lifecycleData.clean.powerWash) {
      cleaningDone.push('Power Wash');
    }
    if (workItemEvent.lifecycleData.clean.tankClean) {
      cleaningDone.push('Tank Clean');
    }
    if (workItemEvent.lifecycleData.clean.extensiveClean) {
      cleaningDone.push('Extensive Clean');
    }

    const cleaningDescription = cleaningDone.join(', ');

    const cleaningProps = {
      time: moment(workItemEvent.lifecycleData.clean.timestamp).utc().format(),
      event_type: 'cleaned',
    };

    if (cleaningDescription) {
      cleaningProps.cleaning_description = cleaningDescription;
    }

    _.extend(vorEvent, cleaningProps);
  } else if (workItemAction === WorkItemActions.DEPARTURE_INSPECTION) {
    _.extend(vorEvent, {
      time: moment(workItemEvent.lifecycleData.departureInspection.timestamp).utc().format(),
      event_type: 'departureInspection',
      inspection_passes: workItemEvent.lifecycleData.departureInspection.passes.toString(),
      inspection_fails: workItemEvent.lifecycleData.departureInspection.fails.toString(),
    });
  }

  return vorEvent;
}

const buildVorCancellationEvent = (customer, workItemEvent) => {
  const vorEvent = buildVorEvent(customer, workItemEvent);
  const cancellationVorEvent = { ...vorEvent, deleted: true };
  return cancellationVorEvent;
};

const buildVorEvent = (customer, workItemEvent, workItemAction) => {
  const site = CompanySiteProfiles.findOne({ _id: workItemEvent.siteId });

  let vorEvent = {
    container_id: workItemEvent.identifier,
    time: moment(workItemEvent.timestamp).utc().format(),
    customer,
    application_id: workItemEvent.lifecycleId,
    site_id: site.identifier,
    version: payloadFormatVersion,
  };

  if (!workItemAction) {
    vorEvent = buildStateChangeVorEvent(vorEvent, workItemEvent);
  } else {
    vorEvent = buildActionCompleteVorEvent(vorEvent, workItemEvent, workItemAction);
  }

  return vorEvent;
};

const vorEventBuilder = {
  buildVorCancellationEvent,
  buildVorEvent,
  buildStateChangeVorEvent,
  buildActionCompleteVorEvent,
};

export { vorEventBuilder as default };
