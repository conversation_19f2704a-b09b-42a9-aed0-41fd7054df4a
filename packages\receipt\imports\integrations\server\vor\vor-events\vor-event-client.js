import { App } from '../../../../shared/app';
import { HTTP } from 'meteor/http';
import { IntegrationConfigurations } from '../../../../api/integration-configurations/server/integration-configurations';
import { LoggerFactory } from '../../../../shared/logger-factory';
const logger = LoggerFactory.getLogger(__filename);

function renewTokenAndPostEvent(vorConfiguration, vorEvent, onSuccess) {
  if (!App.inDevMode) {
    logger.debug('Trying to retrieve new token...');

    const tokenRetrievalUrl = vorConfiguration.oAuthUrl;

    HTTP.call('POST', tokenRetrievalUrl, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      params: {
        grant_type: 'client_credentials',
        resource: vorConfiguration.resource,
        client_id: vorConfiguration.clientId,
        client_secret: vorConfiguration.clientSecret,
      },
    }, (error, result) => {
      if (!error) {
        if (result.statusCode === 200) {
          logger.debug('Token retrieved successfully.');
          vorConfiguration.oAuthToken = result.data.access_token;

          IntegrationConfigurations.update(
            vorConfiguration._id,
            { $set: { oAuthToken: result.data.access_token } },
            (error) => {
              if (error) {
                logger.error(error);
              }
            });
          onSuccess(vorConfiguration, vorEvent);
        }
      } else {
        logger.error(`Error when renewing token: ${error}`);
      }
    });
  } else {
    onSuccess(vorConfiguration, vorEvent);
  }
}

function postEvent(config, vorEvent) {
  if (!App.inDevMode) {
    const eventEmissionUrl = config.endpointUrl;

    logger.info(`Posting event to VOR: ${JSON.stringify(vorEvent)}`);

    HTTP.call('POST', eventEmissionUrl, {
      headers: {
        'Content-Type': 'application/json',
        'X-Source-Id': config.sourceId,
        Authorization: `Bearer ${config.oAuthToken}`,
        'ocp-apim-subscription-key': config.subscriptionKey,
      },
      data: vorEvent,
    }, (error, result) => {
      if (error) {
        logger.error(`Error on VOR POST: ${error}`);
        logger.error(vorEvent);
      }
      if (result) {
        if (result.statusCode === 200) {
          logger.info('Posting VOR event successful.');
        } else if (result.statusCode === 400) {
          logger.error('Posting VOR event failed with 400.');
          logger.error(result.data.message);
        } else if (result.statusCode === 401) {
          logger.warn('Posting VOR event failed with 401. Will renew token and post again.');
          renewTokenAndPostEvent(config, vorEvent, postEvent);
        }
      }
    });
  } else {
    logger.info(
      `VOR event posting suppressed as in dev mode. VOR Event: ${JSON.stringify(vorEvent)}`,
    );
  }
}

function postVorEvent(vorEvent) {
  console.log(vorEvent);
  const vorConfiguration = IntegrationConfigurations.findOne({ name: 'vor' });
  if (vorConfiguration) {
    if (!vorConfiguration.oAuthToken) {
      renewTokenAndPostEvent(vorConfiguration, vorEvent, postEvent);
    } else {
      postEvent(vorConfiguration, vorEvent);
    }
  }
}

const vorEventClient = {
  postVorEvent,
};

export { vorEventClient as default };
