import VorEventBuilder from './vor-event-builder';
import { WorkItemActions } from '../../../../shared/work-item-actions';
import { WorkItemEventStates } from '../../../../shared/work-item-event-states';
import { WorkItemEvents } from '../../../../api/work-item-events/work-item-events';

var getAllVorEvents = function getAllVorEvents() {
	var workItemEvents = WorkItemEvents.find({});

	var events = [];

	workItemEvents.forEach(function (workItemEvent) {
		if (!workItemEvent.deleted) {

			if (workItemEvent.state === WorkItemEventStates.RECEIVED ||
				workItemEvent.state === WorkItemEventStates.COMPLETED ||
				workItemEvent.state === WorkItemEventStates.COLLECTED) {

				var vorEvent = VorEventBuilder.buildVorEvent(workItemEvent);
				events.push(vorEvent);
			}
		}
	});

	return events;
}

var vorEventReplayer = {
	getAllVorEvents: getAllVorEvents
};

export { vorEventReplayer as default };
