import { CompanySiteProfiles } from '../../../../api/company-site-profiles/company-site-profiles';
import { EventTypeExpiryInHours } from './vor-incoming-items-event-type-expiry-in-hours';
import { LoggerFactory } from '../../../../shared/logger-factory';
import { Meteor } from "meteor/meteor";
import { WorkItemEventStates } from '../../../../shared/work-item-event-states';
import { WorkItemEvents } from '../../../../api/work-item-events/work-item-events';
import { moment } from 'meteor/momentjs:moment';
const logger = LoggerFactory.getLogger(__filename);

function startJob(siteId) {
  Meteor.setInterval(() => {
    logger.debug('Running check for any expired incoming items.');
    const site = CompanySiteProfiles.findOne({ _id: siteId });
    const vorIncomingItemsInPlanned = WorkItemEvents.find({
      deleted: { $exists: false },
      isLatest: true,
      state: {
        $in: [
          WorkItemEventStates.PLANNED,
          WorkItemEventStates.RECEIVED,
          WorkItemEventStates.INPROGRESS,
          WorkItemEventStates.COMPLETED,
        ],
      },
      siteId: site._id,
      createdByVor: true,
    }).fetch();

    vorIncomingItemsInPlanned.forEach(x => {
      let momentToCheckForExpiry;
      let ttlInStateInHours;

      if (x.latestVorInformation.eventType === 'Delivered') {
        momentToCheckForExpiry = moment(x.latestVorInformation.deliveredDateTime);
        ttlInStateInHours = EventTypeExpiryInHours.Delivered;
      } else if (x.latestVorInformation.eventType === 'Collected') {
        momentToCheckForExpiry = moment(x.latestVorInformation.collectedDateTime);
        ttlInStateInHours = EventTypeExpiryInHours.Collected;
      } else if (x.latestVorInformation.eventType === 'Allocated') {
        momentToCheckForExpiry = moment(x.latestVorInformation.allocatedDateTime);
        ttlInStateInHours = EventTypeExpiryInHours.Allocated;
      } else if (x.latestVorInformation.eventType === 'Planned') {
        momentToCheckForExpiry = moment(x.latestVorInformation.plannedDateTime);
        ttlInStateInHours = EventTypeExpiryInHours.Planned;
      }

      const momentNow = moment();
      const hoursSinceEnteredState = momentNow.diff(momentToCheckForExpiry, 'hours');
      logger.debug(`${hoursSinceEnteredState} hours since ${x.identifier} entered vor state ${x.latestVorInformation.eventType}`);

      if (hoursSinceEnteredState > ttlInStateInHours) {
        logger.info(
          `Setting incoming item ${x._id} as deleted since was never actioned.` +
          'Incoming item to be set as deleted:', x);
        WorkItemEvents.update({
          _id: x._id,
        },
        {
          $set: { deleted: true },
        },
        );
      }
    });
  }, 60000 * 15);
}

const CleanupIncomingItems = {
  startJob,
};
export { CleanupIncomingItems };
