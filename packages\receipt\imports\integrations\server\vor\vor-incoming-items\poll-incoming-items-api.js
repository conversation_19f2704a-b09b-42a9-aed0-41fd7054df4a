import { LoggerFactory } from '../../../../shared/logger-factory';
import { Meteor } from "meteor/meteor";
import { VorIncomingItemsClient } from './vor-incoming-items-client';

const logger = LoggerFactory.getLogger(__filename);

function startJob(siteId) {
  Meteor.setInterval(() => {
    logger.info('Polling VOR for incoming items.');
    VorIncomingItemsClient.requestIncomingWorkItems(siteId);
  }, 60000);
}

const PollIncomingItemsApi = {
  startJob,
};

export { PollIncomingItemsApi };
