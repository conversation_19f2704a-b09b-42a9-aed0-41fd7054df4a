import { CompanyProfiles } from '../../../../api/company-profiles/company-profiles';
import { CompanySiteProfiles } from '../../../../api/company-site-profiles/company-site-profiles';
import { LoggerFactory } from '../../../../shared/logger-factory';
import { Notifications } from '../../../../api/notifications/notifications';
import { WorkItemEventStates } from '../../../../shared/work-item-event-states';
import { WorkItemEvents } from '../../../../api/work-item-events/work-item-events';
import { moment } from 'meteor/momentjs:moment';

const logger = LoggerFactory.getLogger(__filename);

function generateNotification(
  companyId,
  siteId,
  workItemEventNotificationIsFor,
  vorInformationBefore,
  vorInformationAfter,
) {
  if (!vorInformationBefore || vorInformationBefore.eventType !== vorInformationAfter.eventType) {
    Notifications.insert({
      companyId,
      siteId,
      workItemEvent: workItemEventNotificationIsFor,
      vorInformationBefore,
      vorInformationAfter,
      notificationType: 'StateChanged',
      timestamp: moment()
        .utc()
        .toDate(),
    });
  } else {
    Notifications.insert({
      companyId,
      siteId,
      workItemEvent: workItemEventNotificationIsFor,
      vorInformationBefore,
      vorInformationAfter,
      notificationType: 'DetailsChanged',
      timestamp: moment()
        .utc()
        .toDate(),
    });
  }
}

function messageIsValid(incomingItemMessage) {
  if (
    !incomingItemMessage.toLocationNormalisedId ||
    !incomingItemMessage.toLocationNormalisedId.includes('peterson')
  ) {
    logger.info('Incoming message not valid as it is not an item coming to peterson');
    return false;
  }

  if (incomingItemMessage.identifier.toLowerCase().startsWith('bundle')) {
    logger.info('Incoming message not valid as it is for a bundle');
    return false;
  }

  const site = CompanySiteProfiles.findOne({
    $or: [
      { vorId: incomingItemMessage.toLocationNormalisedId },
      { vorIds: incomingItemMessage.toLocationNormalisedId },
    ],
  });
  if (!site) {
    logger.info('Incoming message invalid as cannot map it to a site within application.');
    return false;
  }

  if (
    !incomingItemMessage.timestamp ||
    !incomingItemMessage.eventType ||
    !incomingItemMessage.identifier
  ) {
    logger.info('Incoming message invalid as does not have mandatory fields');
    return false;
  }

  if (incomingItemMessage.eventType === 'Planned' && !incomingItemMessage.plannedDateTime) {
    logger.info('Incoming message in state Planned is invalid as does not have plannedDateTime');
    return false;
  }

  if (incomingItemMessage.eventType === 'Allocated' && !incomingItemMessage.allocatedDateTime) {
    logger.info(
      'Incoming message in state Allocated is invalid as does not have allocatedDateTime',
    );
    return false;
  }

  if (incomingItemMessage.eventType === 'Collected' && !incomingItemMessage.collectedDateTime) {
    logger.info(
      'Incoming message in state Collected is invalid as does not have collectedDateTime',
    );
    return false;
  }

  if (incomingItemMessage.eventType === 'Delivered' && !incomingItemMessage.deliveredDateTime) {
    logger.info(
      'Incoming message in state Delivered is invalid as does not have deliveredDateTime',
    );
    return false;
  }

  return true;
}

function resolveClient(allNamesForClient, siteProfile) {
  const lowerCaseClientNames = allNamesForClient.map((x) => x.toLowerCase());
  return siteProfile.configuration.clients.find((x) =>
    lowerCaseClientNames.includes(x.name.toLowerCase()),
  );
}

function resolveWorkItemType(description, siteProfile) {
  if (description) {
    const cleanDescription = description.toLowerCase();
    if (cleanDescription.includes('tank')) {
      return siteProfile.configuration.workItemTypes.find((x) => x.name.substring(0, 4) === 'Tote');
    }
    /* Request to default to container unless has tank in name
      TODO: Could be implemented better. */
    return siteProfile.configuration.workItemTypes.find((x) => x.name.substring(0, 6) === 'Doored');
  }
  return siteProfile.configuration.workItemTypes.find((x) => x.name.substring(0, 6) === 'Doored');
}

function getPetersonLogisticsProvider(siteProfile) {
  return 'Peterson';
}

export function findWorkItemEventsInOperationalWorkItemEvents(incomingWorkItemEvents, siteId) {
  const operationalWorkItemEvents = WorkItemEvents.find({
    deleted: { $exists: false },
    isLatest: true,
    siteId,
    'latestVorInformation.cargoLineId': { $in: incomingWorkItemEvents.map((x) => x.cargoLineId) },
  }).fetch();

  return operationalWorkItemEvents;
}

function mapIncomingItemMessageToPlannedLifecycleData(
  incomingItemMessage,
  utcTimestamp,
  user,
  siteProfile,
) {
  let allNamesForClient = [incomingItemMessage.clientName];
  if (incomingItemMessage.clientAliases && incomingItemMessage.clientAliases.length > 0) {
    allNamesForClient = allNamesForClient.concat(
      incomingItemMessage.clientAliases.map((x) => x.value),
    );
  }
  const contents = [];
  if (incomingItemMessage.description) {
    contents.push(incomingItemMessage.description);
  }

  return {
    plannedDateTime: utcTimestamp,
    timestamp: utcTimestamp,
    user,
    client: resolveClient(allNamesForClient, siteProfile),
    contents: [],
    clientLocation: incomingItemMessage.offshoreInstallationName,
    responsibleForCollection: getPetersonLogisticsProvider(siteProfile),
    isHighPriority:
      incomingItemMessage.isPriority &&
      (!incomingItemMessage.comments ||
        (incomingItemMessage.comments &&
          !incomingItemMessage.comments.toUpperCase().includes('48 HOURS'))),
    description: incomingItemMessage.description,
    voyageNo: incomingItemMessage.voyageNo,
    vessel: incomingItemMessage.vessel,
    dischargeTimestamp: incomingItemMessage.dischargeTimestamp,
    manifestNo: incomingItemMessage.manifestNo,
    weightInKg: incomingItemMessage.weightInKg,
  };
}

function mapIncomingItemMessageToReceivedLifecycleData(incomingItemMessage) {
  return {
    truckNoPlate: incomingItemMessage.vehicleRegNo,
  };
}

function mapIncomingItemMessageToVorInformation(incomingItemMessage) {
  let expectedDateTime = null;

  if (incomingItemMessage.eventType === 'Allocated') {
    expectedDateTime = moment(incomingItemMessage.allocatedDateTime)
      .add(1, 'hours')
      .toDate();
  } else if (incomingItemMessage.eventType === 'Collected') {
    expectedDateTime = moment(incomingItemMessage.collectedDateTime)
      .add(1, 'hours')
      .toDate();
  } else if (incomingItemMessage.eventType === 'Delivered') {
    expectedDateTime = moment(incomingItemMessage.deliveredDateTime).toDate();
  }

  return {
    id: incomingItemMessage.id,
    cargoGroupId: incomingItemMessage.cargoGroupId,
    cargoLineId: incomingItemMessage.cargoLineId,
    timestamp: incomingItemMessage.timestamp,
    eventType: incomingItemMessage.eventType,
    quantity: incomingItemMessage.quantity,
    identifier: incomingItemMessage.identifier,
    description: incomingItemMessage.description,
    fromLocationId: incomingItemMessage.masterFromLocationId,
    fromLocationName: incomingItemMessage.fromLocationName,
    fromDistrict: incomingItemMessage.fromDistrict,
    toLocationId: incomingItemMessage.masterToLocationId,
    toLocationName: incomingItemMessage.toLocationName,
    toDistrict: incomingItemMessage.toDistrict,
    clientId: incomingItemMessage.masterClientId,
    clientName: incomingItemMessage.clientName,
    expectedTime: incomingItemMessage.expectedTime,
    isPriority: incomingItemMessage.isPriority,
    comments: incomingItemMessage.comments,
    trailerNo: incomingItemMessage.trailerNo,
    vessel: incomingItemMessage.vessel,
    offshoreInstallationName: incomingItemMessage.offshoreInstallationName,
    offshoreInstallationId: incomingItemMessage.masterOffshoreInstallationId,
    orderNo: incomingItemMessage.orderNo,
    isARedirect: incomingItemMessage.isARedirect,
    plannedDateTime: incomingItemMessage.plannedDateTime,
    allocatedDateTime: incomingItemMessage.allocatedDateTime,
    collectedDateTime: incomingItemMessage.collectedDateTime,
    deliveredDateTime: incomingItemMessage.deliveredDateTime,
    vehicleId: incomingItemMessage.vehicleId,
    vehicleRegNo: incomingItemMessage.vehicleRegNo,
    trailerId: incomingItemMessage.trailerId,
    trailerRegNo: incomingItemMessage.trailerRegNo,
    driverId: incomingItemMessage.driverId,
    driverName: incomingItemMessage.driverName,
    isAllocatedToSubcontractor: incomingItemMessage.isAllocatedToSubcontractor,
    subcontractorName: incomingItemMessage.subcontractorName,
    expectedDateTime,
    normalisedId: incomingItemMessage.normalisedId,
    fromLocationAddress: incomingItemMessage.fromLocationAddress,
    fromLocationAliases: incomingItemMessage.fromLocationAliases,
    fromLocationDetectedType: incomingItemMessage.fromLocationDetectedType,
    fromLocationNormalisedId: incomingItemMessage.fromLocationNormalisedId,
    toLocationAddress: incomingItemMessage.toLocationAddress,
    toLocationAliases: incomingItemMessage.toLocationAliases,
    toLocationDetectedType: incomingItemMessage.toLocationDetectedType,
    toLocationNormalisedId: incomingItemMessage.toLocationNormalisedId,
    clientNormalisedId: incomingItemMessage.clientNormalisedId,
    clientAddress: incomingItemMessage.clientAddress,
    clientAliases: incomingItemMessage.clientAliases,
    voyageNo: incomingItemMessage.voyageNo,
    dischargeTimestamp: incomingItemMessage.dischargeTimestamp,
    manifestNo: incomingItemMessage.manifestNo,
    weightInKg: incomingItemMessage.weightInKg,
    vendorName: incomingItemMessage.vendorName,
    masterVendorId: incomingItemMessage.masterVendorId,
    direction: incomingItemMessage.direction,
    isCancelled: incomingItemMessage.isCancelled,
    isRob: incomingItemMessage.isRob,
    isNob: incomingItemMessage.isNob,
    isBoatSkip: incomingItemMessage.isBoatSkip,
    materials: (incomingItemMessage.materials) ? incomingItemMessage.materials.filter((x) => !x.isCancelled) : [],
  };
}

function addNewIncomingItem(companyId, siteId, incomingItemMessage, siteProfile, source) {
  const utcTimestamp = moment()
    .utc()
    .toDate();
  const user = 'VOR';
  const plannedLifecycleData = mapIncomingItemMessageToPlannedLifecycleData(
    incomingItemMessage,
    utcTimestamp,
    user,
    siteProfile,
  );

  const latestVorInformation = mapIncomingItemMessageToVorInformation(incomingItemMessage);
  const receivedLifecycleData = mapIncomingItemMessageToReceivedLifecycleData(
    incomingItemMessage,
    utcTimestamp,
    user,
    siteProfile,
  );
  const isItemNoLongerIncoming =
    incomingItemMessage.isCancelled ||
    incomingItemMessage.isRob ||
    incomingItemMessage.isNob ||
    incomingItemMessage.isBoatSkip ||
    (incomingItemMessage.quantity !== 1);

  if (!isItemNoLongerIncoming) {
    const workItemEvent = {
      identifier: incomingItemMessage.identifier,
      timestamp: utcTimestamp,
      user,
      lifecycleData: {
        planned: plannedLifecycleData,
        received: receivedLifecycleData,
      },
      state: WorkItemEventStates.PLANNED,
      lifecycleId: new Mongo.ObjectID()._str,
      isLatest: true,
      companyId,
      siteId,
      vorEvents: [incomingItemMessage],
      latestVorInformation,
      createdByVor: true,
      createdBy: source,
      linesReceipted:[],
    };

    WorkItemEvents.insert(workItemEvent, (error, id) => {
      if (!error) {
        logger.info(
          `Work Item Event created for ${incomingItemMessage.identifier} from Incoming message.`,
        );
        const createdWorkItemEvent = WorkItemEvents.findOne({ _id: id });
        generateNotification(
          companyId,
          siteId,
          createdWorkItemEvent,
          null,
          createdWorkItemEvent.latestVorInformation,
        );
      } else {
        logger.error(
          `Failed to create work item event from Incoming message ${JSON.stringify(
            incomingItemMessage,
          )}`,
        );
        logger.error(`Error: ${error}`);
      }
    });
  }
}

function updateUserCreatedIncomingItem(existingWorkItemEvent, incomingItemMessage, siteProfile) {
  const utcTimestamp = moment()
    .utc()
    .toDate();
  const user = 'VOR';
  const newplannedLifecycleData = mapIncomingItemMessageToPlannedLifecycleData(
    incomingItemMessage,
    utcTimestamp,
    user,
    siteProfile,
  );
  const newReceivedLifecycleData = mapIncomingItemMessageToReceivedLifecycleData(
    incomingItemMessage,
    utcTimestamp,
    user,
    siteProfile,
  );

  const workItemEventToUpdate = existingWorkItemEvent;

  if (
    !workItemEventToUpdate.lifecycleData.planned.client ||
    workItemEventToUpdate.lifecycleData.planned.client
  ) {
    workItemEventToUpdate.lifecycleData.planned.client = newplannedLifecycleData.client;
  }

  if (!workItemEventToUpdate.lifecycleData.planned.clientLocation) {
    workItemEventToUpdate.lifecycleData.planned.clientLocation =
      newplannedLifecycleData.clientLocation;
  }

  if (!workItemEventToUpdate.lifecycleData.planned.workItemType) {
    workItemEventToUpdate.lifecycleData.planned.workItemType = newplannedLifecycleData.workItemType;
  }

  if (
    !workItemEventToUpdate.lifecycleData.received ||
    !workItemEventToUpdate.lifecycleData.received.truckNoPlate
  ) {
    workItemEventToUpdate.lifecycleData.received = {
      truckNoPlate: newReceivedLifecycleData.truckNoPlate,
    };
  }

  const latestVorInformation = mapIncomingItemMessageToVorInformation(incomingItemMessage);

  if (workItemEventToUpdate.vorEvents) {
    workItemEventToUpdate.vorEvents.push(incomingItemMessage);
  } else {
    workItemEventToUpdate.vorEvents = [{ incomingItemMessage }];
  }
  const existingVorInformation = existingWorkItemEvent.latestVorInformation;
  workItemEventToUpdate.latestVorInformation = latestVorInformation;

  updateWorkItemEvents(workItemEventToUpdate, existingWorkItemEvent, existingVorInformation, latestVorInformation);
}

function updateVorCreatedIncomingItem(existingWorkItemEvent, incomingItemMessage, siteProfile) {
  const utcTimestamp = moment()
    .utc()
    .toDate();
  const user = 'VOR';
  const plannedLifecycleData = mapIncomingItemMessageToPlannedLifecycleData(
    incomingItemMessage,
    utcTimestamp,
    user,
    siteProfile,
  );
  const receivedLifecycleData = mapIncomingItemMessageToReceivedLifecycleData(
    incomingItemMessage,
    utcTimestamp,
    user,
    siteProfile,
  );
  const latestVorInformation = mapIncomingItemMessageToVorInformation(incomingItemMessage);

  const workItemEventToUpdate = existingWorkItemEvent;

  workItemEventToUpdate.vorEvents.push(incomingItemMessage);
  workItemEventToUpdate.lifecycleData.planned = plannedLifecycleData;
  workItemEventToUpdate.lifecycleData.received = receivedLifecycleData;
  const existingVorInformation = existingWorkItemEvent.latestVorInformation;
  workItemEventToUpdate.latestVorInformation = latestVorInformation;
  workItemEventToUpdate.identifier = incomingItemMessage.identifier;

  const isItemNoLongerIncoming =
    incomingItemMessage.isCancelled ||
    incomingItemMessage.isRob ||
    incomingItemMessage.isNob ||
    incomingItemMessage.isBoatSkip ||
    (incomingItemMessage.quantity !== 1);

  if (
    existingWorkItemEvent.state === WorkItemEventStates.PLANNED &&
    isItemNoLongerIncoming
  ) {
    workItemEventToUpdate.deleted = true;
    logger.info('Marking incoming item as deleted');
  }

  updateWorkItemEvents(workItemEventToUpdate, existingWorkItemEvent, existingVorInformation, latestVorInformation);
}

export function updateWorkItemEvents(workItemEventToUpdate, existingWorkItemEvent, existingVorInformation, latestVorInformation) {
  WorkItemEvents.update(workItemEventToUpdate._id, workItemEventToUpdate, (error) => {
    if (!error) {
      logger.info(
        `Work Item event updated for ${workItemEventToUpdate.identifier} from Incoming message.`,
      );
      generateNotification(
        existingWorkItemEvent.companyId,
        existingWorkItemEvent.siteId,
        workItemEventToUpdate,
        existingVorInformation,
        latestVorInformation,
      );
    } else {
      logger.error(
        `Failed to update work item event from Incoming message ${JSON.stringify(
          workItemEventToUpdate,
        )}`,
      );
      logger.error(`Error: ${JSON.stringify(error)}`);
    }
  });
}

function consumeMessage(incomingItemMessages, source, createEventsOnly) {
  logger.info(`Incoming item messages received from VOR: ${JSON.stringify(incomingItemMessages)}`);

  incomingItemMessages.forEach((incomingItemMessage) => {
    if (!messageIsValid(incomingItemMessage)) {
      logger.error(
        `Incoming item message received from VOR is not valid so will be discarded: ${JSON.stringify(
          incomingItemMessage,
        )}`,
      );
    }
  });

  const petersonCompanyProfile = CompanyProfiles.findOne({ name: 'peterson-nl' });
  const site = CompanySiteProfiles.findOne({
    $or: [{ vorId: incomingItemMessages[0].toLocationNormalisedId }, { vorIds: incomingItemMessages[0].toLocationNormalisedId }],
  });

  const validIncomingItemMessages = incomingItemMessages.filter((incomingItemMessage) =>
    messageIsValid(incomingItemMessage),
  );

  const operationalWorkItemsEventsForAllItems = findWorkItemEventsInOperationalWorkItemEvents(
    validIncomingItemMessages,
    site._id,
  );

  validIncomingItemMessages.forEach((incomingItemMessage) => {
    const eventsForThisItem = operationalWorkItemsEventsForAllItems.filter(
      (x) => x.latestVorInformation.cargoLineId === incomingItemMessage.cargoLineId,
    );

    if (eventsForThisItem.length === 0) {
      addNewIncomingItem(petersonCompanyProfile._id, site._id, incomingItemMessage, site, source);
    } else if (eventsForThisItem.length === 1 && !createEventsOnly) {
      const existingWorkItemEvent = eventsForThisItem[0];

      if (existingWorkItemEvent.state === WorkItemEventStates.PLANNED || 
          existingWorkItemEvent.state === WorkItemEventStates.RECEIVED || 
          existingWorkItemEvent.state === WorkItemEventStates.INPROGRESS) {
        if (existingWorkItemEvent.createdByVor) {
          updateVorCreatedIncomingItem(existingWorkItemEvent, incomingItemMessage, site);
        } else {
        // TODO: User entered - only merge in info that's not been input and link to vor incoming.
          updateUserCreatedIncomingItem(existingWorkItemEvent, incomingItemMessage, site);
        }
      } else {
        logger.error(
          'Incoming item message received from VOR is for item that has already been marked as completed/collected so will be set to ignored',
        );
      }
    }
  });
}

const VorIncomingItemMessageConsumer = {
  consumeMessage,
};

export { VorIncomingItemMessageConsumer };
