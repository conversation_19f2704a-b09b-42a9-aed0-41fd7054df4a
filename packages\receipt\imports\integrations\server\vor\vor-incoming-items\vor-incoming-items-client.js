import { CompanySiteProfiles } from '../../../../api/company-site-profiles/company-site-profiles';
import { EventTypeExpiryInHours } from './vor-incoming-items-event-type-expiry-in-hours';
import { HTTP } from 'meteor/http';
import { LoggerFactory } from '../../../../shared/logger-factory';
import { VorIncomingItemMessageConsumer } from './vor-incoming-item-message-consumer';
import { VorUnitEventsClient } from './vor-unit-events-client';
import { WorkItemEvents } from '../../../../api/work-item-events/work-item-events';
import { moment } from 'meteor/momentjs:moment';
import { Meteor } from 'meteor/meteor';

const logger = LoggerFactory.getLogger(__filename);

function removeEventsCreatedDueToDataReturnedFromTheApiWhichNowDoesNotExist(
  incomingItemMessages,
  siteId,
) {
  const itemIdentifiers = incomingItemMessages.map((x) => x.identifier);
  const workItemEvents =
    WorkItemEvents.find({
      identifier: {
        $nin: itemIdentifiers,
      },
      state: 'PLANNED',
      createdBy: 'VOR-API',
      siteId,
      deleted: { $exists: false },
    }).fetch();

  workItemEvents.forEach((x) => {
    if (x.vorEvents.length === 1) {
      logger.info(
        `Removing workitemevent as it is no longer in api result set. ${JSON.stringify(x)}`,
      );
      WorkItemEvents.update(
        {
          _id: x._id,
        },
        {
          $set:
          {
            deleted: true,
          },
        },
      );
    }
  });
}

function requestIncomingWorkItems(siteId) {
  const site = CompanySiteProfiles.findOne({ _id: siteId });
  console.log('Running incoming items');
  let siteVorIds = [];
  if (site.vorId) {
    siteVorIds = [site.vorId];
  } else if (site.vorIds) {
    siteVorIds = site.vorIds;
  }

  siteVorIds.forEach((siteVorId) => {
    const { vorCustomerApiBaseUrl } = Meteor.settings.private;
    const findVendorByIdUrl = vorCustomerApiBaseUrl
      .concat(`/vendors/FindVendorById/${siteVorId}`);
    HTTP.call(
      'POST',
      findVendorByIdUrl,
      {
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
        data: ['Admin'],
        timeout: 120000,
      },
      (error, result) => {
        if (!error) {
          if (result.statusCode === 200) {
            logger.info(`${
              result.data.IncomingUnits.length
            } incoming items received from VOR API.`);

            console.log(result.data.IncomingUnits);
            let incomingItemMessages = result.data.IncomingUnits.map((x) => ({
              id: null,
              timestamp: x.EventOcurredDateTime,
              eventType: 'Planned',
              quantity: 1,
              identifier: x.DisplayId,
              description: x.Description,
              fromLocationId: null,
              fromLocationName: null,
              fromDistrict: null,
              toLocationId: null,
              toLocationName: siteVorId,
              toLocationNormalisedId: siteVorId,
              toDistrict: null,
              clientId: x.Customer,
              clientName: x.Customer,
              expectedTime: null,
              isPriority: null,
              comments: null,
              trailerNo: null,
              vessel: null,
              offshoreInstallationName: null,
              offshoreInstallationId: null,
              orderNo: null,
              isARedirect: null,
              plannedDateTime: x.EventOcurredDateTime,
              allocatedDateTime: null,
              collectedDateTime: null,
              deliveredDateTime: null,
              vehicleId: null,
              vehicleRegNo: null,
              trailerId: null,
              trailerRegNo: null,
              driverId: null,
              driverName: null,
              isAllocatedToSubcontractor: false,
              subcontractorName: null,
              siteId: site.vorId,
              normalisedId: x.NormalisedId,
            })).filter((x) => !x.identifier.toLowerCase().startsWith('bundle'));

            const obseleteAfterMoment = moment().add(-(EventTypeExpiryInHours.Planned), 'hours');
            incomingItemMessages = incomingItemMessages
              .filter((x) => moment(x.plannedDateTime)
                .isAfter(obseleteAfterMoment));

            VorUnitEventsClient.requestEventsForUnits(
              incomingItemMessages.map((x) => x.normalisedId),
              (events) => {
                const createEventsOnly = true;
                incomingItemMessages.forEach((x) => {
                  const eventForUnit = events.find((y) => y.unitId === x.normalisedId);
                  if (eventForUnit) {
                    x.voyageNo = eventForUnit.voyageNo;
                    x.offshoreInstallationName = eventForUnit.offshoreInstallationName;
                    x.dischargeTimestamp = eventForUnit.dischargeTimestamp;
                    x.vessel = eventForUnit.vessel;
                    x.manifestNo = eventForUnit.manifestNo;
                    x.weight = eventForUnit.weight;
                  }
                  VorIncomingItemMessageConsumer.consumeMessage(x, 'VOR-API', createEventsOnly);
                });
              });
          } else {
            logger.error(
              `Error code received when polling vor for incoming items - Status Code ${
                result.statusCode}. Result:`,
              result,
            );
          }
        } else {
          logger.error('Error when polling vor for incoming items: ', error);
        }
      },
    );
  });
}

const VorIncomingItemsClient = {
  requestIncomingWorkItems,
};

export { VorIncomingItemsClient };
