import { HTTP } from 'meteor/http';
import { LoggerFactory } from '../../../../shared/logger-factory';
import { moment } from 'meteor/momentjs:moment';
import { Meteor } from 'meteor/meteor';

const logger = LoggerFactory.getLogger(__filename);

function requestEventsForUnits(unitIds, callback) {
  if (unitIds && unitIds.length > 0) {
    const { vorCustomerApiBaseUrl } = Meteor.settings.private;
    const findUnitsByNormalisedIdsUrl = vorCustomerApiBaseUrl
      .concat('/units/FindUnitsByNormalisedIds');

    HTTP.call(
      'POST',
      findUnitsByNormalisedIdsUrl,
      {
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
        data: { roles: ['Admin'], unitIds },
        timeout: 240000,
      },
      (error, result) => {
        if (!error) {
          if (result.statusCode === 200) {
            logger.info(`${
              result.data.length
            } units received from VOR API.`);

            const eventsToReturn = result.data.reduce((events, currentUnit) => {
              const liftedWeightEvents = (currentUnit.Events || [])
                .filter((x) => x.EventType === 'LiftedWeight' && !x.DestinationIsOffshore)
                .map((x) => ({
                  unitId: x.NormalisedId,
                  offshoreInstallationName: x.OriginLocation ? x.OriginLocation.Name : null,
                  voyageNo: x.VoyageNo,
                  vessel: x.Vessel ? x.Vessel.DisplayId : '',
                  timestamp: moment.utc(x.EventOcurredDateTime).toDate(),
                  dischargeTimestamp: moment.utc(x.EventOcurredDateTime).toDate(),
                  manifestNo: x.ManifestNo,
                  weight: x.MeasuredWeight,
                }))
                .sort((a, b) => {
                  if (a.dischargeTimestamp > b.dischargeTimestamp) {
                    return -1;
                  } else if (a.dischargeTimestamp == b.dischargeTimestamp) {
                    return 0;
                  } else {
                    return 1;
                  }
                });

              if (liftedWeightEvents.length > 0 && liftedWeightEvents[0]) {
                return events.concat([liftedWeightEvents[0]]);
              }
              return events;
            }, []);

            callback(eventsToReturn);
          } else {
            logger.error(
              `Error code received when retrieving events for units - Status Code ${
                result.statusCode}. Result:`,
              result,
            );
            callback();
          }
        } else {
          logger.error('Error when when  retrieving events for units: ', error);
          callback();
        }
      },
    );
  }
}

const VorUnitEventsClient = {
  requestEventsForUnits,
};

export { VorUnitEventsClient };
