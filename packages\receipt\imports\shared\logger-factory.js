import { Meteor } from "meteor/meteor";
import { App } from './app';

let getLogger;

if (Meteor.isServer) {
  import winston from "winston";

  const getLabel = (caller) => {
    const parts = caller.split('/');
    return parts[parts.length - 2] + '/' + parts.pop();
  }

  getLogger = (caller) => {
    let consoleLogger = new winston.transports.Console({
      name: "console",
      timestamp: true,
      label: get<PERSON>abel(caller),
      level: 'info',
    });

    logger = new winston.Logger({
      transports: [
        consoleLogger
      ]
    });

    logger.cli();

    return logger;
  }
} else if(!Meteor.isServer){

  function prependedConsoleLog(argument) {
    return function() {
      let args = [].slice.call(arguments);
      args.unshift(argument);
      console.log.apply(console, args);
    }
  }

  getLogger = (caller) => { 
    if(App.inDevMode){
      const logger = {
        info: prependedConsoleLog(`${caller}: `),
        warn: prependedConsoleLog(`${caller}: `),
        error: prependedConsoleLog(`${caller}: `),
        log: prependedConsoleLog(`${caller}: `),
        debug: prependedConsoleLog(`${caller}: `),
      };
      return logger;
    }
    const logger = {
      info () {},
      warn () {},
      error () {},
      log () {},
      debug () {},
    };
    return logger;
  }
}

const LoggerFactory = {
  getLogger: getLogger
}

export { LoggerFactory };