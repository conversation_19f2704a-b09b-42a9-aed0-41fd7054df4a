Meteor.startup(() => {
  // NOTE: Changed from moment.locale to moment.updateLocale to remove console deprecate warning.
  moment.updateLocale('en', {
    relativeTime: {
      future: 'in %s',
      past: '%s ago',
      s: 'seconds',
      m: '1 minute',
      mm: '%d min',
      h: '1 hour',
      hh: '%d hours',
      d: '1 day',
      dd: '%d days',
      M: '1 month',
      MM: '%d months',
      y: '1 year',
      yy: '%d years',
    },
    calendar: {
      lastDay: '[Yesterday at] LT',
      sameDay: '[Today at] LT',
      nextDay: '[Tomorrow at] LT',
      lastWeek: '[last] dddd [at] LT',
      nextWeek: 'dddd [at] LT',
      sameElse: '[on] DD/MM/YYYY [at] LT',
    },
  });

  moment().calendar(null, {
    sameDay: '[Today]',
    nextDay: '[Tomorrow]',
    nextWeek: 'dddd',
    lastDay: '[Yesterday]',
    lastWeek: '[Last] dddd',
    sameElse: 'DD/MM/YYYY',
  });

  sAlert.config({
    effect: '',
    position: 'top-right',
    timeout: 5000,
    html: false,
    onRouteClose: true,
    stack: true,
    offset: 0, // in px - will be added to first alert (bottom or top - depends on the position in config)
    beep: false,
    onClose: _.noop,
  });

  Session.set('refreshEnforcer', 1);
  Meteor.setInterval(() => {
    let currentEnforcement = Session.get('refreshEnforcer');
    Session.set('refreshEnforcer', currentEnforcement++);
  }, 60000);
});
