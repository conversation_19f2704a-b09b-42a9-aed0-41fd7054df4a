import '../../ui/views/layout/layout';
import '../../ui/views/accounts/loading.html';
/* Operations */
import '../../ui/views/operations/overview/work-item-overview';
import '../../ui/views/operations/actions/plan-and-receive-work-item/plan-and-receive-work-item';
import '../../ui/views/operations/actions/marshalling-yard-inspection/marshalling-yard-inspection';
import '../../ui/views/operations/actions/plan-work-item/plan-work-item';
/* Reporting */
import '../../ui/views/reporting/work-item-log';
import '../../ui/views/reporting/work-item-lifecycle-details';
import '../../ui/views/reporting/dashboard';
import '../../ui/views/reporting/reports/completed-to-collected';
import '../../ui/views/reporting/reports/ncrs';
import '../../ui/views/reporting/reports/containers-processed';
import '../../ui/views/reporting/reports/received-to-collected';
import '../../ui/views/reporting/reports/received-to-completed';
import '../../ui/views/reporting/reports/received-to-collected-beyond-target-time';

import { Router } from 'meteor/router';

const registerRoutes = (routePrefix = null) => {
  const router = new Router(routePrefix, 'receiptlLayout');

  router
    .routeWithNoTemplate('workItemOverview', '/')
    .route('planWorkItem', '/plan')
    .route('planAndReceiveWorkItem', '/plan-and-receive')
    .route('receiveWorkItem', '/receive/:lifecycleId')
    .route('receiveMultipleWorkItems', '/receive-load')
    .route('marshallingYardInspection', '/marshalling-yard-inspection')
    .route('workItemLog', '/log');

  router.group('reporting', '/dashboard')
    .route('dashboard', '/')
    .route('receivedToCompleted', '/received-to-completed')
    .route('ncrs', '/ncrs')
    .route('containersProcessed', '/containers-processed')
    .route('receivedToCollected', '/received-to-collected')
    .route('completedToCollected', '/completed-to-collected')
    .route('receivedToCollectedBeyondTargetTime', '/received-to-collected-beyond-target-time');
};

export const Routes = {
  register: (routePrefix = null) => registerRoutes(routePrefix),
};
