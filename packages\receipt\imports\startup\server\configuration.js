import { Meteor } from 'meteor/meteor';
import { WorkItems } from '../../api/work-items/work-items';
import { WorkItemEvents } from '../../api/work-item-events/work-item-events';
import { Notifications } from '../../api/notifications/notifications';

WorkItems.createIndex({ Name: 1 });
WorkItemEvents.createIndex({ 'lifecycleData.received.timestamp': 1 });
WorkItemEvents.createIndex({ identifier: 1 });
WorkItemEvents.createIndex({ 'lifecycleData.planned.clientLocation': 1 });
WorkItemEvents.createIndex({ 'latestVorInformation.cargoLineId': 1 });
WorkItemEvents.createIndex({
  companyId: 1, siteId: 1, isLatest: 1, deleted: 1, state: 1, timestamp: 1,
}, { name: 'latestEvents' });
Notifications.createIndex({ timestamp: -1 });

const smtp = (Meteor.settings.private && Meteor.settings.private.smtp)
  ? Meteor.settings.private.smtp
  : {
    username: '',
    password: '',
    server: '',
    port: 25,
  };

process.env.MAIL_URL = 'smtp://' + encodeURIComponent(smtp.username) + ':' + encodeURIComponent(smtp.password) + '@' + encodeURIComponent(smtp.server) + ':' + smtp.port;

process.env.GOOGLE_APPLICATION_CREDENTIALS = Meteor.settings.private ? Assets.absoluteFilePath(Meteor.settings.private.gcpStorage.pathToKeyFile) : '';
