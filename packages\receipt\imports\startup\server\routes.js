import { Picker } from 'meteor/meteorhacks:picker';
import './routes/vor-api-routes';

Picker.route('/email-templates/arrival-inspection-NCR/:_id', (params, req, response) => {
  const html = Meteor.call('renderArrivalInspectionNCR', { lifecycleId: params._id });
  response.end(html);
});

Picker.route('/email-templates/departure-inspection-NCR/:_id', (params, req, response) => {
  const html = Meteor.call('renderDepartureInspectionNCR', { lifecycleId: params._id });
  response.end(html);
});

Picker.route('/email-templates/empty-NCR/:_id', (params, req, response) => {
  const html = Meteor.call('renderEmptyNCR', { lifecycleId: params._id });
  response.end(html);
});

Picker.route('/email-templates/marshalling-yard-inspection-NCR/:_id', (params, req, response) => {
  const html = Meteor.call(
    'renderMarshallingYardInspectionNCR',
    { lifecycleId: params._id },
  );
  response.end(html);
});
