import { CompanyProfiles } from '../../../api/company-profiles/company-profiles';
import { CompanySiteProfiles } from '../../../api/company-site-profiles/company-site-profiles';
import { LoggerFactory } from '../../../shared/logger-factory';
import { Picker } from 'meteor/meteorhacks:picker';
import { VorIncomingItemMessageConsumer } from '../../../integrations/server/vor/vor-incoming-items/vor-incoming-item-message-consumer';
import { WorkItemEventStates } from '../../../shared/work-item-event-states';
import { WorkItemEvents } from '../../../api/work-item-events/work-item-events';
import bodyParser from 'body-parser';
import { moment } from 'meteor/momentjs:moment';
const logger = LoggerFactory.getLogger(__filename);

Picker.middleware(bodyParser.json({
  limit: '100mb',
}));

Picker.route('/api/events', (params, req, res) => {
  const response = res;
  response.statusCode = 200;
  response.setHeader('Content-Type', 'application/json');
  response.setHeader('Access-Control-Allow-Origin', '*');
  response.setHeader(
    'Access-Control-Allow-Headers',
    'Origin, X-Requested-With, Content-Type, Accept',
  );

  const events = Meteor.call('getAllVorEvents');
  response.end(JSON.stringify(events, null, 4));
});

Picker.route('/api/active', (params, req, res) => {
  const response = res;
  response.statusCode = 200;
  response.setHeader('Content-Type', 'application/json');
  response.setHeader('Access-Control-Allow-Origin', '*');
  response.setHeader('Access-Control-Allow-Headers',
    'Origin, X-Requested-With, Content-Type, Accept');

  let activeWorkItems = [];

  const siteName = params.query.siteId;

  if (siteName) {
    const site = CompanySiteProfiles.findOne({ identifier: siteName.toLowerCase() });

    if (site) {
      activeWorkItems = WorkItemEvents.find({
        $or: [
          {
            deleted: { $exists: false },
            isLatest: true,
            state: {
              $in: [
                WorkItemEventStates.PLANNED,
                WorkItemEventStates.RECEIVED,
                WorkItemEventStates.INPROGRESS,
                WorkItemEventStates.COMPLETED,
              ],
            },
            siteId: site._id,
          },
          {
            deleted: { $exists: false },
            isLatest: true,
            state: {
              $in: [
                WorkItemEventStates.COLLECTED,
              ],
            },
            'lifecycleData.collected.timestamp': {
              $exists: true,
              $gt: moment().set({ hour: 0, minute: 0, second: 0 }).toDate(),
            },
            siteId: site._id,
          }],
      }, { fields: { _id: 0, state: 1, identifier: 1, timestamp: 1, lifecycleData: 1, latestVorInformation: 1 } }).fetch();
    }
  }

  const workItemsPerState = {};

  workItemsPerState[WorkItemEventStates.PLANNED] = [];
  workItemsPerState.TANKS = [];
  workItemsPerState.CCUS = [];
  workItemsPerState[WorkItemEventStates.INPROGRESS] = [];
  workItemsPerState['OUTBOUNDCOMPLETED'] = [];
  workItemsPerState['INBOUNDCOMPLETED'] = [];
  workItemsPerState[WorkItemEventStates.COLLECTED] = [];

  activeWorkItems.forEach((workItem) => {
    workItem.offshoreInstallation = workItem.latestVorInformation.offshoreInstallationName;
    workItem.vessel = workItem.latestVorInformation.vessel;
    if (workItem.latestVorInformation.manifestNo) {
      workItem.manifestNo = workItem.latestVorInformation.manifestNo;
    } else {
      if (workItem.latestVorInformation.vessel) {
        const isVesselOverLoadedWithManifest =
          workItem.latestVorInformation.vessel.substring(0, 2) === 'B-' || workItem.latestVorInformation.vessel.substring(0, 2) === 'L-';
        if (isVesselOverLoadedWithManifest) {
          workItem.manifestNo = workItem.latestVorInformation.vessel;
          workItem.vessel = null;
        }
      }
    }

    workItem.vendor = workItem.latestVorInformation.vendorName;
    workItem.direction = workItem.latestVorInformation.direction;
    workItem.receivedDateTime = workItem.lifecycleData.received ? workItem.lifecycleData.received.timestamp : null;
    if (workItem.lifecycleData.received) {
      const momentReceived = moment(workItem.lifecycleData.received.timestamp);
      const momentNow = moment();
      workItem.timeAtBaseInDays = momentNow.diff(momentReceived, 'days');
    } else {
      workItem.timeAtBaseInDays = 0;
    }

    workItem.description = workItem.latestVorInformation.description;

    if (workItem.state === WorkItemEventStates.COMPLETED) {
      if (workItem.direction.toLowerCase() === 'outbound') {
        workItemsPerState['OUTBOUNDCOMPLETED'].push(workItem);
      } else {
        workItemsPerState['INBOUNDCOMPLETED'].push(workItem);
      }
    } else if (workItemsPerState[workItem.state]) {
      workItemsPerState[workItem.state].push(workItem);
    }

    if (workItem.state === WorkItemEventStates.PLANNED) {
      workItem.timestamp = workItem.latestVorInformation.plannedDateTime || workItem.lifecycleData.planned.plannedDateTime;
    }
    delete workItem.lifecycleData;
    delete workItem.latestVorInformation;
  });

  response.end(JSON.stringify(workItemsPerState));
});

// Takes query params fromDate & toDate
Picker.route('/api/ncrs/overview', (params, req, res) => {
  const response = res;
  response.statusCode = 200;
  response.setHeader('Content-Type', 'application/json');
  response.setHeader('Access-Control-Allow-Origin', '*');
  response.setHeader('Access-Control-Allow-Headers',
    'Origin, X-Requested-With, Content-Type, Accept');

  const companyProfile = CompanyProfiles.findOne();
  const companySiteProfile = CompanySiteProfiles.findOne({
    _id: companyProfile.siteIds[0],
  });

  const fromDate = moment(params.query.fromDate).toDate();
  const toDate = moment(params.query.toDate).toDate();

  const ncrsOverview = Meteor.call(
    'ncrsPerClientReport',
    companyProfile._id,
    companySiteProfile._id,
    fromDate,
    toDate,
  );

  response.end(JSON.stringify(ncrsOverview, null, 2));
});

// Takes query params vendorName, fromDate & toDate
Picker.route('/api/ncrs/for-vendor', (params, req, res) => {
  const response = res;
  response.statusCode = 200;
  response.setHeader('Content-Type', 'application/json');
  response.setHeader('Access-Control-Allow-Origin', '*');
  response.setHeader(
    'Access-Control-Allow-Headers',
    'Origin, X-Requested-With, Content-Type, Accept',
  );

  const companyProfile = CompanyProfiles.findOne();
  const companySiteProfile = CompanySiteProfiles.findOne({
    _id: companyProfile.siteIds[0],
  });

  const fromDate = moment(params.query.fromDate).toDate();
  const toDate = moment(params.query.toDate).toDate();

  const ncrsForVendor = Meteor.call(
    'vendorBreakdownOfTypesOfFailure',
    companyProfile._id,
    companySiteProfile._id,
    params.query.vendorName,
    fromDate,
    toDate,
  );

  response.end(JSON.stringify(ncrsForVendor, null, 2));
});

const postRoutes = Picker.filter((req) => req.method === 'POST');

postRoutes.route('/api/incoming-item', (params, req, res) => {
  const response = res;
  response.statusCode = 200;
  response.setHeader('Content-Type', 'application/json');
  response.setHeader('Access-Control-Allow-Origin', '*');
  response.setHeader('Access-Control-Allow-Headers',
    'Origin, X-Requested-With, Content-Type, Accept',
  );

  const siteIncomingItems = req.body.incomingItems.map((x) => {
    x.siteId = 'petersonchemicalsdnhr';
    return x;
  });

  try {
    VorIncomingItemMessageConsumer.consumeMessage(siteIncomingItems, 'VOR-MESSAGE');
    response.end(JSON.stringify({}));
  } catch (err) {
    logger.error(err);
  }
});
