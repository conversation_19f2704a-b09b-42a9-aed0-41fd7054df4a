import { moment } from 'meteor/momentjs:moment';

function getNotification(notification) {
  const previouslyHadVorIncomingItemInformation = !!notification.vorInformationBefore;
  const deliveryIsInProgress =
    ['Collected', 'Delivered'].includes(notification.vorInformationAfter.eventType);

  if (!previouslyHadVorIncomingItemInformation) {
    if (notification.vorInformationAfter.eventType === 'Planned') {
      return {
        primary: 'has been added to Incoming Cargo Items',
      };
    }
    if (notification.vorInformationAfter.eventType === 'Allocated') {
      return {
        primary: 'is on its way.',
        secondary:
          `Expected ${moment(notification.vorInformationAfter.expectedDateTime).calendar()}`,
      };
    }
    if (deliveryIsInProgress) {
      return {
        primary: 'is on its way.',
        secondary:
          `Expected ${moment(notification.vorInformationAfter.expectedDateTime).calendar()}`,
      };
    }
  } else {
    const deliveryWasPreviouslyInProgress =
      ['Allocated', 'Planned'].includes(notification.vorInformationBefore.eventType);
    const expectedDateTimeHasChanged =
      notification.vorInformationBefore.timestamp !== notification.vorInformationAfter.timestamp;

    if (notification.vorInformationBefore.eventType === 'Planned' &&
      notification.vorInformationAfter.eventType === 'Allocated') {
      return {
        primary: 'is on its way.',
        secondary:
          `Expected ${moment(notification.vorInformationAfter.expectedDateTime).calendar()}`,
      };
    }
    if (notification.vorInformationBefore.eventType === 'Allocated' &&
        notification.vorInformationAfter.eventType === 'Allocated' &&
        expectedDateTimeHasChanged) {
      return {
        primary: 'is on its way.',
        secondary:
          `Now expected ${moment(notification.vorInformationAfter.expectedDateTime).calendar()}`,
      };
    }
    if (!deliveryWasPreviouslyInProgress &&
      deliveryIsInProgress) {
      return {
        primary: 'is on its way.',
        secondary:
          `Expected ${moment(notification.vorInformationAfter.expectedDateTime).calendar()}`,
      };
    }
    if (deliveryWasPreviouslyInProgress &&
      deliveryIsInProgress &&
      expectedDateTimeHasChanged) {
      return {
        primary: 'is on its way.',
        secondary:
         `Now expected ${moment(notification.vorInformationAfter.expectedDateTime).calendar()}`,
      };
    }
  }
}

export const NotificationFactory = {
  getNotification,
};
