import { CompanyProfiles } from '../../api/company-profiles/company-profiles';

Template.registerHelper('themeBackClass', () => {
  const companyProfile = CompanyProfiles.findOne();

  if (companyProfile) {
    if (companyProfile.name === 'peterson') {
      return 'black';
    }
  }
  return 'cyan darken-2';
});

Template.registerHelper('themeBackClassDark', () => {
  const companyProfile = CompanyProfiles.findOne();

  if (companyProfile) {
    if (companyProfile.name === 'peterson') {
      return 'peterson-back-dark';
    }
  }
  return 'cyan darken-4';
});

Template.registerHelper('themeForeClass', () => {
  const companyProfile = CompanyProfiles.findOne();

  if (companyProfile) {
    if (companyProfile.name === 'peterson') {
      return 'peterson-fore';
    }
  }
  return 'cyan-text text-darken-2';
});

Template.registerHelper('typeaheadOptions', () => {
  const classes = GetTypeAheadClasses();

  return JSON.stringify({
    classNames: classes,
  });
});

Template.registerHelper('logoUrl', () => {
  const companyProfile = CompanyProfiles.findOne();

  if (companyProfile) {
    if (companyProfile.name === 'peterson') {
      return '/images/peterson-logo-white.png';
    }
  }
  return '/images/peterson-logo-white.png';
});

const GetTypeAheadClasses = () => {
  const companyProfile = CompanyProfiles.findOne();

  if (companyProfile) {
    if (companyProfile.name === 'peterson') {
      return {
        suggestion: 'tt-suggestion peterson-typeahead-suggestion',
        cursor: 'peterson-typeahead-cursor',
      };
    }
  }
  return {
    suggestion: 'tt-suggestion typeahead-suggestion',
    cursor: 'typeahead-cursor',
  };
};
