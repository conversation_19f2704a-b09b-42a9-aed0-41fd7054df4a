<template name="userDropdownVr">
  <div class="item right floated">
    <div class="ui dropdown labled icon item basic inverted button" data-activates="userDropdown" data-constrainwidth="false" style="border-radius: 0.28571429rem !important;">

        <div><i class="user icon"></i></div>
        <span class="text">{{username}}</span>

        <div class="menu" id="userDropdown">
              <div class="item" data-value="changePassword">
                <i class="key icon"></i>
                <span class="text">Update Password</span>
              </div>
              {{#if canAddUsers}}
              <div class="item" data-value="enrollUser">
                <i class="add icon"></i>
                <span class="text">Add User</span>
              </div>
              {{/if}}
              <div class="item" data-value="logout">
                  <i class="sign out icon"></i>
                  <span class="text">Logout</span>
                </div>
        </div>
    </div>
  </div>
</template>