import './user-dropdown-vr.html';
import { Meteor } from 'meteor/meteor';
import { Roles } from 'meteor/alanning:roles';
import { Template } from 'meteor/templating';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';

const logout = () => {
  Meteor.logout();
  FlowRouter.go('login');
};

const changePassword = () => {
  FlowRouter.go('changePassword');
};

const enrollUser = () => {
  FlowRouter.go('enrollUser');
};

Template.userDropdownVr.onRendered(() => {
  const templateInstance = this;
  templateInstance.$('.ui.dropdown').dropdown({
    onChange(value) {
      if (value === 'logout') logout();
      if (value === 'changePassword') changePassword();
      if (value === 'enrollUser') enrollUser();
    },
    action: 'hide',
  });
});

Template.userDropdownVr.helpers({
  username() {
    const user = Meteor.user();
    return user ? user.username : 'User';
  },
  canAddUsers() {
    const user = Meteor.user();
    return (Roles.userIsInRole(user, 'admin', 'peterson-chemicals-dnhr'));
  },
});
