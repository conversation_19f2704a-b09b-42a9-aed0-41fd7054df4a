<template name="receiptlLayout">

      {{#if Template.subscriptionsReady}}
      <div class="ui center aligned container">
        <div class="ui top fixed inverted menu">
            <a class="header item" href="{{pathFor 'App.home' query=''}}">
                <img class="ui middle aligned tiny image" src="{{publicUrlFor pkgName '/images/peterson-logo-white.png'}}" />
            </a>
            <div class="left menu">
                <a class="item" href="{{pathFor 'workItemOverview'}}" style="border-bottom: 3px solid #e7e9ed">
                    <span style="padding-top:3px;">Cargo</span>
                </a>
                <a class="item" href="{{pathFor 'unpackCargo' clientId='null'}}" >
                    Waste
                </a>
            </div>

        {{#if userLoggedIn}}
          <div class="right menu">
              <div class="item" style="width:225px;">
                  <div class="ui icon input" style="display: {{searchInputDisplayStyle}}">
                    <input id="workItemSearch" class="overview-filter-search-input" type="text" placeholder="Filter..." >
                  </div>
                </div>
            {{#if userBelongsToMoreThanOneSite }}
              {{> siteDropdown}}
            {{/if}}
            <a class="item" href="{{pathFor 'workItemOverview'}}">
                <i class="table icon">
                </i> Overview
            </a>
            <a class="item" href="{{pathFor 'workItemLog' }}">
                <i class="history icon">
                </i> History
            </a>
            {{> userDropdownVr}}
          </div>
        {{/if}}
          </div>
          </div>
          <div id="main">
            {{> Template.dynamic template=main}}
              <span style="display: {{overviewVisibility}};">
                {{> workItemOverview}} 
              </span>
          </div>
          {{> sAlert template='notification'}}      
      {{else}}
          {{>loading}}
      {{/if}} 

</template>
