import './layout.html';
import '../accounts/loading.html';
import './notification';
import '../../helpers/theme-helpers';
import '../accounts/user-dropdown-vr';
import './user-dropdown';
import './site-dropdown';
import '../operations/overview/work-item-overview';

import { CompanyProfiles } from '../../../api/company-profiles/company-profiles';
import { CompanySiteProfiles } from '../../../api/company-site-profiles/company-site-profiles';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Meteor } from 'meteor/meteor';
import { NotificationFactory } from '../../../ui/helpers/notification-factory';
import { Notifications } from '../../../api/notifications/notifications';
import { ReactiveVar } from 'meteor/reactive-var';
import { Session } from 'meteor/session';
import { Template } from 'meteor/templating';
import { PackageSettings } from 'meteor/receipt/imports/startup/package-settings';

Template.receiptlLayout.onCreated(function onCreated() {
  const self = this;
  let notificationsLoaded = false;
  self.isOverviewScreen = new ReactiveVar(false);
  self.autorun(() => {
    const company = CompanyProfiles.findOne();
    const userSites = CompanySiteProfiles.find();
    const currentUser = Meteor.users.findOne({ _id: Meteor.userId() });

    self.subscribe('currentUserCompanyProfile');
    self.subscribe('currentUserCompanySiteProfiles');
    self.subscribe('users');

    const siteSelected = Session.get('application.currentSite');

    if (currentUser) {
      if (!siteSelected && userSites) {
        let siteToSelectId;
        const defaultSiteMapping = currentUser.profile.sites.find((x) => x.isDefault);
        if (defaultSiteMapping) {
          siteToSelectId = defaultSiteMapping.companySiteProfileId;
        } else {
          siteToSelectId = currentUser.profile.sites[0].companySiteProfileId;
        }
        Session.set(
          'application.currentSite',
          userSites.fetch().find((x) => x._id === siteToSelectId),
        );
      }

      if (company && siteSelected) {
        self.subscribe('latestWorkItemEvents', company._id, siteSelected._id);
        notificationsLoaded = false;
        self.subscribe('notifications', company._id, siteSelected._id, () => {
          notificationsLoaded = true;
        });
      }
    }
  });

  self.notificationObserver = Notifications.find().observe({
    added(notification) {
      if (notificationsLoaded && !Meteor.isCordova) {
        const notificationMessages = NotificationFactory.getNotification(notification);
        sAlert.info(
          {
            identifier: notification.workItemEvent.identifier,
            primaryMessage: notificationMessages.primary,
            secondaryMessage: notificationMessages.secondary,
          },
          {
            effect: 'scale',
            offset: '75px',
            timeout: 600000,
            stack: { spacing: 10 },
          },
        );
      }
    },
  });

  // Setup autorun to listen for path changes
  self.autorun(() => {
    FlowRouter.watchPathChange();
    const currentRoute = FlowRouter.current();

    const isOverviewScreen = currentRoute && currentRoute.route.name === 'workItemOverview';

    self.isOverviewScreen.set(isOverviewScreen);
  });
});

Template.receiptlLayout.onDestroyed(function onDestroyed() {
  // Observer must be stopped.
  this.notificationObserver.stop();
});

Template.receiptlLayout.events({
  'click #logoutButton': function onClick() {
    Meteor.logout((error) => {
      if (error) {
        console.log(error);
      } else {
        FlowRouter.go('login');
      }
    });
  },
});

Template.receiptlLayout.helpers({
  userLoggedIn() {
    const user = Meteor.user();
    return user;
  },
  userBelongsToMoreThanOneSite() {
    return CompanySiteProfiles.find().count() > 1;
  },
  searchInputDisplayStyle() {
    return Template.instance().isOverviewScreen.get() ? 'intial' : 'none';
  },
  overviewVisibility() {
    return Template.instance().isOverviewScreen.get() ? 'initial' : 'none';
  },
  pkgName() {
    return PackageSettings.name;
  },
});
