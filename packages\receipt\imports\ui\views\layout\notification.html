<template name="notification">
    <div class="custom-alert-class s-alert-box s-alert-{{condition}} s-alert-{{position}} {{#if effect}}s-alert-is-effect s-alert-effect-{{effect}}{{/if}} s-alert-show" id="{{_id}}" 
    style="{{boxPosition}} padding-top: 20px; padding-bottom: 0px; border: 3px solid #7a003b; color:#7a003b; background-color: rgba(245, 245, 245, 0.88);">
        <div class="s-alert-box-inner">
            <div class="ui grid" style="margin-bottom:0px;">
                <div class="three wide column" style="padding:0px; padding-top:5px;">
                    <img id="petersonLogo" height="48px" src="/images/petersoniconblue.png">
                </div>
                <div class="thirteen wide column" style="padding:0px; padding-left:10px">
                    <span>
                        <span style="font-size:20px; font-weight:700;">{{identifier}}</span>
                        <span style="font-weight:400; padding-left:5px;">{{primaryMessage}}</span>
                    </span>
                    <span style="margin-top: 5px; display: block; color: black; font-weight: 300;font-size:16px;">
                        {{secondaryMessage}}
                    </span>
                    </div>
            </div>

        </div>
        <span class="s-alert-close" style="color:#7a003b;"></span>
    </div>
</template>