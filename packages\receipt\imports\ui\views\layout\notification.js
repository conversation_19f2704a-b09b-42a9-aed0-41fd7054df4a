import './notification.html';

import { Template } from 'meteor/templating';
import { WorkItemEventStates } from '../../../shared/work-item-event-states';
import { WorkItemEvents } from '../../../api/work-item-events/work-item-events';

Template.notification.events({
  'click .s-alert-close': function onClick(event) {
    event.preventDefault();
    event.stopPropagation();
    sAlert.close(this._id);
  },
});
