import './site-dropdown.html';

import { $ } from 'meteor/jquery';
import { CompanySiteProfiles } from '../../../api/company-site-profiles/company-site-profiles';
import { Meteor } from 'meteor/meteor';
import { Template } from 'meteor/templating';
import authorization from "../../../ui/helpers/authorization";

Template.siteDropdown.onRendered(function onRendered() {
  const template = this;
  template.$('.ui.dropdown').dropdown();
});

Template.siteDropdown.helpers({
  currentUserSites() {
    return CompanySiteProfiles.find({}, { sort: { displayName: 1 } });
  },
  selectedSite() {
    return Session.get('application.currentSite');
  },
});

Template.siteDropdown.events({
  'click .site-option': function onClick(event) {
    Session.set('application.currentSite', this);
  },
  selectedSite() {
    return Session.get('application.currentSite');
  },
});
