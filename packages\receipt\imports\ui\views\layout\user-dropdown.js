import './user-dropdown.html';

import { $ } from 'meteor/jquery';
import { Meteor } from 'meteor/meteor';
import { Template } from 'meteor/templating';
import authorization from "../../../ui/helpers/authorization";

Template.receiptUserDropdown.onRendered(function onRendered() {
  const template = this;
  template.$('.ui.dropdown').dropdown();
});

Template.receiptUserDropdown.helpers({
  userLoggedIn() {
    var user = Meteor.user();
    return user;
  },

  isReadOnly() {
    var user = Meteor.user();

    if (user) {
      var userProfile = user.profile;
      var userReadOnly =  userProfile.readOnly; 
      return userReadOnly;
    } else {
      return true;
    }
  },

  inAdminMode() {
    return authorization.inAdminMode();
  },

  isMobile() {
    return Meteor.isCordova;
  },
});
