<template name="marshallingYardInspection">
  <div class="ui card centered" style="width:80%">
    <div class="content">
      <div class="header" style="text-transform: uppercase;">
        Inspection - {{identifiers}}
      </div>
    </div>
    <div class="content">
      <form class="ui form">
        <div class="field">
            {{#if Template.subscriptionsReady}} 
              {{#each actionSteps}}
                {{> formActionStep currentWorkItemEvent=representativeWorkItemEvent action=action actionStep=this.actionStep actionStepLabel=this.actionStepLabel noDescription=this.noDescription noImages=this.noImages}}
              {{/each}}
            {{/if}}
        </div>
        <div class="field">
          <label class="ncr-select-label" for="ncrSelect" style="margin: auto;pointer-events:none;">Failure Reasons</label>
          <select id="ncrSelect" name="reasons" multiple="" class="ui fluid dropdown">
            <option value="">Reasons</option>
            {{#each inspectionFailureCategories}}
              <option value="{{this}}">{{this}}</option>
            {{/each}}
          </select>
        </div>  
        <div class="field" style="width:50%">
          <label for="notes" id="notesLabel">Notes</label>            
          <input type="text" id="notes" name="notes">
        </div>  
        <div class="field">
          <span style="float:right;">{{> formActionStep currentWorkItemEvent=representativeWorkItemEvent action=action actionStep=rectifyAtHoistAction.actionStep actionStepLabel=rectifyAtHoistAction.actionStepLabel noDescription=true noImages=true}}</span>
      </div>
      </form>
    </div>
    <span style="margin-bottom:1em">
        <div class="ui submit button {{themeBackClass}}" style="float:right" id="okButton" disabled="{{completeButtonsDisabled}}">OK</div>
        <div id="passAllButton" class="ui submit button" style="float:right;background-color:#45c24a" disabled="{{completeButtonsDisabled}}">PASS ALL</div>
        <div id="cancelButton" class="ui button" style="float:right;">CANCEL</div>
    </span>  
  </div>
  {{> viewImages currentWorkItemEvent=currentWorkItemEvent}}
</template>