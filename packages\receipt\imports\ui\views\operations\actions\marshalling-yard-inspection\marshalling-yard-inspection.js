import 'select2';
import './marshalling-yard-inspection.html';
import '../shared/form-action-step';
import '../shared/view-images';

import { $ } from 'meteor/jquery';
import { CompanyProfiles } from '../../../../../api/company-profiles/company-profiles';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Meteor } from 'meteor/meteor';
import { Template } from 'meteor/templating';
import { WorkItemActions } from '../../../../../shared/work-item-actions';
import { WorkItemEvents } from '../../../../../api/work-item-events/work-item-events';
import { currentSiteProfile } from '../../../../../ui/helpers/current-site-profile';

const completeInspection = (inspectionResults, templateInstance) => {
  const companyId = CompanyProfiles.findOne()._id;
  const siteId = currentSiteProfile()._id;
  const workItemEventLifecycleIds = FlowRouter.getQueryParam('lifecycleIds').split(',');
  const currentWorkItemEvents = WorkItemEvents
    .find({ lifecycleId: { $in: workItemEventLifecycleIds } })
    .fetch();

  Meteor.call(
    'inspectMultipleWorkItems',
    companyId,
    siteId,
    currentWorkItemEvents,
    WorkItemActions.MARSHALLING_YARD_INSPECTION,
    inspectionResults,
    () => {
      templateInstance.completeButtonsDisabled.set(true);
    },
  );
  FlowRouter.go('workItemOverview');
};

Template.marshallingYardInspection.onRendered(function onRendered() {
  const template = this;
  const inspectionForm = template.$('form');
  $('#ncrSelect').dropdown();
});

Template.marshallingYardInspection.onCreated(function onCreated() {
  const template = this;
  template.completeButtonsDisabled = new ReactiveVar(false);
});

Template.marshallingYardInspection.helpers({
  completeButtonsDisabled() {
    return Template.instance().completeButtonsDisabled.get();
  },
  currentWorkItemEvent() {
    const workItemEventLifecycleIdsParam = FlowRouter.getQueryParam('lifecycleIds');
    const currentWorkItemEvents = WorkItemEvents.find({
      lifecycleId: { $in: workItemEventLifecycleIdsParam.split(',') },
    }).fetch();
    return currentWorkItemEvents[0];
  },
  action() {
    return WorkItemActions.MARSHALLING_YARD_INSPECTION;
  },
  rectifyAtHoistAction() {
    return {
      actionStep: 'willBeRectifiedAtHoist',
      actionStepLabel:
        'Will be rectified at hoist?',
    };
  },
  actionSteps() {
    return [
      {
        actionStep: 'doesNotRequireInspection',
        actionStepLabel:
          'Does the inspection plate show at least 30 days full remaining before statutory examination is due?',
      },
      {
        actionStep: 'freeFromExcessiveCorrosionOrHoles',
        actionStepLabel: 'Are the units free from excessive corrosion or holes?',
      },
      {
        actionStep: 'allDrainageHolesAreClear',
        actionStepLabel: 'Are all drainage holes clear on open CCUs?',
      },
      {
        actionStep: 'liftingSetsProperlyFitted',
        actionStepLabel: 'Are all lifting sets properly fitted and configured, e.g. not twisted?',
      },
      {
        actionStep: 'slingsVisuallyInspected',
        actionStepLabel:
          'Have slings been visually inspected for damage & split pins on shackles checked to see they are correctly fitted?',
      },
      {
        actionStep: 'droppedObjectsRemoved',
        actionStepLabel:
          'Have you removed all potential dropped objects, e.g. Tools, debris on the lift or items strapped to lift?',
      },
      {
        actionStep: 'hasDestinationLabelBeenAdded',
        actionStepLabel: 'Has the destination label been added?',
      },
      {
        actionStep: 'itemsSecured',
        actionStepLabel:
          'Are items packed and secured to prevent movement/damage in “Worst Weather” conditions?',
      },
      {
        actionStep: 'dgLabelsArePresent',
        actionStepLabel:
          'Where Dangerous Goods are being shipped, have they been pre-notified and the container correctly labelled on all four sides (Refer IMDG code)? – Chemical Tanks must have a product label attached to the tank.',
      },
      {
        actionStep: 'retainingNetSecure',
        actionStepLabel:
          'Is the cargo retaining net secure and positioned to prevent goods falling out?',
      },
      {
        actionStep: 'snagHazardsPrevented',
        actionStepLabel:
          'Have adequate precautions been taken to prevent Snag Hazards? (e.g. Removal or covering of hazards)',
      },
      {
        actionStep: 'doorsSecured',
        actionStepLabel:
          'Have you checked that the doors and locking mechanisms are secure, with Secondary Securing Device attached e.g. tie-wraps?',
      },
      {
        actionStep: 'loadLiftsHorizontally',
        actionStepLabel:
          'Have you checked that the load lifts horizontally? (See  Oil & Gas Guidelines for parameters)',
      },
      {
        actionStep: 'weightBelowMaximumWeight',
        actionStepLabel:
          'Have you confirmed that the Actual Gross Weight is less than or equal to Maximum Gross Weight?',
      },
      {
        actionStep: 'heavyLiftTagHasBeenAdded',
        actionStepLabel:
          'If Actual Weight is seven (7) tonnes or above, has a Heavy Lift pennant been attached?',
      },
      {
        actionStep: 'tubularsSlungCorrectly',
        actionStepLabel:
          'Have all tubulars been slung & secured correctly and checked for potential dropped objects externally and internally?',
      },
    ];
  },
  representativeWorkItemEvent() {
    const workItemEventLifecycleIdsParam = FlowRouter.getQueryParam('lifecycleIds');
    if (workItemEventLifecycleIdsParam) {
      const workItemEventLifecycleIds = workItemEventLifecycleIdsParam.split(',');
      const currentWorkItemEvents = WorkItemEvents.find({
        lifecycleId: { $in: workItemEventLifecycleIds },
      }).fetch();
      return currentWorkItemEvents[0];
    }
    return [];
  },
  identifiers() {
    const workItemEventLifecycleIdsParam = FlowRouter.getQueryParam('lifecycleIds');
    if (workItemEventLifecycleIdsParam) {
      const workItemEventLifecycleIds = workItemEventLifecycleIdsParam.split(',');
      const currentWorkItemEvents = WorkItemEvents.find({
        lifecycleId: { $in: workItemEventLifecycleIds },
      }).fetch();
      return currentWorkItemEvents.map(x => x.identifier).join(',');
    }
    return '';
  },
  inspectionFailureCategories() {
    const siteProfile = currentSiteProfile();

    if (siteProfile && siteProfile.configuration.inspectionFailureReasons) {
      return Object.keys(siteProfile.configuration.inspectionFailureReasons);
    }

    return [];
  },
  inspectionFailureSubCategories(category) {
    const siteProfile = currentSiteProfile();

    if (siteProfile && siteProfile.configuration.inspectionFailureReasons) {
      return siteProfile.configuration.inspectionFailureReasons[category];
    }

    return [];
  },
});

Template.marshallingYardInspection.events({
  'click #okButton': function onClick(event, templateInstance) {
    const processButtonClick =
      !templateInstance.completeButtonsDisabled.get();

    if (processButtonClick) {
      templateInstance.completeButtonsDisabled.set(true);
      event.preventDefault();

      const inspection = {
        doesNotRequireInspection: $('#doesNotRequireInspection').prop('checked'),
        doesNotRequireInspectionDescription: $('#doesNotRequireInspectionDescription').val(),
        freeFromExcessiveCorrosionOrHoles: $('#freeFromExcessiveCorrosionOrHoles').prop('checked'),
        freeFromExcessiveCorrosionOrHolesDescription: $('#freeFromExcessiveCorrosionOrHolesDescription').val(),
        allDrainageHolesAreClear: $('#allDrainageHolesAreClear').prop('checked'),
        allDrainageHolesAreClearDescription: $('#allDrainageHolesAreClearDescription').val(),
        liftingSetsProperlyFitted: $('#liftingSetsProperlyFitted').prop('checked'),
        liftingSetsProperlyFittedDescription: $('#liftingSetsProperlyFittedDescription').val(),
        slingsVisuallyInspected: $('#slingsVisuallyInspected').prop('checked'),
        slingsVisuallyInspectedDescription: $('#slingsVisuallyInspectedDescription').val(),
        droppedObjectsRemoved: $('#droppedObjectsRemoved').prop('checked'),
        droppedObjectsRemovedDescription: $('#droppedObjectsRemovedDescription').val(),
        hasDestinationLabelBeenAdded: $('#hasDestinationLabelBeenAdded').prop('checked'),
        hasDestinationLabelBeenAddedDescription: $('#hasDestinationLabelBeenAddedDescription').val(),
        itemsSecured: $('#itemsSecured').prop('checked'),
        itemsSecuredDescription: $('#itemsSecuredDescription').val(),
        dgLabelsArePresent: $('#dgLabelsArePresent').prop('checked'),
        dgLabelsArePresentDescription: $('#dgLabelsArePresentDescription').val(),
        retainingNetSecure: $('#retainingNetSecure').prop('checked'),
        retainingNetSecureDescription: $('#retainingNetSecureDescription').val(),
        snagHazardsPrevented: $('#snagHazardsPrevented').prop('checked'),
        snagHazardsPreventedDescription: $('#snagHazardsPreventedDescription').val(),
        doorsSecured: $('#doorsSecured').prop('checked'),
        doorsSecuredDescription: $('#doorsSecuredDescription').val(),
        loadLiftsHorizontally: $('#loadLiftsHorizontally').prop('checked'),
        loadLiftsHorizontallyDescription: $('#loadLiftsHorizontallyDescription').val(),
        weightBelowMaximumWeight: $('#weightBelowMaximumWeight').prop('checked'),
        weightBelowMaximumWeightDescription: $('#weightBelowMaximumWeightDescription').val(),
        heavyLiftTagHasBeenAdded: $('#heavyLiftTagHasBeenAdded').prop('checked'),
        heavyLiftTagHasBeenAddedDescription: $('#heavyLiftTagHasBeenAddedDescription').val(),
        tubularsSlungCorrectly: $('#tubularsSlungCorrectly').prop('checked'),
        tubularsSlungCorrectlyDescription: $('#tubularsSlungCorrectlyDescription').val(),
        failureReasons: $('#ncrSelect').val(),
        notes: $('#notes').val(),
        willBeRectifiedAtHoist: $('#willBeRectifiedAtHoist').prop('checked'),
        passes: 0,
        fails: 0,
      };

      _.each(inspection, (value, prop) => {
        if (prop !== 'completed' && prop !== 'willBeRectifiedAtHoist') {
          if (inspection[prop] && inspection[prop].constructor.name === 'Array') {
            inspection.fails = inspection.fails + inspection[prop].length;
          } else if (inspection[prop] === true) {
            inspection.passes++;
          } else if (inspection[prop] === false) {
            inspection.fails++;
          }
        }
      });
      completeInspection(inspection, templateInstance);
    }
  },
  'click #cancelButton': function onClick(event) {
    event.preventDefault();
    FlowRouter.go('workItemOverview');
  },
  'click #passAllButton': function onClick(event, templateInstance) {
    const processButtonClick =
      !templateInstance.completeButtonsDisabled.get();

    if (processButtonClick) {
      templateInstance.completeButtonsDisabled.set(true);
      const inspection = {
        doesNotRequireInspection: true,
        doesNotRequireInspectionDescription: '',
        freeFromExcessiveCorrosionOrHoles: true,
        freeFromExcessiveCorrosionOrHolesDescription: '',
        allDrainageHolesAreClear: true,
        allDrainageHolesAreClearDescription: '',
        liftingSetsProperlyFitted: true,
        liftingSetsProperlyFittedDescription: '',
        slingsVisuallyInspected: true,
        slingsVisuallyInspectedDescription: '',
        droppedObjectsRemoved: true,
        droppedObjectsRemovedDescription: '',
        hasDestinationLabelBeenAdded: true,
        hasDestinationLabelBeenAddedDescription: '',
        itemsSecured: true,
        itemsSecuredDescription: '',
        dgLabelsArePresent: true,
        dgLabelsArePresentDescription: '',
        retainingNetSecure: true,
        retainingNetSecureDescription: '',
        snagHazardsPrevented: true,
        snagHazardsPreventedDescription: '',
        doorsSecured: true,
        doorsSecuredDescription: '',
        loadLiftsHorizontally: true,
        loadLiftsHorizontallyDescription: '',
        weightBelowMaximumWeight: true,
        weightBelowMaximumWeightDescription: '',
        heavyLiftTagHasBeenAdded: true,
        heavyLiftTagHasBeenAddedDescription: '',
        tubularsSlungCorrectly: true,
        tubularsSlungCorrectlyDescription: '',
        failureReasons: $('#ncrSelect').val(),
        notes: $('#notes').val(),
        passes: 16,
        fails: 0,
        willBeRectifiedAtHoist: false,
      };
      completeInspection(inspection, templateInstance);
    }
  },
});
