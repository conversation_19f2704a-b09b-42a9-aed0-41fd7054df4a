<template name="planAndReceiveWorkItem">
  <form>
  	<div class="row" style="margin-bottom:0px;">

  		<div class="card form-card col s12 m12 l10 offset-l1">
    	
        <div class="card-title {{themeForeClass}}">
      		RECEIVE
      	</div>

        <div class="row" style="margin-bottom:0px;">

          <div class="row">
              <div class="input-field col s12 m12 l12">
                <label for="identifierInput" id="identifierInputLabel">Container</label>
                <input type="text" id="identifierInput" name="identifierInput" class="typeahead" required autocomplete="off" spellcheck="off"
           data-source="vorResults" data-highlight="true" data-autoselect="true" data-limit="10" data-options='{{typeaheadOptions}}'>

            </div>
          </div>

          <div class="row">
            <div class="input-field col s6">
              <select id="type" name="type">

                <option value="" disabled selected>Select Type</option>
                
                {{#each types}}
                  <option value="{{_id}}">{{name}}</option>
                {{/each}}
                
              </select>
              <label>Type</label>
            </div>
           </div>

          <div class="row">
            <div class="input-field col s6">
              <select id="client" name="client">

                <option value="" disabled selected>Select Company</option>
                
                {{#each clients}}
                  <option value="{{_id}}">{{name}}</option>
                {{/each}}
                
              </select>
              <label>Company</label>
            </div>
           </div>

          <div class="row">
            <div class="input-field col s6">
              <input id="clientLocation" name="clientLocation" type="text" class="typeahead" autocomplete="off" spellcheck="off"
           data-source="locations" data-highlight="true" data-options='{{typeaheadOptions}}'>
              <label for="clientLocation" id="locationLabel">Location</label>
            </div>
            
          </div>
          
          
          {{#each contentLines}} 
            {{#if this.isLast}} 
            <div class="row">
                {{> contentLineInput id=this.id fieldValue=this.fieldValue isFirst=this.isFirst}}   
            
                <div class="input-field col s6">
                  <button id="addContentLine" class="btn-floating btn-small waves-effect waves-light white" style="margin-top: 20px;"><i class="material-icons {{themeForeClass}} ">add</i></button>
                
                  {{#unless this.isFirst}}
                  <button id="removeContentLine" class="btn-floating btn-small waves-effect waves-light white" style="margin-top: 20px;"><i class="material-icons {{themeForeClass}} ">remove</i></button>
                  {{/unless}}
                </div>
              </div>
            
            {{else}}
            <div class="row">
                {{> contentLineInput id=this.id fieldValue=this.fieldValue isFirst=this.isFirst}}   
          
              </div>
            {{/if}}

          {{/each}}
          
               
          <div class="row">
            <div class="input-field col s6">
              <input id="truckNoPlate" name="truckNoPlate" type="text" class="" >
              <label for="truckNoPlate" id="truckLabel">Truck No. Plate</label>
            </div>
          </div>

          {{#if weighBridgeInUse}}
          <div class="row">
            <div class="input-field col s3">
              <input id="tareWeight" name="tareWeight" type="text" class="">
              <label for="tareWeight">Tare Weight (KG)</label>
            </div>
            <div class="input-field col s3">
              <input id="grossWeight" name="grossWeight" type="text" class="">
              <label for="grossWeight">Gross Weight (KG)</label>
            </div>
            <div class="input-field col s3">
              <input disabled id="netWeight" name="netWeight" type="text" class="" value="{{netValue}}" style="border-bottom: 1px solid #7B7B7B; color: inherit;">
              <label for="netWeight" id="netLabel" style="color:#7B7B7B;">Net Weight (KG)</label>
            </div>
          </div>
          {{/if}}

        </div>

        <div class="row">
          <div class="card-action">
            <span class="right">
              <a href="{{pathFor 'workItemOverview'}}" class="waves-effect waves-light btn white black-text" style="margin-right:0px;">CANCEL</a>
              <button type="submit" class="waves-effect waves-light btn {{themeBackClass}} white-text disabled" disabled>RECEIVE</button>
              <button  id="receiveAndNext" type="submit" class="waves-effect waves-light btn {{themeBackClass}} white-text disabled" disabled>RECEIVE & NEXT <i class="material-icons right">navigate_next</i></button>
            </span>
          </div>
        </div>
      
      </div>

  	</div>

  </form>
</template>
