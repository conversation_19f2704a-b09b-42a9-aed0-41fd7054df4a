import './plan-and-receive-work-item.html';
import '../shared/content-line-input';

import { $ } from 'meteor/jquery';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Meteor } from 'meteor/meteor';
import { Template } from 'meteor/templating';
import { CompanyProfiles } from '../../../../../api/company-profiles/company-profiles';
import { WorkItemEventStates } from '../../../../../shared/work-item-event-states';
import { currentSiteProfile } from '../../../../../ui/helpers/current-site-profile';

function updateWhetherAllowedToSubmit(template) {
  if (template.$('#identifierInput').val() && template.$('#client').val()) {
    template.$('input[type="submit"], button[type="submit"]').prop('disabled', false);

    template.$('input[type="submit"], button[type="submit"]').removeClass('disabled');
  } else {
    template.$('input[type="submit"], button[type="submit"]').prop('disabled', true);
    template.$('input[type="submit"], button[type="submit"]').addClass('disabled');
  }
}

Template.planAndReceiveWorkItem.onCreated(function onCreated() {
  const template = this;

  template.netValue = new ReactiveVar;
  template.netValue.set('');

  template.numberOfContentLines = new ReactiveVar;
  template.numberOfContentLines.set(1);

  template.contentLines = new ReactiveVar;
  template.contentLines.set([{
    id: 1,
    isFirst: true,
    isLast: true,
    fieldValue: '',
  }]);
});

Template.planAndReceiveWorkItem.onRendered(function onRendered() {
  const template = this;

  template.$('select').material_select();

  Meteor.typeahead.inject();

  template.autorun(() => {
    const companySiteProfile = currentSiteProfile();

    if (companySiteProfile && companySiteProfile.configuration.clients.length === 1) {
      template.$('#client').val(companySiteProfile.configuration.clients[0]._id);
      template.$('select').material_select();
    }
  });
});

Template.planAndReceiveWorkItem.helpers({
  vorResults(query, sync, callback) {
    Meteor.call('searchWorkItems', query, {}, (err, res) => {
      if (err) {
        console.log(err);
        return;
      }
      callback(res.map(function (v) { return { value: v.Name }; }));
    });
  },
  clients() {
    const siteProfile = currentSiteProfile();

    if (siteProfile) {
      const siteClients = siteProfile.configuration.clients;
      return _.sortBy(siteClients, (client) => client.name);
    }
    return [];
  },
  types() {
    const siteProfile = currentSiteProfile();

    if (siteProfile) {
      const siteWorkItemTypes = siteProfile.configuration.workItemTypes;
      return _.sortBy(siteWorkItemTypes, (type) => type.name);
    }
    return [];
  },
  locations() {
    const siteProfile = currentSiteProfile();

    if (siteProfile) {
      return siteProfile
        .configuration
        .locations
        .map((location) => location.name);
    }
    return [];
  },
  netValue() {
    const netValue = Template.instance().netValue.get();
    return netValue;
  },
  contentLines() {
    const contentLines = Template.instance().contentLines.get();
    return contentLines;
  },
  weighBridgeInUse() {
    const siteProfile = currentSiteProfile();
    return siteProfile && siteProfile.identifier === 'augean-tullos';
  },
});

Template.planAndReceiveWorkItem.events({
  'click button[type=submit]': function onClick(event, templateInstance) {
    event.preventDefault();

    const form = $(event.target).parents('form:first');
    const selectedClient = form.find('#client option:selected');
    const selectedType = form.find('#type option:selected');
    const contentLineVals = [];

    $('.content-line').each(function () {
      const contentLineVal = $(this).val();

      if (contentLineVal && contentLineVal.length > 0) {
        contentLineVals.push(contentLineVal);
      }
    });

    const planned = {
      plannedDateTime: null,
      client: {
        _id: selectedClient.val(),
        name: selectedClient.text(),
      },
      contents: contentLineVals,
      clientLocation: form.find('#clientLocation').val(),
    };

    if (selectedType.val()) {
      planned.workItemType = {
        _id: selectedType.val(),
        name: selectedType.text(),
      };
    }

    const workItemEvent = {
      identifier: form.find('#identifierInput').val(),
    };

    const received = {
      truckNoPlate: form.find('[name=truckNoPlate]').val(),
      tareWeight: form.find('[name=tareWeight]').val(),
      grossWeight: form.find('[name=grossWeight]').val(),
      netWeight: form.find('[name=netWeight]').val(),
    };

    const companyId = CompanyProfiles.findOne()._id;
    const siteId = currentSiteProfile()._id;

    Meteor.call('createWorkItemEvent', companyId, siteId, workItemEvent, WorkItemEventStates.PLANNED, planned, function (error, createdEvent) {
      if (!error) {
        Meteor.call('createWorkItemEvent', companyId, siteId, createdEvent, WorkItemEventStates.RECEIVED, received, (error, id) => { });
      }
    });

    if ($(event.target).prop('id') === 'receiveAndNext') {
      form.find('#identifierInput').val('');
      form.find('#contents').val('');
      form.find('[name=tareWeight]').val('');
      form.find('[name=grossWeight]').val('');
      form.find('[name=netWeight]').val('');

      updateWhetherAllowedToSubmit(templateInstance);
    } else {
      FlowRouter.go('workItemOverview');
    }
  },
  'click #addContentLine': function onClick(e, templateInstance) {
    e.preventDefault();

    const contentLines = templateInstance.contentLines.get();
    const currentNumberOfContentLines = templateInstance.numberOfContentLines.get();
    const newContentLineNumber = currentNumberOfContentLines + 1;

    const mappedContentLines = contentLines.map((contentLine, index) => {
      return {
        id: contentLine.id,
        isFirst: index == 0,
        isLast: false,
        fieldValue: $('#contents' + contentLine.id).val()
      };
    });

    mappedContentLines.push({
      id: newContentLineNumber,
      fieldValue: '',
      isLast: true,
      isFirst: false,
    });

    templateInstance.numberOfContentLines.set(newContentLineNumber);
    templateInstance.contentLines.set(mappedContentLines);
  },
  'click #removeContentLine': function onClick(e, templateInstance) {
    e.preventDefault();

    const contentLines = templateInstance.contentLines.get();
    const currentNumberOfContentLines = templateInstance.numberOfContentLines.get();
    const newContentLineNumber = currentNumberOfContentLines - 1;

    const mappedContentLines = contentLines.map((contentLine, index) => {
      return {
        id: contentLine.id,
        isFirst: (index == 0),
        isLast: (index == contentLines.length - 2),
        fieldValue: $('#contents' + contentLine.id).val(),
      };
    });
    mappedContentLines.splice(-1, 1);

    templateInstance.numberOfContentLines.set(newContentLineNumber);
    templateInstance.contentLines.set(mappedContentLines);
  },
  'keyup #identifierInput, change #client': function onKeyup(e, templateInstance) {
    updateWhetherAllowedToSubmit(templateInstance);
  },
  'keyup #grossWeight, keyup #tareWeight': function onKeyup(e, templateInstance) {
    const tareWeight = templateInstance.$('#tareWeight').val();
    const grossWeight = templateInstance.$('#grossWeight').val();

    if (!isNaN(tareWeight) && !isNaN(grossWeight)) {
      templateInstance.netValue.set(grossWeight - tareWeight);
      templateInstance.$('#netLabel').addClass('active');
    }
  },
  'focus #clientLocation, focus #identifierInput': function onFocus(e) {
    $(e.target)
      .parents('.input-field')
      .find('label')
      .addClass('active');
  },

  'blur #clientLocation, blur #identifierInput': function onBlur(e) {
    const inputVal = $(e.target).val();
    const inputLabel = $(e.target)
      .parents('.input-field')
      .find('label');

    if (inputVal) {
      inputLabel.addClass('active');
    } else {
      inputLabel.removeClass('active');
    }
  },
  'keydown #grossWeight, keydown #tareWeight': function onKeydown(e) {
    // Allow: backspace, delete, tab, escape, enter and .
    if ($.inArray(e.keyCode, [46, 8, 9, 27, 13, 110, 190]) !== -1 ||
      // Allow: Ctrl+A
      (e.keyCode == 65 && e.ctrlKey === true) ||
      // Allow: Ctrl+C
      (e.keyCode == 67 && e.ctrlKey === true) ||
      // Allow: Ctrl+X
      (e.keyCode == 88 && e.ctrlKey === true) ||
      // Allow: home, end, left, right
      (e.keyCode >= 35 && e.keyCode <= 39)) {
      // let it happen, don't do anything
      return;
    }
    // Ensure that it is a number and stop the keypress
    if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57)) && (e.keyCode < 96 || e.keyCode > 105)) {
      e.preventDefault();
    }
  },
});
