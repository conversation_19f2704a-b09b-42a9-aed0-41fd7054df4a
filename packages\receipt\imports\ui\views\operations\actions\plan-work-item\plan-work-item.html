<template name="planWorkItem">
  <div class="ui card centered" style="width:80%; margin-top: 20px;">
    <div class="content">
      <div class="header">
        PLAN
      </div>
    </div>
    
    <div class="content">
      <form class="ui form">
          <div class="field">
              <label for="identifierInput" id="identifierInputLabel">Container</label>
              <input type="text" id="identifierInput" name="identifierInput" class="typeahead" required autocomplete="off" spellcheck="off"
              data-source="vorResults" data-highlight="true" data-autoselect="true" data-limit="10" data-options='{{typeaheadOptions}}' placeholder="Container">        
          </div>              
          <div class="field" style="width:50%">
              <label for="description">Container Description</label>
              <input id="description" name="description" type="text" class="" placeholder="Container Description">              
          </div>     
          <div class="field" style="width:50%">
              <label for="vendorName">Vendor Name</label>
              <input id="vendorName" name="vendorName" type="text" class="" placeholder="Vendor Name">              
          </div>            
          <div class="field" style="width:50%">
              <label for="clientLocation" id="locationLabel">Asset</label>
              <input id="clientLocation" name="clientLocation" type="text" class="typeahead" autocomplete="off" spellcheck="off"
              data-source="locations" data-highlight="true" data-options='{{typeaheadOptions}}' placeholder="Asset">              
          </div>
          
          <div class="grouped fields">
            <div class="field">
              <div class="ui radio checkbox">
                <input name="direction" type="radio" id="inboundRadio" checked/>
                <label for="inboundRadio">Inbound</label>
              </div>
            </div>
            <div class="field">
              <div class="ui radio checkbox">
                <input name="direction" type="radio" id="outboundRadio" />
                <label for="outboundRadio">Outbound</label>
              </div>
            </div>
          </div>

          <div class="field" style="width:50%">
            <label for="datetimePick" class="active">Exp Delivery Date</label>
            <div class="ui calendar" id="datetimePick">
                <div class="ui input left icon">
                    <i class="calendar icon"></i>
                    <input id="datetimePick" name="datetimePick" type="text" placeholder="Date">
                </div>
            </div>
          </div>

          <span class="right">
              <a href="{{pathFor 'workItemOverview'}}" class="ui button" style="margin-right:0px;">CANCEL</a>
              <div class="ui submit button {{themeBackClass}} plan disabled" disabled>PLAN</div>
              <div class="ui submit button {{themeBackClass}} plan disabled" id="planAndNext" disabled>PLAN &amp; NEXT</div>
          </span>     
      </form>
    </div>
  </div>
</template>
