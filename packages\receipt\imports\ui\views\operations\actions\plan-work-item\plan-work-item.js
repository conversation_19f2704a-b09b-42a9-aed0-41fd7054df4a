import './plan-work-item.html';
import '../shared/content-line-input';
import { $ } from 'meteor/jquery';
import { CompanyProfiles } from '../../../../../api/company-profiles/company-profiles';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Meteor } from 'meteor/meteor';
import { Session } from 'meteor/session';
import { Template } from 'meteor/templating';
import { WorkItemEventStates } from '../../../../../shared/work-item-event-states';
import { currentSiteProfile } from '../../../../../ui/helpers/current-site-profile';
import { moment } from 'meteor/momentjs:moment';

// Setup Datepicker.
// https://github.com/mdehoog/Semantic-UI/blob/49178d6d0321357b959ff56f424ea1b3ed76a6ed/src/definitions/modules/calendar.js#L902-L1279
const calendarSettingsFormatter = {
  date: (date, settings) => {
    if (_.isUndefined(date)) return '';
    const day = (`0${date.getDate()}`).slice(-2); // zero pad.
    const month = settings.text.monthsShort[date.getMonth()];
    const year = date.getFullYear();
    return day + '-' + month + '-' + year;
  },
  time: (date, settings) => {
    if (_.isUndefined(date)) return '';
    const hours = (`0${date.getHours()}`).slice(-2); // zero pad.
    const minutes = (`0${date.getMinutes()}`).slice(-2); // zero pad.
    return hours + ':' + minutes;
  },
};

function updateWhetherAllowedToSubmit(template) {
  if (template.$('#identifierInput').val()) {
    template.$('.ui.submit.button').prop('disabled', false);
    template.$('.ui.submit.button').removeClass('disabled');
  } else {
    template.$('.ui.submit.button').prop('disabled', true);
    template.$('.ui.submit.button').addClass('disabled');
  }
}

Template.planWorkItem.onRendered(function onRendered() {
  const template = this;

  Meteor.typeahead.inject();

  const datePicker = $('#datetimePick');
  // Show modal and initialise its contents
  datePicker.calendar({
    type: 'datetime',
    maxDate: moment().utc().toDate(),
    ampm: false,
    formatter: calendarSettingsFormatter,
  });

  Meteor.defer(() => {
    template.$('#datetimePick').calendar('set date', moment().utc().toDate());
  });

  template.autorun(() => {
    const companySiteProfile = currentSiteProfile();

    if (companySiteProfile && companySiteProfile.configuration.clients.length === 1) {
      template.$('#client').val(companySiteProfile.configuration.clients[0]._id);
    }
  });
});

Template.planWorkItem.onCreated(function onCreated() {
  const template = this;
  template.planButtonDisabled = new ReactiveVar(false);
  template.numberOfContentLines = new ReactiveVar;
  template.numberOfContentLines.set(1);

  template.contentLines = new ReactiveVar;
  template.contentLines.set([{
    id: 1,
    isFirst: true,
    isLast: true,
    fieldValue: '',
  }]);
});

Template.planWorkItem.helpers({
  vorResults(query, sync, callback) {
    Meteor.call('searchWorkItems', query, {}, (err, res) => {
      if (err) {
        console.log(err);
        return;
      }
      callback(res.map((v) => ({ value: v.Name })));
    });
  },
  clients() {
    const siteProfile = currentSiteProfile();
    if (siteProfile) {
      const siteClients = siteProfile.configuration.clients;
      return _.sortBy(siteClients, (client) => client.name);
    }
    return [];
  },
  types() {
    const siteProfile = currentSiteProfile();

    if (siteProfile) {
      const siteWorkItemTypes = siteProfile.configuration.workItemTypes;
      return _.sortBy(siteWorkItemTypes, type => type.name);
    }
    return [];
  },
  wasteDescriptions() {
    const siteProfile = currentSiteProfile();

    if (siteProfile) {
      return siteProfile.configuration.wasteDescriptions.map((description) => description.name);
    }
    return [];
  },
  locations() {
    const siteProfile = currentSiteProfile();

    if (siteProfile) {
      return siteProfile.configuration.locations.map((location) => location.name);
    }
    return [];
  },
  contentLines() {
    const contentLines = Template.instance().contentLines.get();
    return contentLines;
  },
  logisticsProvider() {
    return [
      { _id: 'peterson', name: 'Peterson' },
      { _id: 'asco', name: 'Asco' },
      { _id: 'augean', name: 'Augean' },
    ];
  },
});

Template.planWorkItem.events({
  'click .ui.submit.button.plan': function handleClick(event, templateInstance) {
    const processButtonClick =
      !templateInstance.planButtonDisabled.get();

    if (processButtonClick) {
      templateInstance.planButtonDisabled.set(true);

      event.preventDefault();

      const form = $(event.target).parents('form:first');

      const plannedDate = form.find('#datetimePick').calendar('get date');

      const utcPlannedDateTime = moment(plannedDate).utc().toDate();

      const selectedType = form.find('#type option:selected');

      const contentLineVals = [];

      $('.content-line-input').each(function () {
        const contentLineVal = $(this).val();

        if (contentLineVal && contentLineVal.length > 0) {
          contentLineVals.push(contentLineVal);
        }
      });

      let selectedClient;
      const siteProfile = currentSiteProfile();
      if (siteProfile && siteProfile.configuration.clients.length === 1) {
        selectedClient = siteProfile.configuration.clients[0];
      } else {
        const selectedClientInForm = form.find('#client option:selected');

        selectedClient = {
          _id: selectedClientInForm.val(),
          name: selectedClientInForm.text(),
        };
      }

      let direction = 'Inbound';

      if (form.find('#outboundRadio').prop('checked')) {
        direction = 'Outbound';
      }

      const planned = {
        plannedDateTime: utcPlannedDateTime,
        client: selectedClient,
        contents: contentLineVals,
        clientLocation: form.find('#clientLocation').val(),
        responsibleForCollection: form.find('#logisticsProvider').val(),
        isHighPriority: form.find('#is-high-priority').prop('checked'),
        description: form.find('#description').val(),
        voyageNo: form.find('#voyageNo').val(),
        weight: form.find('#weight').val(),
        vendorName: form.find('#vendorName').val(),
        direction,
      };

      if (selectedType.val()) {
        planned.workItemType = {
          _id: selectedType.val(),
          name: selectedType.text(),
        };
      }
      console.log(planned);
      const workItemEvent = {
        identifier: form.find('#identifierInput').val(),
      };

      const companyId = CompanyProfiles.findOne()._id;
      const siteId = currentSiteProfile()._id;

      Meteor.call(
        'createWorkItemEvent',
        companyId,
        siteId,
        workItemEvent,
        WorkItemEventStates.PLANNED,
        planned,
        () => {
          templateInstance.planButtonDisabled.set(true);
        },
      );

      if ($(event.target).prop('id') === 'planAndNext') {
        form.find('#identifierInput').val('');
        form.find('input[id^=contents]').val('');
        templateInstance.contentLines.set([{
          id: 1,
          isFirst: true,
          isLast: true,
          fieldValue: '',
        }]);
        templateInstance.numberOfContentLines.set(1);

        updateWhetherAllowedToSubmit(templateInstance);
      } else {
        Session.set('showPlannedWorkItems', true);
        FlowRouter.go('workItemOverview');
      }
    }
  },

  'keyup #identifierInput, change #client': function handleKeyup(event, templateInstance) {
    updateWhetherAllowedToSubmit(templateInstance);
  },
  'focus #contents, focus #clientLocation, focus #identifierInput': function handleFocus(event) {
    $(event.target)
      .parents('.input-field')
      .find('label')
      .addClass('active');
  },
  'blur #contents, blur #clientLocation, blur #identifierInput': function handleBlur(event) {
    const inputVal = $(event.target).val();
    const inputLabel = $(event.target)
      .parents('.input-field')
      .find('label');

    if (inputVal) {
      inputLabel.addClass('active');
    } else {
      inputLabel.removeClass('active');
    }
  },
  'click #addContentLine': function handleClick(event, templateInstance) {
    event.preventDefault();

    const contentLines = templateInstance.contentLines.get();
    const currentNumberOfContentLines = templateInstance.numberOfContentLines.get();
    const newContentLineNumber = currentNumberOfContentLines + 1;

    const mappedContentLines = contentLines.map((contentLine, index) => ({
      id: contentLine.id,
      isFirst: index == 0,
      isLast: false,
      fieldValue: $(`#contents${contentLine.id}`).val(),
    }));

    mappedContentLines.push({
      id: newContentLineNumber,
      fieldValue: '',
      isLast: true,
      isFirst: false,
    });

    templateInstance.numberOfContentLines.set(newContentLineNumber);
    templateInstance.contentLines.set(mappedContentLines);
  },
  'click #removeContentLine': function handleClick(event, templateInstance) {
    event.preventDefault();

    const contentLines = templateInstance.contentLines.get();
    const currentNumberOfContentLines = templateInstance.numberOfContentLines.get();
    const newContentLineNumber = currentNumberOfContentLines - 1;

    const mappedContentLines = contentLines.map((contentLine, index) => ({
      id: contentLine.id,
      isFirst: (index == 0),
      isLast: (index == contentLines.length - 2),
      fieldValue: $(`#contents${contentLine.id}`).val(),
    }));

    mappedContentLines.splice(-1, 1);

    templateInstance.numberOfContentLines.set(newContentLineNumber);
    templateInstance.contentLines.set(mappedContentLines);
  },
});
