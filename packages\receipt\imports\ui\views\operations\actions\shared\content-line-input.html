<template name="contentLineInput">
	
		<div class="input-field col s6">
		 <input id="contents{{this.id}}" name="contents{{this.id}}" type="text" class="content-line-input" autocomplete="off" spellcheck="off"
	             data-source="wasteDescriptions" data-highlight="true" data-options='{{typeaheadOptions}}'>
	   <label for="contents{{this.id}}" id="contentsLabel{{this.id}}">Contents {{#unless this.isFirst}}({{this.id}}){{/unless}}</label>
	  </div>
	
</template>