import './content-line-input.html';

import { $ } from 'meteor/jquery';
import { Meteor } from 'meteor/meteor';
import { Template } from 'meteor/templating';
import { currentSiteProfile } from '../../../../../ui/helpers/current-site-profile';

Template.contentLineInput.onRendered(function onRendered() {
  const template = this;
  const templateData = template.data;
  template.$('input').val(templateData.fieldValue);

  Meteor.typeahead.inject('.content-line-input');

  const inputLabel = template.$('input')
    .parents('.input-field')
    .find('label');

  if (templateData.fieldValue) {
    inputLabel.addClass('active');
  } else {
    inputLabel.removeClass('active');
  }
});

Template.contentLineInput.helpers({
  wasteDescriptions() {
    const siteProfile = currentSiteProfile();

    if (siteProfile) {
      return siteProfile.configuration
        .wasteDescriptions
        .map((description) => description.name);
    }
    return [];
  },
});

Template.contentLineInput.events({
  'focus input.content-line-input': function onFocus(e) {
    $(e.target)
      .parents('.input-field')
      .find('label')
      .addClass('active');
  },
  'blur input.content-line-input': function onBlur(e) {
    const inputVal = $(e.target).val();
    const inputLabel = $(e.target)
      .parents('.input-field')
      .find('label');

    if (inputVal) {
      inputLabel.addClass('active');
    } else {
      inputLabel.removeClass('active');
    }
  },
});
