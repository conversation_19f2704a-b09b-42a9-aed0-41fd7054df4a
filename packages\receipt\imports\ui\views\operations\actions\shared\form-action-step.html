<template name="formActionStep">
  <div class="ui grid">
    <div class="sixteen wide column">
      <p>
        <div class="fields">
          <div class="twelve wide field">
            <div class="ui checkbox">
              <input type="checkbox" id="{{actionStep}}" class="filled-in">
              <label for="{{actionStep}}">{{actionStepLabel}}</label> 
            </div>
          </div>
          
          {{#unless noDescriptionAndNoImages}}
          <div class="four wide field">
            {{> imagesControl currentWorkItemEvent=currentWorkItemEvent action=action imagesCollection=imagesCollectionName}}
            <div style="display:inline">
              <i id="{{actionStep}}DescriptionToggle" class="right small material-icons white {{themeForeClass}} add-images-icon description-toggle" style="float:inline; cursor: pointer;">insert_comment</i>
            </div>
          </div>
          {{/unless}}
        </div>
        {{#unless noDescription}}
        <div class="field">
          <div id="{{actionStep}}DescriptionContainer" class="row description-container" style="padding-left:35px; display:none;">      
              <input id="{{actionStep}}Description" name="{{actionStep}}Description" type="text" placeholder="Description">
          </div>
        </div>
      {{/unless}}
      </p>
    </div>
  </div>
</template>