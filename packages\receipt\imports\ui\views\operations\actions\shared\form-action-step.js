import { Meteor } from 'meteor/meteor';
import { Template } from 'meteor/templating';
import { $ } from 'meteor/jquery';

import './form-action-step.html';

import './images-control';

Template.formActionStep.onRendered(function onRendered() {
  const template = this;
  const templateData = template.data;

  let currentWorkItemEvent;
  if (templateData.currentWorkItemEvent.length && templateData.currentWorkItemEvent.length > 1) {
    currentWorkItemEvent = templateData.currentWorkItemEvent[0];
  } else {
    currentWorkItemEvent = templateData.currentWorkItemEvent;
  }
  $('.ui.checkbox')
    .checkbox();
  if (currentWorkItemEvent.lifecycleData[templateData.action]) {
    const actionStepValue = templateData.currentWorkItemEvent.lifecycleData[templateData.action][templateData.actionStep];
    const actionStepDescription = templateData.currentWorkItemEvent.lifecycleData[templateData.action][templateData.actionStep + 'Description'];

    template.$('[name=' + templateData.actionStep + ']').prop('checked', actionStepValue);

    if (!template.data.noDescription) {
      template.$('[name=' + templateData.actionStep + 'Description' + ']').val(actionStepDescription);

      if (actionStepDescription) {
        template.$('#' + templateData.actionStep + 'DescriptionContainer').show(400);
      }
    }
  }
});

Template.formActionStep.helpers({
  isActive() {
    let currentWorkItemEvent;
    if (this.currentWorkItemEvent.length && this.currentWorkItemEvent.length > 1) {
      currentWorkItemEvent = this.currentWorkItemEvent[0];
    } else {
      currentWorkItemEvent = this.currentWorkItemEvent;
    }

    if (currentWorkItemEvent
      && currentWorkItemEvent.lifecycleData
      && currentWorkItemEvent.lifecycleData[this.action]
      && currentWorkItemEvent.lifecycleData[this.action][this.actionStep + 'Description']) {
      return 'active';
    }
    return '';
  },

  imagesCollectionName() {
    return this.actionStep + 'Images';
  },

  noDescriptionAndNoImages() {
    return this.noImages && this.noDescription;
  }
});

Template.formActionStep.events({
  'click .description-toggle': function onClick(event, templateInstance) {
    event.preventDefault();

    if (templateInstance.$('.description-container').is(':visible')) {
      templateInstance.$('.description-container').hide(400);
    } else {
      templateInstance.$('.description-container').show(400);
    }
  },
});
