<template name="imagesControl">

	{{#if uploadingImage }} 
		
		<div class="preloader-wrapper active" style="width:24px; height:24px;">
	    <div class="spinner-layer spinner-blue-only" style="border-color: #1a2b59;">
				<div class="ui mini active inline loader" style="margin-right:10px; display:inline;position: relative;bottom:5px"></div>
					
	    </div>
	  </div>

	{{else}} 

		<div style="display:inline">
			<label id="launchViewImagesModal" style="cursor: {{getImageLibraryCursor numberOfImages}};position: relative;bottom:5px">{{numberOfImages}} images </label>
		</div>
		
	{{/if}}
	<div style="display:inline">
		<i class="small material-icons right white {{themeForeClass}} add-images-icon" style="cursor: pointer;">camera_alt</i>
	</div>
	<input type="file" id="fileupload" style="display:none" multiple>
</template>