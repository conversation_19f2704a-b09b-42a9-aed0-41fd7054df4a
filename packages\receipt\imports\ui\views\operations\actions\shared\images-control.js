import { Meteor } from 'meteor/meteor';
import { Template } from 'meteor/templating';
import { $ } from 'meteor/jquery';
import './images-control.html';

function updateWorkItemEvent(
  template,
  currentWorkItemEvent,
  fileIdentifier,
  action,
  imagesCollection) {
  Meteor.call('updateWorkItemEventActionImages', 
    currentWorkItemEvent._id,
    action,
    imagesCollection,
    fileIdentifier,
    () => {
      template.uploadingImage.set(false);
    });
}

function uploadPhoto(dataUri, template, currentWorkItemEvent, action, imagesCollection) {
  template.uploadingImage.set(true);

  Meteor.call('uploadImageFileAsync', dataUri, currentWorkItemEvent, (err, success) => {
    if (err) {
      throw err;
    } else {
      console.log(success);
    }
    updateWorkItemEvent(template, currentWorkItemEvent, success, action, imagesCollection);
  });
}

Template.imagesControl.onCreated(function onCreated() {
  const template = this;
  template.numberOfImages = new ReactiveVar;
  template.numberOfImages.set(0);

  template.uploadingImage = new ReactiveVar;
  template.uploadingImage.set(false);
});

Template.imagesControl.events({
  'click .add-images-icon': function onClick(event, templateInstance) {
    const currentWorkItemEvent = this.currentWorkItemEvent;
    const action = this.action;
    const imagesCollection = this.imagesCollection;
    if (Meteor.isCordova) {
      MeteorCameraUI.getPicture({}, (error, dataUri) => {
        if (!error) {
          uploadPhoto(dataUri, templateInstance, currentWorkItemEvent, action, imagesCollection);
        } else {
          console.log(error);
        }
      });
    } else {
      templateInstance.$('#fileupload').click(); // trigger file upload
    }
  },
  'change #fileupload': function onChange(event, templateInstance) {
    const currentWorkItemEvent = this.currentWorkItemEvent;
    const action = this.action;
    const imagesCollection = this.imagesCollection;
    const files = $(event.target)[0].files;
    _.each(files, (file) => {
      const reader = new FileReader();

      reader.onloadend = function onLoadEnd(e) {
        const dataUri = e.target.result;
        uploadPhoto(dataUri, templateInstance, currentWorkItemEvent, action, imagesCollection);
      };
      reader.readAsDataURL(file);
    });
  },
  'click #launchViewImagesModal': function onClick(event, templateInstance) {
    if (templateInstance.numberOfImages.get() > 0) {
      Session.set('ActiveImagesLibrary', {
        imagesCollection: this.imagesCollection,
        action: this.action,
      });
      $('#viewImages').modal('show');
    }
  },
});

Template.imagesControl.helpers({
  numberOfImages() {
    const currentWorkItemEvent = this.currentWorkItemEvent;

    if (currentWorkItemEvent) {
      const lifecycleAction = currentWorkItemEvent.lifecycleData[this.action];
      let number = 0;
      if (lifecycleAction &&
        lifecycleAction[this.imagesCollection]) {
        number = lifecycleAction[this.imagesCollection].length;
      }
      Template.instance().numberOfImages.set(number);
      return number;
    }
    return 0;
  },
  getImageLibraryCursor(numberOfImages) {
    if (numberOfImages > 0) {
      return 'pointer';
    }
    return 'default';
  },
  uploadingImage() {
    return Template.instance().uploadingImage.get();
  },
});
