import { Meteor } from 'meteor/meteor';
import { Template } from 'meteor/templating';
import { $ } from 'meteor/jquery';

import './view-images.html';

Template.viewImages.onRendered(() => {
  $('#viewImages').modal('show');
});

Template.viewImages.helpers({
  title() {
    var activeImagesLibrary = Session.get('ActiveImagesLibrary');

    if (activeImagesLibrary) {
      return activeImagesLibrary.title;
    }
    return '';
  },

  images() {
    var activeImagesLibrary = Session.get('ActiveImagesLibrary');

    if (activeImagesLibrary && activeImagesLibrary.imagesCollection && activeImagesLibrary.action) {
      var template = Template.instance();
      console.log(template.data);
      var currentWorkItemEvent = template.data.currentWorkItemEvent;
      var imageCollection = currentWorkItemEvent.lifecycleData[activeImagesLibrary.action][activeImagesLibrary.imagesCollection];

      if (imageCollection) {
        return imageCollection;
      } else {
        return [];
      }
    }
    return [];
  },

  baseImageStorageUrl() {
    return Meteor.settings['public'].gcpStorage.url;
  },
});
