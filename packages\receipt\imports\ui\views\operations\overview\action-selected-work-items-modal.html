<template name="actionSelectedWorkItemsModal">
    <div id="workItemActionChoiceModal"  class="ui modal" style="height:11em;width:100%;left:0%;right:0%;margin-left:0;margin-right:0;background-color:#fafafa">
        <div style="height:100%;background-color:#fafafa">
          <div class="ui centered grid" style="text-transform:uppercase;">
            <div class="ui center aligned large header" style="margin-top:10px;">
            <span style="display:inline;margin-right:10px;">{{activeIdentifiers}}
              {{#if singleActiveItemContainsDgs}}
                <i class="exclamation triangle icon orange"></i>       
              {{/if}}
            </span> 
            {{#if singleActiveItem}}
              <h6 style="display:inline; color:darkred;margin:0">
                {{#if activeDescription}}<span style="display:inline;margin-right:20px;"><span style="text-transform:initial; color: #444; font-weight:300;">Description: </span>{{activeDescription}}</span>{{/if}}
                {{#if activeLocation}}<span style="display:inline;margin-right:20px;"><span style="text-transform:initial; color: #444;  font-weight:300;">Installation: </span>{{activeLocation}}</span>{{/if}}
                {{#if activeClient}}<span style="display:inline;margin-right:20px;"><span style="text-transform:initial; color: #444;  font-weight:300;">Client: </span>{{activeClient}}</span>{{/if}}
              </h6>
            {{/if}}
            {{#if userIsAdmin}}
              <div class="actions" style="display:inline">
              <span style="float:left; cursor: pointer; color: #c62828;  position: absolute; left: 0;" >
                {{#if inAdminMode}}
                <a id="exit-admin-mode" class="ui button ok" style="
                padding-right: 10px;
                padding-left: 10px;
                border: 1px solid red;
                border-radius: 0px;
                color:red;
                background-color:white;
                margin-left: 10px;
                font-size:15px;
                margin-top:5px;
                padding-bottom:5px;
                padding-top: 5px"><i class="material-icons left" style="margin-right: 4px; padding-bottom:2px; font-size:18px;">exit_to_app</i>Exit Admin</a>
                {{else}}
                <a id="enter-admin-mode" class="ui button ok" style="
                padding-right: 10px;
                padding-left: 10px;
                border: 1px solid red;
                border-radius: 0px;
                color:red;
                background-color:white;
                margin-left: 10px;
                font-size:15px;
                margin-top:5px;
                padding-bottom:5px;
                padding-top: 5px"><i class="material-icons left" style="margin-right: 4px; padding-bottom:2px; font-size:18px;">report</i>Admin</a>
                {{/if}}
              </span>
              </div>
            {{/if}}
          </div>

             <span style="float:right; cursor: pointer; position: absolute; right: 0;" class="cancel" id="closeButton">
               <i class="small material-icons" style="padding-top: 5px; font-weight:600; font-size: 30px;">close</i>
              </span>
        </div>
        <div class="ui centered grid" style="margin-top:0px;">
              {{#if inAdminMode}}
                {{#if inPlannedState}}
                  <div class="seven wide column"></div>
                  <div class="four wide column" style="padding: 0 0.25rem;">
                    <div class="ui red button ok" id="deleteButton" disabled="{{buttonsDisabled}}">Delete</div>
                  </div>
                {{else}}
                  <div class="four wide column"></div>
                  <div class="four wide column" style="padding: 0 0.25rem;">
                    <div class="ui white button ok" id="moveBackStateButton">Move Back State</div>
                  </div>
                  <div class="four wide column" style="padding: 0 0.25rem;">
                      <div class="ui red button ok" id="deleteButton" disabled="{{buttonsDisabled}}">Delete</div>                      
                  </div>
              {{/if}}
    
            {{else}}
                  {{#if incomingItemsSelected}}
                    <div class="four wide column">
                        <div class="fluid ui primary basic big button ok" id="receiveButton">Receive</div>
                    </div>
                    {{#if singleActiveItem}}
                      <div class="four wide column">
                        <div class="fluid ui primary basic big button ok" id="removeButton">Delete</div>
                      </div>
                    {{/if}}
                  {{else if receivedItemsSelected}}
                    <div class="four wide column">
                        <div class="fluid ui red basic big button ok" id="markAsIncomingButton">Return To Incoming</div>
                    </div>
                    <div class="four wide column">
                        <div class="fluid ui primary basic big button ok" id="startWasteRemovalButton">Start Waste Removal</div>
                    </div>
                  {{else if inProgressItemsSelected}}
                  
                      <div class="four wide column">
                          <div class="fluid ui primary basic big button ok" id="unpackButton">Unpack</div>
                      </div>
                      <div class="four wide column">
                        <div class="fluid ui primary basic big button ok" id="receiveButton">Return to Awaiting Info</div>
                      </div>
                      <div class="four wide column">
                          <div class="fluid ui primary basic big button ok" id="completeButton">Mark as Completed</div>
                      </div>
                  {{else if completedItemsSelected}}
                    <div class="four wide column">
                        <div class="fluid ui primary basic big button ok" id="dispatchButton">Mark as Collected</div>
                    </div>

                    <div class="four wide column">
                      <div class="fluid ui primary basic big button ok" id="startWasteRemovalButton">Return to Waste Removal</div>
                    </div>
                  {{/if}}
            {{/if}}
        </div>
      </div>
    </div>
</template>