import './action-selected-work-items-modal.html';
import './confirm-delete-work-item-modal.html';

import { $ } from 'meteor/jquery';
import { CompanyProfiles } from '../../../../api/company-profiles/company-profiles';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Meteor } from 'meteor/meteor';
import { Template } from 'meteor/templating';
import { WorkItemEvents } from '../../../../api/work-item-events/work-item-events';
import { WorkItemOverviewEventEmitter } from './work-item-overview-event-emitter';
import { currentSiteProfile } from '../../../../ui/helpers/current-site-profile';

const getItemsSelectedInOverview = (template) => {
  const selectedReceived = template.data.selectedReceivedItems.get();
  const selectedIncoming = template.data.selectedIncomingItems.get();
  const selectedCompleted = template.data.selectedCompletedItems.get();
  const selectedInProgress = template.data.selectedInProgressItems.get();
  return selectedIncoming
    .concat(selectedReceived).concat(selectedCompleted).concat(selectedInProgress);
};

const actionModalTracking = (template) => {
  if (FlowRouter.getRouteName() === 'workItemOverview') {
    if (getItemsSelectedInOverview(template).length > 0) {
      $('#workItemActionChoiceModal')
        .modal({
          transition: 'horizontal flip',
          duration: 0,
          onDeny() {
            WorkItemOverviewEventEmitter.emit('WORK_ITEMS_SELECTED_PROCESSED');
            return false;
          },
          onApprove() {
            return false;
          },
        })
        .modal('show', function onShow() {
          const d = document.getElementsByClassName('ui dimmer modals page transition visible active');
          if (d[0])
            d[0].className += ' disabled override';
        })
        .on('click', '.ui.button', function onClick() {
          const processButtonClick = !template.buttonsDisabled.get();
          switch ($(this).attr('id')) {
            case 'moveBackStateButton':
              const workItemEvents = getItemsSelectedInOverview(template);

              Meteor.call(
                'setLastEventAsDeleted',
                workItemEvents,
                () => {
                  WorkItemOverviewEventEmitter.emit('WORK_ITEMS_SELECTED_PROCESSED');
                },
              );

              break;
            case 'deleteButton':
              const workItemEventLifecycleIds = getItemsSelectedInOverview(template).map(
                (x) => x.lifecycleId,
              );
              Meteor.call(
                'setLifecycleAsDeleted',
                workItemEventLifecycleIds,
                () => {
                  WorkItemOverviewEventEmitter.emit('WORK_ITEMS_SELECTED_PROCESSED');
                },
              );
              break;
            case 'removeButton':
              $('#workItemActionChoiceModal')
                .modal('hide');
              $('#confirmDeleteItemModal')
                .modal('show');
              break;
            case 'exit-admin-mode':
              template.inAdminMode.set(false);
              break;
            case 'enter-admin-mode':
              template.inAdminMode.set(true);
              break;
            case 'dispatchButton':
              if (processButtonClick) {
                template.buttonsDisabled.set(true);

                const workItemEventLifecycleIds = getItemsSelectedInOverview(template).map(
                  (x) => x.lifecycleId,
                );
                const currentWorkItemEvents = WorkItemEvents.find({
                  lifecycleId: { $in: workItemEventLifecycleIds },
                }).fetch();
                const companyId = CompanyProfiles.findOne()._id;
                const siteId = currentSiteProfile()._id;

                Meteor.call(
                  'collectMultipleWorkItems',
                  companyId,
                  siteId,
                  currentWorkItemEvents,
                  {},
                  {},
                  () => {
                    $('#workItemActionChoiceModal').modal('hide');
                    WorkItemOverviewEventEmitter.emit('WORK_ITEMS_SELECTED_PROCESSED');
                    setTimeout(function () {
                      template.buttonsDisabled.set(false);
                    }, 500);
                  },
                );
              }
              break;
            case 'markAsIncomingButton':
              if (processButtonClick) {
                template.buttonsDisabled.set(true);
                const workItemEventLifecycleIds = getItemsSelectedInOverview(template).map(
                  (x) => x.lifecycleId,
                );
                const currentWorkItemEvents = WorkItemEvents.find({
                  lifecycleId: { $in: workItemEventLifecycleIds },
                }).fetch();

                Meteor.call(
                  'markIncomingMultipleWorkItems',
                  currentWorkItemEvents,
                  {},
                  () => {
                    $('#workItemActionChoiceModal').modal('hide');
                    WorkItemOverviewEventEmitter.emit('WORK_ITEMS_SELECTED_PROCESSED');
                    setTimeout(function () {
                      template.buttonsDisabled.set(false);
                    }, 500);
                  },
                );
              }
              break;
            case 'startWasteRemovalButton':

              if (processButtonClick) {
                template.buttonsDisabled.set(true);
                const workItemEventLifecycleIds = getItemsSelectedInOverview(template).map(
                  (x) => x.lifecycleId,
                );
                const currentWorkItemEvents = WorkItemEvents.find({
                  lifecycleId: { $in: workItemEventLifecycleIds },
                }).fetch();
                const companyId = CompanyProfiles.findOne()._id;
                const siteId = currentSiteProfile()._id;

                // Send each selected item as individual meteor calls at 0.5 sec intervals.
                function doSetTimeout(singleWorkItemEvent,i) {
                  setTimeout(function () {
                    Meteor.call(
                      'startWorkOnMultipleWorkItems',
                      companyId,
                      siteId,
                      singleWorkItemEvent,
                      {},
                      () => {},
                    );
                  },(10 + i*1000));
                };

                var i;
                for (i = 0; i < currentWorkItemEvents.length; i++) {
                  const currentWorkItemEvent = currentWorkItemEvents[i];
                  const singleWorkItemEvent = [currentWorkItemEvent];
                  doSetTimeout(singleWorkItemEvent,i);
                }

                $('#workItemActionChoiceModal').modal('hide');
                WorkItemOverviewEventEmitter.emit('WORK_ITEMS_SELECTED_PROCESSED');
                setTimeout(function () {
                  template.buttonsDisabled.set(false);
                }, 500);
              }
              break;
            case 'completeButton':

              if (processButtonClick) {
                template.buttonsDisabled.set(true);
                const workItemEventLifecycleIds = getItemsSelectedInOverview(template).map(
                  (x) => x.lifecycleId,
                );
                const currentWorkItemEvents = WorkItemEvents.find({
                  lifecycleId: { $in: workItemEventLifecycleIds },
                }).fetch();
                const companyId = CompanyProfiles.findOne()._id;
                const siteId = currentSiteProfile()._id;
                Meteor.call(
                  'completeMultipleWorkItems',
                  companyId,
                  siteId,
                  currentWorkItemEvents,
                  {},
                  () => {
                    $('#workItemActionChoiceModal').modal('hide');
                    WorkItemOverviewEventEmitter.emit('WORK_ITEMS_SELECTED_PROCESSED');
                    setTimeout(function () {
                      template.buttonsDisabled.set(false);
                    }, 500);
                  },
                );
              }
              break;
            case 'receiveButton':
              if (processButtonClick) {
                template.buttonsDisabled.set(true);
                const workItemEventLifecycleIds = getItemsSelectedInOverview(template).map(
                  (x) => x.lifecycleId,
                );
                const currentWorkItemEvents = WorkItemEvents.find({
                  lifecycleId: { $in: workItemEventLifecycleIds },
                }).fetch();
                const companyId = CompanyProfiles.findOne()._id;
                const siteId = currentSiteProfile()._id;
                Meteor.call(
                  'receiveMultipleWorkItems',
                  companyId,
                  siteId,
                  currentWorkItemEvents,
                  {},
                  {},
                  () => {
                    $('#workItemActionChoiceModal').modal('hide');
                    WorkItemOverviewEventEmitter.emit('WORK_ITEMS_SELECTED_PROCESSED');
                    setTimeout(function () {
                      template.buttonsDisabled.set(false);
                    }, 500);
                  },
                );
              }
              break;
            case 'unpackButton':
              FlowRouter.go('unpackCargo', {
                clientId: 'null',
              });
              break;
            default:
              break;
          }
        })
        .on('click', '#closeButton', function () {
          $('#workItemActionChoiceModal').modal('hide');
          WorkItemOverviewEventEmitter.emit('WORK_ITEMS_SELECTED_PROCESSED');
        });
      // $('#workItemActionChoiceModal').modal('hide');
    } else {
      $('#workItemActionChoiceModal').modal('hide');
    }
  } else {
    $('#workItemActionChoiceModal').modal('hide');
    WorkItemOverviewEventEmitter.emit('WORK_ITEMS_SELECTED_PROCESSED');
  }
};

Template.actionSelectedWorkItemsModal.onRendered(function onRendered() {
  const template = this;

  template.autorun(() => {
    actionModalTracking(template);
  });
});

Template.actionSelectedWorkItemsModal.onCreated(function onCreated() {
  const template = this;
  template.buttonsDisabled = new ReactiveVar(false);
  template.inAdminMode = new ReactiveVar(false);

  template.autorun(() => {
    const selectedWorkItems = getItemsSelectedInOverview(Template.instance());
    if (selectedWorkItems.length === 0) {
      template.inAdminMode.set(false);
    }
  });
});

Template.actionSelectedWorkItemsModal.onDestroyed(function onDestroyed() {
  const template = this;

  $('#workItemActionChoiceModal').modal('hide');
  WorkItemOverviewEventEmitter.emit('WORK_ITEMS_SELECTED_PROCESSED');
  const modals = document.getElementsByClassName('ui dimmer modals');
  if (modals) {
    for (var i = 0; i < modals.length; i++) {
      modals[i].parentNode.removeChild(modals[i]);
    }
  }
});

Template.actionSelectedWorkItemsModal.helpers({
  userIsAdmin() {
    const user = Meteor.user();
    return user && user.profile && user.profile.isAdmin;
  },
  activeIdentifiers() {
    const selectedWorkItems = getItemsSelectedInOverview(Template.instance());
    if (selectedWorkItems.length > 0) {
      return selectedWorkItems.map((x) => x.identifier).join(', ');
    }
    return '';
  },
  activeDescription() {
    const activeWorkItem = getItemsSelectedInOverview(Template.instance())[0];
    if (activeWorkItem) {
      return activeWorkItem.lifecycleData.planned.description;
    }
    return '';
  },
  activeLocation() {
    const activeWorkItem = getItemsSelectedInOverview(Template.instance())[0];
    if (activeWorkItem) {
      return activeWorkItem.lifecycleData.planned.clientLocation;
    }
    return '';
  },
  activeClient() {
    const activeWorkItem = getItemsSelectedInOverview(Template.instance())[0];
    if (activeWorkItem && activeWorkItem.latestVorInformation) {
      return activeWorkItem.latestVorInformation.clientName;
    }
    return '';
  },
  activeVendor() {
    const activeWorkItem = getItemsSelectedInOverview(Template.instance())[0];
    if (activeWorkItem) {
      return activeWorkItem.latestVorInformation.vendorName;
    }
    return '';
  },
  singleActiveItem() {
    return getItemsSelectedInOverview(Template.instance()).length === 1;
  },
  singleActiveItemContainsDgs() {
    let singleItemContainsDgs = false;
    if (getItemsSelectedInOverview(Template.instance()).length === 1) {
      const activeWorkItem = getItemsSelectedInOverview(Template.instance())[0];
      singleItemContainsDgs = activeWorkItem.latestVorInformation.materials.some(
        (x) => x.unNo ||
             x.imoHazardClass ||
             x.imoSublcass,
      );
      singleItemContainsDgs = singleItemContainsDgs && (activeWorkItem.state !== 'COMPLETED');
    }
    return singleItemContainsDgs;
  },
  incomingItemsSelected() {
    const representativeSelectedItems = getItemsSelectedInOverview(Template.instance())[0];
    return representativeSelectedItems && representativeSelectedItems.state === 'PLANNED';
  },
  receivedItemsSelected() {
    const representativeSelectedItems = getItemsSelectedInOverview(Template.instance())[0];
    return representativeSelectedItems && representativeSelectedItems.state === 'RECEIVED';
  },
  inProgressItemsSelected() {
    const representativeSelectedItems = getItemsSelectedInOverview(Template.instance())[0];
    return representativeSelectedItems && representativeSelectedItems.state === 'INPROGRESS';
  },
  completedItemsSelected() {
    const representativeSelectedItems = getItemsSelectedInOverview(Template.instance())[0];
    return representativeSelectedItems && representativeSelectedItems.state === 'COMPLETED';
  },
  buttonsDisabled() {
    return Template.instance().buttonsDisabled.get();
  },
  inAdminMode() {
    return Template.instance().inAdminMode.get();
  },
  inPlannedState() {
    const activeWorkItem = getItemsSelectedInOverview(Template.instance())[0];
    if (activeWorkItem) {
      return activeWorkItem.state === 'PLANNED';
    }
    return false;
  },
  canBeReinspected() {
    const activeWorkItem = getItemsSelectedInOverview(Template.instance())[0];
    if (activeWorkItem) {
      return activeWorkItem.state === 'COMPLETED' &&
        activeWorkItem.lifecycleData.marshallingYardInspection &&
        activeWorkItem.lifecycleData.marshallingYardInspection.willBeRectifiedAtHoist;
    }
    return false;
  },
});

Template.actionSelectedWorkItemsModal.events({
  'click #deleteButton': function onClick(event, templateInstance) {
    const workItemEventLifecycleIds = getItemsSelectedInOverview(templateInstance).map(
      (x) => x.lifecycleId,
    );
    Meteor.call(
      'setLifecycleAsDeleted',
      workItemEventLifecycleIds,
      () => {
        WorkItemOverviewEventEmitter.emit('WORK_ITEMS_SELECTED_PROCESSED');
      },
    );
  },
  'click #moveBackStateButton': function onClick(event, templateInstance) {
    const workItemEvents = getItemsSelectedInOverview(templateInstance);

    Meteor.call(
      'setLastEventAsDeleted',
      workItemEvents,
      () => {
        WorkItemOverviewEventEmitter.emit('WORK_ITEMS_SELECTED_PROCESSED');
      },
    );
  },
});
