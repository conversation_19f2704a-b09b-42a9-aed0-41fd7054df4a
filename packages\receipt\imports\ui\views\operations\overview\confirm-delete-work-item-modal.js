import './confirm-delete-work-item-modal.html';

import { $ } from 'meteor/jquery';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Meteor } from 'meteor/meteor';
import { Template } from 'meteor/templating';
import { WorkItemOverviewEventEmitter } from './work-item-overview-event-emitter';

const getItemsSelectedInOverview = (template) => {
  const selectedReceived = template.data.selectedReceivedItems.get();
  const selectedIncoming = template.data.selectedIncomingItems.get();
  const selectedCompleted = template.data.selectedCompletedItems.get();
  const selectedInProgress = template.data.selectedInProgressItems.get();
  return selectedIncoming.concat(selectedReceived).concat(selectedCompleted).concat(selectedInProgress);
};

const confirmDeleteWorkItemModalTracking = (template) => {
  if (FlowRouter.getRouteName() === 'workItemOverview') {
    if (getItemsSelectedInOverview(template).length > 0) {
      $('#confirmDeleteItemModal')
        .modal({
          transition: 'horizontal flip',
          duration: 0,
        })
        .on('click', '.ui.button', function onClick() {
          switch ($(this).attr('id')) {
            case 'deleteButton':
              const workItemEventLifecycleIds = getItemsSelectedInOverview(template).map(
                (x) => x.lifecycleId
              );
              Meteor.call(
                'setLifecycleAsDeleted',
                workItemEventLifecycleIds,
                () => {
                  WorkItemOverviewEventEmitter.emit('WORK_ITEMS_SELECTED_PROCESSED');
                },
              );
              break;
            case 'noButton':
              $('#confirmDeleteItemModal')
                .modal('hide');
              $('#workItemActionChoiceModal')
                .modal('show');
              console.log('end of new button code');
              break;
          }
        })
        .on('click', '#closeConfirmModalButton', function () {
          $('#confirmDeleteItemModal').modal('hide');
          WorkItemOverviewEventEmitter.emit('WORK_ITEMS_SELECTED_PROCESSED');
        });
    } else {
      $('#confirmDeleteItemModal').modal('hide');
    }
  } else {
    $('#confirmDeleteItemModal').modal('hide');
    WorkItemOverviewEventEmitter.emit('WORK_ITEMS_SELECTED_PROCESSED');
  }
};

Template.confirmDeleteWorkItemModal.onRendered(function onRendered() {
  const template = this;
  template.autorun(() => {
    confirmDeleteWorkItemModalTracking(template);
  });
});

Template.confirmDeleteWorkItemModal.onCreated(function onCreated() {
  const template = this;
  template.buttonsDisabled = new ReactiveVar(false);
});

Template.confirmDeleteWorkItemModal.onDestroyed(function onDestroyed() {
  const template = this;

  $('#confirmDeleteItemModal').modal('hide');
  WorkItemOverviewEventEmitter.emit('WORK_ITEMS_SELECTED_PROCESSED');
  const modals = document.getElementsByClassName('ui dimmer modals');
  if (modals) {
    for (var i = 0; i < modals.length; i++) {
      modals[i].parentNode.removeChild(modals[i]);
    }
  }
});

Template.confirmDeleteWorkItemModal.helpers({
  activeIdentifiers() {
    const selectedWorkItems = getItemsSelectedInOverview(Template.instance());
    if (selectedWorkItems.length > 0) {
      return selectedWorkItems.map((x) => x.identifier).join(', ');
    }
    return '';
  },
  buttonsDisabled() {
    return Template.instance().buttonsDisabled.get();
  },
});
