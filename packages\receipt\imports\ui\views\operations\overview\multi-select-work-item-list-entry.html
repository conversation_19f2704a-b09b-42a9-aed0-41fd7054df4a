<template name="multiSelectWorkItemListEntry">
    <div class="ui card work-item-card {{failedInspectionStyling}}"  style="cursor: pointer; width:100%">
        <div class="ui grid">
            <div class="{{#if isIncomingItem}}{{#if anyWorkItemSelected}}thirteen wide column{{else}}fifteen wide column{{/if}}{{else}}thirteen wide column{{/if}}" style="padding-right:0;">
                <div class="content" style="width: 100%;">
                    <h5 class="truncate work-item-identifier" 
                        style="border-bottom-color: rgba(221, 221, 221, 0.83);border-bottom-style: dashed;border-bottom-width: thin; width:100%; float:left; margin-top:8px; padding-left: 3px; padding-right: 0; font-size:1.7rem; text-align:left;">
                        {{item.identifier}}
                        {{#if isContainingDangerousGoods}}
                           <i class="exclamation triangle icon orange dg-icon" style="float:right;"></i>
                        {{/if}}
                    </h5>
                </div>
                <div class="extra content">
                    
                    <div style="float:left; font-size: 11px; padding-top: 3px; padding-bottom: 2px; font-weight: 400; text-transform:uppercase; padding-left:3px; padding-right:3px; color:darkred; width: 100%; text-align:right">
                        
                        {{#if isOkToShowMaterialLineCounts}}
                            <span style="margin-right:8px; float:left;">
                                {{noOfMaterials}} Lines ({{noOfWasteMaterialLines}} Waste)
                            </span>                           
                        {{/if}}
                        {{#if isCompletedItem}}
                            <span style="margin-right:8px; float:left;">
                                {{noOfRestLines}} Rest Load ({{noOfWasteRestLines}} Waste)
                            </span>
                        {{/if}}
                        <span style="margin-right:8px; float:left; color:#253160; text-transform:none" >Voyage No: {{item.latestVorInformation.voyageNo}}</span>
                        
                        <span style="float:right">{{dischargeTimeFormatted}}</span>
                    </div>
                </div>
            </div>
            {{#if isIncomingItem}}
                {{#if anyWorkItemSelected}}
                    <div class="three wide column" style="padding-left:0">
                        <center>
                        {{#if anyWorkItemSelected}}
                            <h6 class="itemRightHandSide" style="height:100%; padding-top: 1.25rem;padding-bottom: 1.25rem; margin-top: 0px;  margin-bottom: 0px; color: #1a2b59; background-color: #f2f2f2;">
                                <i class="small material-icons" style="display: inline; vertical-align: middle; font-size:1.7rem;">
                                    {{#if isSelected}}
                                        check_box
                                    {{else}}
                                        check_box_outline_blank
                                    {{/if}}
                                </i>
                            </h6>
                        {{else}}
                            {{#if isIncomingItem}}
                                
                            {{else}}
                                <h6 class="itemRightHandSide" style="height:100%; font-size:18px;padding-top: 1.5rem; padding-bottom: 1.5rem; margin-top: 0px; margin-bottom: 0px; color: {{daysSinceReceivedText}};background-color: {{daysSinceReceivedHighlight}};">
                                    {{daysSinceReceived}}
                                </h6>
                            {{/if}}
                        {{/if}}
                        </center>                   
                    </div> 
                {{/if}}
            {{else}}
                <div class="three wide column" style="padding-left:0">
                    <center>
                    {{#if anyWorkItemSelected}}
                        <h6 class="itemRightHandSide" style="height:100%; padding-top: 1.25rem;padding-bottom: 1.25rem; margin-top: 0px;  margin-bottom: 0px; color: #1a2b59; background-color: #f2f2f2;">
                            <i class="small material-icons" style="display: inline; vertical-align: middle; font-size:1.7rem;">
                                {{#if isSelected}}
                                    check_box
                                {{else}}
                                    check_box_outline_blank
                                {{/if}}
                            </i>
                        </h6>
                    {{else}}
                        {{#if isIncomingItem}}
                             
                        {{else}}
                            <h6 class="itemRightHandSide" style="height:100%; font-size:18px;padding-top: 1.5rem; padding-bottom: 1.5rem; margin-top: 0px; margin-bottom: 0px; color: {{daysSinceReceivedText}};background-color: {{daysSinceReceivedHighlight}};">
                                {{daysSinceReceived}}
                            </h6>
                        {{/if}}
                    {{/if}}
                    </center>                   
                </div> 
            {{/if}}
        </div>
        {{#if isMoreDetailsVisible}}    
        <div id="attachedDetails" class="ui bottom attached segment" style="padding: 10px 10px 10px 10px">         
                <div class='content' style="font-size: 13px;">
                    <span style="margin-right:8px; margin-bottom:8px;float:left; color:darkred; text-transform:none" ><span style="color:#253160">Client: </span>{{item.latestVorInformation.clientName}}</span>
                    <span style="margin-right:8px; margin-bottom:8px;float:left; color:darkred; text-transform:none" ><span style="color:#253160">Installation: </span>{{item.lifecycleData.planned.clientLocation}}</span>
                    {{#if itemHasNoWeight}}
                        <span style="margin-right:8px; margin-bottom:8px;float:left; color:darkred; text-transform:none" ><span style="color:#253160">Weight:</span> {{item.latestVorInformation.weightInKg}} KG</span>
                    {{/if}}
                    
                    {{#if isOkToShowRestLoadList}}
                        <table class="ui compact celled table">
                            <caption  style="text-align:left"> Remaining Dangerous Materials</caption>
                            <thead><tr><th>UnNo</th><th>Class</th><th>Subclass</th><th>Qty</th><th>Description</th></tr></thead>
                            <tbody>
                                {{#each remainingDGMaterials}}
                                    {{> materialLongRow}}
                                {{/each}}
                            </tbody>
                        </table>
                    {{/if}}

                    {{#if containsDangerousGoodsButNotCompleted}}
                        <table class="ui compact celled table">
                            <thead><tr><th>UnNo</th><th>Class</th><th>Subclass</th></tr></thead>
                            <tbody>
                                {{#each dgMaterials}}
                                    {{> dangerousMaterialShortRow}}
                                {{/each}}
                            </tbody>
                        </table>
                    {{/if}}
                </div>
            </div>
        {{/if}}
    </div>
</template>

<template name="materialLongRow"> 
    <tr><td>{{unNo}}</td><td>{{class}}</td><td>{{subclass}}</td><td>{{quantity}}</td><td>{{description}}</td></tr>
</template>

<template name="dangerousMaterialShortRow"> 
    <tr><td>{{unNo}}</td><td>{{class}}</td><td>{{subclass}}</td></tr>
</template>