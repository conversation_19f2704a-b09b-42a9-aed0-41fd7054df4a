import { Template } from 'meteor/templating';
import './multi-select-work-item-list-entry.html';
import { WorkItemOverviewEventEmitter } from './work-item-overview-event-emitter';
import { Tracker } from 'meteor/tracker';

const getRemainingFullMaterials = (activeWorkItem) => {
  if (activeWorkItem.linesReceipted) {
    let wastematerials = activeWorkItem.latestVorInformation.materials;
    var removedMaterialsIds = activeWorkItem.linesReceipted;
    var materialsLeft = wastematerials.filter((x) => !removedMaterialsIds.includes(x.lineId));
    return materialsLeft;
  } else {
    return activeWorkItem.latestVorInformation.materials;
  }
};

const getRemainingDgValues = (activeWorkItem) => {
  let remainingMaterials = getRemainingFullMaterials(activeWorkItem);
  let remainingDGMaterials = remainingMaterials.filter((x) => x.unNo || x.imoHazardClass || x.imoSublcass);
  return remainingDGMaterials.map((x) => {
    return {
      quantity: x.quantity || '-',
      unNo: x.unNo || '-',
      class: x.imoHazardClass || '-',
      subclass: x.imoSubclass || '-',
      description: x.description || '-',
    };
  });
};

const getMaterialDgValues = (activeWorkItem) => {
  let materialsDgValues = [];
  if (activeWorkItem) {
    let materials = activeWorkItem.latestVorInformation.materials;
    let dgMaterials = materials.filter((x) => x.unNo || x.imoHazardClass || x.imoSublcass);
    materialsDgValues = dgMaterials.map((x) => {
      return {
        unNo: x.unNo || '-',
        class: x.imoHazardClass || '-',
        subclass: x.imoSubclass || '-',
      };
    });

    // Remove duplicate objects - with same dg values.
    materialsDgValues = materialsDgValues.filter(
      (x, index, self) => self.findIndex(
        (t) => t.unNo === x.unNo
             && t.class === x.class
             && t.subclass === x.subclass,
      ) === index,
    );
  }
  return materialsDgValues;
};

const daysSinceReceived = (workItemEvent) => {
  const momentReceived = moment(workItemEvent.lifecycleData.received.timestamp);
  const momentNow = moment();
  return momentNow.diff(momentReceived, 'days');
};

const hasDangerousGoods = (workItemEvent) => {
  var remainingMaterials = getRemainingFullMaterials(workItemEvent);
  return remainingMaterials.some(
    (x) => x.unNo
            || x.imoHazardClass
            || x.imoSublcass,
  );
};

Template.multiSelectWorkItemListEntry.onCreated(function onCreated() {
  const template = this;
  template.isSelected = false;
  template._moreDetailsVisibility = new ReactiveVar(false);
  Tracker.autorun(() => {
    template._moreDetailsVisibility.set(template.data.moreDetailsVisibility.get());
  });
});

Template.multiSelectWorkItemListEntry.events({
  'click .work-item-card': function onClick(event, templateInstance) {
    event.preventDefault();
    const user = Meteor.user();
    const userIsAdmin = user && user.profile && user.profile.isAdmin;

    if (this.item.state !== 'COLLECTED' || (this.item.state === 'COLLECTED' && userIsAdmin)) {
      if (!this.isSelected) {
        WorkItemOverviewEventEmitter.emit('WORK_ITEM_SELECTED', this.item);
      } else {
        WorkItemOverviewEventEmitter.emit('WORK_ITEM_DESELECTED', this.item);
      }
    }
  },
});

Template.multiSelectWorkItemListEntry.helpers({
  failedInspectionStyling() {
    const inspection = this.item.lifecycleData.marshallingYardInspection;
    const isInspectedState = inspection !== null && inspection !== undefined;
    const failed = isInspectedState && inspection.fails > 0;

    return failed ? 'failed-inspection' : '';
  },
  dischargeTimeFormatted() {
    return moment(
      this.item.latestVorInformation.plannedDateTime
      || this.item.lifecycleData.planned.plannedDateTime,
    ).format('DD/MM/YYYY');
  },
  outbound() {
    return this.item.latestVorInformation && this.item.latestVorInformation.direction.toUpperCase() === 'OUTBOUND';
  },
  daysSinceReceived() {
    const refresher = Session.get('refreshEnforcer');
    if (refresher) {
      return `${daysSinceReceived(this.item)}d`;
    }
    return `${daysSinceReceived(this.item)}d`;
  },
  daysSinceReceivedHighlight() {
    const daysSinceThisReceived = daysSinceReceived(this.item);

    if (daysSinceThisReceived >= 30) {
      return '#f44336';
    } else if (daysSinceThisReceived >= 25) {
      return '#ffc107';
    }
    return '#eeeeee';
  },
  daysSinceReceivedText() {
    if (daysSinceReceived(this.item) >= 25) {
      return 'white';
    }
    return 'black';
  },
  isIncomingItem() {
    return this.item.state === 'PLANNED';
  },
  isCompletedItem() {
    return this.item.state === 'COMPLETED';
  },
  isIncomingAndNoItemsSelected() {
    return this.item.state === 'PLANNED' && !this.anyWorkItemSelected;
  },
  isOkToShowMaterialLineCounts() {
    // For now its ambiguous to show 'original' material line counts when CCU is being emptied or is ready for pickup.
    // (as materials may/may not have been removed).
    // Ideally line counts in WasteRemoval and Awaiting Collection would show 'remaining' materials.
    return this.item.state === 'PLANNED'
           || this.item.state === 'RECEIVED';
  },
  isOkToShowRestLoadList() {
    return hasDangerousGoods(this.item) && this.item.state === 'COMPLETED';
  },
  containsDangerousGoodsButNotCompleted() {
    return hasDangerousGoods(this.item) && (this.item.state !== 'COMPLETED');
  },
  isContainingDangerousGoods() {
    // Show warning icon if any contained materials have IMDG CLass, Subclass or UnNO set.
    // Continue to show icon if item is in completed state - if DGs has not been removed from container.
    return hasDangerousGoods(this.item);
  },
  noOfMaterials() {
    return this.item.latestVorInformation
            && this.item.latestVorInformation.materials ? this.item.latestVorInformation.materials.length : '0';
  },
  noOfWasteMaterialLines() {
    return this.item.latestVorInformation
            && this.item.latestVorInformation.materials ? this.item.latestVorInformation.materials.filter((x) => x.isWaste).length : '0';
  },
  noOfRestLines() {
    return this.item.linesReceipted
      ? this.item.latestVorInformation.materials.length - this.item.linesReceipted.length : 0;
  },
  noOfWasteRestLines() {
    return this.item.linesReceipted ? this.item.latestVorInformation.materials
      .filter((x) => x.isWaste).length - this.item.linesReceipted.length : 0;
  },
  dgMaterials() {
    return getMaterialDgValues(this.item);
  },
  remainingDGMaterials() {
    return getRemainingDgValues(this.item);
  },
  itemHasNoWeight() {
    return this.item.latestVorInformation.weightInKg != null;
  },
  isMoreDetailsVisible() {
    return Template.instance()._moreDetailsVisibility.get();
  },
});
