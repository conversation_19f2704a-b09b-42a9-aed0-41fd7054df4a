<template name="multiSelectWorkItemList">
    <div class="ui center aligned large header">
          {{displayName}} ({{workItemsCount}})
      </div>
    <div class="scrollable-work-item-list" style="height: 90vh;">
        <div style="margin-bottom: 130px;">
            {{#each workItems}} 
                {{> multiSelectWorkItemListEntry itemAndSelectionState}} 
            {{/each}}
        </div>
    </div>

</template>