import './multi-select-work-item-list.html';
import './multi-select-work-item-list-entry';
import { CompanyProfiles } from '../../../../api/company-profiles/company-profiles';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Meteor } from 'meteor/meteor';
import { Session } from 'meteor/session';
import { Template } from 'meteor/templating';
import { WorkItemEvents } from '../../../../api/work-item-events/work-item-events';
import { currentSiteProfile } from '../../../../ui/helpers/current-site-profile';

function getWorkItemsForList(context, selectedWorkItems) {
  const escapeRegex = (str) => str.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&').trim();

  const selector = {
    $and: [{ state: context.state }, { isLatest: true }],
  };

  if (context.filter && context.filter.length) {
    selector.$and.push({
      $or: [
        {
          'lifecycleData.planned.clientLocation':
        { $regex: escapeRegex(context.filter), $options: 'i' },
        },
        {
          identifier:
        { $regex: escapeRegex(context.filter), $options: 'i' },
        },
        {
          lifecycleId: { $in: selectedWorkItems.map(x => x.lifecycleId) },
        },
      ],
    });
  }

  return WorkItemEvents
    .find(selector, { sort: { 'latestVorInformation.voyageNo': 1, identifier: 1 } }).fetch();
}

Template.multiSelectWorkItemList.onRendered(function onRendered() {
  if (!Darwin.device.mobile) {
    Ps.initialize(this.find('.scrollable-work-item-list'), { suppressScrollX: true });
  }
});

Template.multiSelectWorkItemList.helpers({
  itemAndSelectionState() {
    const selectedItems = Template.instance().data.selectedItems.get();
    const moreDetailsVisibility = Template.instance().data.moreDetailsVisibility;
    const isSelected = selectedItems.some((x) => this.lifecycleId === x.lifecycleId);
    return {
      item: this,
      isSelected,
      anyWorkItemSelected: selectedItems.length > 0,
      moreDetailsVisibility: moreDetailsVisibility,
    };
  },
  workItems() {
    return getWorkItemsForList(Template.currentData(),
      Template.instance().data.selectedItems.get(),
    );
  },
  workItemsCount() {
    return getWorkItemsForList(Template.currentData(),
      Template.instance().data.selectedItems.get(),
    ).length;
  },
  anyWorkItemsSelected() {
    return Template.instance().selectedItems.get().length > 0;
  },
  singleWorkItemSelected() {
    return Template.instance().selectedItems.get().length === 1;
  },
  isPlannedItemsList() {
    return this.state === 'PLANNED';
  },
});

Template.multiSelectWorkItemList.events({
  'click #planAndReceiveWorkItemButton': function handleClick() {
    FlowRouter.go('planWorkItem');
  },
  'click #confirmSelectionButton': function handleClick(event, templateInstance) {
    const workItemEventLifecycleIds = Session.get('planned.selectedWorkItems');
    const currentWorkItemEvents =
      WorkItemEvents.find({ lifecycleId: { $in: workItemEventLifecycleIds } }).fetch();

    const companyId = CompanyProfiles.findOne()._id;
    const siteId = currentSiteProfile()._id;

    Meteor.call('receiveMultipleWorkItems',
      companyId,
      siteId,
      currentWorkItemEvents,
      {},
      {},
      () => { },
    );

    Template.instance().selectedItems.set([]);
    Session.set('planned.inMultiSelectMode', false);
  },
  'click #editItemButton': function handleClick(event, templateInstance) {
    const selectedWorkItemIds = Session.get('planned.selectedWorkItems');
    FlowRouter.go('workItemLifecycleDetails', { lifecycleId: selectedWorkItemIds[0] });
    Template.instance().selectedItems.set([]);
    Session.set('planned.inMultiSelectMode', false);
  },
});
