<template name="workItemListEntry">
  
  <div class="ui card work-item-card {{failedInspectionStyling}}"  style="width:100%; cursor: {{rollover}}">
      <div class="ui grid">
          <div class="thirteen wide column" style="padding-right:0;">
              <div class="content" style="width: 100%;">
                  <h5 class="truncate work-item-identifier" style="border-bottom-color: gray;border-bottom-style: dashed;border-bottom-width: thin; width:100%; float:left; margin-top:8px; padding-left: 3px; padding-right: 0; font-size:1.5rem; text-align:left;">{{identifier}}</h5>
              </div>
              <div class="extra content">
                  <div style="float:left; font-size: 11px; padding-top: 3px; padding-bottom: 2px; font-weight: 400; text-transform:uppercase; padding-left:3px; padding-right:0; color:darkred; width: 100%; text-align:right">
                      {{#if lifecycleData.planned.clientLocation}}
                          <span style="margin-right:8px; float:left;">
                              {{#if outbound}}
                                  <span style="margin-right: 2px; background-color: #00aff0; color: white; padding-right: 3px; padding-left: 3px;">
                                      Out
                                  </span> 
                              {{else}}
                                  <span style="margin-right: 2px; background-color:#E01765; color: white; padding-right: 4px; padding-left: 3px;">
                                      In
                                  </span> 
                              {{/if}}
                              {{lifecycleData.planned.clientLocation}}
                          </span>
                      {{/if}}
                      <span style="float:right">{{dischargeTimeFormatted}}</span>
                  </div>
              </div>
          </div>
          <div class="three wide column" style="padding-left:0">
              <center>
                <h6 class="itemRightHandSide" style="height:100%; font-size:18px;padding-top: 1.4rem; padding-bottom: 1.4rem; margin-top: 0px; margin-bottom: 0px; color: {{daysSinceReceivedText}};background-color: {{daysSinceReceivedHighlight}};">
                    {{daysSinceReceived}}
                </h6>
              </center>                   
          </div> 
      </div>
  </div>

</template>