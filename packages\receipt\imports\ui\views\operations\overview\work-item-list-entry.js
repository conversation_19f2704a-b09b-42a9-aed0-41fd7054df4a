import './work-item-list-entry.html';
import { $ } from 'meteor/jquery';
import { Meteor } from 'meteor/meteor';
import { Session } from 'meteor/session';
import { Template } from 'meteor/templating';
import { WorkItemEventStates } from '../../../../shared/work-item-event-states';
import authorization from "../../../../ui/helpers/authorization";
import { moment } from 'meteor/momentjs:moment';

function isReadOnly() {
  const user = Meteor.user();
  if (user) {
    const userProfile = user.profile;
    const userReadOnly = userProfile.readOnly;
    return userReadOnly;
  }
  return true;
}

function daysSinceReceived(workItemEvent) {
  const momentReceived = moment(workItemEvent.lifecycleData.received.timestamp);
  const momentNow = moment();
  return momentNow.diff(momentReceived, 'days');
}

Template.workItemListEntry.events({
  'click .work-item-card': function handleClick(event) {
    event.preventDefault();

    if (!isReadOnly()) {
      Session.set('activeWorkItemEvent', this);
      const currentlyInAdminMode = authorization.inAdminMode();

      if (currentlyInAdminMode) {
        Session.set('showAdminActionSelection', true);
      } else {
        if (this.state === WorkItemEventStates.RECEIVED) {
          Session.set('showActiveWorkItemReceiveEventActions', true);
          return;
        } else if (this.state === WorkItemEventStates.INPROGRESS) {
          Session.set('showActiveWorkItemEventActions', true);
          return;
        } else if (this.state === WorkItemEventStates.COMPLETED) {
          Session.set('showCompletedActionSelection', true);
          return;
        }
        else if (this.state === WorkItemEventStates.COLLECTED) {
          Session.set('showCompletedActionSelection', true);
          return;
        }
      }
    }
  },
});

Template.workItemListEntry.helpers({
  daysSinceReceived() {
    const refresher = Session.get('refreshEnforcer');
    if (refresher) {
      return `${daysSinceReceived(this)}d`;
    }
    return `${daysSinceReceived(this)}d`;
  },
  daysSinceReceivedHighlight() {
    const daysSinceThisReceived = daysSinceReceived(this);

    if (daysSinceThisReceived > 30) {
      return '#f44336';
    } else if (daysSinceThisReceived >= 25) {
      return '#ffc107';
    }
    return '#eeeeee';
  },
  daysSinceReceivedText() {
    if (daysSinceReceived(this) >= 25) {
      return 'white';
    }
    return 'black';
  },
  inAdminMode() {
    return authorization.inAdminMode();
  },
  isInPlannedState() {
    return false;
  },
  rollover() {
    if (isReadOnly()) {
      return 'default';
    }
    return 'pointer';
  },
  dischargeTimeFormatted() {
    return moment(this.latestVorInformation.plannedDateTime || this.lifecycleData.planned.plannedDateTime).format('DD/MM/YYYY');
  },
  outbound() {
    return this.latestVorInformation &&
      this.latestVorInformation.direction.toUpperCase() === 'OUTBOUND';
  },
  failedInspectionStyling() {
    const inspection = this.lifecycleData.marshallingYardInspection;
    const isInspectedState = inspection !== null && inspection !== undefined;
    const failed = isInspectedState && inspection.fails > 0;

    return failed ? 'failed-inspection' : '';
  },
});
