import './work-item-list.html';
import './work-item-list-entry';

import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Meteor } from 'meteor/meteor';
import { Template } from 'meteor/templating';
import { WorkItemEventStates } from '../../../../shared/work-item-event-states';
import { WorkItemEvents } from '../../../../api/work-item-events/work-item-events';

function isReadOnly() {
  const user = Meteor.user();
  if (user) {
    return user.profile.readOnly;
  }
  return true;
}

function getWorkItemsForList(thisContext) {
  const escapeRegex = (str) => str.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&').trim();

  const selector = {
    $and: [{ state: thisContext.state }, { isLatest: true }],
  };

  if (thisContext.filter && thisContext.filter.length) {
    selector.$and.push({ $or: [
      { 'lifecycleData.planned.clientLocation':
        { $regex: escapeRegex(thisContext.filter), $options: 'i' },
      },
      { identifier:
        { $regex: escapeRegex(thisContext.filter), $options: 'i' },
      },
    ] });
  }

  return WorkItemEvents.find(
    selector,
    { sort: { 'latestVorInformation.plannedDateTime': 1 } },
  );
}

Template.workItemList.onRendered(function onRendered() {
  if (!Darwin.device.mobile) {
    Ps.initialize(this.find('.scrollable-work-item-list'), { suppressScrollX: true });
  }
});

Template.workItemList.helpers({
  workItems() {
    return getWorkItemsForList(Template.currentData());
  },
  workItemsCount() {
    return getWorkItemsForList(Template.currentData()).count();
  },
  isPlannedList() {
    return this.state === WorkItemEventStates.PLANNED;
  },
  isReadOnly() {
    return isReadOnly();
  },
  isPlanned() {
    return this.state === WorkItemEventStates.PLANNED;
  },
});

Template.workItemList.events({
  'click #planAndReceiveWorkItemButton': function onClick() {
    if (this.state === WorkItemEventStates.PLANNED) {
      FlowRouter.go('planWorkItem');
    } else if (this.state === WorkItemEventStates.RECEIVED) {
      FlowRouter.go('planAndReceiveWorkItem');
    }
  },
});
