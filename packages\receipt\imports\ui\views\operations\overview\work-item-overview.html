<template name="workItemOverview">
  <div class="{{adminClass}}" style="height:100%">
    <div class="ui padded grid" style="margin-top:1em;">
        <div class="eight wide column"></div>
        <div class="eight wide column">
            <div class="ui button {{themeBackClass}}" style="float: right;" id="moreDetailsToggleButton">
                {{moreDetailsButtonTitle}} Details
            </div>
        </div>
    </div>
    <div class="ui padded grid work-item-overview-container" style="overflow-y:hidden;">  
      <div class="four wide column" style="padding-left:0.1rem; padding-right:0.1rem;">
        {{> multiSelectWorkItemList state=planned displayName="Incoming" filter=filter selectedItems=selectedIncomingItems moreDetailsVisibility=moreDetailsVisibility}}
      </div>

      <div class="four wide column" style="padding-left:0.1rem; padding-right:0.1rem;">
        {{> multiSelectWorkItemList state=received displayName="Awaiting Info" filter=filter selectedItems=selectedReceivedItems moreDetailsVisibility=moreDetailsVisibility}}
      </div>

      <div class="four wide column" style="padding-left:0.1rem; padding-right:0.1rem;">
        {{> multiSelectWorkItemList state=inprogress displayName="Waste Removal" filter=filter selectedItems=selectedInProgressItems moreDetailsVisibility=moreDetailsVisibility}}
      </div>

      <div class="four wide column" style="padding-left:0.1rem; padding-right:0.1rem;">
        {{> multiSelectWorkItemList state=completed displayName="Awaiting Collection" filter=filter selectedItems=selectedCompletedItems moreDetailsVisibility=moreDetailsVisibility}}
      </div>
    </div>
    {{> actionSelectedWorkItemsModal selectedReceivedItems=selectedReceivedItems selectedIncomingItems=selectedIncomingItems selectedCompletedItems=selectedCompletedItems selectedInProgressItems=selectedInProgressItems}}
    {{> confirmDeleteWorkItemModal selectedReceivedItems=selectedReceivedItems selectedIncomingItems=selectedIncomingItems selectedCompletedItems=selectedCompletedItems selectedInProgressItems=selectedInProgressItems}}
  </div>
</template>