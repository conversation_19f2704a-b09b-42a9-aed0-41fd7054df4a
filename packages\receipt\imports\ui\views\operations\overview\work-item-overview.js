import './work-item-list';
import './work-item-overview.html';
import './multi-select-work-item-list';
import './action-selected-work-items-modal';
import './confirm-delete-work-item-modal';

import { $ } from 'meteor/jquery';
import { ReactiveVar } from 'meteor/reactive-var';
import { Template } from 'meteor/templating';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { WorkItemEventStates } from '../../../../shared/work-item-event-states';
import { WorkItemOverviewEventEmitter } from './work-item-overview-event-emitter';
import authorization from '../../../../ui/helpers/authorization';

const filterResultsBySearchText = (event, templateInstance) => {
  const input = $(event.target);
  templateInstance.filter.set(input.val());
  $('.scrollable-work-item-list').scrollTop(0); // ensure tiles don't get lost
};

Template.workItemOverview.onCreated(function onCreated() {
  const self = this;
  self.filter = new ReactiveVar('');
  self.moreDetailsVisibility = new ReactiveVar(true);
  self.selectedIncomingItems = new ReactiveVar([]);
  self.selectedReceivedItems = new ReactiveVar([]);
  self.selectedInProgressItems = new ReactiveVar([]);
  self.selectedCompletedItems = new ReactiveVar([]);
  const updateSelectedItems = (toggledWorkItem) => {
    let selectedItemsStore;
    if (toggledWorkItem.state === 'PLANNED') {
      selectedItemsStore = self.selectedIncomingItems;
      self.selectedReceivedItems.set([]);
      self.selectedInProgressItems.set([]);
      self.selectedCompletedItems.set([]);
    } else if (toggledWorkItem.state === 'RECEIVED') {
      selectedItemsStore = self.selectedReceivedItems;
      self.selectedIncomingItems.set([]);
      self.selectedInProgressItems.set([]);
      self.selectedCompletedItems.set([]);
    } else if (toggledWorkItem.state === 'COMPLETED') {
      selectedItemsStore = self.selectedCompletedItems;
      self.selectedIncomingItems.set([]);
      self.selectedReceivedItems.set([]);
      self.selectedInProgressItems.set([]);
    } else {
      selectedItemsStore = self.selectedInProgressItems;
      self.selectedReceivedItems.set([]);
      self.selectedCompletedItems.set([]);
      self.selectedIncomingItems.set([]);
    }
    const selectedWorkItems = selectedItemsStore.get();
    const thisItemIsSelected =
      selectedWorkItems.some((x) => x.lifecycleId === toggledWorkItem.lifecycleId);
    const nowSelectedItems = thisItemIsSelected ?
      selectedWorkItems.filter((x) => x.lifecycleId !== toggledWorkItem.lifecycleId) :
      [...selectedWorkItems, toggledWorkItem];
    selectedItemsStore.set(nowSelectedItems);
  };

  WorkItemOverviewEventEmitter.on('WORK_ITEM_SELECTED', updateSelectedItems);
  WorkItemOverviewEventEmitter.on('WORK_ITEM_DESELECTED', updateSelectedItems);
  WorkItemOverviewEventEmitter.on('WORK_ITEMS_SELECTED_PROCESSED', () => {
    self.selectedReceivedItems.set([]);
    self.selectedCompletedItems.set([]);
    self.selectedInProgressItems.set([]);
    self.selectedIncomingItems.set([]);
  });

  self.autorun(() => {
    FlowRouter.watchPathChange();
    const currentRoute = FlowRouter.current();
    const isOverviewScreen = currentRoute && currentRoute.route.name === 'workItemOverview';

    // Overview is now always rendered so need to ensure we clear selected items
    // if navigating to another view and this is hidden so treats it like leaving page.

    if (!isOverviewScreen) {
      WorkItemOverviewEventEmitter.emit('WORK_ITEMS_SELECTED_PROCESSED');
      self.selectedReceivedItems.set([]);
      self.selectedIncomingItems.set([]);
      self.selectedCompletedItems.set([]);
      self.selectedInProgressItems.set([]);
      $('#workItemSearch').off('');
      setTimeout(() => {
        self.filter.set('');
        $('#workItemSearch').val('');
      }, 1);
    } else {
      $('#workItemSearch').on('input', (event) => {
        filterResultsBySearchText(event, self);
      });
    }
  });
});

Template.workItemOverview.onRendered(function onRendered() {
  const self = this;
  $(document).on('input', '#workItemSearch', (event) => {
    filterResultsBySearchText(event, self);
  });
});

Template.workItemOverview.onDestroyed(function onDestroyed() {
  const self = this;
  $('#workItemSearch').off('input', (event) => {
    filterResultsBySearchText(event, self);
  });

  WorkItemOverviewEventEmitter.removeAllListeners();
});

Template.workItemOverview.helpers({
  inprogress() {
    return WorkItemEventStates.INPROGRESS;
  },
  completed() {
    return WorkItemEventStates.COMPLETED;
  },
  collected() {
    return WorkItemEventStates.COLLECTED;
  },
  received() {
    return WorkItemEventStates.RECEIVED;
  },
  planned() {
    return WorkItemEventStates.PLANNED;
  },
  adminClass() {
    if (authorization.inAdminMode()) {
      return 'admin-mode';
    }
    return '';
  },
  filter() {
    return Template.instance().filter.get();
  },
  selectedReceivedItems() {
    return Template.instance().selectedReceivedItems;
  },
  selectedIncomingItems() {
    return Template.instance().selectedIncomingItems;
  },
  selectedCompletedItems() {
    return Template.instance().selectedCompletedItems;
  },
  selectedInProgressItems() {
    return Template.instance().selectedInProgressItems;
  },
  selectedCollectedItems() {
    return Template.instance().selectedCollectedItems;
  },
  moreDetailsVisibility() {
    return Template.instance().moreDetailsVisibility;
  },
  moreDetailsButtonTitle() {
    return Template.instance().moreDetailsVisibility.get() ? 'Hide' : 'Show';
  },
});

Template.workItemOverview.events({
  'click #moreDetailsToggleButton': () => {
    const templateInstance = Template.instance();
    templateInstance.moreDetailsVisibility.set(!templateInstance.moreDetailsVisibility.get());
  },
});
