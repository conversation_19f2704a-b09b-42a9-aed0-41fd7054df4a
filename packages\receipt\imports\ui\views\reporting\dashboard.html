<template name="dashboard">

  <div class="row" style="margin-bottom:0px;">
    <div class="col m4">
      <h5 class="thin">Summary
      </h5>
    </div>

    <div class="col m4">
      <h5 class="left"><i class="material-icons" style=" cursor: pointer; margin-left:10px; font-size:1.8rem;" id="backMonth">chevron_left</i></h5>
      {{#if isCurrentMonth}}

      <h5 class="right"><i class="material-icons" style="margin-right:10px; visibility: hidden; font-size:1.8rem;" id="forwardMonth">
           chevron_right </i></h5>

      {{else}}

      <h5 class="right"><i class="small material-icons" style="cursor: pointer; margin-right:10px; font-size:1.8rem;" id="forwardMonth">
            chevron_right </i></h5>
      {{/if}}
      <h5 class="thin" style="text-align: center;">
        {{selectedMonth}}
      </h5>
    </div>
  </div>
  <div class="row" style="height:40%;">
    <div class="col m4 offset-m2" style="height:100%;">
      <div class="card-panel valign-wrapper dashboard-summary-container" style="height:100%;" id="containersProcessed">
        <div class="valign" style="width:100%;">
          <div class="row" style="margin-bottom:0px;">
            <div class="col s12" style="text-align:center">
              <h5 class="thin">Containers Processed</h5>
            </div>
            <div class="col s12" style="text-align:center">
              <h2 style="margin-bottom: 0px;" class="col s4">{{containersProcessed.in}}</h2>
              <h2 style="margin-bottom: 0px;" class="col s4">{{containersProcessed.processed}}</h2>
              <h2 style="margin-bottom: 0px;" class="col s4">{{containersProcessed.out}}</h2>
              <h5 style="margin-top: 5px;" class="col s4 thin">IN</h5>
              <h5 style="margin-top: 5px;" class="col s4 thin">PROC.</h5>
              <h5 style="margin-top: 5px;" class="col s4 thin">OFF HIRED</h5>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col m4" style="height:100%;">
      <div class="card-panel valign-wrapper dashboard-summary-container" style="height:100%;" id="ncrsSummary">
        <div class="valign" style="width:100%;">
          <div class="row" style="margin-bottom:0px;">
            <div class="col s12" style="text-align:center">
              <h5 class="thin">NCRs</h5>
            </div>
            <div class="col s12" style="text-align:center">
              <h2>{{totalNcrs}}</h2>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

</template>