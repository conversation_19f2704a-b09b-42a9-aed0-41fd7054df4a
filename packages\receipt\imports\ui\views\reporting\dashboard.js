import './dashboard.html';

import { $ } from 'meteor/jquery';
import { CompanyProfiles } from '../../../api/company-profiles/company-profiles';
import { Meteor } from 'meteor/meteor';
import { Template } from 'meteor/templating';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { currentSiteProfile } from '../../../ui/helpers/current-site-profile';

Template.dashboard.onCreated(function onCreated() {
  const template = this;
  template.selectedClient = new ReactiveVar;
  let selectedMonth;

  const clientQueryString = FlowRouter.getQueryParam('client');
  const monthQueryString = FlowRouter.getQueryParam('month');

  const curentMoment = moment();

  if (clientQueryString && clientQueryString.length > 0) {
    template.selectedClient.set(clientQueryString);
  }
  if (monthQueryString && monthQueryString.length === 6) {
    selectedMonth = moment(monthQueryString, 'YYYYMM');
    if (selectedMonth.year() >= curentMoment.year() && selectedMonth.month() > curentMoment.month()) {
      selectedMonth = curentMoment;
    }
  } else {
    selectedMonth = curentMoment;
  }

  const defaultFromDate = moment(selectedMonth).startOf('month').toDate();
  const defaultToDate = moment(selectedMonth).endOf('month').toDate();

  template.fromDate = new ReactiveVar;
  template.fromDate.set(defaultFromDate);
  template.toDate = new ReactiveVar;
  template.toDate.set(defaultToDate);

  template.summaryReport = new ReactiveVar;
  template.beyondTargetTimeSummary = new ReactiveVar;
  template.containersProcessedReport = new ReactiveVar;

  refreshSummary(template);
  refreshBeyondTargetTimeSummary(template);
  refreshContainersProcessedSummary(template);
});

Template.dashboard.onRendered(function onRendered() {
  const template = this;
  template.$('#client').val(template.selectedClient.get());
  template.$('#client').material_select();
});

Template.dashboard.events({
  'click #receivedToReadySummary': function onClick(e, template) {
    e.preventDefault();

    const query = {
      month: moment(Template.instance().fromDate.get()).format('YYYYMM'),
    };

    const client = Template.instance().selectedClient.get();
    if (client) {
      query.client = client;
    }

    FlowRouter.go(
      'receivedToCompleted',
      {},
      query,
    );
  },
  'click #receivedToCollectedSummary': function onClick(e) {
    e.preventDefault();

    const query = {
      month: moment(Template.instance().fromDate.get()).format('YYYYMM'),
    };

    const client = Template.instance().selectedClient.get();
    if (client) {
      query.client = client;
    }

    FlowRouter.go(
      'receivedToCollected',
      {},
      query,
    );
  },
  'click #completedToCollectedSummary': function onClick(e) {
    e.preventDefault();

    const query = {
      month: moment(Template.instance().fromDate.get()).format('YYYYMM'),
    };

    const client = Template.instance().selectedClient.get();
    if (client) {
      query.client = client;
    }
    FlowRouter.go(
      'completedToCollected',
      {},
      query,
    );
  },
  'click #ncrsSummary': function onClick(e) {
    e.preventDefault();

    const query = {
      month: moment(Template.instance().fromDate.get()).format('YYYYMM'),
    };

    const client = Template.instance().selectedClient.get();
    if (client) {
      query.client = client;
    }
    FlowRouter.go(
      'ncrs',
      {},
      query,
    );
  },
  'click #receivedToCollectedBeyondTargetTime': function onClick(e) {
    e.preventDefault();

    const query = {
      month: moment(Template.instance().fromDate.get()).format('YYYYMM'),
    };

    const client = Template.instance().selectedClient.get();
    if (client) {
      query.client = client;
    }

    FlowRouter.go(
      'receivedToCollectedBeyondTargetTime',
      {},
      query,
    );
  },
  'click #containersProcessed': function onClick(event) {
    event.preventDefault();

    const query = {
      month: moment(Template.instance().fromDate.get()).format('YYYYMM'),
    };

    const client = Template.instance().selectedClient.get();
    if (client) {
      query.client = client;
    }

    FlowRouter.go(
      'containersProcessed',
      {},
      query,
    );
  },
});

Template.dashboard.helpers({
  selectedMonth() {
    return moment(Template.instance().fromDate.get()).format('MMMM YYYY');
  },

  isCurrentMonth() {
    const currentFromMoment = moment(Template.instance().fromDate.get());
    const currentMoment = moment();

    return currentFromMoment.month() === currentMoment.month() &&
      currentFromMoment.year() === currentMoment.year();
  },

  receivedToCollected() {
    const summary = Template.instance().summaryReport.get();

    if (summary && summary.averageReceivedToCollected) {
      return Math.ceil((((summary.averageReceivedToCollected / 1000) / 60) / 60)) + ' Hrs';
    }
    return 'N/A';
  },

  receivedToCompleted() {
    const summary = Template.instance().summaryReport.get();

    if (summary && summary.averageReceivedToCompleted) {
      return Math.ceil((((summary.averageReceivedToCompleted / 1000) / 60) / 60)) + ' Hrs';
    }
    return 'N/A';
  },

  completedToCollected() {
    const summary = Template.instance().summaryReport.get();

    if (summary && summary.averageCompletedToCollected) {
      return Math.ceil((((summary.averageCompletedToCollected / 1000) / 60) / 60)) + ' Hrs';
    }
    return 'N/A';
  },

  receivedToCollectedBeyondTargetTime() {
    const summary = Template.instance().beyondTargetTimeSummary.get();

    if (summary) {
      if (summary.totalProcessed > 0) {
        const percentage = ((summary.totalBeyondTargetTime / summary.totalProcessed) * 100).toFixed(1);
        return summary.totalBeyondTargetTime + ' (' + percentage + '%)';
      } else {
        return '0 (0%)';
      }
    }
    return 'N/A';
  },

  totalNcrs() {
    const summary = Template.instance().summaryReport.get();

    if (summary && (summary.totalNcrFails || summary.totalNcrFails === 0)) {
      return summary.totalNcrFails;
    }
    return 'N/A';
  },

  containersProcessed() {
    const containersProcessedSummary = Template.instance().containersProcessedReport.get();

    if (containersProcessedSummary && (containersProcessedSummary)) {
      return containersProcessedSummary;
    }
    return {
      in: '-',
      processed: '-',
      out: '-',
    };
  },

  clients() {
    const siteProfile = currentSiteProfile();

    if (siteProfile) {
      const siteClients = siteProfile.configuration.clients;
      return _.sortBy(siteClients, (client) => client.name);
    }
    return [];
  },
});

Template.dashboard.events({
  'click #backMonth': function onClick(e, template) {
    var currentFromMoment = moment(template.fromDate.get());

    currentFromMoment.add(-1, 'months');

    template.fromDate.set(moment(currentFromMoment).startOf('month').toDate());
    template.toDate.set(moment(currentFromMoment).endOf('month').toDate());

    refreshSummary(template);
    refreshBeyondTargetTimeSummary(template);
    refreshContainersProcessedSummary(template);
  },

  'click #forwardMonth': function onClick(e, template) {
    var currentFromMoment = moment(template.fromDate.get());

    currentFromMoment.add(1, 'months');

    template.fromDate.set(moment(currentFromMoment).startOf('month').toDate());
    template.toDate.set(moment(currentFromMoment).endOf('month').toDate());

    refreshSummary(template);
    refreshBeyondTargetTimeSummary(template);
    refreshContainersProcessedSummary(template);
  },

  'change #client': function onChange(e, template) {
    template.selectedClient.set(template.$('#client').val());

    refreshSummary(template);
    refreshBeyondTargetTimeSummary(template);
    refreshContainersProcessedSummary(template);
  },

});

var refreshContainersProcessedSummary = function refreshContainersProcessedSummary(template) {
  var fromDate = template.fromDate.get();
  var toDate = template.toDate.get();
  var client = template.selectedClient.get();
  var companyProfileId = CompanyProfiles.findOne()._id;
  var siteProfileId = currentSiteProfile()._id;

  Meteor.call('containersProcessedReport', companyProfileId, siteProfileId, fromDate, toDate, client, function (err, res) {
    template.containersProcessedReport.set(res);
  });
};

var refreshSummary = function refreshSummary(template) {
  var fromDate = template.fromDate.get();
  var toDate = template.toDate.get();
  var client = template.selectedClient.get();
  var companyProfileId = CompanyProfiles.findOne()._id;
  var siteProfileId = currentSiteProfile()._id;

  Meteor.call('summaryReport', companyProfileId, siteProfileId, fromDate, toDate, client, function (err, res) {
    template.summaryReport.set(res);
  });
};

var refreshBeyondTargetTimeSummary = function refreshBeyondTargetTimeSummary(template) {
  var fromDate = template.fromDate.get();
  var toDate = template.toDate.get();
  var client = template.selectedClient.get();

  var companyProfileId = CompanyProfiles.findOne()._id;
  var siteProfileId = currentSiteProfile()._id;

  Meteor.call('internalTurnaroundBeyondTargetTimeSummaryReport', companyProfileId, siteProfileId, fromDate, toDate, client, function (err, res) {
    console.log(res);
    template.beyondTargetTimeSummary.set(res);
  });
};
