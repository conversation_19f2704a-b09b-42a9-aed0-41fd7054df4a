import './completed-to-collected.html';

import { $ } from 'meteor/jquery';
import { CompanyProfiles } from '../../../../api/company-profiles/company-profiles';
import { Meteor } from 'meteor/meteor';
import { Template } from 'meteor/templating';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { currentSiteProfile } from '../../../../ui/helpers/current-site-profile';

var currentChart;

Template.completedToCollected.onCreated(function onCreated() {
  const template = this;

  template.selectedClient = new ReactiveVar;
  template.fromDate = new ReactiveVar;
  template.toDate = new ReactiveVar;

  let selectedMonth;

  const clientQueryString = FlowRouter.getQueryParam('client');
  const monthQueryString = FlowRouter.getQueryParam('month');

  const curentMoment = moment();

  if (clientQueryString && clientQueryString.length > 0) {
    template.selectedClient.set(clientQueryString);
  }
  if (monthQueryString && monthQueryString.length === 6) {
    selectedMonth = moment(monthQueryString, 'YYYYMM');
    if (selectedMonth.year() >= curentMoment.year() && selectedMonth.month() > curentMoment.month()) {
      selectedMonth = curentMoment;
    }
  } else {
    selectedMonth = curentMoment;
  }

  const defaultFromDate = moment(selectedMonth).startOf('month').toDate();
  const defaultToDate = moment(selectedMonth).endOf('month').toDate();

  template.fromDate.set(defaultFromDate);
  template.toDate.set(defaultToDate);
});

Template.completedToCollected.onRendered(function onRendered() {
  const template = this;
  template.$('#client').dropdown();

  refreshChart(template);
});

Template.completedToCollected.helpers({

  selectedMonth() {
    return moment(Template.instance().fromDate.get()).format('MMMM YYYY');
  },

  isCurrentMonth() {
    const currentFromMoment = moment(Template.instance().fromDate.get());
    const currentMoment = moment();

    return currentFromMoment.month() === currentMoment.month() &&
      currentFromMoment.year() === currentMoment.year();
  },

  clients() {
    const siteProfile = currentSiteProfile();

    if (siteProfile) {
      const siteClients = siteProfile.configuration.clients;
      return _.sortBy(siteClients, (client) => client.name);
    }
    return [];
  },
});

Template.completedToCollected.events({

  'click #backMonth': function onClick(e, templateInstance) {
    var currentFromMoment = moment(templateInstance.fromDate.get());

    currentFromMoment.add(-1, 'months');

    templateInstance.fromDate.set(moment(currentFromMoment).startOf('month').toDate());
    templateInstance.toDate.set(moment(currentFromMoment).endOf('month').toDate());

    refreshChart(templateInstance);
  },
  'click #forwardMonth': function onClick(e, templateInstance) {
    var currentFromMoment = moment(templateInstance.fromDate.get());

    currentFromMoment.add(1, 'months');

    templateInstance.fromDate.set(moment(currentFromMoment).startOf('month').toDate());
    templateInstance.toDate.set(moment(currentFromMoment).endOf('month').toDate());

    refreshChart(templateInstance);
  },
  'click #toDashboard': function onClick(e, templateInstance) {
    var query = {
      month: moment(Template.instance().fromDate.get()).format('YYYYMM'),
    };

    var client = Template.instance().selectedClient.get();
    if (client) {
      query.client = client;
    }

    FlowRouter.go(
      'dashboard',
      {},
      query,
    );
  },
  'change #client': function onChange(e, templateInstance) {
    templateInstance.selectedClient.set($('#client').dropdown('get value'));
    refreshChart(templateInstance);
  },
});

var refreshChart = function refreshChart(template) {
  var fromDate = template.fromDate.get();
  var toDate = template.toDate.get();
  var client = template.selectedClient.get();
  var companyProfileId = CompanyProfiles.findOne()._id;
  var siteProfileId = currentSiteProfile()._id;

  if (client) {
    Meteor.call('completedToCollectedPerWorkItemTypeReport', companyProfileId, siteProfileId, fromDate, toDate, client, (err, res) => {
      renderWorkItemTypeChart(res, fromDate);
    });
  } else {
    Meteor.call('completedToCollectedPerClientReport', companyProfileId, siteProfileId, fromDate, toDate, (err, res) => {
      renderChart(res, fromDate, template);
    });
  }
};

var renderChart = function renderChart(res, currentDate, template) {
  var sortedResult = _.sortBy(res, (a) => a._id.clientName);

  var clientTurnaroundResults = sortedResult.map((clientTurnaroundResult) => ({
    clientId: clientTurnaroundResult._id.clientId,
    clientName: clientTurnaroundResult._id.clientName,
    averageTurnaround: Math.ceil((((clientTurnaroundResult.averageTurnaround / 1000) / 60) / 60)),
    minTurnaround: Math.ceil((((clientTurnaroundResult.minTurnaround / 1000) / 60) / 60)),
    maxTurnaround: Math.ceil((((clientTurnaroundResult.maxTurnaround / 1000) / 60) / 60)),
    processed: clientTurnaroundResult.processed,
  }));

  var chart = AmCharts.makeChart('chartContainer', {
    type: 'serial',
    theme: 'none',
    dataProvider: clientTurnaroundResults,
    valueAxes: [{
      gridColor: '#FFFFFF',
      gridAlpha: 0,
      dashLength: 0,
      integersOnly: true,
      title: 'Average Turnaround (Hours)',
      totalText: '[[total]]',
    }],
    gridAboveGraphs: true,
    startDuration: 0,
    graphs: [{
      balloonFunction(graphDataItem) {
        if (graphDataItem && graphDataItem.values && graphDataItem.values.total > 0) {
          return '<div style="margin: 5px; font - size:19px;"><span style="font- size:13px;">' +
            graphDataItem.category + '</span><br>' + graphDataItem.values.total + ' Hours<br>' +
            graphDataItem.dataContext.processed + ' Processed </div>' + '<ul class="right"><li style="text- align:right;">Min: ' +
            graphDataItem.dataContext.minTurnaround + ' hours</li><li style="text- align:right;">Max: ' +
            graphDataItem.dataContext.maxTurnaround + ' hours</li></ul>';
        }
        return '';
      },
      fillAlphas: 0.8,
      lineAlpha: 0.2,
      type: 'column',
      valueField: 'averageTurnaround',
      labelText: '[[value]]',
      labelPosition: 'top',
      labelFunction(graphDataItem) {
        if (graphDataItem.values.total == 0) {
          return '0 Processed';
        } else {
          return graphDataItem.values.total + ' Hours (' + graphDataItem.dataContext.processed + ')';
        }
      },
    }],
    chartCursor: {
      categoryBalloonEnabled: false,
      cursorAlpha: 0,
      zoomable: false,
    },
    categoryField: 'clientName',
    categoryAxis: {
      gridPosition: 'start',
      gridAlpha: 0,
      tickPosition: 'start',
      tickLength: 5,
      title: 'Customer',
    },
    export: {
      enabled: !Meteor.isCordova,
      fileName: 'CompletedToCollected_' + moment(currentDate).format('MMMM_YYYY'),
      libs: {
        path: 'https://www.amcharts.com/lib/3/plugins/export/libs/',
      },
    },

  });

  chart.addListener('clickGraphItem', (event) => {
    template.$('#client').val(event.item.dataContext.clientId);
    template.$('#client').material_select();

    template.selectedClient.set(template.$('#client').val());

    refreshChart(template);
  });
};

var renderWorkItemTypeChart = function renderWorkItemTypeChart(res, currentDate) {
  var sorted = _.sortBy(res, function (a) {
    return a._id.workItemType === 'Unknown' ? 'zzz' : a._id.workItemType;
  });

  var workItemTypes = sorted.map((workItemTypeResult) => ({
    workItemTypeResult: workItemTypeResult._id.workItemType,
    averageTurnaround: Math.ceil((((workItemTypeResult.averageTurnaround / 1000) / 60) / 60)),
    minTurnaround: Math.ceil((((workItemTypeResult.minTurnaround / 1000) / 60) / 60)),
    maxTurnaround: Math.ceil((((workItemTypeResult.maxTurnaround / 1000) / 60) / 60)),
    processed: workItemTypeResult.processed,
  }));

  var chart = AmCharts.makeChart('chartContainer', {
    type: 'serial',
    theme: 'none',
    dataProvider: workItemTypes,
    valueAxes: [{
      gridColor: '#FFFFFF',
      gridAlpha: 0,
      dashLength: 0,
      integersOnly: true,
      title: 'Average Turnaround (Hours)',
      totalText: '[[total]]',
    }],
    gridAboveGraphs: true,
    startDuration: 0,
    graphs: [{
      balloonFunction(graphDataItem) {
        if (graphDataItem && graphDataItem.values && graphDataItem.values.total > 0) {
          return '<div style="margin: 5px; font-size:19px;"><span style="font- size:13px;">' +
            graphDataItem.category + '</span><br>' + graphDataItem.values.total + ' Hours<br>' +
            graphDataItem.dataContext.processed + ' Processed </div>' + '<ul class="right"><li style="text- align:right;">Min: ' +
            graphDataItem.dataContext.minTurnaround + ' hours</li><li style="text- align:right;">Max: ' +
            graphDataItem.dataContext.maxTurnaround + ' hours</li></ul>';
        }
        return '';
      },
      fillAlphas: 0.8,
      lineAlpha: 0.2,
      type: 'column',
      valueField: 'averageTurnaround',
      labelText: '[[value]]',
      labelPosition: 'top',
      labelFunction(graphDataItem) {
        if (graphDataItem.values.total == 0) {
          return '0 Processed';
        } else {
          return graphDataItem.values.total + ' Hours (' + graphDataItem.dataContext.processed + ')';
        }
      },
    }],
    chartCursor: {
      categoryBalloonEnabled: false,
      cursorAlpha: 0,
      zoomable: false,
    },
    categoryField: 'workItemType',
    categoryAxis: {
      gridPosition: 'start',
      gridAlpha: 0,
      tickPosition: 'start',
      tickLength: 5,
      title: 'Container Type',
    },
    export: {
      enabled: !Meteor.isCordova,
      fileName: 'CompletedToCollected_' + moment(currentDate).format('MMMM_YYYY'),
      libs: {
        path: 'https://www.amcharts.com/lib/3/plugins/export/libs/',
      },
    },
  });
};
