import './containers-processed.html';

import { AmCharts } from 'meteor/em0ney:amcharts';
import { CompanyProfiles } from '../../../../api/company-profiles/company-profiles';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { LoggerFactory } from '../../../../shared/logger-factory';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import { Template } from 'meteor/templating';
import { currentSiteProfile } from '../../../../ui/helpers/current-site-profile';
import { moment } from 'meteor/momentjs:moment';

const logger = LoggerFactory.getLogger(__filename);

function getMonthlySummary(countsPerDay) {
  const monthlySummary = {
    containersIn: 0,
    containersProcessed: 0,
    containersOut: 0,
  };

  for (const daySummary of countsPerDay) {
    monthlySummary.containersIn += daySummary.containersIn;
    monthlySummary.containersProcessed += daySummary.containersProcessed;
    monthlySummary.containersOut += daySummary.containersOut;
  }
  return monthlySummary;
}

function renderContainersProcessedChart(res, currentDate, templateInstance) {
  const containersProcessedSummaries = res;

  templateInstance.containersProcessedSummary.set(getMonthlySummary(res));
  const momentForMonth = moment(currentDate);
  AmCharts.makeChart('chartContainer', {
    type: 'serial',
    dataProvider: containersProcessedSummaries,
    fontSize: 14,
    legend: {
      autoMargins: true,
      borderAlpha: 0,
      equalWidths: false,
      horizontalGap: 10,
      markerSize: 15,
      useGraphSettings: true,
      valueAlign: 'center',
      align: 'center',
      valueWidth: 0,
    },
    valueAxes: [{
      gridColor: '#FFFFFF',
      fontSize: 17,
      integersOnly: true,
      title: 'Number of Containers',
    }],
    gridAboveGraphs: true,
    startDuration: 0,
    graphs: [
      {
        showBalloon: false,
        fillAlphas: 0.8,
        lineAlpha: 0.2,
        type: 'column',
        title: 'Containers In',
        valueField: 'containersIn',
      },
      {
        showBalloon: false,
        fillAlphas: 0.8,
        lineAlpha: 0.2,
        type: 'column',
        title: 'Containers Processed',
        valueField: 'containersProcessed',
      },
      {
        showBalloon: false,
        fillAlphas: 0.8,
        lineAlpha: 0.2,
        type: 'column',
        title: 'Containers Offhired',
        valueField: 'containersOut',
      },
      {
        balloonFunction: (graphDataItem) => {
          momentForMonth.set('date', graphDataItem.dataContext.day);
          const dateFormatted = momentForMonth.format('DD/MM/YYYY');
          const balloonText =
                        `<span style="font-size: 16px; font-weight:700; text-decoration:underline">${dateFormatted}</span><div style="text-align: left">
            <span style="margin-bottom: 5px; display: block;"><span style="font-size:14px; margin-bottom:0px;"><b>Containers In: </b></span> <span>${graphDataItem.dataContext.containersIn}</span></span>
            <span style="margin-bottom: 5px; display: block;"><span style="font-size:14px"><b>Containers Processed: </b></span> <span>${graphDataItem.dataContext.containersProcessed}</span></span>
            <span style="margin-bottom: 5px; display: block;"><span style="font-size:14px"><b>Containers Out: </b></span> <span> ${graphDataItem.dataContext.containersOut} </span></span>
            <span style="margin-bottom: 5px; display: block;"><span style="font-size:14px"><b>On Site: </b></span> <span> ${graphDataItem.dataContext.totalNumberOfContainersOnSite} </span></span>
            <span style="margin-bottom: 5px; display: block;"><span style="font-size:14px"><b>Available To Collect: </b></span><span>${graphDataItem.dataContext.totalNumberOfContainersAvailableToCollect}</span></span></div>`;
          return balloonText;
        },
        bullet: 'round',
        bulletBorderAlpha: 1,
        bulletColor: '#FFFFFF',
        useLineColorForBulletBorder: true,
        fillAlphas: 0,
        lineThickness: 2,
        lineAlpha: 1,
        bulletSize: 5,
        title: 'Total Number of Containers On Site',
        valueField: 'totalNumberOfContainersOnSite',
      },
      {
        showBalloon: false,
        bullet: 'round',
        bulletBorderAlpha: 1,
        bulletColor: '#FFFFFF',
        useLineColorForBulletBorder: true,
        fillAlphas: 0,
        lineThickness: 2,
        lineAlpha: 1,
        bulletSize: 5,
        title: 'Total Number of Containers Ready to Offhire',
        valueField: 'totalNumberOfContainersAvailableToCollect',
      },
    ],
    chartCursor: {
      categoryBalloonEnabled: false,
      cursorAlpha: 0,
      oneBalloonOnly: true,
    },
    categoryField: 'day',
    categoryAxis: {
      fontSize: 17,
      gridPosition: 'start',
      gridAlpha: 0,
      tickPosition: 'start',
      tickLength: 5,
      title: 'Date',
    },
    export: {
      enabled: !Meteor.isCordova,
      fileName: `ContainersProcessed_${moment(currentDate).format('MMMM_YYYY')}`,
      libs: {
        path: 'https://www.amcharts.com/lib/3/plugins/export/libs/',
      },
    },
  });
}

function refreshChart(templateInstance) {
  const fromDate = templateInstance.fromDate.get();
  const toDate = templateInstance.toDate.get();

  const client = templateInstance.selectedClient.get();
  const companyProfileId = CompanyProfiles.findOne()._id;
  const siteProfileId = currentSiteProfile()._id;

  Meteor.call(
    'containersProcessedPerCalendarDay',
    companyProfileId,
    siteProfileId,
    fromDate,
    toDate,
    client,
    (err, res) => renderContainersProcessedChart(res, fromDate, templateInstance)
  );
}

Template.containersProcessed.onCreated(function onCreated() {
  const template = this;

  template.selectedClient = new ReactiveVar;
  template.fromDate = new ReactiveVar;
  template.toDate = new ReactiveVar;
  template.containersProcessedSummary = new ReactiveVar;

  let selectedMonth;

  const clientQueryString = FlowRouter.getQueryParam('client');
  const monthQueryString = FlowRouter.getQueryParam('month');

  const currentMoment = moment();

  if (clientQueryString && clientQueryString.length > 0) {
    template.selectedClient.set(clientQueryString);
  }
  if (monthQueryString && monthQueryString.length === 6) {
    selectedMonth = moment(monthQueryString, 'YYYYMM');
    if (selectedMonth.year() >= currentMoment.year() &&
      selectedMonth.month() > currentMoment.month()) {
      selectedMonth = currentMoment;
    }
  } else {
    selectedMonth = currentMoment;
  }

  const defaultFromDate = moment(selectedMonth).startOf('month').toDate();
  const defaultToDate = moment(selectedMonth).endOf('month').toDate();

  template.fromDate.set(defaultFromDate);
  template.toDate.set(defaultToDate);
});

Template.containersProcessed.onRendered(function onRendered() {
  const templateInstance = this;

  templateInstance.$('#client').val(templateInstance.selectedClient.get());
  templateInstance.$('#client').material_select();

  refreshChart(templateInstance);
});

Template.containersProcessed.helpers({
  selectedMonth() {
    return moment(Template.instance().fromDate.get()).format('MMMM YYYY');
  },
  isCurrentMonth() {
    const currentFromMoment = moment(Template.instance().fromDate.get());
    const currentMoment = moment();

    return currentFromMoment.month() === currentMoment.month() &&
      currentFromMoment.year() === currentMoment.year();
  },
  clients() {
    const siteProfile = currentSiteProfile();

    if (siteProfile) {
      const siteClients = siteProfile.configuration.clients;
      return _.sortBy(siteClients, (client) => client.name);
    }
    return [];
  },
  containersIn() {
    const containersProcessedSummary = Template.instance().containersProcessedSummary.get();
    if (containersProcessedSummary) {
      return containersProcessedSummary.containersIn;
    }
    return 'N/A';
  },
  containersProcessed() {
    const containersProcessedSummary = Template.instance().containersProcessedSummary.get();
    if (containersProcessedSummary) {
      return containersProcessedSummary.containersProcessed;
    }
    return 'N/A';
  },
  containersOut() {
    const containersProcessedSummary = Template.instance().containersProcessedSummary.get();
    if (containersProcessedSummary) {
      return containersProcessedSummary.containersOut;
    }
    return 'N/A';
  },
});

Template.containersProcessed.events({
  'click #toDashboard': function onClick(event, templateInstance) {
    const query = {
      month: moment(templateInstance.fromDate.get()).format('YYYYM'),
    };

    const client = Template.instance().selectedClient.get();
    if (client) {
      query.client = client;
    }

    FlowRouter.go(
      'dashboard',
      {},
      query,
    );
  },
  'click #backMonth': function onClick(event, templateInstance) {
    const currentFromMoment = moment(templateInstance.fromDate.get());

    currentFromMoment.add(-1, 'months');

    templateInstance.fromDate.set(moment(currentFromMoment).startOf('month').toDate());
    templateInstance.toDate.set(moment(currentFromMoment).endOf('month').toDate());

    refreshChart(templateInstance);
  },
  'click #forwardMonth': function onClick(event, templateInstance) {
    const currentFromMoment = moment(templateInstance.fromDate.get());

    currentFromMoment.add(1, 'months');

    templateInstance.fromDate.set(moment(currentFromMoment).startOf('month').toDate());
    templateInstance.toDate.set(moment(currentFromMoment).endOf('month').toDate());

    refreshChart(templateInstance);
  },
  'change #client': function onChange(event, templateInstance) {
    templateInstance.selectedClient.set(templateInstance.$('#client').val());

    refreshChart(templateInstance);
  },
});
