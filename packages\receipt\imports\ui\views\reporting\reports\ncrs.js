import './ncrs.html';

import { $ } from 'meteor/jquery';
import { CompanyProfiles } from '../../../../api/company-profiles/company-profiles';
import { Meteor } from 'meteor/meteor';
import { Template } from 'meteor/templating';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { currentSiteProfile } from '../../../../ui/helpers/current-site-profile';

const ncrCheckToDisplayName = {
  doesNotRequireInspection: 'Inspection plate shows at least 30 days before statutory examination is due',
  freeFromExcessiveCorrosionOrHoles: 'Units free from excessive corrosion or holes',
  allDrainageHolesAreClear: 'All drainage holes clear on open CCU',
  liftingSetsProperlyFitted: 'All lifting sets properly fitted and configured',
  slingsVisuallyInspected: 'Slings visually inspected for damage & split pins on shackles correctly fitted',
  droppedObjectsRemoved: 'Removed all potential dropped objects debris on the lift or items strapped to lift',
  hasDestinationLabelBeenAdded: 'Destination label added',
  itemsSecured: 'Items packed and secured to prevent movement/damage in “Worst Weather” conditions',
  dgLabelsArePresent: 'Dangerous Goods pre-notified and container correctly labelled on all four sides',
  retainingNetSecure: 'Cargo retaining net secure and positioned to prevent goods falling out',
  snagHazardsPrevented: 'Snag Hazards prevented',
  doorsSecured: 'Doors and locking mechanisms are secure with Secondary Securing Device attached.',
  loadLiftsHorizontally: 'Load lifts horizontally',
  weightBelowMaximumWeight: 'Actual Gross Weight is less than or equal to Maximum Gross Weight',
  heavyLiftTagHasBeenAdded: 'If Actual Weight is seven (7) tonnes or above, Heavy Lift pennant attached',
  tubularsSlungCorrectly: 'Tubulars slung & secured correctly and checked for potential dropped objects externally and internally',
  fulfilledOperatorsRequirementsForHiredAndPortable: 'Fulfilled the Operator’s requirements if the cargo is classified as Hired and Portable Equipment',
};

Template.ncrs.onCreated(function () {
  const template = this;

  template.selectedClient = new ReactiveVar;
  template.fromDate = new ReactiveVar;
  template.toDate = new ReactiveVar;

  let selectedMonth;

  const clientQueryString = FlowRouter.getQueryParam('client');
  const monthQueryString = FlowRouter.getQueryParam('month');

  const curentMoment = moment();

  if (clientQueryString && clientQueryString.length > 0) {
    template.selectedClient.set(clientQueryString);
  }
  if (monthQueryString && monthQueryString.length === 6) {
    selectedMonth = moment(monthQueryString, 'YYYYMM');

    if (selectedMonth.year() >= curentMoment.year() &&
      selectedMonth.month() > curentMoment.month()) {
      selectedMonth = curentMoment;
    }
  } else {
    selectedMonth = curentMoment;
  }

  const defaultFromDate = moment(selectedMonth).startOf('month').toDate();
  const defaultToDate = moment(selectedMonth).endOf('month').toDate();

  template.fromDate.set(defaultFromDate);
  template.toDate.set(defaultToDate);
});

Template.ncrs.onRendered(function onRendered() {
  const template = this;

  refreshChart(template);
});

Template.ncrs.helpers({
  selectedMonth() {
    return moment(Template.instance().fromDate.get()).format('MMMM YYYY');
  },
  isCurrentMonth() {
    const currentFromMoment = moment(Template.instance().fromDate.get());
    const currentMoment = moment();
    return currentFromMoment.month() === currentMoment.month() &&
      currentFromMoment.year() === currentMoment.year();
  },
  isViewingBreakdown() {
    const client = Template.instance().selectedClient.get();

    return client !== null && client !== undefined && client.length;
  },
  dateHeaderClass() {
    const client = Template.instance().selectedClient.get();

    const isViewingBreakdown = client !== null && client !== undefined && client.length;

    return isViewingBreakdown ? '' : 'five wide column';
  },
  pageHeaderText() {
    const client = Template.instance().selectedClient.get();

    const isViewingBreakdown = client !== null && client !== undefined && client.length;

    return isViewingBreakdown ? `${client} NCR Breakdown` : 'NCRs Overview';
  },
});

Template.ncrs.events({
  change(e, templateInstance) {
    if (e.currentTarget.id === 'viewOverall') {
      templateInstance.$('#viewBreakdown').prop('checked', false);
      templateInstance.$('#viewOverall').prop('checked', true);
      refreshChart(templateInstance, false);
    } else if (e.currentTarget.id === 'viewBreakdown') {
      templateInstance.$('#viewOverall').prop('checked', false);
      templateInstance.$('#viewBreakdown').prop('checked', true);

      refreshChart(templateInstance, true);
    }
  },
  'click #backMonth': function onClick(e, templateInstance) {
    const currentFromMoment = moment(templateInstance.fromDate.get());

    currentFromMoment.add(-1, 'months');

    templateInstance.fromDate.set(moment(currentFromMoment).startOf('month').toDate());
    templateInstance.toDate.set(moment(currentFromMoment).endOf('month').toDate());

    refreshChart(templateInstance, templateInstance.$('#viewBreakdown').prop('checked'));
  },
  'click #forwardMonth': function onClick(e, templateInstance) {
    const currentFromMoment = moment(templateInstance.fromDate.get());

    currentFromMoment.add(1, 'months');

    templateInstance.fromDate.set(moment(currentFromMoment).startOf('month').toDate());
    templateInstance.toDate.set(moment(currentFromMoment).endOf('month').toDate());

    refreshChart(templateInstance, templateInstance.$('#viewBreakdown').prop('checked'));
  },
  'click #toDashboard': function onClick(e, templateInstance) {
    templateInstance.selectedClient.set(null);
    refreshChart(templateInstance, true);
  },
  'change #client': function onChange(e, templateInstance) {
    templateInstance.selectedClient.set(templateInstance.$('#client').val());
    refreshChart(templateInstance, templateInstance.$('#viewBreakdown').prop('checked'));
  },
});

const refreshChart = function refreshChart(template, isBreakdown) {
  const fromDate = template.fromDate.get();
  const toDate = template.toDate.get();
  const client = template.selectedClient.get();
  const companyProfileId = CompanyProfiles.findOne()._id;
  const siteProfileId = currentSiteProfile()._id;

  if (client) {
    Meteor.call(
      'vendorBreakdownOfTypesOfFailure',
      companyProfileId,
      siteProfileId,
      client,
      fromDate,
      toDate,
      (err, res) => { renderNcrBreakdownChart(res, fromDate, template); },
    );
  } else {
    Meteor.call('ncrsPerClientReport', companyProfileId, siteProfileId, fromDate, toDate, (err, res) => {
      renderChart(res, fromDate, isBreakdown, template);
    });
  }
};

const renderChart = function (res, currentDate, isBreakdown, template) {
  const sorted = _.sortBy(res, (a) => {
    return a._id.clientName;
  });

  const vendorFails = sorted.map((vendorBreakdown) => {
    return {
      vendorName: vendorBreakdown._id.clientName,
      failed: vendorBreakdown.failed,
      processed: vendorBreakdown.processed,
    };
  });

  const prototypeGraph = {
    balloonFunction(graphDataItem) {
      if (graphDataItem && graphDataItem.values && graphDataItem.values.total > 0) {
        return "<div style='margin:5px; font-size:19px;'><span style='font-size:13px;'>" +
          graphDataItem.category + '</span><br>' + graphDataItem.values.total + ' Fail(s)<br>' +
          graphDataItem.dataContext.processed + ' Processed </div>';
      }
      return '';
    },
    id: '1',
    fillAlphas: 0.8,
    lineAlpha: 0.2,
    type: 'column',
    valueField: 'failed',
    title: 'Fails',
    labelText: '[[value]]',
    labelPosition: 'top',
    labelFunction(graphDataItem) {
      return graphDataItem.values.total +
        ' (' +
        graphDataItem.dataContext.processed + ')';
    },
  };

  const graphs = [];
  let legend = null;

  graphs.push(prototypeGraph);

  var chart = AmCharts.makeChart('chartContainer', {
    type: 'serial',
    theme: 'none',
    dataProvider: vendorFails,
    legend,
    valueAxes: [{
      gridColor: '#FFFFFF',
      gridAlpha: 0,
      dashLength: 0,
      integersOnly: true,
      title: 'Fails',
      totalText: '[[total]]',
    }],
    gridAboveGraphs: true,
    startDuration: 0,
    graphs,
    chartCursor: {
      categoryBalloonEnabled: false,
      cursorAlpha: 0,
      zoomable: false,
    },
    categoryField: 'vendorName',
    categoryAxis: {
      gridPosition: 'start',
      gridAlpha: 0,
      tickPosition: 'start',
      tickLength: 5,
      labelRotation: 45,
      title: 'Vendor',
    },
  });

  AmCharts.checkEmptyData = function(chart) {
    if (0 == chart.dataProvider.length) {
    // set min/max on the value axis
      chart.valueAxes[0].minimum = 0;
      chart.valueAxes[0].maximum = 100;

      // add dummy data point
      var dataPoint = {
        dummyValue: 0,
      };
      dataPoint[chart.categoryField] = '';
      chart.dataProvider = [dataPoint];

      // add label
      chart.addLabel(0, '50%', 'No NCRs to report for month.', 'center');

      // set opacity of the chart div
      chart.chartDiv.style.opacity = 0.5;

      // redraw it
      chart.validateNow();
    }
  };

  AmCharts.checkEmptyData(chart);

  chart.addListener('clickGraphItem', function (event) {
    template.selectedClient.set(event.item.dataContext.vendorName);
    refreshChart(template, true);
  });
};

const renderNcrBreakdownChart = (res, currentDate, template) => {
  const graphData = res.map((check) => {
    return {
      ncrCheck: ncrCheckToDisplayName[check.ncrCheck] || check.ncrCheck,
      itemsFailedCheck: check.itemsFailedCheck,
      workItems: check.workItems,
    };
  });

  const prototypeGraph = {
    balloonFunction(graphDataItem) {
      if (graphDataItem && graphDataItem.values && graphDataItem.values.total > 0) {
        return "<div style='margin:5px; font-size:19px;'><span style='font-size:13px;'>" + graphDataItem.values.total + ' Item(s)<br>' +
          graphDataItem.dataContext.workItems
            .map((x) => `<hr/>${x.identifier}<br/>${moment(graphDataItem.dataContext.timestamp).format('DD/MM/YYYY HH:mm')}`)
            .toString()
            .split(',')
            .join('<br />') + '</div>';
      }
      return '';
    },
    id: '1',
    fillAlphas: 0.8,
    lineAlpha: 0.2,
    type: 'column',
    valueField: 'itemsFailedCheck',
    title: 'Fails',
    labelText: '[[value]]',
    labelPosition: 'top',
  };

  const graphs = [];
  let legend = null;

  graphs.push(prototypeGraph);

  var chart = AmCharts.makeChart('chartContainer', {
    type: 'serial',
    theme: 'none',
    rotate: true,
    dataProvider: graphData,
    legend,
    valueAxes: [{
      gridColor: '#FFFFFF',
      gridAlpha: 0,
      dashLength: 0,
      integersOnly: true,
      title: 'Failed Items',
    }],
    gridAboveGraphs: true,
    startDuration: 0,
    graphs,
    chartCursor: {
      categoryBalloonEnabled: false,
      cursorAlpha: 0,
      zoomable: false,
    },
    categoryField: 'ncrCheck',
    categoryAxis: {
      gridPosition: 'start',
      gridAlpha: 0,
      tickPosition: 'start',
      tickLength: 5,
      autoWrap: true,
      title: 'NCR Check',
      ignoreAxisWidth: true,
      autoWrap: true,
    },
    marginLeft: 600,
  });

  AmCharts.checkEmptyData = function checkEmptyData(chart) {
    if (0 == chart.dataProvider.length) {
    // set min/max on the value axis
      chart.valueAxes[0].minimum = 0;
      chart.valueAxes[0].maximum = 100;

      // add dummy data point
      var dataPoint = {
        dummyValue: 0,
      };
      dataPoint[chart.categoryField] = '';
      chart.dataProvider = [dataPoint];

      // add label
      chart.addLabel(0, '50%', 'No NCRs to report for month.', 'center');

      // set opacity of the chart div
      chart.chartDiv.style.opacity = 0.5;

      // redraw it
      chart.validateNow();
    }
  };

  AmCharts.checkEmptyData(chart);
};
