import './received-to-collected-beyond-target-time.html';

import { $ } from 'meteor/jquery';
import { CompanyProfiles } from '../../../../api/company-profiles/company-profiles';
import { Meteor } from 'meteor/meteor';
import { Template } from 'meteor/templating';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { currentSiteProfile } from '../../../../ui/helpers/current-site-profile';

var currentChart;

Template.receivedToCollectedBeyondTargetTime.created = function onCreated() {
  var template = this;

  template.selectedClient = new ReactiveVar;
  template.fromDate = new ReactiveVar;
  template.toDate = new ReactiveVar;

  var selectedMonth;

  var clientQueryString = FlowRouter.getQueryParam('client');
  var monthQueryString = FlowRouter.getQueryParam('month');

  var curentMoment = moment();

  if (clientQueryString && clientQueryString.length > 0) {
    template.selectedClient.set(clientQueryString);
  }
  if (monthQueryString && monthQueryString.length === 6) {
    selectedMonth = moment(monthQueryString, 'YYYYMM');
    if (selectedMonth.year() >= curentMoment.year() && selectedMonth.month() > curentMoment.month()) {
      selectedMonth = curentMoment;
    }
  } else {
    selectedMonth = curentMoment;
  }

  var defaultFromDate = moment(selectedMonth).startOf('month').toDate();
  var defaultToDate = moment(selectedMonth).endOf('month').toDate();

  template.fromDate.set(defaultFromDate);
  template.toDate.set(defaultToDate);
};

Template.receivedToCollectedBeyondTargetTime.rendered = function rendered() {
  var template = this;

  template.$('#client').val(template.selectedClient.get());
  template.$('#client').material_select();

  refreshChart(template);
};

Template.receivedToCollectedBeyondTargetTime.helpers({

  selectedMonth() {
    return moment(Template.instance().fromDate.get()).format('MMMM YYYY');
  },

  isCurrentMonth() {
    var currentFromMoment = moment(Template.instance().fromDate.get());
    var currentMoment = moment();

    return currentFromMoment.month() === currentMoment.month() &&
            currentFromMoment.year() === currentMoment.year();
  },

  clients() {
    var siteProfile = currentSiteProfile();

    if (siteProfile) {
      var siteClients = siteProfile.configuration.clients;

      return _.sortBy(siteClients, function (client) {
        return client.name;
      });
    } else {
      return [];
    }
  },
});

Template.receivedToCollectedBeyondTargetTime.events({

  'click #backMonth': function onClick(e, templateInstance) {
    var currentFromMoment = moment(templateInstance.fromDate.get());

    currentFromMoment.add(-1, 'months');

    templateInstance.fromDate.set(moment(currentFromMoment).startOf('month').toDate());
    templateInstance.toDate.set(moment(currentFromMoment).endOf('month').toDate());

    refreshChart(templateInstance);
  },

  'click #forwardMonth': function onClick(e, templateInstance) {
    var currentFromMoment = moment(templateInstance.fromDate.get());

    currentFromMoment.add(1, 'months');

    templateInstance.fromDate.set(moment(currentFromMoment).startOf('month').toDate());
    templateInstance.toDate.set(moment(currentFromMoment).endOf('month').toDate());

    refreshChart(templateInstance);
  },

  'click #toDashboard': function onClick(e, templateInstance) {
    var query = {
      month: moment(Template.instance().fromDate.get()).format('YYYYMM'),
    };

    var client = Template.instance().selectedClient.get();
    if (client) {
      query.client = client;
    }

    FlowRouter.go(
      'dashboard',
      {},
      query,
    );
  },

  'change #client': function onChange(e, templateInstance) {
    templateInstance.selectedClient.set(templateInstance.$('#client').val());
    refreshChart(templateInstance);
  },
});

var refreshChart = function refreshChart(template) {
  var fromDate = template.fromDate.get();
  var toDate = template.toDate.get();

  var client = template.selectedClient.get();
  var companyProfileId = CompanyProfiles.findOne()._id;
  var siteProfileId = currentSiteProfile()._id;

  if (client) {
    Meteor.call('internalTurnaroundBeyondTargetTimePerWorkItemTypeReport', companyProfileId, siteProfileId, fromDate, toDate, client, function(err, res) {
      renderWorkItemTypeChart(res, fromDate);
    });
  } else {
    Meteor.call('internalTurnaroundBeyondTargetTimePerClientReport', companyProfileId, siteProfileId, fromDate, toDate, function(err, res) {
      console.log(res);
      renderChart(res, fromDate, template);
    });
  }
};

var renderChart = function renderChart(res, currentDate, template) {
  console.log(res);
  var sorted = _.sortBy(res, function (a) {
    return a.offshoreInstallationName;
  });

  var clientTargetTimeSummaries = _.map(sorted, function (clientTargetTimeSummary) {
    return {
      offshoreInstallationName: clientTargetTimeSummary.offshoreInstallationName,
      clientTargetTimes: clientTargetTimeSummary.clientTargetTimes,
      processed: clientTargetTimeSummary.totalProcessed,
      beyondTargetTime: clientTargetTimeSummary.totalBeyondTargetTime,
      percentBeyondTarget:
        clientTargetTimeSummary.totalProcessed > 0 ?
          ((clientTargetTimeSummary.totalBeyondTargetTime / clientTargetTimeSummary.totalProcessed) * 100).toFixed(2) :
          0,
    };
  });

  var chart = AmCharts.makeChart('chartContainer', {
    type: 'serial',
    theme: 'none',
    dataProvider: clientTargetTimeSummaries,
    valueAxes: [{
      gridColor: '#FFFFFF',
      gridAlpha: 0,
      dashLength: 0,
      integersOnly: true,
      title: 'Beyond Target Time (%)',
      totalText: '[[total]]',
      minimum: 0,
      maximum: 100,
    }],
    gridAboveGraphs: true,
    startDuration: 0,
    graphs: [{
      balloonFunction(graphDataItem) {
        if (graphDataItem && graphDataItem.values && graphDataItem.values.total > 0) {
          var balloontext = "<div style='margin:5px; font-size:19px;'><span style='font-size:13px;'>" +
          graphDataItem.category +'</span><br>' + graphDataItem.values.total + ' %<br>' +
          graphDataItem.dataContext.beyondTargetTime  + '/' + graphDataItem.dataContext.processed + ' Completed </div>';

          var targetTimesLabel = "<ul class='right'>";
          _.each(graphDataItem.dataContext.clientTargetTimes, function (targetTime) {
            targetTimesLabel += "<li style='text-align:right;'>"+ targetTime.workItemTypeName + ': ' + targetTime.targetTimeInHours + ' Hours</li>';
          });

          balloontext += targetTimesLabel;

          return balloontext;
        } else {
          return '';
        }
      },
      fillAlphas: 0.8,
      lineAlpha: 0.2,
      type: 'column',
      valueField: 'percentBeyondTarget',
      labelText: '[[value]]',
      labelPosition: 'top',
      labelFunction(graphDataItem) {
        if (graphDataItem.dataContext.processed === 0) {
          return '0 Completed';
        } else {
          return graphDataItem.values.total + ' % (' + graphDataItem.dataContext.beyondTargetTime  + '/' + graphDataItem.dataContext.processed + ')';
        }
      },
    }],
    chartCursor: {
      categoryBalloonEnabled: false,
      cursorAlpha: 0,
      zoomable: false,
    },
    categoryField: 'offshoreInstallationName',
    categoryAxis: {
      gridPosition: 'start',
      gridAlpha: 0,
      tickPosition: 'start',
      tickLength: 5,
      title: 'Asset',
    },
    export: {
      enabled: !Meteor.isCordova,
      fileName: 'BeyondTarget_' + moment(currentDate).format('MMMM_YYYY'),
      libs: {
        path: 'https://www.amcharts.com/lib/3/plugins/export/libs/',
      },
    },

  });

  chart.addListener('clickGraphItem', function (event) {
    template.$('#client').val(event.item.dataContext.clientId);
    template.$('#client').material_select();

    template.selectedClient.set(template.$('#client').val());

    refreshChart(template);
  });
};

var renderWorkItemTypeChart = function renderWorkItemTypeChart(res, currentDate) {
  console.log(res);
  var sorted = _.sortBy(res, function (a) {
    return a.workItemType === 'Unknown' ? 'zzz' : a.workItemType;
  });

  var workItemTypeResults = _.map(sorted, function(workItemTypeResult) {
    return {
      workItemType: workItemTypeResult.workItemType,
      clientTargetTimes: workItemTypeResult.clientTargetTimes,
      processed: workItemTypeResult.totalProcessed,
      beyondTargetTime: workItemTypeResult.totalBeyondTargetTime,
      percentBeyondTarget:
        workItemTypeResult.totalProcessed > 0 ?
          ((workItemTypeResult.totalBeyondTargetTime / workItemTypeResult.totalProcessed) * 100).toFixed(2) :
          0,
    };
  });

  var chart = AmCharts.makeChart('chartContainer', {
    type: 'serial',
    theme: 'none',
    dataProvider: workItemTypeResults,
    valueAxes: [{
      gridColor: '#FFFFFF',
      gridAlpha: 0,
      dashLength: 0,
      integersOnly: true,
      title: 'Beyond Target Time (%)',
      totalText: '[[total]]',
      minimum: 0,
      maximum: 100,
    }],
    gridAboveGraphs: true,
    startDuration: 0,
    graphs: [{
      balloonFunction(graphDataItem) {
        if (graphDataItem && graphDataItem.values && graphDataItem.values.total > 0) {
          var balloontext = "<div style='margin:5px; font-size:19px;'><span style='font-size:13px;'>" +
          graphDataItem.category +'</span><br>' + graphDataItem.values.total + ' %<br>' +
          graphDataItem.dataContext.beyondTargetTime  + '/' + graphDataItem.dataContext.processed + ' Processed </div>';

          var targetTimesLabel = "<ul class='right'>";
          _.each(graphDataItem.dataContext.clientTargetTimes, function(targetTime) {
            targetTimesLabel += "<li style='text-align:right;'>"+ targetTime.workItemTypeName + ': ' + targetTime.targetTimeInHours + ' Hours</li>';
          });

          balloontext += targetTimesLabel;

          return balloontext;
        } else {
          return '';
        }
      },
      fillAlphas: 0.8,
      lineAlpha: 0.2,
      type: 'column',
      valueField: 'percentBeyondTarget',
      labelText: '[[value]]',
      labelPosition: 'top',
      labelFunction(graphDataItem) {
        if (graphDataItem.dataContext.processed === 0) {
          return '0 Processed';
        } else {
          return graphDataItem.values.total + ' % (' + graphDataItem.dataContext.beyondTargetTime  + '/' + graphDataItem.dataContext.processed + ')';
        }
      },
    }],
    chartCursor: {
      categoryBalloonEnabled: false,
      cursorAlpha: 0,
      zoomable: false,
    },
    categoryField: 'workItemType',
    categoryAxis: {
      gridPosition: 'start',
      gridAlpha: 0,
      tickPosition: 'start',
      tickLength: 5,
      title: 'Customer',
    },
    export: {
      enabled: !Meteor.isCordova,
      fileName: 'BeyondTarget_' + moment(currentDate).format('MMMM_YYYY'),
      libs: {
        path: 'https://www.amcharts.com/lib/3/plugins/export/libs/',
      },
    },

  });
};
