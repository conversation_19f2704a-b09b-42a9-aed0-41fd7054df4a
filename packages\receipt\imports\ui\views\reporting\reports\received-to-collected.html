 <template name="receivedToCollected">
  <div class="ui grid">
    <div class="five wide column">  
      <h5 style="display: inline-block; margin-left:5px; font-weight: lighter;">
        <i class="arrow left icon" id="toDashboard" style="display: inline; font-size:1.8rem"></i>
        Received to Collected
      </h5>
    </div>
    <div class="one wide column">
        <h5 style="display:inline"><i class="angle left icon" style="cursor: pointer; margin-left:10px; font-size:1.8rem;" id="backMonth"></i></h5>
      </div>
      <div class="four wide column">
        <center>
          <h5 style="display: inline; font-weight: lighter;">
            {{selectedMonth}}
          </h5>
        </center>
      </div>
      <div class="one wide column">
        {{#if isCurrentMonth}}
          <h5 style="display:inline"><i class="angle right icon" style="margin-right:10px; visibility: hidden; font-size:1.8rem;" id="forwardMonth"></i></h5>
        {{else}}
          <h5 style="display:inline"><i class="angle right icon" style="cursor: pointer; margin-right:10px; font-size:1.8rem;" id="forwardMonth"></i></h5> 
        {{/if}}
      </div>
    
    <div class="one wide column"></div>

    <div class="two wide column" style="text-align: right;">
      <div class="ui selection dropdown" id="client">
        <input name="client" type="hidden" value="default">
        <i class="dropdown icon"></i>
        <div class="text">ALL CLIENTS</div>
        <div class="menu">
          {{#each clients}}
            <div class="item" data-value="{{_id}}">{{name}}</div>
          {{/each}}
          <div class="item" data-value="">ALL CLIENTS</div>            
        </div>
      </div>       
    </div>
  </div>
  <div class="ui grid" style="height:88%; margin-bottom:0px;">
    <div class="one wide column"></div>
    <div class="fourteen wide column" style="height:100%;" id="chartContainer"></div>
    <div class="one wide column"></div>
  </div>
</template>