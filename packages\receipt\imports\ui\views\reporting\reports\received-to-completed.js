import './received-to-completed.html';

import { $ } from 'meteor/jquery';
import { CompanyProfiles } from '../../../../api/company-profiles/company-profiles';
import { Meteor } from 'meteor/meteor';
import { Template } from 'meteor/templating';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { currentSiteProfile } from '../../../../ui/helpers/current-site-profile';

var currentChart;

Template.receivedToCompleted.created = function onCreated() {
  var template = this;

  template.selectedClient = new ReactiveVar;
  template.fromDate = new ReactiveVar;
  template.toDate = new ReactiveVar;

  var selectedMonth;

  var clientQueryString = FlowRouter.getQueryParam('client');
  var monthQueryString = FlowRouter.getQueryParam('month');

  var curentMoment = moment();

  if (clientQueryString && clientQueryString.length > 0) {
    template.selectedClient.set(clientQueryString);
  }
  if (monthQueryString && monthQueryString.length === 6) {
    selectedMonth = moment(monthQueryString, 'YYYYMM');
    if (selectedMonth.year() >= curentMoment.year() && selectedMonth.month() > curentMoment.month()) {
      selectedMonth = curentMoment;
    }
  } else {
    selectedMonth = curentMoment;
  }

  var defaultFromDate = moment(selectedMonth).startOf('month').toDate();
  var defaultToDate = moment(selectedMonth).endOf('month').toDate();

  template.fromDate.set(defaultFromDate);
  template.toDate.set(defaultToDate);
};

Template.receivedToCompleted.rendered = function onRendered() {
  var template = this;

  template.$('#client').val(template.selectedClient.get());
  template.$('#client').material_select();

  refreshChart(template);
};

Template.receivedToCompleted.helpers({

  selectedMonth() {
    return moment(Template.instance().fromDate.get()).format('MMMM YYYY');
  },

  isCurrentMonth() {
    var currentFromMoment = moment(Template.instance().fromDate.get());
    var currentMoment = moment();

    return currentFromMoment.month() === currentMoment.month()
            && currentFromMoment.year() === currentMoment.year();
  },

  clients() {
    var siteProfile = currentSiteProfile();

    if (siteProfile) {
      var siteClients = siteProfile.configuration.clients;

      return _.sortBy(siteClients, function (client) {
        return client.name;
      });
    } else {
      return [];
    }
  },
});

Template.receivedToCompleted.events({

  'click #backMonth': function onClick(e, template) {
    var currentFromMoment = moment(template.fromDate.get());

    currentFromMoment.add(-1, 'months');

    template.fromDate.set(moment(currentFromMoment).startOf('month').toDate());
    template.toDate.set(moment(currentFromMoment).endOf('month').toDate());

    refreshChart(template);
  },

  'click #forwardMonth': function onClick(e, template) {
    var currentFromMoment = moment(template.fromDate.get());

    currentFromMoment.add(1, 'months');

    template.fromDate.set(moment(currentFromMoment).startOf('month').toDate());
    template.toDate.set(moment(currentFromMoment).endOf('month').toDate());

    refreshChart(template);
  },

  'click #toDashboard': function onClick(e, template) {
    var query = {
      month: moment(Template.instance().fromDate.get()).format('YYYYMM'),
    };

    var client = Template.instance().selectedClient.get();
    if (client) {
      query.client = client;
    }

    FlowRouter.go('dashboard',
      {},
      query);
  },

  'change #client': function onChange(e, template) {
    template.selectedClient.set(template.$('#client').val());
    refreshChart(template);
  },
});

var refreshChart = function refreshChart(template) {
  var fromDate = template.fromDate.get();
  var toDate = template.toDate.get();
  var client = template.selectedClient.get();
  var companyProfileId = CompanyProfiles.findOne()._id;
  var siteProfileId = currentSiteProfile()._id;

  if (client) {
    Meteor.call('internalTurnaroundPerWorkItemTypeReport', companyProfileId, siteProfileId, fromDate, toDate, client, function(err, res) {
      renderWorkItemTypeChart(res, fromDate);
    });
  } else {
    Meteor.call('internalTurnaroundPerInstallationReport', companyProfileId, siteProfileId, fromDate, toDate, function(err, res) {
      renderChart(res, fromDate, template);
    });
  }
};

var renderChart = function renderChart(res, currentDate, template) {
  var sortedResult = _.sortBy(res, function (a) {
    return a._id.clientName;
  });

  var clientTurnaroundResults = _.map(sortedResult, function (clientTurnaroundResult) {
    return {
      offshoreInstallationName: clientTurnaroundResult._id.offshoreInstallationName,
      averageTurnaround: Math.ceil((((clientTurnaroundResult.averageTurnaround/ 1000)/ 60) / 60)),
      minTurnaround: Math.ceil((((clientTurnaroundResult.minTurnaround/ 1000)/ 60) / 60)),
      maxTurnaround: Math.ceil((((clientTurnaroundResult.maxTurnaround/ 1000)/ 60) / 60)),
      processed: clientTurnaroundResult.processed,
    };
  });

  var chart = AmCharts.makeChart('chartContainer', {
    type: 'serial',
    theme: 'none',
    dataProvider: clientTurnaroundResults,
    valueAxes: [{
      gridColor: '#FFFFFF',
      gridAlpha: 0,
      dashLength: 0,
      integersOnly: true,
      title: 'Average Turnaround (Hours)',
      totalText: '[[total]]',
    }],
    gridAboveGraphs: true,
    startDuration: 0,
    graphs: [{
      balloonFunction(graphDataItem) {
        if (graphDataItem && graphDataItem.values && graphDataItem.values.total > 0) {
          return "<div style='margin:5px; font-size:19px;'><span style='font-size:13px;'>"
            + graphDataItem.category +'</span><br>' + graphDataItem.values.total + ' Hours<br>'
            + graphDataItem.dataContext.processed + ' Processed </div>' + "<ul class='right'><li style='text-align:right;'>Min: "
            + graphDataItem.dataContext.minTurnaround + " hours</li><li style='text-align:right;'>Max: "
            + graphDataItem.dataContext.maxTurnaround + ' hours</li></ul>';
        } else {
          return '';
        }
      },
      fillAlphas: 0.8,
      lineAlpha: 0.2,
      type: 'column',
      valueField: 'averageTurnaround',
      labelText: '[[value]]',
      labelPosition: 'top',
      labelFunction(graphDataItem) {
        if (graphDataItem.values.total == 0) {
          return '0 Processed';
        } else {
          return graphDataItem.values.total + ' Hours (' + graphDataItem.dataContext.processed + ')';
        }
      },
    }],
    chartCursor: {
      categoryBalloonEnabled: false,
      cursorAlpha: 0,
      zoomable: false,
    },
    categoryField: 'offshoreInstallationName',
    categoryAxis: {
      gridPosition: 'start',
      gridAlpha: 0,
      tickPosition: 'start',
      tickLength: 5,
      title: 'Asset',
    },
    export: {
      enabled: !Meteor.isCordova,
      fileName: 'ReceivedToReady_' + moment(currentDate).format('MMMM_YYYY'),
      libs: {
        path: 'https://www.amcharts.com/lib/3/plugins/export/libs/',
      },
    },
  });

  chart.addListener('clickGraphItem', function (event) {
    template.$('#client').val(event.item.dataContext.clientId);
    template.$('#client').material_select();

    template.selectedClient.set(template.$('#client').val());

    refreshChart(template);
  });
};

var renderWorkItemTypeChart = function renderWorkItemTypeChart(res, currentDate) {
  var sorted = _.sortBy(res, function (a) {
    return a._id.workItemType === 'Unknown' ? 'zzz' : a._id.workItemType;
  });

  var workItemTypes = _.map(sorted, function(workItemTypeResult) {
    return {
      workItemType: workItemTypeResult._id.workItemType,
      averageTurnaround: Math.ceil((((workItemTypeResult.averageTurnaround/ 1000)/ 60) / 60)),
      minTurnaround: Math.ceil((((workItemTypeResult.minTurnaround/ 1000)/ 60) / 60)),
      maxTurnaround: Math.ceil((((workItemTypeResult.maxTurnaround/ 1000)/ 60) / 60)),
      processed: workItemTypeResult.processed,
    };
  });

  var chart = AmCharts.makeChart('chartContainer', {
    type: 'serial',
    theme: 'none',
    dataProvider: workItemTypes,
    valueAxes: [{
      gridColor: '#FFFFFF',
      gridAlpha: 0,
      dashLength: 0,
      integersOnly: true,
      title: 'Average Turnaround (Hours)',
      totalText: '[[total]]',
    }],
    gridAboveGraphs: true,
    startDuration: 0,
    graphs: [{
      balloonFunction(graphDataItem) {
        if (graphDataItem && graphDataItem.values && graphDataItem.values.total > 0) {
          return "<div style='margin:5px; font-size:19px;'><span style='font-size:13px;'>"
            + graphDataItem.category +'</span><br>' + graphDataItem.values.total + ' Hours<br>'
            + graphDataItem.dataContext.processed + ' Processed </div>' + "<ul class='right'><li style='text-align:right;'>Min: "
            + graphDataItem.dataContext.minTurnaround + " hours</li><li style='text-align:right;'>Max: "
            + graphDataItem.dataContext.maxTurnaround + ' hours</li></ul>';
        } else {
          return '';
        }
      },
      fillAlphas: 0.8,
      lineAlpha: 0.2,
      type: 'column',
      valueField: 'averageTurnaround',
      labelText: '[[value]]',
      labelPosition: 'top',
      labelFunction(graphDataItem) {
        if (graphDataItem.values.total == 0) {
          return '0 Processed';
        } else {
          return graphDataItem.values.total + ' Hours (' + graphDataItem.dataContext.processed + ')';
        }
      },
    }],
    chartCursor: {
      categoryBalloonEnabled: false,
      cursorAlpha: 0,
      zoomable: false,
    },
    categoryField: 'workItemType',
    categoryAxis: {
      gridPosition: 'start',
      gridAlpha: 0,
      tickPosition: 'start',
      tickLength: 5,
      title: 'Container Type',
    },
    export: {
      enabled: !Meteor.isCordova,
      fileName: 'ReceivedToReady_' + moment(currentDate).format('MMMM_YYYY'),
      libs: {
        path: 'https://www.amcharts.com/lib/3/plugins/export/libs/',
      },
    },
  });
};
