<template name="workItemLifecycleDetails">

  <form>
  	<div class="row" style="margin-bottom:0px;">

  		<div class="card form-card col s12 m12 l10 offset-l1">
    	
        <div class="card-title {{themeForeClass}}">
      		Details
      	</div>

        <div class="row" style="margin-bottom:0px;">

          <div class="row">
              <div class="input-field col s12 m12 l12">
                <label for="identifierInput" id="identifierInputLabel">Container</label>
                <input type="text" id="identifierInput" name="identifierInput" class="typeahead" required autocomplete="off" spellcheck="off"
           data-source="vorResults" data-highlight="true" data-autoselect="true" data-limit="10" data-options='{{typeaheadOptions}}'>

            </div>
          </div>


          <div class="row">
            <div class="input-field col s6">
              <select id="client" name="client">

                <option value="" disabled selected>Select Company</option>
                
                {{#each clients}}
                  <option value="{{_id}}">{{name}}</option>
                {{/each}}
                
              </select>
              <label>Company</label>
            </div>
           </div>

          <div class="row">
            <div class="input-field col s6">
              <input id="clientLocation" name="clientLocation" type="text" class="typeahead" autocomplete="off" spellcheck="off"
           data-source="locations" data-highlight="true" data-options='{{typeaheadOptions}}'>
              <label for="clientLocation" id="locationLabel">Asset</label>
            </div>
            
          </div>

        </div>

        <div class="row">
          <div class="card-action">
            <span class="right">
              <a href="/" class="waves-effect waves-light btn white black-text" style="margin-right:0px;" id="back">BACK</a>
              <button type="submit" class="waves-effect waves-light btn {{themeBackClass}} white-text">Update</button>
            </span>
          </div>
        </div>
      
      </div>

  	</div>

  </form>
</template>
