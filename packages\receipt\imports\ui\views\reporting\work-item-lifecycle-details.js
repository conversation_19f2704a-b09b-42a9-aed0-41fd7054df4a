import './work-item-lifecycle-details.html';

import { $ } from 'meteor/jquery';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Meteor } from 'meteor/meteor';
import { ReactiveVar } from 'meteor/reactive-var';
import { Template } from 'meteor/templating';
import { WorkItemEvents } from '../../../api/work-item-events/work-item-events';
import { currentSiteProfile } from '../../../ui/helpers/current-site-profile';

function updateWhetherAllowedToSubmit(template) {
  if (template.$('#identifierInput').val() && template.$('#client').val()) {
    template.$('input[type="submit"], button[type="submit"]').prop('disabled', false);
  } else {
    template.$('input[type="submit"], button[type="submit"]').prop('disabled', true);
  }
}

Template.workItemLifecycleDetails.onCreated(function onCreated() {
  const template = this;

  template.netValue = new ReactiveVar;
  template.netValue.set('');

  template.numberOfContentLines = new ReactiveVar;
  template.numberOfContentLines.set(1);

  template.contentLines = new ReactiveVar;

  template.autorun(() => {
    const workItemEventLifecycleId = FlowRouter.getParam('lifecycleId');
    template.subscribe('latestWorkItemEvent', workItemEventLifecycleId);
  });
});

Template.workItemLifecycleDetails.onRendered(function onRendered() {
  const template = this;

  template.$('select').material_select();
  Meteor.typeahead.inject();

  const detailsForm = template.$('form');

  template.autorun(() => {
    const workItemEventLifecycleId = FlowRouter.getParam('lifecycleId');
    const currentWorkItemEvent = WorkItemEvents.findOne({ lifecycleId: workItemEventLifecycleId });

    if (currentWorkItemEvent) {
      const latestWorkItemEvent = currentWorkItemEvent;
      const existingPlannedDetails = currentWorkItemEvent.lifecycleData.planned;
      const existingReceivedDetails = currentWorkItemEvent.lifecycleData.received;

      if (existingPlannedDetails) {
        detailsForm.find('[name=identifierInput]').val(latestWorkItemEvent.identifier);
        detailsForm.find('#identifierInputLabel').addClass('active');

        if (existingPlannedDetails.workItemType) {
          detailsForm.find('[name=type]').val(existingPlannedDetails.workItemType._id);
        }

        if (existingPlannedDetails.client) {
          detailsForm.find('[name=client]').val(existingPlannedDetails.client._id);
        }

        if (existingPlannedDetails.clientLocation) {
          detailsForm.find('[name=clientLocation]').val(existingPlannedDetails.clientLocation);
          detailsForm.find('#locationLabel').addClass('active');
        }

        if (existingPlannedDetails.contents && existingPlannedDetails.contents.length > 0) {
          const contentLines = existingPlannedDetails.contents.map((contentLine, index, array) => {
            return {
              id: index + 1,
              isFirst: index === 0,
              isLast: index === (array.length - 1),
              fieldValue: contentLine,
            };
          });
          template.numberOfContentLines.set(contentLines.length);
          template.contentLines.set(contentLines);
        } else {
          template.contentLines.set([{
            id: 1,
            isFirst: true,
            isLast: true,
            fieldValue: '',
          }]);
        }
      }

      if (existingReceivedDetails) {
        if (existingReceivedDetails.truckNoPlate) {
          detailsForm.find('[name=truckNoPlate]').val(existingReceivedDetails.truckNoPlate);
          detailsForm.find('#truckLabel').addClass('active');
        }

        if (existingReceivedDetails.tareWeight) {
          detailsForm.find('[name=tareWeight]').val(existingReceivedDetails.tareWeight);
          detailsForm.find('#tareWeightLabel').addClass('active');
        }

        if (existingReceivedDetails.grossWeight) {
          detailsForm.find('[name=grossWeight]').val(existingReceivedDetails.grossWeight);
          detailsForm.find('#grossWeightLabel').addClass('active');
        }

        if (existingReceivedDetails.netWeight) {
          detailsForm.find('[name=netWeight]').val(existingReceivedDetails.netWeight);
          detailsForm.find('#netLabel').addClass('active');
        }
      }
    }

    template.$('select').material_select();
  });
});

Template.workItemLifecycleDetails.helpers({
  vorResults(query, sync, callback) {
    Meteor.call('vorSearch', query, {}, (err, res) => {
      if (err) {
        console.log(err);
        return;
      }
      callback(res.map((v) => ({ value: v.Name })));
    });
  },
  clients() {
    const siteProfile = currentSiteProfile();

    if (siteProfile) {
      const siteClients = siteProfile.configuration.clients;
      return _.sortBy(siteClients, (client) => client.name);
    }
    return [];
  },
  types() {
    const siteProfile = currentSiteProfile();

    if (siteProfile) {
      const siteWorkItemTypes = siteProfile.configuration.workItemTypes;
      return _.sortBy(siteWorkItemTypes, (type) => type.name);
    }
    return [];
  },
  wasteDescriptions() {
    const siteProfile = currentSiteProfile();

    if (siteProfile) {
      return siteProfile
        .configuration
        .wasteDescriptions
        .map((description) => description.name);
    }
    return [];
  },
  locations() {
    const siteProfile = currentSiteProfile();

    if (siteProfile) {
      return siteProfile
        .configuration
        .locations
        .map((location) => location.name);
    }
    return [];
  },
  netValue() {
    const netValue = Template.instance().netValue.get();
    return netValue;
  },
  weighBridgeInUse() {
    const siteProfile = currentSiteProfile();
    return siteProfile && siteProfile.identifier === 'augean-tullos';
  },
  contentLines() {
    const contentLines = Template.instance().contentLines.get();
    return contentLines;
  },
});

Template.workItemLifecycleDetails.events({
  'click #back': function onClick(event) {
    event.preventDefault();
    const pageToReturnTo = FlowRouter.getQueryParam('returnTo');
    if (pageToReturnTo === 'workItemLog') {
      FlowRouter.go('workItemLog');
    } else {
      FlowRouter.go('workItemOverview');
    }
  },
  'click button[type=submit]': function onClick(event, templateInstance) {
    event.preventDefault();

    const form = $(event.target).parents('form:first');
    const selectedClient = form.find('#client option:selected');
    const selectedType = form.find('#type option:selected');

    const contentLineVals = [];
    templateInstance.$('.content-line-input').each(function () {
      const contentLineVal = $(this).val();

      if (contentLineVal && contentLineVal.length > 0) {
        contentLineVals.push(contentLineVal);
      }
    });

    const workItemEventLifecycleId = FlowRouter.getParam('lifecycleId');
    const currentWorkItemEvent = WorkItemEvents.findOne({ lifecycleId: workItemEventLifecycleId });

    const updatedDetails = {
      identifier: form.find('#identifierInput').val(),
      client: {
        _id: selectedClient.val(),
        name: selectedClient.text(),
      },
      workItemType: {
        _id: selectedType.val(),
        name: selectedType.text(),
      },
      clientLocation: form.find('#clientLocation').val(),
      truckNoPlate: form.find('[name=truckNoPlate]').val(),
      tareWeight: form.find('[name=tareWeight]').val(),
      grossWeight: form.find('[name=grossWeight]').val(),
      netWeight: form.find('[name=netWeight]').val(),
      contents: contentLineVals,
    };

    Meteor.call(
      'updateWorkItemDetails',
      currentWorkItemEvent._id,
      updatedDetails,
      () => { FlowRouter.go('workItemLog'); },
    );
  },
  'click #addContentLine': function onClick(event, templateInstance) {
    event.preventDefault();

    const contentLines = templateInstance.contentLines.get();
    const currentNumberOfContentLines = templateInstance.numberOfContentLines.get();
    const newContentLineNumber = currentNumberOfContentLines;

    const mappedContentLines = contentLines.map(
      (contentLine, index) =>
        ({
          id: contentLine.id,
          isFirst: index === 0,
          isLast: false,
          fieldValue: $(`#contents${contentLine.id}`).val(),
        }));

    mappedContentLines.push({
      id: newContentLineNumber + 1,
      fieldValue: '',
      isLast: true,
      isFirst: false,
    });

    templateInstance.numberOfContentLines.set(mappedContentLines.length);
    templateInstance.contentLines.set(mappedContentLines);
  },
  'click #removeContentLine': function onClick(event, templateInstance) {
    event.preventDefault();

    const contentLines = templateInstance.contentLines.get();
    const currentNumberOfContentLines = templateInstance.numberOfContentLines.get();
    const newContentLineNumber = currentNumberOfContentLines - 1;

    const mappedContentLines = contentLines.map((contentLine, index) => ({
      id: contentLine.id,
      isFirst: (index === 0),
      isLast: (index === contentLines.length - 2),
      fieldValue: $(`#contents${contentLine.id}`).val(),
    }));

    mappedContentLines.splice(-1, 1);

    templateInstance.numberOfContentLines.set(newContentLineNumber);
    templateInstance.contentLines.set(mappedContentLines);
  },
  'keyup #identifierInput, change #client': function onKeyup(event, templateInstance) {
    updateWhetherAllowedToSubmit(templateInstance);
  },
  'keyup #grossWeight, keyup #tareWeight': function onKeyup(event, templateInstance) {
    const tareWeight = templateInstance.$('#tareWeight').val();
    const grossWeight = templateInstance.$('#grossWeight').val();

    if (!isNaN(tareWeight) && !isNaN(grossWeight)) {
      templateInstance.netValue.set(grossWeight - tareWeight);
      templateInstance.$('#netLabel').addClass('active');
    }
  },
  'focus #clientLocation, focus #identifierInput': function onFocus(event) {
    $(event.target)
      .parents('.input-field')
      .find('label')
      .addClass('active');
  },
  'blur #clientLocation, blur #identifierInput': function onBlur(event) {
    const inputVal = $(event.target).val();
    const inputLabel = $(event.target)
      .parents('.input-field')
      .find('label');

    if (inputVal) {
      inputLabel.addClass('active');
    } else {
      inputLabel.removeClass('active');
    }
  },
  'keydown #grossWeight, keydown #tareWeight': function onKeydown(event) {
    // Allow: backspace, delete, tab, escape, enter and .
    if ($.inArray(event.keyCode, [46, 8, 9, 27, 13, 110, 190]) !== -1 ||
    // Allow: Ctrl+A
        (event.keyCode === 65 && event.ctrlKey === true) ||
    // Allow: Ctrl+C
        (event.keyCode === 67 && event.ctrlKey === true) ||
    // Allow: Ctrl+X
        (event.keyCode === 88 && event.ctrlKey === true) ||
    // Allow: home, end, left, right
        (event.keyCode >= 35 && event.keyCode <= 39)) {
      // let it happen, don't do anything
      return;
    }
    // Ensure that it is a number and stop the keypress
    if ((event.shiftKey || (event.keyCode < 48 || event.keyCode > 57)) &&
        (event.keyCode < 96 || event.keyCode > 105)) {
      event.preventDefault();
    }
  },
});
