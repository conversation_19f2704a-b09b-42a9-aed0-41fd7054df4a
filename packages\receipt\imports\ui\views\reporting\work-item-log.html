<template name="workItemLog">
	{{#unless isTableReady}}
		{{> loading}}
    {{/unless}}
    <div class="ui padded grid" style="margin-top:1em;">
        <div class="eight wide column">
            <div class="ui button {{themeBackClass}}" id="csvExportButton">
                Export
            </div>
        </div>
        <div class="eight wide column">
            <div class="ui input" style="float: right;">
                <input type="text" placeholder="Filter" id="tableFilter" name="tableFilter" >
            </div>
        </div>
    </div>
    <div class="ui padded grid">
        <div class="sixteen wide column">
            {{> reactiveTable class="ui selectable cell striped single line table" settings=settings ready=isReady}}  
        </div>
    </div>
    <div id="exportModal" class="ui modal">
        <div class="header">Select Date Range to Export:</div>
        <div class="content">
            <form class="ui form">
                <div class="two fields">                             
                    <div class="action field" style="padding-right:2em">
                        <label>From</label>
                        <div class="ui calendar" id="fromDatepicker">
                            <div class="ui input left icon">
                                <i class="calendar icon"></i>
                                <input type="text" placeholder="Date" />
                            </div>
                        </div>
                    </div>
                    <div class="action field" style="padding-right:2em">
                        <label>To</label>
                        <div class="ui calendar" id="toDatepicker">
                            <div class="ui input left icon">
                                <i class="calendar icon"></i>
                                <input type="text" placeholder="Date" id="toDate" />
                            </div>
                        </div>
                    </div>
                </div> 
            </form>
        </div>
        <div class="actions">
            <div class="ui white cancel button" id="cancelExport" >
                CANCEL
            </div>
            <div class="ui button {{themeBackClass}} ok" id="completeExport">
                Export
            </div>
        </div>
    </div>
</template>