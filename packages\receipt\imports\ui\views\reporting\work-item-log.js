import './work-item-log.html';

import { $ } from 'meteor/jquery';
import { CompanyProfiles } from '../../../api/company-profiles/company-profiles';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Meteor } from 'meteor/meteor';
import { ReactiveTable } from 'meteor/fcallem:reactive-table-semantic';
import { ReactiveVar } from 'meteor/reactive-var';
import { Template } from 'meteor/templating';
import { WorkItemEventStates } from '../../../shared/work-item-event-states';
import { currentSiteProfile } from '../../../ui/helpers/current-site-profile';
import moment from 'moment';

// Setup Datepicker.
// https://github.com/mdehoog/Semantic-UI/blob/49178d6d0321357b959ff56f424ea1b3ed76a6ed/src/definitions/modules/calendar.js#L902-L1279
const calendarSettingsFormatter = {
  date: (date, settings) => {
    if (_.isUndefined(date)) return '';
    const day = (`0${date.getDate()}`).slice(-2); // zero pad.
    const month = settings.text.monthsShort[date.getMonth()];
    const year = date.getFullYear();
    return day + '-' + month + '-' + year;
  },
  time: (date, settings) => {
    if (_.isUndefined(date)) return '';
    const hours = (`0${date.getHours()}`).slice(-2); // zero pad.
    const minutes = (`0${date.getMinutes()}`).slice(-2); // zero pad.
    return hours + ':' + minutes;
  },
};

Template.workItemLog.onCreated(function onCreated() {
  const template = this;

  const companyProfileId = CompanyProfiles.findOne()._id;
  const siteProfileId = currentSiteProfile()._id;

  template.isReady = new ReactiveVar(true);
  template.filter = new ReactiveTable.Filter('companyFilter', ['companyId']);
  template.filter.set(companyProfileId);

  template.filter = new ReactiveTable.Filter('siteFilter', ['siteId']);
  template.filter.set(siteProfileId);

  template.tableFilter = new ReactiveTable.Filter('tableFilter', []);
  template.tableFilter.set('');
});

Template.workItemLog.onRendered(() => {
  const fromDatepicker = $('#fromDatepicker');
  // Show modal and initialise its contents
  fromDatepicker.calendar({
    type: 'date',
    maxDate: moment().subtract(7, 'days').startOf('day').utc().toDate(),
    ampm: false,
    formatter: calendarSettingsFormatter,
  });

  Meteor.defer(() => {
    $('#fromDatepicker').calendar('set date', moment().subtract(7, 'days').startOf('day').utc().toDate());
  });

  const toDatepicker = $('#toDatepicker');
  // Show modal and initialise its contents
  toDatepicker.calendar({
    type: 'date',
    maxDate: moment().utc().toDate(),
    ampm: false,
    formatter: calendarSettingsFormatter,
  });

  Meteor.defer(() => {
    $('#toDatepicker').calendar('set date', moment().utc().toDate());
  });
});

Template.workItemLog.events({
  'click #csvExportButton': function onClick() {
    $('#exportModal')
      .modal({
        autofocus: false,
        transition: 'horizontal flip',
        duration: 0,
        onDeny() {
          $('#exportModal').modal('hide');
        },
        onApprove() {
          const fromDate = $('#fromDatepicker').calendar('get date');
          const toDate = $('#toDatepicker').calendar('get date');
          const fileName = `ContainerLog${moment().format('YYYY-MM-DD-HH-mm')}.csv`;
          Meteor.call(
            'workItemEvents.exportAsCsv',
            moment(fromDate).startOf('day').toDate(),
            moment(toDate).endOf('day').toDate(),
            (err, fileContent) => {
              if (fileContent) {
                const a = document.createElement('a');
                document.body.appendChild(a);
                const blob = new Blob([fileContent], {
                  type: 'text/plain;charset=utf-8',
                });
                const url = window.URL.createObjectURL(blob);
                a.href = url;
                a.download = fileName;
                a.click();
                window.URL.revokeObjectURL(url);
              }
              if (err) {
                console.log(err);
              }
            },
          );
          $('#exportModal').modal('hide');
        },
      })
      .modal('show', function onShow() {
        const fromDatepicker = $('#fromDatepicker');
        // Show modal and initialise its contents
        fromDatepicker.calendar({
          type: 'date',
          maxDate: moment().subtract(7, 'days').startOf('day').utc().toDate(),
          ampm: false,
          formatter: calendarSettingsFormatter,
        });

        Meteor.defer(() => {
          $('#fromDatepicker').calendar('set date', moment().subtract(7, 'days').startOf('day').utc().toDate());
        });

        const toDatepicker = $('#toDatepicker');
        // Show modal and initialise its contents
        toDatepicker.calendar({
          type: 'date',
          maxDate: moment().utc().toDate(),
          ampm: false,
          formatter: calendarSettingsFormatter,
        });

        Meteor.defer(() => {
          $('#toDatepicker').calendar('set date', moment().utc().toDate());
        });
        const d = document.getElementsByClassName('ui dimmer modals page disabled override transition visible active');
        d[0].className = 'ui dimmer modals page transition visible active';
      });
  },
  'keyup #tableFilter, input #tableFilter': function onKeyup(event, template) {
    const input = $(event.target).val();
    template.tableFilter.set(input);
  },
});

Template.workItemLog.helpers({
  isTableReady() {
    return Template.instance().isReady.get();
  },
  settings() {
    return {
      ready: Template.instance().isReady,
      collection: 'latest-work-item-events',
      filters: ['companyFilter', 'siteFilter', 'tableFilter'],
      rowsPerPage: 50,
      showFilter: false,
      fields: [
        {
          key: 'identifier',
          label: 'Container #',
          sortOrder: 2,
          sortDirection: 'ascending',
        },
        {
          key: 'lifecycleData.planned.description',
          label: 'Description',
          fn(value) {
            if (value) {
              return value;
            }
            return '-';
          },
        },
        {
          key: 'lifecycleData.planned.clientLocation',
          label: 'Asset',
          fn(value) {
            if (value) {
              return value;
            }
            return '-';
          },
        },
        {
          key: 'latestVorInformation.clientName',
          label: 'Client',
          fn(value) {
            if (value) {
              return value;
            }
            return '-';
          },
        },
        {
          key: 'latestVorInformation.plannedDateTime',
          label: 'Exp Delivery',
          sortOrder: 0, 
          sortDirection: 'descending',
          fn(value) {
            if (value) {
              return moment(value).format('DD/MM/YYYY');
            }
            return '-';
          },
        },
        {
          key: 'lifecycleData.received.timestamp',
          label: 'Received',
          sortOrder: 1, 
          sortDirection: 'descending',
          fn(value) {
            if (value) {
              return moment(value).format('DD/MM/YYYY HH:mm');
            }
            return '-';
          },
        },
        {
          key: 'lifecycleData.completed.timestamp',
          label: 'Completed',
          fn(value) {
            if (value) {
              return moment(value).format('DD/MM/YYYY HH:mm');
            }
            return '-';
          },
        },
        {
          key: 'lifecycleData.collected.timestamp',
          label: 'Collected',
          fn(value) {
            if (value) {
              return moment(value).format('DD/MM/YYYY HH:mm');
            }
            return '-';
          },
        },
        {
          key: 'state',
          label: 'Status',
          fn(value) {
            if (value === WorkItemEventStates.PLANNED) {
              return 'Incoming';
            }
            else if (value === WorkItemEventStates.RECEIVED) {
              return 'Preparation';
            }
            else if (value === WorkItemEventStates.INPROGRESS) {
              return 'Waste Removal';
            }
            else if (value === WorkItemEventStates.COMPLETED) {
              return 'Awaiting Collection';
            }
            else if (value === WorkItemEventStates.COLLECTED) {
              return 'Collected';
            }
            return '';
          },
        },
      ],
      showColumnToggles: false,
    };
  },
});
