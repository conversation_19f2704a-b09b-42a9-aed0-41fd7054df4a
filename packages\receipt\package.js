const glob = Npm.require('glob');
const fs = Npm.require('fs');
const path = Npm.require('path');

const filesInDir = (src) => {
  const pathPrefix = 'packages/receipt';
  const fullPath = path.join(pathPrefix, src);

  return (glob.sync(`${fullPath}/**/*`) || [])
    .filter((filePath) => filePath && fs.lstatSync(filePath).isFile())
    .map((filePath) => filePath.replace(pathPrefix, './'));
};

Package.describe({
  name: 'receipt',
  version: '0.0.1',
  // Brief, one-line summary of the package.
  summary: '',
  // URL to the Git repository containing the source code for this package.
  git: '',
  // By default, Meteor will default to using README.md for documentation.
  // To avoid submitting documentation, set this field to null.
  documentation: 'README.md',
});

Npm.depends({
  '@google-cloud/storage': '1.1.1',
  '@babel/runtime': '7.24.1',
  bcrypt: '3.0.8',
  'body-parser': '1.15.2',
  'fast-crc32c': '1.0.4',
  jquery: '2.2.4',
  'meteor-node-stubs': '1.2.9',
  moment: '2.20.1',
  pickadate: '3.5.6',
  select2: '4.0.3',
  winston: '2.4.7',
});

Package.onUse((api) => {
  api.versionsFrom('2.6.1');
  api.use([
    'accounts-password',
    'arsnebula:darwin@1.0.0',
    'blaze@2.5.0',
    'blaze-html-templates@1.2.1',
    'chart:chart@1.0.1-beta.4',
    'check',
    'clinical:csv@0.2.0',
    'collection-manager@0.0.1',
    'dynamic-import@0.2.0',
    'ecmascript',
    'edgee:slingshot@0.7.1',
    'ejson@1.1.0',
    'em0ney:amcharts@3.17.3',
    'email',
    'fastclick@1.0.13',
    'fcallem:reactive-table-semantic@0.0.1',
    'harrison:papa-parse@1.1.7',
    'http@1.3.0',
    'jamesfebin:azure-blob-upload@1.0.0',
    'juliancwirko:postcss@1.2.0',
    'juliancwirko:s-alert@3.2.0',
    'juliancwirko:s-alert-scale@3.1.3',
    'kadira:blaze-layout@2.3.0',
    'less@2.8.0',
    'logging@1.1.19',
    'matb33:collection-hooks@1.1.4',
    'mdg:camera@1.4.1',
    'mediator@0.0.1',
    'meteor',
    'meteor-base',
    'meteorhacks:aggregate@1.3.0',
    'meteorhacks:picker@1.0.3',
    'minifier-js@2.2.1',
    'mobile-experience',
    'momentjs:moment@2.19.4',
    'mongo',
    'mquandalle:perfect-scrollbar@0.6.5_2',
    'natestrauser:font-awesome@4.6.3',
    'okland:camera-ui@0.0.2',
    'ostrio:flow-router-extra@3.9.0',
    'percolate:synced-cron@1.3.2',
    'planettraining:material-design-icons-font@2.2.3',
    'random@1.0.10',
    'router@0.0.1',
    'reactive-var@1.0.11',
    'reload@1.1.11',
    'semantic:ui@2.2.6_5',
    'sergeyt:typeahead@0.11.1_9',
    'session@1.1.7',
    'shell-server',
    'spacebars-compiler@1.2.1',
    'standard-minifier-js',
    'templating@1.4.1',
    'tracker',
    'ui@1.0.13',
    'underscore']);
  api.addFiles(filesInDir('./client/styles/'), 'client');
  api.addFiles('./imports/startup/server/index.js', 'server');
  api.addAssets(filesInDir('/public'), ['client', 'server']);
  api.addAssets(filesInDir('/private'), 'server');
  api.mainModule('receipt-package-client.js', 'client');
  api.mainModule('receipt-package-server.js', 'server');
});

Package.onTest((api) => {
  api.use(['ecmascript', 'meteortesting:mocha']);
  api.use('receipt');
  api.mainModule('receipt-package-client-tests.js', 'client');
  api.mainModule('receipt-package-server-tests.js', 'server');
});
