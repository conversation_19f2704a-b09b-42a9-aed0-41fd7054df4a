import { Meteor } from 'meteor/meteor';
import { Template } from 'meteor/templating';
import path from 'path';
import './imports/startup/client/index';

Meteor.startup(() => {
  if (Meteor.isClient) {
    // Careful here as these helpers get added to global template helpers -
    // and can conflict with other package helpers with same name.

    // This helpers are deliberately take package name as a parameter so
    // they are not specific to a package.
    Template.registerHelper('publicUrlFor', (pkgName, relativePath) => {
      const pathToPackage = path.join('/packages', pkgName);
      const pathToPublicDir = path.join(pathToPackage, 'public');
      return path.join(pathToPublicDir, relativePath);
    });

    Template.registerHelper('concat', (...strs) => (strs && strs.length
      ? strs.filter((str) => typeof str === 'string').join('')
      : ''));
  }
});

export const name = 'receipt-client';
