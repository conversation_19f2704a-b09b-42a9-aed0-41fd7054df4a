import { BlazeLayout } from 'meteor/kadira:blaze-layout';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import path from 'path';

class _Router {
  constructor(routePrefix, layoutTemplateName = 'layout', router = FlowRouter) {
    this.router = router;
    this.baseRoute = routePrefix || '';
    this.layout = layoutTemplateName;
  }

  fullUrl(url) {
    return path.join('/', this.baseRoute, url);
  }

  route(routeName, url, templateName = null) {
    const self = this;

    self.router.route(self.fullUrl(url), {
      name: routeName,
      action(params, queryParams) {
        BlazeLayout.render(self.layout, { main: templateName || routeName });
      },
    });

    return self;
  }

  routeWithNoTemplate(routeName, url) {
    const self = this;
    this.router.route(self.fullUrl(url), {
      name: routeName,
      action(params, queryParams) {
        BlazeLayout.render(self.layout);
      },
    });

    return self;
  }

  group(name, prefix) {
    const self = this;

    const group = self.router.group({
      prefix,
      name,
    });

    return new _Router(null, self.layout, group);
  }

  redirect(routeName, url, actionFn) {
    const self = this;

    self.router.route(self.fullUrl(url), {
      name: routeName,
      action: actionFn,
    });

    return self;
  }
}

export const Router = _Router;
export const name = 'router';
