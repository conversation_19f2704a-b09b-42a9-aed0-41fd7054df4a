self.addEventListener('install', (event) => {
  console.log('[ServiceWorker:install] Installing service worker.');
  self.skipWaiting();
});
self.addEventListener('activate', () => {
  console.log('[ServiceWorker:activate] Activating service worker.');
  self.clients.claim();
});
self.addEventListener('fetch', (event) => {
  console.log('[ServiceWorker:fetch] Fetch Event: ', event);
  if (event.request.cache === 'only-if-cached' && event.request.mode !== 'same-origin') {
    return;
  }
  event.respondWith(fetch(event.request));
});
